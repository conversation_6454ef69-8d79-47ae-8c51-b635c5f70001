## 第2章 线性表
【考纲内容】
(一)线性表的基本概念
(二)线性表的实现
顺序存储;链式存储
(三)线性表的应用
【知识框架】
*   线性表
    *   顺序存储：顺序表
    *   链式存储
        *   单链表、双链表、循环链表 (指针实现)
        *   静态链表 (借助数组实现)

【复习提示】
线性表是算法题命题的重点。这类算法题的实现比较容易且代码量较少,但是要求具有最优的性能(时间/空间复杂度),才能获得满分。因此,应牢固掌握线性表的各种基本操作(基于两种存储结构),在平时的学习中多注重培养动手能力。另需提醒的是,算法最重要的是思想!考场上的时间紧迫,在试卷上不一定要求代码具有实际的可执行性,因此应尽力表达出算法的思想和步骤,而不必过于拘泥所有细节。此外,采用时间/空间复杂度较差的方法也能拿到大部分分数,因此在时间紧迫的情况下,建议直接采用暴力法。注意,算法题只能用C/C++语言实现。

### 2.1 线性表的定义和基本操作
#### 2.1.1 线性表的定义
线性表是具有相同数据类型的$n(n \ge 0)$个数据元素的有限序列,其中$n$为表长,当$n=0$时线性表是一个空表。若用$L$命名线性表,则其一般表示为
$L = (a_1, a_2, ..., a_i, a_{i+1}, ..., a_n)$
式中,$a_1$是唯一的“第一个”数据元素,也称表头元素;$a_n$是唯一的“最后一个”数据元素,也称表尾元素。除第一个元素外,每个元素有且仅有一个直接前驱。除最后一个元素外,每个元素有且仅有一个直接后继(“直接前驱”和“前驱”、“直接后继”和“后继”通常被视为同义词)。以上就是线性表的逻辑特性,这种线性有序的逻辑结构正是线性表名字的由来。

由此,我们得出线性表的特点如下:
*   表中元素的个数有限。
*   表中元素具有逻辑上的顺序性,表中元素有其先后次序。
*   表中元素都是数据元素,每个元素都是单个元素。
*   表中元素的数据类型都相同,这意味着每个元素占有相同大小的存储空间。
*   表中元素具有抽象性,即仅讨论元素间的逻辑关系,而不考虑元素究竟表示什么内容。

**注意**
线性表是一种逻辑结构,表示元素之间一对一的相邻关系。顺序表和链表是指存储结构,两者属于不同层面的概念,因此不要将其混淆。

#### 2.1.2 线性表的基本操作
一个数据结构的基本操作是指其最核心、最基本的操作。其他较复杂的操作可通过调用其基本操作来实现。线性表的主要操作如下。
*   InitList(&L): 初始化表。构造一个空的线性表。
*   Length(L): 求表长。返回线性表$L$的长度,即$L$中数据元素的个数。
*   LocateElem(L,e): 按值查找操作。在表$L$中查找具有给定关键字值的元素。
*   GetElem(L,i): 按位查找操作。获取表$L$中第$i$个位置的元素的值。
*   ListInsert(&L,i,e): 插入操作。在表$L$的第$i$个位置上插入指定元素$e$。
*   ListDelete(&L,i,&e): 删除操作。删除表$L$中第$i$个位置的元素,并用$e$返回删除元素的值。
*   PrintList(L): 输出操作。按前后顺序输出线性表$L$的所有元素值。
*   Empty(L): 判空操作。若$L$为空表,则返回true,否则返回false。
*   DestroyList(&L): 销毁操作。销毁线性表,并释放线性表$L$所占用的内存空间。

**注意**
①基本操作的实现取决于采用哪种存储结构,存储结构不同,算法的实现也不同。②符号“&”表示C++语言中的引用调用,在C语言中采用指针也可达到同样的效果。

#### 2.1.3 本节试题精选
**单项选择题**
01. 线性表是具有n个()的有限序列。
    A. 数据表
    B. 字符
    C. 数据元素
    D. 数据项
02. 下列几种描述中,()是一个线性表。
    A. 由n个实数组成的集合
    B. 由100个字符组成的序列
    C. 所有整数组成的序列
    D. 邻接表
03. 在线性表中,除开始元素外,每个元素()。
    A. 只有唯一的前驱元素
    B. 只有唯一的后继元素
    C. 有多个前驱元素
    D. 有多个后继元素
04. 若非空线性表中的元素既没有直接前驱,又没有直接后继,则该表中有()个元素。
    A. 1
    B. 2
    C. 3
    D. n

### 2.1.4 答案与解析
**单项选择题**
01. C
线性表是由具有相同数据类型的有限数据元素组成的,数据元素是由数据项组成的。
02. B
线性表定义的要求为:相同数据类型、有限序列。选项C的元素个数是无穷个,错误;选项A集合中的元素没有前后驱关系,错误;选项D属于一种存储结构,本题要求选出的是一个具体的线性表,不要将二者混为一谈。只有选项B符合线性表定义的要求。
03. A
线性表中,除最后一个(或第一个)元素外,每个元素都只有一个后继(或前驱)元素。
04. A
线性表中的第一个元素没有直接前驱,最后一个元素没有直接后继;当线性表中仅有一个元素时,该元素既没有直接前驱,又没有直接后继。

### 2.2 线性表的顺序表示
#### 2.2.1 顺序表的定义
命题追踪 ▶ (算法题)顺序表的应用(2010、2011、2018、2020)

线性表的顺序存储也称顺序表。它是用一组地址连续的存储单元依次存储线性表中的数据元素,从而使得逻辑上相邻的两个元素在物理位置上也相邻。第1个元素存储在顺序表的起始位置,第$i$个元素的存储位置后面紧接着存储的是第$i+1$个元素,称$i$为元素$a_i$在顺序表中的位序。因此,顺序表的特点是表中元素的逻辑顺序与其存储的物理顺序相同。
假设顺序表$L$存储的起始位置为$LOC(A)$,`sizeof(ElemType)`是每个数据元素所占用存储空间的大小,则表$L$所对应的顺序存储结构如图2.1所示。

| 数组下标 | 顺序表 | 内存地址 |
| :---: | :---: | :--- |
| 0 | $a_1$ | $LOC(A)$ |
| 1 | $a_2$ | $LOC(A)+sizeof(ElemType)$ |
| $\vdots$ | $\vdots$ | $\vdots$ |
| $i-1$ | $a_i$ | $LOC(A)+(i-1) \times sizeof(ElemType)$ |
| $\vdots$ | $\vdots$ | $\vdots$ |
| $n-1$ | $a_n$ | $LOC(A)+(n-1) \times sizeof(ElemType)$ |
| $\vdots$ | | $\vdots$ |
| MaxSize-1 | | $LOC(A)+(MaxSize-1) \times sizeof(ElemType)$ |

图2.1 线性表的顺序存储结构

每个数据元素的存储位置都和顺序表的起始位置相差一个和该数据元素的位序成正比的常数,因此,顺序表中的任意一个数据元素都可以随机存取,所以线性表的顺序存储结构是一种随机存取的存储结构。通常用高级程序设计语言中的数组来描述线性表的顺序存储结构。

**注意**
线性表中元素的位序是从1开始的,而数组中元素的下标是从0开始的。