系统调用。对于选项B,进程调度完全由操作系统完成,无法通过系统调用完成。对于选项C,创建新进程可以通过系统调用来完成,如Linux中通过 fork 系统调用来创建子进程。对于选项D,生成随机数是普通的函数调用,不涉及请求操作系统的服务,如C语言的 random()函数。
30. C
CPU 在用户态时只能执行非特权指令,在内核态时可以执行特权指令和非特权指令。
31. B
发生系统调用时,CPU通过执行软中断指令将CPU的运行状态从用户态切换到内核态,这个过程与中断和异常的响应过程相同,由硬件负责保存断点和程序状态字,并将 CPU 模式改为内核态。然后,执行操作系统内核的系统调用入口程序,该内核程序负责保存通用寄存器的内容,再调用执行特定的系统调用服务例程。综上,选项I、IV由硬件完成,选项II、III由操作系统完成。
32. A
本题考查了“计算机组成原理”的考点,且综合了“数据结构”的内容。中断向量表用于存放中断处理程序的入口地址,CPU通过查询得到中断类型号,然后据此计算可以得到对应中断服务程序的入口地址在中断向量表的位置,采用数组作为中断向量表的存储结构,可实现时间复杂度为$O(1)$的快速访问,从而提高中断处理的效率。
33. A
当中断或异常发生时,CPU既可能处于内核态,又可能处于用户态,具体取决于当时 CPU正在处理的任务,选项A错误。不同的系统调用对应不同的内核服务例程,选项B正确。在中断响应阶段,若CPU处于用户态,则需要切换到内核态,因此在中断处理阶段,CPU一定处于内核态,选项C正确。设备种类繁多,计算机不可能事先准备好所有设备对应的中断服务例程(实际上属于设备驱动程序),因此当系统添加新类型的设备时,需要注册相应的中断服务例程。

### 1.4 操作系统结构
随着操作系统功能的不断增多和代码规模的不断扩大,提供合理的结构,对降低操作系统复杂度、提升操作系统安全与可靠性来说变得尤为重要。
**1. 分层法**
分层法是将操作系统分为若干层,底层(层0)为硬件,顶层(层N)为用户接口,每层只能调用紧邻它的低层的功能和服务(单向依赖)。分层的操作系统如图1.4所示。

> 层N
> 用户接口
>
> 层1
>
> 层0
> 硬件

<center>图1.4 分层的操作系统</center>

分层法的优点:①便于系统的调试和验证,简化了系统的设计和实现。第1层可先调试而无须考虑系统的其他部分,因为它只使用了基本硬件。第1层调试完且验证正确之后,就可以调试第2层,如此向上。若在调试某层时发现错误,则错误应在这一层上,这是因为它的低层都调试好了。②易扩充和易维护。在系统中增加、修改或替换一层中的模块或整层时,只要不改变相应层间的接口,就不会影响其他层。
分层法的问题:①合理定义各层比较困难。因为依赖关系固定后,往往就显得不够灵活。②效率较差。操作系统每执行一个功能,通常要自上而下地穿越多层,各层之间都有相应的层间通信机制,这无疑增加了额外的开销,导致系统效率降低。

**2. 模块化**
模块化是将操作系统按功能划分为若干具有一定独立性的模块。每个模块具有某方面的管理功能,并规定好各模块间的接口,使各模块之间能够通过接口进行通信。还可以进一步将各模块细分为若干具有一定功能的子模块,同样也规定好各子模块之间的接口。这种设计方法被称为模块-接口法,图1.5所示为由模块、子模块等组成的模块化操作系统结构。
<center>操作系统</center>
<center>↓</center>
<center>模块</center>

| 进程管理 | 存储器管理 | 文件管理 |
| :--- | :--- | :--- |
| **↓** | **↓** | **↓** |
| 子模块 | 子模块 | 子模块 |
| 进程控制 进程调度 | 内存分配 内存保护 | 磁盘管理 目录管理 |

<center>图1.5 由模块、子模块等组成的模块化操作系统结构</center>

在划分模块时,若将模块划分得太小,则虽然能降低模块本身的复杂性,但会使得模块之间的联系过多,造成系统比较混乱;若模块划分得过大,则又会增加模块内部的复杂性,显然应在两者间进行权衡。此外,在划分模块时,要充分考虑模块的独立性问题,因为模块独立性越高,各模块间的交互就越少,系统的结构也就越清晰。衡量模块的独立性主要有两个标准:
*   内聚性,模块内部各部分间联系的紧密程度。内聚性越高,模块独立性越好。
*   耦合度,模块间相互联系和相互影响的程度。耦合度越低,模块独立性越好。
模块化的优点:①提高了操作系统设计的正确性、可理解性和可维护性;②增强了操作系统的可适应性;③加速了操作系统的开发过程。
模块化的缺点:①模块间的接口规定很难满足对接口的实际需求。②各模块设计者齐头并进,每个决定无法建立在上一个已验证的正确决定的基础上,因此无法找到一个可靠的决定顺序。

**3. 宏内核**
从操作系统的内核架构来划分,可分为宏内核和微内核。
宏内核,也称单内核或大内核,是指将系统的主要功能模块都作为一个紧密联系的整体运行在内核态,从而为用户程序提供高性能的系统服务。因为各管理模块之间共享信息,能有效利用相互之间的有效特性,所以具有无可比拟的性能优势。
随着体系结构和应用需求的不断发展,需要操作系统提供的服务越来越复杂,操作系统的设计规模急剧增长,操作系统也面临着“软件危机”困境。就像一个人,越胖活动起来就越困难。所以就出现了微内核技术,就是将一些非核心的功能移到用户空间,这种设计带来的好处是方便扩展系统,所有新服务都可以在用户空间增加,内核基本不用去做改动。
从操作系统的发展来看,宏内核获得了绝对的胜利,目前主流的操作系统,如Windows、Android、iOS、macOS、Linux等,都是基于宏内核的构架。但也应注意到,微内核和宏内核一直是同步发展的,目前主流的操作系统早已不是当年纯粹的宏内核构架了,而是广泛吸取微内核构架的优点而后揉合而成的混合内核。当今宏内核构架遇到了越来越多的困难和挑战,而微内核的优势似乎越来越明显,尤其是谷歌的Fuchsia和华为的鸿蒙OS,都瞄准了微内核构架。

**4. 微内核**
**(1) 微内核的基本概念**
微内核构架,是指将内核中最基本的功能保留在内核,而将那些不需要在内核态执行的功能移到用户态执行,从而降低内核的设计复杂性。那些移出内核的操作系统代码根据分层的原则被划分成若干服务程序,它们的执行相互独立,交互则都借助于微内核进行通信。
微内核结构将操作系统划分为两大部分:微内核和多个服务器。微内核是指精心设计的、能实现操作系统最基本核心功能的小型内核,通常包含:①与硬件处理紧密相关的部分;②一些较基本的功能;③客户和服务器之间的通信。这些部分只是为构建通用操作系统提供一个重要基础,这样就可以确保将内核做得很小。操作系统中的绝大部分功能都放在微内核外的一组服务器(进程)中实现,如用于提供对进程(线程)进行管理的进程(线程)服务器、提供虚拟存储器管理功能的虚拟存储器服务器等,它们都是作为进程来实现的,运行在用户态,客户与服务器之间是借助微内核提供的消息传递机制来实现交互的。图1.6展示了单机环境下的客户/服务器模式。

> **用户方式**
>
> 客户进程 | 客户进程 | 进程服务器 | I/O设备服务器 | 文件服务器 | 虚拟存储器服务器
> ---|---|---|---|---|---
> **↓ 请求**
> **核心方式**
> 核心
> **↑ 回答**

<center>图1.6 单机环境下的客户/服务器模式</center>

在微内核结构中,为了实现高可靠性,只有微内核运行在内核态,其余模块都运行在用户态,一个模块中的错误只会使这个模块崩溃,而不会使整个系统崩溃。例如,文件服务代码运行时出了问题,宏内核因为文件服务是运行在内核态的,系统直接就崩溃了。而微内核的文件服务是运行在用户态的,只要将文件服务功能强行停止,然后重启,就可以继续使用,系统不会崩溃。

**(2) 微内核的基本功能**
微内核结构通常利用“机制与策略分离”的原理来构造OS结构,将机制部分以及与硬件紧密相关的部分放入微内核。微内核通常具有如下功能:
①进程(线程)管理。进程(线程)之间的通信功能是微内核OS最基本的功能,此外还有进程的切换、进程的调度,以及多处理机之间的同步等功能,都应放入微内核。举个例子,为实现进程调度功能,需要在进程管理中设置一个或多个进程优先级队列,这部分属于调度功能的机制部分,应将它放入微内核。而对用户进程如何分类,以及优先级的确认方式,则属于策略问题,可将它们放入微内核外的进程管理服务器中。
②低级存储器管理。在微内核中,只配置最基本的低级存储器管理机制,如用于实现将逻辑地址变换为物理地址等的页表机制和地址变换机制,这一部分是依赖于硬件的,因此放入微内核。而实现虚拟存储器管理的策略,则包含应采取何种页面置换算法、采用何种内存分配与回收的策略,应将这部分放在微内核外的存储器管理服务器中。
③中断和陷入处理。微内核OS将与硬件紧密相关的一小部分放入微内核,此时微内核的主要功能是捕获所发生的中断和陷入事件,并进行中断响应处理,在识别中断或陷入的事件后,再发送给相关的服务器来处理,所以中断和陷入处理也应放入微内核。
微内核操作系统将进程管理、存储器管理以及I/O管理这些功能一分为二,属于机制的很小一部分放入微内核,而绝大部分放入微内核外的各种服务器实现,大多数服务器都要比微内核大。因此,在采用客户/服务器模式时,能将微内核做得很小。

**(3) 微内核的特点**

**命题追踪** 微内核操作系统的特点 (2023)

微内核结构的主要优点如下所示。
①扩展性和灵活性。许多功能从内核中分离出来,当要修改某些功能或增加新功能时,只需在相应的服务器中修改或新增功能,或再增加一个专用的服务器,而无须改动内核代码。
②可靠性和安全性。前面已举例说明。
③可移植性。与CPU和I/O硬件有关的代码均放在内核中,而其他各种服务器均与硬件平台无关,因而将操作系统移植到另一个平台上所需做的修改是比较小的。
④分布式计算。客户和服务器之间、服务器和服务器之间的通信采用消息传递机制,这就使得微内核系统能很好地支持分布式系统和网络系统。
微内核结构的主要问题是性能问题,因为需要频繁地在内核态和用户态之间进行切换,操作系统的执行开销偏大。为了改善运行效率,可以将那些频繁使用的系统服务移回内核,从而保证系统性能,但这又会使微内核的容量明显地增大。
虽然宏内核在桌面操作系统中取得了绝对的胜利,但是微内核在实时、工业、航空及军事应用中特别流行,这些领域都是关键任务,需要有高度的可靠性。

**5. 外核**
不同于虚拟机克隆真实机器,另一种策略是对资源进行划分,为每个用户分配整个资源的一个子集。例如,某虚拟机可能得到磁盘的0~1023盘块,而另一虚拟机得到磁盘的1024~2047盘块等。在底层,一种称为外核(exokernel)的程序在内核态中运行。它的任务是为虚拟机分配资源,并检查这些资源使用的安全性,以确保没有机器会使用他人的资源。每个用户的虚拟机可以运行自己的操作系统,但限制只能使用已经申请并且获得分配的那部分资源。
外核机制的优点是减少了资源的“映射层”。在其他设计中,每个虚拟机系统都认为它拥有完整的磁盘(或其他资源),这样虚拟机监控程序就必须维护一张表格以重映像磁盘地址,有了外核,这个重映射处理就不需要了。外核只需要记录已分配给各个虚拟机的有关资源即可。这种方法还有一个优点,它将多道程序(在外核内)与用户操作系统代码(在用户空间内)加以分离,而且相应的负载并不重,因为外核所做的只是保持多个虚拟机彼此不发生冲突。

### 1.5 操作系统引导
操作系统(如 Windows、Linux等)是一种程序,程序以数据的形式存放在硬盘中,而硬盘通常分为多个区,一台计算机中又可能有多个或多种外部存储设备。操作系统引导是指计算机利用 CPU 运行特定程序,通过程序识别硬盘,识别硬盘分区,识别硬盘分区上的操作系统,最后通过程序启动操作系统,一环扣一环地完成上述过程。

**命题追踪** 操作系统的引导过程 (2021)

常见操作系统的引导过程如下:
①激活 CPU。激活的CPU读取ROM 中的 boot程序,将指令寄存器置为 BIOS (基本输入/输出系统)的第一条指令,即开始执行BIOS的指令。