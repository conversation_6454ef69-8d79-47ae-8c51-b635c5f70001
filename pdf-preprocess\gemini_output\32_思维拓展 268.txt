```c
        return p->next->data; //返回下一个邻接顶点
    }
    return -1;
}
```

2.  关于图的遍历、连通性、生成树、关键路径的几个要点
    1) 在执行图的遍历时，因为图中可能存在回路，且图的任意一个顶点都可能与其他顶点相连，所以在访问完某个顶点后可能会沿某些边又回到了曾经访问过的顶点。因此，需要设置一个辅助数组`visited[]`标记顶点是否已被访问过，避免重复访问。
    2) 深度优先搜索时利用回溯法对图遍历，一般利用递归方法实现，每当向前递归查找某一邻接结点之前，必须判断该结点是否访问过。另外，递归算法均可借助栈来实现非递归算法，深度优先搜索也不例外，具体程序见6.3.4节的综合应用题03。
    3) 广度优先搜索是一种分层的遍历过程，每向前走一步可能访问一批顶点，不像深度优先搜索那样有回退的情况。因此，它不是一个递归的过程。
    4) 一个给定的图的邻接矩阵表示是唯一的，但对于邻接表来说，若边的输入先后次序不同，则生成的邻接表表示也不同。
    5) 图的最小生成树首先必须是带权连通图，其次要在$n$个顶点的图中选择$n-1$条边将其连通，使得其权值总和达到最小，且不出现回路。
    6) 加速某一关键活动不一定能缩短整个工程的工期，因为AOE网中可能存在多条关键路径。可能存在称为桥的一种特殊关键活动，它位于所有的关键路径上，只有它加速才会缩短整个工期。

### 思维拓展

【网易有道笔试题】求一个无向连通图的割点。割点的定义是，若除去此结点和与其相关的边，无向图不再连通，描述算法。

> **提示**
> 要判断一个点是否为割点，最简单直接的方法是，先把这个点和所有与它相关的边从图中去掉，再用深搜或广搜来判断剩下的图的连通性，这种方法适合判断给定结点是否为割点；还有一种比较复杂的方法可以快速找出所有割点，有兴趣的读者可自行搜索相关资料。

---

## 第七章 查找

**【考纲内容】**
(一) 查找的基本概念
(二) 顺序查找法
(三) 分块查找法
(四) 折半查找法
(五) 树形查找
二叉搜索树；平衡二叉树；红黑树
(六) B树及其基本操作、B+树的基本概念
(七) 散列(Hash)表
(八) 查找算法的分析及应用

**【知识框架】**
*   基本概念：静态查找、动态查找
*   线性结构
    *   顺序查找
    *   折半查找
    *   分块查找
*   查找
    *   树形结构
        *   二叉排序树
        *   二叉平衡树
        *   红黑树
        *   B树、B+树
*   散列结构————散列表
    *   性能分析
    *   冲突处理
*   效率指标————平均查找长度
    *   查找成功
    *   查找失败

**【复习提示】**
本章是考研命题的重点。对于折半查找，应掌握折半查找的过程、构造判定树、分析平均查找长度等。对于二叉排序树、二叉平衡树和红黑树，要了解它们的概念、性质和相关操作等。B树和B+树是本章的难点。对于B树，考研大纲要求掌握插入、删除和查找的操作过程；对于B+树，仅要求了解其基本概念和性质。对于散列查找，应掌握散列表的构造、冲突处理方法（各种方法的处理过程）、查找成功和查找失败的平均查找长度、散列查找的特征和性能分析。

### 7.1 查找的基本概念
1) **查找**。在数据集合中寻找满足某种条件的数据元素的过程称为查找。查找的结果一般分为两种：一是查找成功，即在数据集合中找到了满足条件的数据元素；二是查找失败。