排序的总趟数取决于元素个数$n$，两者都是$n-1$趟。元素的移动次数都取决于初始序列，两者相同。使用辅助空间的数量也都是$O(1)$。折半插入排序的比较次数与序列初态无关，时间复杂度为$O(n\log_2n)$；而直接插入排序的比较次数与序列初态有关，时间复杂度为$O(n)\sim O(n^2)$。

**19. B**

首先，第二个元素为1，是整个序列中的最小元素，可知该希尔排序为从小到大排序。然后考虑增量问题，若增量为2，则第1+2个元素4明显比第1个元素9要小，排除选项A。若增量为3，则第$i,i+3,i+6(i=1,2,3)$个元素都为有序序列，符合希尔排序的特点。若增量为4，则第1个元素9比第1+4个元素7要大，排除选项C。若增量为5，则第1个元素9比第1+5个元素8要大，排除选项D。

**20. A**

希尔排序的思想是：先将待排元素序列分割成若干子序列（由相隔某个“增量”的元素组成），分别进行直接插入排序，然后依次缩减增量再进行排序，待整个序列中的元素基本有序（增量足够小）时，再对全体元素进行一次直接插入排序。

**21. D**

如下图所示。

初始序列：8, 3, 9, 11, 2, 1, 4, 7, 5, 10, 6
第一趟：  `1, 3, 7, 5, 2, 6, 4, 9, 11, 10, 8`
第二趟：  `1, 2, 6, 4, 3, 7, 5, 8, 11, 10, 9`

第一趟分组：8, 1, 6；3, 4；9, 7；11, 5；2, 10；间隔为5，排序后组内递增。
第二趟分组：1, 5, 4, 10；3, 2, 9, 8；7, 6, 11；间隔为3，排序后组内递增。
因此，选择选项D。

**二、综合应用题**

**01.【解答】**

直接插入排序过程如下。
初始序列： 4, 5, 1, 2, 6, 3
第一趟：   4, 5, 1, 2, 6, 3    （将5插入{4}）
第二趟：   1, 4, 5, 2, 6, 3    （将1插入{4,5}）
第三趟：   1, 2, 4, 5, 6, 3    （将2插入{1,4,5}）
第四趟：   1, 2, 4, 5, 6, 3    （将6插入{1,2,4,5}）
第五趟：   1, 2, 3, 4, 5, 6    （将3插入{1,2,4,5,6}）

**02.【解答】**

原始序列：        50, 26, 38, 80, 70, 90, 8, 30, 40, 20
第一趟（增量5）： 50, 8, 30, 40, 20, 90, 26, 38, 80, 70
第二趟（增量3）： 26, 8, 30, 40, 20, 80, 50, 38, 90, 70
第三趟（增量1）： 8, 20, 26, 30, 38, 40, 50, 70, 80, 90

### 8.3 交换排序

所谓交换，是指根据序列中两个元素关键字的比较结果来对换这两个记录在序列中的位置。
## 第八章 排序
基于交换的排序算法很多，本书主要介绍冒泡排序和快速排序，其中冒泡排序算法比较简单，一般很少直接考查，通常会重点考查快速排序算法的相关内容。

### 8.3.1 冒泡排序
冒泡排序的基本思想是：从后往前（或从前往后）两两比较相邻元素的值，若为逆序（$A[i-1]>A[i]$），则交换它们，直到序列比较完。我们称它为第一趟冒泡，结果是将最小的元素交换到待排序列的第一个位置（或将最大的元素交换到待排序列的最后一个位置），关键字最小的元素如气泡一般逐渐往上“漂浮”至“水面”（或关键字最大的元素如石头一般下沉至水底）。下一趟冒泡时，前一趟确定的最小元素不再参与比较，每趟冒泡的结果是把序列中的最小元素（或最大元素）放到了序列的最终位置……这样最多做$n-1$趟冒泡就能把所有元素排好序。
图8.3所示为冒泡排序的过程，第一趟冒泡时：$27<49$，不交换；$13<27$，不交换；$76>13$，交换；$97>13$，交换；$65>13$，交换；$38>13$，交换；$49>13$，交换。通过第一趟冒泡后，最小元素已交换到第一个位置，也是它的最终位置。第二趟冒泡时对剩余子序列采用同样方法进行排序，如此重复，到第五趟结束后没有发生交换，说明表已有序，冒泡排序结束。

| 初始状态 | 第一趟后 | 第二趟后 | 第三趟后 | 第四趟后 | 第五趟后 | 最终状态 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| 49 | **13** | **13** | **13** | **13** | **13** | **13** |
| 38 | 49 | **27** | **27** | **27** | **27** | **27** |
| 65 | 38 | 49 | **38** | **38** | **38** | **38** |
| 97 | 65 | 38 | 49 | **49** | **49** | **49** |
| 76 | 97 | 65 | 49 | 49 | **49** | **49** |
| 13 | 76 | 97 | 65 | 65 | 65 | 65 |
| 27 | 27 | 76 | 97 | 76 | 76 | 76 |
| 49 | 49 | 49 | 76 | 97 | 97 | 97 |

图8.3 冒泡排序示例

冒泡排序算法的代码如下：
```c
void BubbleSort(ElemType A[], int n) {
    for(int i=0; i<n-1; i++) {
        bool flag=false;          //表示本趟冒泡是否发生交换的标志
        for(int j=n-1; j>i; j--)  //一趟冒泡过程
            if (A[j-1]>A[j]) {     //若为逆序
                swap(A[j-1],A[j]); //使用封装的 swap 函数交换
                flag=true;
            }
        if(flag==false)
            return;               //本趟遍历后没有发生交换,说明表已经有序
    }
}
```
冒泡排序的性能分析如下：
空间效率：仅使用了常数个辅助单元，因而空间复杂度为$O(1)$。
时间效率：当初始序列有序时，显然第一趟冒泡后 flag 依然为false（本趟没有元素交换），
① 本章有多处要两两交换元素，为使代码更简洁，使用函数swap(int &a, int &b)，其代码如下：
int temp=a; a=b; b=temp;
从而直接跳出循环，比较次数为$n-1$，移动次数为0，从而最好情况下的时间复杂度为$O(n)$；当初始序列为逆序时，需要进行$n-1$趟排序，第$i$趟排序要进行$n-i$次关键字的比较，而且每次比较后都必须移动元素3次来交换元素位置。这种情况下，
$$
比较次数=\sum_{i=1}^{n-1}(n-i)=\frac{n(n-1)}{2}，移动次数=\sum_{i=1}^{n-1}3(n-i)=\frac{3n(n-1)}{2}
$$
从而，最坏情况下的时间复杂度为$O(n^2)$，平均时间复杂度为$O(n^2)$。
稳定性：$i>j$且$A[i]=A[j]$时，不会发生交换，因此冒泡排序是一种稳定的排序算法。
适用性：冒泡排序适用于顺序存储和链式存储的线性表。

**注**
冒泡排序中所产生的有序子序列一定是全局有序的（不同于直接插入排序），也就是说，有序子序列中的所有元素的关键字一定小于（或大于）无序子序列中所有元素的关键字，这样每趟排序都会将一个元素放置到其最终的位置上。

### 8.3.2 快速排序
**命题追踪** 快速排序的思想 (2024)

快速排序的基本思想：在待排序表$L[1...n]$中任取一个元素pivot作为枢轴（或称基准，通常取首元素），通过一趟排序将待排序表划分为独立的两部分$L[1...k-1]$和$L[k+1...n]$，使得$L[1...k-1]$中的所有元素小于pivot，$L[k+1...n]$中的所有元素大于或等于pivot，则pivot放在了其最终位置$L(k)$上，这个过程称为一次划分。然后分别递归地对两个子表重复上述过程，直至每部分内只有一个元素或为空为止，即所有元素放在了其最终位置上。
一趟快速排序的过程是一个交替搜索和交换的过程，下面通过实例来介绍，附设两个指针$i$和$j$，初值分别为low和high，取第一个元素49为枢轴赋值到变量pivot。
指针$j$从high往前搜索找到第一个小于枢轴的元素27，将27交换到$i$所指位置。
pivot
`49` `38` `65` `97` `76` `13` **27** `49`
`i`                             `j`
指针$i$从low往后搜索找到第一个大于枢轴的元素65，将65交换到$j$所指位置。
`27` `38` **65** `97` `76` `13`     `49`
`i`                        `j`
指针$j$继续往前搜索找到小于枢轴的元素13，将13交换到$i$所指位置。
`27` `38`      `97` `76` **13** `65` `49`
`i`                   `j`
指针$i$继续往后搜索找到大于枢轴的元素97，将97交换到$j$所指位置。
`27` `38` `13` **97** `76`      `65` `49`
`i`         `j`
指针$j$继续往前搜索小于枢轴的元素，直至$i==j$。
`27` `38` `13`      `76` `97` `65` `49`
`i,j`
**命题追踪** 快速排序的中间过程的分析 (2014、2019、2023)

此时，指针$i(==j)$之前的元素均小于49，指针$i$之后的元素均大于或等于49，将49放在$i$所指位置即其最终位置，经过第一趟排序后，将原序列分割成了前后两个子序列。
第一趟后： {27 38 13} **49** {76 97 65 49}
按照同样的方法对各子序列进行快速排序，若待排序列中只有一个元素，显然已有序。
第二趟后： {13} **27** {38} **49** {49 65} **76** {97}
第三趟后： **13** **27** **38** **49** **49** {65} **76** **97**
第四趟后： **13** **27** **38** **49** **49** **65** **76** **97**
用二叉树的形式描述这个快速排序示例的递归调用过程，如图8.4所示。

(a)第一层快排处理后
第一层快排处理后：`49`
第二层快排要处理的部分：`27 38 13` `76 97 65 49`

(b)第二层快排处理后
第一层快排处理后：`49`
第二层快排处理后：`27` `76`
第三层快排要处理的部分：`13 38` `49 65 97`

(c)第三层快排处理后
第一层快排处理后：`49`
第二层快排处理后：`27` `76`
第三层快排处理后：`13` `38` `49` `97`
第四层快排要处理的部分：`65`

(d)第四层快排处理后：最终结果
第一层快排处理后：`49`
第二层快排处理后：`27` `76`
第三层快排处理后：`13` `38` `49` `97`
第四层快排处理后：`65`

图8.4 快速排序的递归执行过程

假设划分算法已知，记为Partition()，返回的是上述的k，则L(k)已放在其最终位置。因此可以先对表进行划分，然后对两个子表递归地调用快速排序算法进行排序。代码如下：
```c
void QuickSort(ElemType A[], int low, int high) {
    if (low<high) { //递归跳出的条件
        //Partition()就是划分操作，将表A[low…high]划分为满足上述条件的两个子表
        int pivotpos=Partition(A,low,high); //划分
        QuickSort(A,low,pivotpos-1); //依次对两个子表进行递归排序
        QuickSort(A,pivotpos+1,high);
    }
}
```
**命题追踪** (算法题)快速排序中划分操作的应用 (2016)

快速排序算法的性能主要取决于划分操作的好坏。考研所考查的快速排序的划分操作通常总以表中第一个元素作为枢轴来对表进行划分，则将表中比枢轴大的元素向右移动，将比枢轴小的元素向左移动，使得一趟Partition()操作后，表中的元素被枢轴一分为二。代码如下：
```c
int Partition(ElemType A[], int low, int high) { //一趟划分
    ElemType pivot=A[low]; //将当前表中第一个元素设为枢轴，对表进行划分
```
```c
    while (low<high) { //循环跳出条件
        while (low<high&&A[high]>=pivot) --high;
        A[low]=A[high]; //将比枢轴小的元素移动到左端
        while (low<high&&A[low]<=pivot) ++low;
        A[high]=A[low]; //将比枢轴大的元素移动到右端
    }
    A[low]=pivot;   //枢轴元素存放到最终位置
    return low;     //返回存放枢轴的最终位置
}
```
快速排序算法的性能分析如下：

**命题追踪** 快速排序中递归次数的影响因素分析 (2010)

空间效率：快速排序是递归的，因此需要借助一个递归工作栈来保存每层递归调用的必要信息，其容量与递归调用的最大层数一致。最好情况下为$O(\log_2n)$；最坏情况下，要进行$n-1$次递归调用，因此栈的深度为$O(n)$；平均情况下，栈的深度为$O(\log_2n)$。
时间效率：快速排序的运行时间与划分是否对称有关，快速排序的最坏情况发生在两个区域分别包含$n-1$个元素和0个元素时，这种最大限度的不对称性若发生在每层递归上，即对应于初始排序表基本有序或基本逆序时，就得到最坏情况下的时间复杂度为$O(n^2)$。
有很多方法可以提高算法的效率：一种方法是尽量选取一个可以将数据中分的枢轴元素，如从序列的头尾及中间选取三个元素，再取这三个元素的中间值作为最终的枢轴元素；或者随机地从当前表中选取枢轴元素，这样做可使得最坏情况在实际排序中几乎不会发生。
在最理想的状态下，即Partition()能做到最平衡的划分，得到的两个子问题的大小都不可能大于$n/2$，在这种情况下，快速排序的运行速度将大大提升，此时，时间复杂度为$O(n\log_2n)$。好在快速排序平均情况下的运行时间与其最佳情况下的运行时间很接近，而不是接近其最坏情况下的运行时间。快速排序是所有内部排序算法中平均性能最优的排序算法。
稳定性：在划分算法中，若右端区间有两个关键字相同，且均小于基准值的记录，则在交换到左端区间后，它们的相对位置会发生变化，即快速排序是一种不稳定的排序算法。例如，表$L=\{3,2,2\}$，经过一趟排序后$L = \{2,2,3\}$，最终排序序列也是$L=\{2,2,3\}$，显然，2与2的相对次序已发生了变化。

**命题追踪** 快速排序适合采用的存储方式 (2011)

适用性：快速排序仅适用于顺序存储的线性表。

**注**
在快速排序算法中，并不产生有序子序列，但每一趟排序后会将上一趟划分的各个无序子表的枢轴（基准）元素放到其最终的位置上。

### 8.3.3 本节试题精选
**一、单项选择题**

**01.** 对n个不同的元素利用冒泡法从小到大排序，在（ ）情况下元素交换的次数最多。
A. 从大到小排列好的 B. 从小到大排列好的
C. 元素无序 D. 元素基本有序

**02.** 若用冒泡排序算法对序列{10, 14, 26, 29, 41, 52}从大到小排序，则需进行（ ）次比较。
A. 3
B. 10
C. 15
D. 25
**03.** 用某种排序算法对线性表{25, 84, 21, 47, 15, 27, 68, 35, 20}进行排序时，元素序列的变化情况如下：
1) 25, 84, 21, 47, 15, 27, 68, 35, 20
2) 20, 15, 21, 25, 47, 27, 68, 35, 84
3) 15, 20, 21, 25, 35, 27, 47, 68, 84
4) 15, 20, 21, 25, 27, 35, 47, 68, 84
则所采用的排序算法是（ ）。
A. 选择排序 B. 插入排序 C. 二路归并排序 D. 快速排序

**04.** 一组记录的关键码为(46, 79, 56, 38, 40, 84)，则利用快速排序算法，以第一个记录为基准，从小到大得到的一次划分结果为（ ）。
A. (38, 40, 46, 56, 79, 84) B. (40, 38, 46, 79, 56, 84)
C. (40, 38, 46, 56, 79, 84) D. (40, 38, 46, 84, 56, 79)

**05.** 快速排序算法在（ ）情况下最不利于发挥其长处。
A. 要排序的数据量太大 B. 要排序的数据中含有多个相同值
C. 要排序的数据个数为奇数 D. 要排序的数据已基本有序

**06.** 就平均性能而言，目前最好的内部排序算法是（ ）。
A. 冒泡排序 B. 直接插入排序 C. 希尔排序 D. 快速排序

**07.** 数据序列 F={2, 1, 4, 9, 8, 10, 6, 20}只能是下列排序算法中的（ ）两趟排序后的结果。
A. 快速排序 B. 冒泡排序 C. 选择排序 D. 插入排序

**08.** 对元素序列{8, 9, 10, 4, 5, 6, 20, 1, 2}采用冒泡排序（从后往前次序进行，要求升序），需要进行元素交换的趟数至少是（ ）（不考虑无元素交换的最后一趟）。
A. 3 B. 4 C. 5 D. 8

**09.** 双向冒泡排序是指对一个序列在正反两个方向交替进行扫描，第一趟把最大值放在序列的最右端，第二趟把最小值放在序列的最左端，之后在缩小的范围内进行同样的扫描，放在次右端、次左端，直至序列有序。对数组{4, 7, 8, 3, 5, 6, 10, 9, 1, 2}进行双向冒泡排序，则排序趟数是（ ）。（第一趟从左往右开始，从左往右或从右往左都称为一趟。）
A. 7 B. 6 C. 8 D. 9

**10.** 对下列关键字序列用快速排序进行排序时，每次选取的基准元素都为待处理序列的第一个元素，速度最快的情形是（ ），速度最慢的情形是（ ）。
A. {21, 25, 5, 17, 9, 23, 30}
B. {25, 23, 30, 17, 21, 5, 9}
C. {21, 9, 17, 30, 25, 23, 5}
D. {5, 9, 17, 21, 23, 25, 30}

**11.** 对下列4个序列，以第一个关键字为基准用快速排序算法进行排序，在第一趟过程中移动记录次数最多的是（ ）。
A. 92, 96, 88, 42, 30, 35, 110, 100
B. 92, 96, 100, 110, 42, 35, 30, 88
C. 100, 96, 92, 35, 30, 110, 88, 42
D. 42, 30, 35, 92, 100, 96, 88, 110

**12.** 下列序列中，（ ）可能是执行第一趟快速排序后所得到的序列（按从大到小排序和从小到大排序来分别讨论）。
I. {68, 11, 18, 69, 23, 93, 73}
II. {68, 11, 69, 23, 18, 93, 73}
III. {93, 73, 68, 11, 69, 23, 18}
IV. {68, 11, 69, 23, 18, 73, 93}
A. I、IV B. II、III C. III、IV D. 只有IV

**13.** 对$n$个关键字进行快速排序，最大递归深度为（ ），最小递归深度为（ ）。
A. 1
B. $n$
C. $\log_2n$
D. $n\log_2n$

**14.** 对8个元素的序列进行快速排序，在最好情况下的关键字比较次数是（ ）。
A. 7
B. 8
C. 12
D. 13

**15.** **【2010 统考真题】** 采用递归方式对顺序表进行快速排序。下列关于递归次数的叙述中，正确的是（ ）。
A. 递归次数与初始数据的排列次序无关
B. 每次划分后，先处理较长的分区可以减少递归次数
C. 每次划分后，先处理较短的分区可以减少递归次数
D. 递归次数与每次划分后得到的分区的处理顺序无关

**16.** **【2011 统考真题】** 为实现快速排序算法，待排序序列宜采用的存储方式是（ ）。
A. 顺序存储
B. 散列存储
C. 链式存储
D. 索引存储

**17.** **【2014 统考真题】** 下列选项中，不可能是快速排序第二趟排序结果的是（ ）。
A. 2, 3, 5, 4, 6, 7, 9
B. 2, 7, 5, 6, 4, 3, 9
C. 3, 2, 5, 4, 7, 6, 9
D. 4, 2, 3, 5, 7, 6, 9

**18.** **【2019 统考真题】** 排序过程中，对尚未确定最终位置的所有元素进行一遍处理称为一趟。下列序列中，不可能是快速排序第二趟结果的是（ ）。
A. 5, 2, 16, 12, 28, 60, 32, 72
B. 2, 16, 5, 28, 12, 60, 32, 72
C. 2, 12, 16, 5, 28, 32, 72, 60
D. 5, 2, 12, 28, 16, 32, 72, 60

**19.** **【2023 统考真题】** 使用快速排序算法对数据进行升序排序，若经过一次划分后得到的数据序列是68, 11, 70, 23, 80, 77, 48, 81, 93, 88，则该次划分的枢轴是（ ）。
A. 11
B. 70
C. 80
D. 81

**20.** **【2024 统考真题】** 使用快速排序算法对含$n(n\ge3)$个元素的数组M进行排序，若第一趟排序将M中除枢轴外的$n-1$个元素划分为均不为空的P和Q两块，则下列叙述中，正确的是（ ）。
A. P与Q块间有序
B. P与Q均块内有序
C. P和Q的元素个数大致相等
D. P中和Q中均不存在相等的元素

**二、综合应用题**

**01.** 已知线性表按顺序存储，且每个元素都是不相同的整数型元素，设计把所有奇数移动到所有偶数前边的算法（要求时间最短，辅助空间最小）。

**02.** 试编写一个算法，使之能够在数组L[1...n]中找出第k小的元素（从小到大排序后处于第k个位置的元素）。

**03.** 荷兰国旗问题：设有一个仅由红、白、蓝三种颜色的条块组成的条块序列，存储在一个顺序表中，请编写一个时间复杂度为$O(n)$的算法，使得这些条块按红、白、蓝的顺序排好，即排成荷兰国旗图案。请完成算法实现：
```c
typedef enum{RED, WHITE, BLUE} color; //设置枚举数组
void Flag_Arrange(color a[],int n) { ... }
```

**04.** **【2016 统考真题】** 已知由$n(n\ge2)$个正整数构成的集合$A=\{a_k|0\le k<n\}$，将其划分为两个不相交的子集$A_1$和$A_2$，元素个数分别是$n_1$和$n_2$，$A_1$和$A_2$中的元素之和分别为$S_1$和$S_2$。设计一个尽可能高效的划分算法，满足$|n_1-n_2|$最小且$|S_1-S_2|$最大。要求：
1) 给出算法的基本设计思想。
2) 根据设计思想，采用C或C++语言描述算法，关键之处给出注释。
3) 说明所设计算法的平均时间复杂度和空间复杂度。

### 8.3.4 答案与解析
**一、单项选择题**

**01. A**
冒泡排序最少进行1趟冒泡，最多进行$n-1$趟冒泡。初始序列为逆序时，需进行$n-1$趟冒泡，并且元素交换的次数最多。初始序列为正序时，进行1趟冒泡（无交换）就可结束算法。

**02. C**
冒泡排序始终在调整“逆序”，因此交换次数为排列中逆序的个数。对逆序序列进行冒泡排序，每个元素向后调整时都需要进行比较，因此共需要比较5+4+3+2+1=15次。

**03. D**
选择排序在每趟结束后可以确定一个元素的最终位置，不对。插入排序，第$i$趟后前$i+1$个元素应该是有序的，不对。第二趟{20,15}和{21,25}是反序的，因此不是归并排序。快速排序每趟都将基准元素放在其最终位置，然后以它为基准将序列划分为两个子序列。观察题中的排序过程，可知是快速排序。

**04. C**
以46为基准元素，首先从后往前扫描比46小的元素，并与之进行交换，而后从前往后扫描比46大的元素并将46与该元素交换，得到(40, 46, 56, 38, 79, 84)。此后，继续重复从后往前扫描与从前往后扫描的操作，直到46处于最终位置。

**05. D**
当待排序数据为基本有序时，每次选取第$n$个元素为基准，会导致划分区间分配不均匀，不利于发挥快速排序算法的优势。相反，当待排序数据分布较为随机时，基准元素能将序列划分为两个长度大致相等的序列，这时才能发挥快速排序的优势。

**06. D**
这里问的是平均性能，选项A、B的平均性能都会达到$O(n^2)$，而希尔排序虽然大大降低了直接插入排序的时间复杂度，但其平均性能不如快速排序。另外，虽然众多排序算法的平均时间复杂度也是$O(n\log_2n)$，但快速排序算法的常数因子是最小的。

**07. A**
若为插入排序，则前三个元素应该是有序的，显然不对。而冒泡排序和选择排序经过两趟排序后应该有两个元素处于最终位置（最左/右端），无论是按从小到大还是从大到小排序，数据序列中都没有两个满足这样的条件的元素，因此只可能选择选项A。
**【另解】** 先写出排好序的序列，并和题中的序列做对比。
题中序列：2 1 4 9 8 10 6 20
已排好序序列：1 2 4 6 8 9 10 20
在已排好序的序列中，与题中序列相同的元素有4、8和20，最左和最右两个元素与题中的序列不同，所以不可能是冒泡排序、选择排序或插入排序。

**08. C**
从后往前冒泡的过程为，第一趟{1, 8, 9, 10, 4, 5, 6, 20, 2}，第二趟{1, 2, 8, 9, 10, 4, 5, 6, 20}，第三趟{1, 2, 4, 8, 9, 10, 5, 6, 20}，第四趟{1, 2, 4, 5, 8, 9, 10, 6, 20}，第五趟{1, 2, 4, 5, 6, 8, 9, 10, 20}，经过第五趟冒泡后，序列已经全局有序，因此选择选项C。实际每趟冒泡发生交换后可以判断是否会产生新的逆序对，若不会产生，则本趟冒泡之后序列全局有序，所以最少5趟即可。
**09. B**
第一趟从左往右的排序结果为4, 7, 3, 5, 6, 8, 9, 1, 2, 10；第二趟从右往左的排序结果为1, 4, 7, 3, 5, 6, 8, 9, 2, 10；第三趟从左往右的排序结果为1, 4, 3, 5, 6, 7, 8, 2, 9, 10；第四趟从右往左的排序结果为1, 2, 4, 3, 5, 6, 7, 8, 9, 10；第五趟从左往右的排序结果为1, 2, 3, 4, 5, 6, 7, 8, 9, 10，此时序列已有序，但仍需进行一趟无交换的排序才能确定序列已有序，因此共需6趟排序。

**10. A、D**
当每趟的枢轴值都把表等分为长度相近的两个子表时，速度是最快的；当表本身已经有序或逆序时，速度最慢。选项D中的序列已按关键字排好序，因此它是最慢的，而选项A中第一趟枢轴值21将表划分为两个子表{9, 17, 5}和{25, 23, 30}，而后对两个子表划分时，枢轴值再次将它们等分，所以该序列是快速排序最优的情况，速度最快。针对其他选项，可以进行类似的分析。

**11. B**
对各序列分别执行一趟快速排序，可做如下分析（以选项A为例）：枢轴值为92，因此35移动到第一个位置，96移动到第六个位置，30移动到第二个位置，再将枢轴值移动到30所在的单元，即第五个位置，所以选项A中序列移动的次数为4。同样，可以分析出选项B中序列的移动次数为8，选项C中序列的移动次数为4，选项D中序列的移动次数为2。

**12. C**
显然，若按从小到大排序，则最终有序的序列是{11, 18, 23, 68, 69, 73, 93}；若按从大到小排序，则最终有序的序列是{93, 73, 69, 68, 23, 18, 11}。对比可知选项I、II中没有处于最终位置的元素，所以选项I、II都不可能。选项III中73和93处于从大到小排序后的最终位置，而且73将序列分割成大于73和小于73的两部分，所以选项III是有可能的。选项IV中73和93处于从小到大排列后的最终位置，73也将序列分割成大于73和小于73的两部分。

**13. B、C**
快速排序过程构成一个递归树，递归深度即递归树的高度。枢轴值每次都将子表等分时，递归树的高为$\log_2n$；枢轴值每次都是子表的最大值或最小值时，递归树退化为单链表，树高为$n$。

**14. D**
快速排序的最好情况是每次划分将待排序列划分为等长的两部分。因此，第一趟将第1个元素与后面的7个元素进行比较，将原序列划分为长度为3和4的两个子表，比较7次；第二趟对两个子表进行划分，将长度为3的子表划分为长度为1的两个子表（不用继续划分），比较2次，将长度为4的子表划分为长度为1和2的两个子表，比较3次；第三趟将长度为2的子表划分为长度为1的子表，比较1次。至此，排序结束，共进行的比较次数是7+2+3+1=13。

**15. D**
快速排序的递归次数与元素的初始排列有关。若每次划分后分区比较平衡，则递归次数少；若划分后分区不平衡，则递归次数多。递归次数与分区处理顺序无关。

**16. A**
对于绝大部分内部排序而言，只适用于顺序存储结构。快速排序在排序的过程中，既要从后往前查找，也要从前往后查找，因此宜采用顺序存储。

**17. C**
对n个元素进行第一趟快速排序后，会确定一个基准元素，根据这个基准元素在数组中的位置，有两种情况：①基准元素在数组的首端或尾端，接下来对剩下的$n-1$个元素构成的子序列进行第二趟快速排序，再确定一个基准元素。这样，在两趟排序后就至少能确定两个元素的最终位置，其中至少有一个元素是在数组的首端或尾端。②基准元素不在数组的首端或尾端，第二趟快速排序对基准元素划分开的两个子序列分别进行一次划分，两个子序列各确定一个基准元素。这样，两趟排序后就至少能确定三个元素的最终位置。基于上述结论，观察题中的四个选项，选项A的2, 3, 6, 7, 9符合第一种或第二种情况；选项B中2, 9符合第一种情况；选项D中5, 9符合第一种情况；最后看选项C，只有9处于最终位置，因此不可能是快速排序第二趟的结果。

**18. D**
基于上题中分析得出的结论，观察题中的四个选项，选项A的28, 72符合第一种情况；选项B的2, 72符合第一种情况；选项C的2, 28, 32符合第一种或第二种情况；最后看选项D，只有12和32处于最终位置，既不符合第一种情况，又不符合第二种情况。

**19. D**
第一趟划分后得到的序列中只有一个枢轴，因此可将当前序列和最终排好序的序列进行比较，如下表所示。枢轴会出现在两个序列的相同位置，可以看出枢轴只可能是77、81，选项只有81。在当前序列中，77左边有比它大的元素80，因此77不是枢轴；而81左边都是比它小的元素，右边都是比它大的元素，因此81是枢轴。

| | | | | | | | | | | |
| :--- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- |
| **当前序列** | 68 | 11 | 70 | 23 | 80 | 77 | 48 | **81** | 93 | 88 |
| **最终序列** | 11 | 23 | 48 | 68 | 70 | 77 | 80 | **81** | 88 | 93 |

**20. A**
依题意，对数组M进行第一趟快速排序后，会将M划分为三部分：小于枢轴值的块P，枢轴元素，大于枢轴值的块Q。P和Q块内都是无序的，但是P块内的数据≤枢轴值，Q块内的数据≥枢轴值，因此P块内的数据≤Q块内的数据，即P与Q块间有序。

**二、综合应用题**

**01.【解答】**
本题可采用基于快速排序的划分思想来设计算法，只需遍历一次即可，其时间复杂度为$O(n)$，空间复杂度为$O(1)$。假设表为L[1...n]，基本思想是：先从前往后找到一个偶数元素L(i)，再从后往前找到一个奇数元素L(j)，将二者交换；重复上述过程直到$i$大于$j$。
算法的实现如下：
```c
void move(ElemType A[], int len) {
    //对表A按奇偶进行一趟划分
    int i=0, j=len-1; //i表示左端偶数元素的下标;j表示右端奇数元素的下标
    while(i<j) {
        while(i<j && A[i]%2!=0) i++; //从前往后找到一个偶数元素
        while(i<j && A[j]%2!=1) j--; //从后往前找到一个奇数元素
        if (i<j){
            Swap(A[i],A[j]); //交换这两个元素
            i++; j--;
        }
    }
}
```

**02.【解答】**
显然，本题最直接的做法是用排序算法对数组先进行从小到大的排序，然后直接提取L(k)便得到了第k小的元素，但其平均时间复杂度将达到$O(n\log n)$以上。此外，还可采用小顶堆的方法，每次堆顶元素都是最小值元素，时间复杂度为$O(n+k\log_2n)$。下面介绍一个更精彩的算法，它基于快速排序的划分操作。
这个算法的主要思想如下：从数组L[1...n]中选择枢轴pivot（随机或直接取第一个）进行和快速排序一样的划分操作后，表L[1...n]被划分为L[1...m-1]和L[m+1...n]，其中$L(m)=\text{pivot}$。
讨论$m$与$k$的大小关系：
1) 当$m=k$时，显然pivot就是所要寻找的元素，直接返回pivot即可。
2) 当$m<k$时，所要寻找的元素一定落在L[m+1...n]中，因此可对L[m+1...n]递归地查找第$k-m$小的元素。
3) 当$m>k$时，所要寻找的元素一定落在L[1..m-1]中，因此可对L[1...m-1]递归地查找第$k$小的元素。
该算法的时间复杂度在平均情况下可以达到$O(n)$，而所占空间的复杂度则取决于划分的方法。算法的实现如下：
```c
int kth_elem(int a[], int low, int high, int k) {
    int pivot=a[low];
    int low_temp=low; //由于下面会修改low与high,在递归时又要用到它们
    int high_temp=high;
    while (low<high) {
        while (low<high && a[high]>=pivot)
            --high;
        a[low]=a[high];
        while (low<high && a[low]<=pivot)
            ++low;
        a[high]=a[low];
    }
    a[low]=pivot;
    //上面为快速排序中的划分算法
    //以下是本算法思想中所述的内容
    if (low==k) //由于与k相同，直接返回 pivot 元素
        return a[low];
    else if (low>k) //在前一部分表中递归寻找
        return kth_elem(a,low_temp,low-1,k);
    else //在后一部分表中递归寻找
        return kth_elem(a,low+1,high_temp,k);
}
```

**03.【解答】**
算法思想：顺序扫描线性表，将红色条块交换到线性表的最前面，蓝色条块交换到线性表的最后面。为此，设立三个指针，其中，$j$为工作指针，表示当前扫描的元素，$i$以前的元素全部为红色，$k$以后的元素全部为蓝色。根据$j$所指示元素的颜色，决定将其交换到序列的前部或尾部。初始时$i=0, k=n-1$，算法的实现如下：
```c
typedef enum{RED,WHITE,BLUE} color; //设置枚举数组
void Flag_Arrange(color a[],int n) {
    int i=0, j=0, k=n-1;
    while(j<=k) {
        switch(a[j]){ //判断条块的颜色
        case RED: 
            Swap(a[i],a[j]); i++; j++; break; //红色，则和i交换
        case WHITE: 
            j++; break;
        case BLUE: 
            Swap(a[j],a[k]); k--;            //蓝色，则和k交换
            //这里没有j++语句,以防止交换后a[j]仍为蓝色
        }
    }
}
```
例如，将元素值正数、负数和零排序为前面都是负数，接着是0，最后是正数，也用同样的方法。思考：为什么case RED语句不用考虑交换后a[j]仍为红色，而case BLUE语句中却需要考虑交换后a[j]仍为蓝色？

**04.【解答】**
1) 算法的基本设计思想
由题意可知，将最小的$\lfloor n/2 \rfloor$个元素放在$A_1$中，其余的元素放在$A_2$中，分组结果即可满足题目要求。仿照快速排序的思想，基于枢轴将$n$个整数划分为两个子集。根据划分后枢轴所处的位置$i$分别处理：
① 若$i=\lfloor n/2 \rfloor$，则分组完成，算法结束。
② 若$i<\lfloor n/2 \rfloor$，则枢轴及之前的所有元素均属于$A_1$，继续对$i$之后的元素进行划分。
③ 若$i>\lfloor n/2 \rfloor$，则枢轴及之后的所有元素均属于$A_2$，继续对$i$之前的元素进行划分。
基于该设计思想实现的算法，无须对全部元素进行全排序，其平均时间复杂度是$O(n)$，空间复杂度是$O(1)$。
2) 算法实现
```c
int setPartition(int a[], int n) {
    int pivotkey, low=0, low0=0, high=n-1, high0=n-1, flag=1, k=n/2, i;
    int s1=0, s2=0;
    while (flag) {
        pivotkey=a[low]; //选择枢轴
        while (low<high) { //基于枢轴对数据进行划分
            while (low<high && a[high]>=pivotkey) --high;
            if (low!=high) a[low]=a[high];
            while (low<high && a[low]<=pivotkey) ++low;
            if (low!=high) a[high]=a[low];
        } //end of while(low<high)
        a[low]=pivotkey;
        if (low==k-1) //若枢轴是第n/2小的元素,划分成功
            flag=0;
        else { //是否继续划分
            if (low<k-1){
                low0=++low;
                high=high0;
            }
            else{
                high0=--high;
                low=low0;
            }
        }
    }
    for(i=0;i<k;i++) s1+=a[i];
    for(i=k;i<n;i++) s2+=a[i];
    return s2-s1;
}
```
3) 本算法的平均时间复杂度是$O(n)$，空间复杂度是$O(1)$。

### 8.4 选择排序
选择排序的基本思想是：每一趟（如第$i$趟）在后面$n-i+1$ ($i=1, 2, \dots, n-1$)个待排序元素中选取关键字最小的元素，作为有序子序列的第$i$个元素，直到第$n-1$趟做完，待排序元素只剩下一个，就不用再选了。