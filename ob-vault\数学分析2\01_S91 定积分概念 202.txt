## 第九章 定积分

### §1 定积分概念

#### 一 问题提出
不定积分和定积分是积分学中的两大基本问题.求不定积分是求导数的逆运算,定积分则是某种特殊和式的极限,它们之间既有区别又有联系,现在先从两个例子来看定积分概念是怎样提出来的.

1.  **曲边梯形的面积** 设$f$为闭区间$[a,b]$上的连续函数,且$f(x) \ge 0$. 由曲线$y=f(x)$,直线$x=a,x=b$以及$x$轴所围成的平面图形(图9-1),称为曲边梯形.下面讨论曲边梯形的面积(这是求任何曲线边界图形面积的基础).

    在初等数学里,圆面积是用一系列边数无限增多的内接(或外切)正多边形面积的极限来定义的,现在我们仍用类似的办法来定义曲边梯形的面积.

    在区间$[a,b]$上任取$n-1$个分点,它们依次为
    $$
    a=x_0<x_1<x_2<\cdots<x_{n-1}<x_n=b,
    $$
    这些点把$[a,b]$分割成$n$个小区间$[x_{i-1},x_i],i=1,2,\cdots,n$.再用直线$x=x_i, i=1,2,\cdots,n-1$把曲边梯形分割成$n$个小曲边梯形(图9-2).

<center>
<img src="https://i.imgur.com/G5Ym5iJ.png" alt="图9-1和图9-2" style="width:500px">
</center>
<div style="text-align: center;">
<span style="display: inline-block; margin-right: 120px;">图 9-1</span>
<span style="display: inline-block;">图 9-2</span>
</div>

    在每个小区间$[x_{i-1},x_i]$上任取一点$\xi_i$,作以$f(\xi_i)$为高,$[x_{i-1},x_i]$为底的小矩形.当分割$[a,b]$的分点较多,又分割得较细密时,由于$f$为连续函数,它在每个小区间上的值变化不大,从而可用这些小矩形的面积近似替代相应小曲边梯形

的面积,于是,这$n$个小矩形面积之和就可作为该曲边梯形面积$S$的近似值,即
$$
S \approx \sum_{i=1}^{n} f(\xi_i) \Delta x_i \quad (\Delta x_i = x_i - x_{i-1}). \tag{1}
$$

注意到(1)式右边的和式既依赖于对区间$[a,b]$的分割,又与所有中间点$\xi_i(i=1,2,\cdots,n)$的取法有关.可以想象,当分点无限增多,且对$[a,b]$无限细分时,如果此和式与某一常数无限接近,而且与分点$x_i$和中间点$\xi_i$的选取无关,则就把此常数定义作为曲边梯形的面积$S$.

2.  **变力所做的功** 设质点受力$F$的作用沿$x$轴由点$a$移动到点$b$,并设$F$处处平行于$x$轴(图9-3).如果$F$为常力,则它对质点所做的功为$W=F(b-a)$.现在的问题是,$F$为变力,它连续依赖于质点所在位置的坐标$x$,即$F=F(x),x\in[a,b]$为一连续函数,此时$F$对质点所做的功$W$又该如何计算?

<center>
<img src="https://i.imgur.com/39hN76o.png" alt="图9-3" style="width:250px">
</center>
<div style="text-align: center;">图 9-3</div>

由假设$F(x)$为一连续函数,故在很小的一段位移区间上$F(x)$可以近似地看作一常量.类似于求曲边梯形面积那样,把$[a,b]$细分为$n$个小区间$[x_{i-1},x_i], \Delta x_i = x_i - x_{i-1}, i=1,2,\cdots,n$;并在每个小区间上任取一点$\xi_i$,就有
$F(x) \approx F(\xi_i),x \in [x_{i-1},x_i], i=1,2,\cdots,n$.
于是,质点从$x_{i-1}$位移到$x_i$时,力$F$所做的功就近似等于$F(\xi_i)\Delta x_i$,从而
$$
W \approx \sum_{i=1}^{n} F(\xi_i) \Delta x_i. \tag{2}
$$

同样地,对$[a,b]$作无限细分时,若(2)式右边的和式与某一常数无限接近,则就把此常数定义作为变力所做的功$W$.

上面两个例子,一个是计算曲边梯形面积的几何问题,另一个是求变力做功的力学问题,它们最终都归结为一个特定形式的和式逼近.在科学技术中还有许多同样类型的数学问题,解决这类问题的思想方法概括说来就是“分割,近似求和,取极限”.这就是产生定积分概念的背景.

#### 二 定积分的定义
**定义 1** 设闭区间$[a,b]$上有$n-1$个点,依次为
$$
a=x_0<x_1<x_2<\cdots<x_{n-1}<x_n=b,
$$
它们把$[a,b]$分成$n$个小区间$\Delta_i = [x_{i-1},x_i], i=1,2,\cdots,n$. 这些分点或这些闭子区间构成对$[a,b]$的一个分割,记为
$$
T=\{x_0,x_1,\cdots,x_n\}\text{或}\{\Delta_1,\Delta_2,\cdots,\Delta_n\}.
$$
小区间$\Delta_i$的长度为$\Delta x_i = x_i - x_{i-1}$,并记
$$
||T|| = \max_{1 \le i \le n} \{\Delta x_i\},
$$
称为分割$T$的模.

**注** 由于$\Delta x_i \le ||T||, i=1,2,\cdots,n$,因此$||T||$可用来反映$[a,b]$被分割的细密程度.另外,分割$T$一旦给出,$||T||$就随之而确定;但是,具有同一细度$||T||$的分割$T$却有无限多个.

**定义 2** 设$f$是定义在$[a,b]$上的一个函数.对于$[a,b]$的一个分割$T=\{\Delta_1,\Delta_2,\cdots,\Delta_n\}$,任取点$\xi_i \in \Delta_i, i=1,2,\cdots,n$,并作和式
$$
\sum_{i=1}^{n} f(\xi_i) \Delta x_i
$$
称此和式为函数$f$在$[a,b]$上的一个积分和,也称黎曼和.
显然,积分和既与分割$T$有关,又与所选取的点集$\{\xi_i\}$有关.

**定义 3** 设$f$是定义在$[a,b]$上的一个函数,$J$是一个确定的实数.若对任给的正数$\varepsilon$,总存在某一正数$\delta$,使得对$[a,b]$的任何分割$T$,以及在其上任意选取的点集$\{\xi_i\}$,只要$||T|| < \delta$,就有
$$
\left|\sum_{i=1}^{n} f(\xi_i) \Delta x_i - J\right| < \varepsilon,
$$
则称函数$f$在区间$[a,b]$上可积或黎曼可积;数$J$称为$f$在$[a,b]$上的定积分或黎曼积分,记作
$$
J = \int_a^b f(x) dx. \tag{3}
$$
其中,$f$称为被积函数,$x$称为积分变量,$[a,b]$称为积分区间,$a,b$分别称为这个定积分的下限和上限.

以上定义1至定义3是定积分抽象概念的完整叙述.下面是与定积分概念有关的几点补充注释.

**注 1** 把定积分定义的$\varepsilon-\delta$说法和函数极限的$\varepsilon-\delta$说法相对照,便会发现两者有相似的陈述方式,因此我们也常用极限符号来表达定积分,即把它写作
$$
J = \lim_{||T|| \to 0} \sum_{i=1}^{n} f(\xi_i) \Delta x_i = \int_a^b f(x)dx. \tag{4}
$$
然而,积分和的极限与函数的极限之间其实有着很大的区别:在函数极限$\lim_{x\to x_0}f(x)$中,对每一个极限变量$x$来说,$f(x)$的值是唯一确定的;而对于积分和的极限而言,每一个$||T||$并不唯一对应积分和的一个值,这使得积分和的极限要比通常的函数极限复杂得多.

**注 2** 可积性是函数的又一分析性质.稍后(定理9.3)就会知道连续函数是可积的,于是本节开头两个实例都可用定积分记号来表示:
1) 连续曲线$y=f(x) \ge 0$在$[a,b]$上形成的曲边梯形面积为$S = \int_a^b f(x)dx$;
2) 在连续变力$F(x)$作用下,质点从$a$位移到$b$所做的功为$W = \int_a^b F(x)dx$.

**注 3** (定积分的几何意义) 由上述1)看到,对于$[a,b]$上的连续函数$f$,当$f(x) \ge 0, x \in [a,b]$时,定积分(3)的几何意义就是该曲边梯形的面积;当$f(x) \le 0, x \in [a,b]$时,这时$J = -\int_a^b [-f(x)]dx$是位于$x$轴下方的曲边梯形面积的相反数,不妨称之为“负面积”;对于一般非定号的$f(x)$而言(图9-4),定积分$J$的值则是曲线$y=f(x)$在$x$轴上方部分所有曲边梯形的正面积与下方部分所有曲边梯形的负面积的代数和.

<center>
<img src="https://i.imgur.com/Bf98cR5.png" alt="图9-4" style="width:250px">
</center>
<div style="text-align: center;">图 9-4</div>

**注 4** 定积分作为积分和的极限,它的值只与被积函数$f$和积分区间$[a,b]$有关,而与积分变量所用的符号无关,即
$$
\int_a^b f(x)dx = \int_a^b f(t)dt = \int_a^b f(\theta)d\theta = \cdots.
$$

**例 1** 求在区间$[0,1]$上,以抛物线$y=x^2$为曲边的曲边三角形的面积(图9-5).

<center>
<img src="https://i.imgur.com/1B9q2xN.png" alt="图9-5" style="width:250px">
</center>
<div style="text-align: center;">图 9-5</div>

**解** 由注3,因$y=x^2$在$[0,1]$上连续,故所求面积为
$$
S = \int_0^1 x^2 dx = \lim_{||T||\to 0} \sum_{i=1}^n \xi_i^2 \Delta x_i.
$$
为求得此极限,在定积分存在的前提下,允许选择某种特殊的分割$T$和特殊的点集$\{\xi_i\}$.在此只需取等分分割:
$$
T = \left\{0, \frac{1}{n}, \frac{2}{n}, \cdots, \frac{n-1}{n}, 1\right\}, \quad ||T|| = \frac{1}{n};
$$
并取$\xi_i = \frac{i-1}{n} \in [x_{i-1}, x_i], i=1,2,\cdots,n$.则有
$$
\begin{aligned}
S &= \lim_{n\to\infty} \sum_{i=1}^{n} \left(\frac{i-1}{n}\right)^2 \cdot \frac{1}{n} = \lim_{n\to\infty} \frac{1}{n^3} \sum_{i=1}^{n} (i-1)^2 \\
&= \lim_{n\to\infty} \frac{(n-1)n(2n-1)}{6n^3} = \frac{1}{3}. \quad \Box
\end{aligned}
$$

### 习题

1. 按定积分定义证明:$\int_a^b kdx = k(b-a)$.
2. 通过对积分区间作等分分割,并取适当的点集$\{\xi_i\}$,把定积分看作是对应的积分和的极限,来计算下列定积分:
   (1) $\int_0^1 x^3 dx$; 提示:$\sum_{i=1}^n i^3 = \frac{1}{4}n^2(n+1)^2$
   (2) $\int_0^1 e^x dx$;
   (3) $\int_a^b e^x dx$;
   (4) $\int_a^b \frac{dx}{x^2} (0<a<b)$. (提示:取$\xi_i = \sqrt{x_{i-1}x_i}$)

### §2 牛顿—莱布尼茨公式

从上节例题和习题看到,通过求积分和的极限来计算定积分一般是很困难的.下面要介绍的牛顿—莱布尼茨公式不仅为定积分计算提供了一个有效的方法,而且在理论上把定积分与不定积分联系了起来.

**定理 9.1** 若函数$f$在$[a,b]$上连续,且存在原函数$F$,即$F'(x)=f(x), x\in[a,b]$,则$f$在$[a,b]$上可积,且
$$
\int_a^b f(x)dx = F(b) - F(a). \tag{1}
$$
上式称为牛顿—莱布尼茨公式,它也常写成
$$
\int_a^b f(x)dx = F(x)\bigg|_a^b.
$$

**证** 由定积分定义,任给$\varepsilon > 0$,要证存在$\delta > 0$,当$||T|| < \delta$时,有
$$
\left|\sum_{i=1}^n f(\xi_i) \Delta x_i - [F(b) - F(a)]\right| < \varepsilon.
$$
下面证明满足如此要求的$\delta$确实是存在的.

事实上,对于$[a,b]$的任一分割$T=\{a=x_0, x_1, \cdots, x_n=b\}$,在每个小区间$[x_{i-1},x_i]$上对$F(x)$使用拉格朗日中值定理,则分别存在$\eta_i \in (x_{i-1},x_i), i=1,2,\cdots,n$,使得
$$
F(b) - F(a) = \sum_{i=1}^n [F(x_i) - F(x_{i-1})]
$$