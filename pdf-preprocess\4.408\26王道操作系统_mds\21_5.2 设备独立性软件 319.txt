调出其他进程执行,而当前用户进程被阻塞,CPU和设备准备并行。在DMA方式下,驱动程序对DMA 控制器初始化后,便发送“启动DMA”命令,在外设和主存之间传送数据,同时 CPU执行调度程序,转其他进程执行,当前用户进程被阻塞时,CPU和数据传送并行。
21. C
中断处理结束后,是否返回到被中断的进程,有两种情况:①采用的是屏蔽中断方式(单重中断),此时会返回被中断的进程。②采用的是中断嵌套方式(多重中断),若没有更高优先级的中断请求,则会返回被中断的进程;否则,系统将处理更高优先级的中断请求。
22. A
块设备是指数据的存取和传输都是以数据块为单位的设备,其特征是传输速率较高且可寻址,典型的块设备(如磁盘)通常采用DMA方式。字符设备(也称流设备)是指数据的存取和传输是以字符为单位的设备,如键盘、打印机等,字符设备的传输速率较低且不可寻址。
23. B
键盘是典型的通过中断I/O方式工作的外设,当用户输入信息时,计算机响应中断并通过中断处理程序获得输入信息。
24. B
输入/输出软件一般从上到下分为4个层次:用户层、与设备无关的软件层、设备驱动程序及中断处理程序。与设备无关的软件层也就是系统调用的处理程序。
当用户使用设备时,首先在用户程序中发起一次系统调用,操作系统的内核接到该调用请求后,请求调用处理程序进行处理,再转到相应的设备驱动程序,当设备准备好或所需数据到达后,设备硬件发出中断,将数据按上述调用顺序逆向回传到用户程序中。
25. A
考查内容同上题。设备管理软件一般分为4个层次:用户层、与设备无关的系统调用处理层、设备驱动程序及中断处理程序。
26. B
DMA 的传送过程分为预处理、数据传送和后处理三个阶段。在预处理阶段,由CPU 初始化DMA 控制器中的有关寄存器、设置传送方向、测试并启动设备等。在数据传送阶段,完全由DMA控制,DMA 控制器接管系统总线。在后处理阶段,DMA控制器向CPU发送中断请求,CPU执行中断服务程序做DMA结束处理。因此,正确的执行顺序是②③①④。

### 二、综合应用题
**01.【解答】**
时钟中断频率为60Hz,因此中断周期为1/60s,每个时钟周期中用于中断处理的时间为2ms,因此比率为$0.002/(1/60) = 12\%$。
**02.【解答】**
因为一个字符占10位,因此在56kb/s的速率下,每秒传送$56000/10=5600$个字符,即产生5600次中断。每次中断需0.1ms,因此处理调制解调器占用的CPU时间共为$5600×0.1ms = 560ms$,占56%的CPU时间。

### 5.2 设备独立性软件
在学习本节时,请读者思考以下问题:
1) 当处理机和外部设备的速度差距较大时,有什么办法可以解决这个问题?
2) 什么是设备的独立性?引入设备的独立性有什么好处?

### 5.2.1 设备独立性软件
也称与设备无关的软件,是I/O 系统的最高层软件,它的下层是设备驱动程序,其界限因操作系统和设备的不同而有所差异。比如,一些本应由设备独立性软件实现的功能,也可能放在设备驱动程序中实现。这样的差异主要是出于对操作系统、设备独立性软件和设备驱动程序运行效率等多方面因素的权衡。总体而言,设备独立性软件包括执行所有设备公有操作的软件。

### 5.2.2 高速缓存与缓冲区
**1. 磁盘高速缓存(Disk Cache)**
**命题追踪** 设置磁盘缓冲区的目的 (2015)
操作系统中使用磁盘高速缓存技术来提高磁盘的I/O速度,对访问高速缓存要比访问原始磁盘数据更为高效。例如,正在运行进程的数据既存储在磁盘上,又存储在物理内存上,也被复制到CPU的二级和一级高速缓存中。不过,磁盘高速缓存技术不同于通常意义下的介于CPU与内存之间的小容量高速存储器,而是指利用内存中的存储空间来暂存从磁盘中读出的一系列盘块中的信息。因此,磁盘高速缓存逻辑上属于磁盘,物理上是驻留在内存中的盘块。
磁盘高速缓存在内存中分为两种形式:一种是在内存中开辟一个单独的空间作为缓存区,大小固定;另一种是将未利用的内存空间作为一个缓冲池,供请求分页系统和磁盘I/O 时共享。

**2. 缓冲区(Buffer)**
在设备管理子系统中,引入缓冲区的目的主要如下:
1) 缓和CPU与I/O 设备间速度不匹配的矛盾。
2) 减少对CPU的中断频率,放宽对CPU中断响应时间的限制。
3) 解决基本数据单元大小(数据粒度)不匹配的问题。
4) 提高CPU和I/O设备之间的并行性。
缓冲区的实现方法如下:
1) 采用硬件缓冲器,但由于成本太高,除一些关键部位外,一般不采用硬件缓冲器。
2) 利用内存作为缓冲区,本节要介绍的正是由内存组成的缓冲区。
根据系统设置缓冲区的个数,缓冲技术可以分为如下几种:
(1) 单缓冲
每当用户进程发出一个I/O请求,操作系统便在内存中为之分配一个缓冲区。通常,一个缓冲区的大小就是一个块。如图5.6所示,在块设备输入时,假定从设备将一块数据输入缓冲区的时间为$T$,操作系统将该缓冲区中的数据传送到工作区的时间为$M$,而CPU 对这一块数据进行处理的时间为$C$。注意,必须等缓冲区装满后才能从缓冲区中取出数据。
**命题追踪** 单缓冲的工作时间的理解与计算 (2011、2013)
在单缓冲区中,$T$是可以和$C$并行的。
当$T>C$时,CPU处理完一块数据后,暂时不能将下一块数据传送到工作区,必须等待缓冲区装满数据,再将下一块数据从缓冲区传送到工作区,平均处理一块数据的时间为$T+M$。
当$T<C$时,缓冲区中装满数据后,暂时不能继续送入下一块数据,必须等待CPU 处理完上一块数据,再将下一块数据从缓冲区传送到工作区,平均处理一块数据的时间为$C+M$。
(a)
用户进程
处理(C)
工作区 <-> 传送(M) <-> 缓冲区 <-> 输入(T) <-> I/O设备

(b)
T1 M1 C1
T2 M2 C2
T3 M3 C3
...

**图5.6 单缓冲工作示意图**

总结:单缓冲区处理每块数据的平均时间为$Max(C, T) + M$。
缓冲区是共享资源,因此使用时必须互斥。若CPU 尚未取走缓冲区中的数据,则即使设备又生产出新的数据,也无法将其送入缓冲区,此时设备需要等待。
(2) 双缓冲
为了加快输入和输出速度,提高设备利用率,引入了双缓冲机制,也称缓冲对换。如图 5.7所示,当设备输入数据时,先将数据送入缓冲区1,装满后便转向缓冲区2。此时,操作系统可以从缓冲区1中取出数据,送入用户进程,并由CPU对数据进行处理。当缓冲区1中取出的数据处理完后,若缓冲区2已装满,则操作系统又从缓冲区2中取出数据送入用户进程处理,而设备又可以开始将数据送入缓冲区1。可见,双缓冲机制提高了设备和CPU的并行程度。

(a)
用户进程
工作区 <-> 缓冲区1, 缓冲区2 <-> I/O设备

(b)
T1 M1 C1 (处理缓冲1)
T2 M2 C2 (处理缓冲2)
T3 M3 C3 (处理缓冲1)
T4 M4 C4 (处理缓冲2)
...

**图5.7 双缓冲工作示意图**
仍然假设设备输入数据到缓冲区、数据传送到用户进程和处理的时间分别为$T$、$M$和$C$。
**命题追踪** 双缓冲的工作时间的理解与计算 (2011)
在双缓冲区中,$C$和$M$是可以与$T$并行的。
当$T>C+M$时,说明设备输入的时间比数据传送和处理的时间多,可使设备连续输入。假设在某个时刻,缓冲区1是空的,缓冲区2是满的,缓冲区2开始向工作区传送数据,缓冲区1开始装入数据。传送并处理的时间为$C+M$,但缓冲区1还未装满,必须等待缓冲区1装满数据后,才能将下一块数据从缓冲区1传送到工作区,平均处理一块数据的时间为$T$。
当$T<C+M$时,说明设备输入的时间比数据传送和处理的时间少,可使CPU不必等待设备输入。假设在某个时刻,缓冲区1是空的,缓冲区2是满的,缓冲区2开始向工作区传送数据,缓冲区1开始装入数据。缓冲区1装满数据的时间为$T$,必须等待缓冲区2中的数据传送并处理完后,才能将下一块数据从缓冲区1传送到工作区,平均处理一块数据的时间为$C+M$。
总结:双缓冲区处理每块数据的平均时间为$Max(C + M, T)$。
若两台机器之间仅配置了单缓冲,如图5.8(a)所示,则它们在任意时刻都只能实现单方向的
数据传输,而绝不允许双方同时向对方发送数据。为了实现双向数据传输,必须在两台机器中都设置两个缓冲区,一个用作发送缓冲区,另一个用作接收缓冲区,如图5.8(b)所示。

A机 <-> 缓冲区 <-> B机 <-> 缓冲区 <-> A机
(a) 单缓冲区

A机: 发送缓冲区 <-> B机: 接收缓冲区
A机: 接收缓冲区 <-> B机: 发送缓冲区
(b) 双缓冲区

**图5.8 双机通信时缓冲区的设置**
(3) 循环缓冲
在双缓冲机制中,当输入与输出的速度基本匹配时,能取得较好的效果。但若两者的速度相差甚远,则双缓冲区的效果不会太理想。为此,又引入了多缓冲机制,将多个缓冲区组成循环缓冲区的形式,如图5.9所示,灰色表示已装满数据的缓冲区,白色表示空缓冲区。

(环形队列，含6个缓冲区，编号1-6)
in -> 1 (空) -> 2 (空) -> 3 (满) -> out -> 4 (满) -> 5 (满) -> 6 (空) -> 1
**图5.9 循环缓冲工作示意图**
循环缓冲包含多个大小相等的缓冲区,每个缓冲区中有一个链接指针指向下一个缓冲区,最后一个缓冲区指针指向第一个缓冲区,多个缓冲区链接成一个循环队列。
循环缓冲中还需设置$in$和$out$两个指针,$in$指向第一个可以输入数据的空缓冲区,$out$指向第一个可以提取数据的满缓冲区。输入/输出时,$in$和$out$指针沿链接方向循环移动。
(4) 缓冲池
相比于缓冲区(仅是一块内存空间),缓冲池是包含一个用于管理自身的数据结构和一组操作函数的管理机制,用于管理多个缓冲区。缓冲池可供多个进程共享使用。
缓冲池由多个系统公用的缓冲区组成,缓冲区按其使用状况可以分为:①空缓冲队列,由空缓冲区链接而成的队列;②输入队列,由装满输入数据的缓冲区链接而成的队列;③输出队列,由装满输出数据的缓冲区链接而成的队列。此外还应具有4种工作缓冲区:①用于收容输入数据的工作缓冲区($hin$),②用于提取输入数据的工作缓冲区($sin$),③用于收容输出数据的工作缓冲区($hout$),④用于提取输出数据的工作缓冲区($sout$),如图5.10所示。

缓冲池 <-> 用户程序
缓冲池内部:
收容输入: hin
提取输入: sin
提取输出: sout
收容输出: hout

**图5.10 缓冲池的4种工作方式**
缓冲池中的缓冲区有以下4种工作方式。
1) 收容输入。输入进程需要输入数据时,从空缓冲队列的队首摘下一个空缓冲区,作为收
容输入工作缓冲区,然后将数据输入其中,装满后再将它挂到输入队列的队尾。
2) 提取输入。计算进程需要输入数据时,从输入队列的队首取得一个缓冲区,作为提取输入工作缓冲区,从中提取数据,用完该数据后将它挂到空缓冲队列的列尾。
3) 收容输出。计算进程需要输出数据时,从空缓冲队列的队首取得一个空缓冲区,作为收容输出工作缓冲区,当其中装满数据后,再将它挂到输出队列的队尾。
4) 提取输出。输出进程需要输出数据时,从输出队列的队首取得一个装满输出数据的缓冲区,作为提取输出工作缓冲区,当数据提取完后,再将它挂到空缓冲队列的队尾。
对于循环缓冲和缓冲池,我们只是定性地介绍它们的机理,而不去定量研究它们平均处理一块数据所需要的时间。而对于单缓冲和双缓冲,我们只要按照上面的模板分析,就可以解决任何单缓冲和双缓冲情况下数据块处理时间的问题,以不变应万变。

**3. 高速缓存与缓冲区的对比**
高速缓存是可以保存数据拷贝的高速存储器,访问高速缓存比访问原始数据更高效,速度更快。高速缓存和缓冲区的对比见表5.1。

**表5.1 高速缓存和缓冲区的对比**

**相同点**
都介于高速设备和低速设备之间

**区别**
| | 高速缓存 | 缓冲区 |
| :--- | :--- | :--- |
| **存放数据** | 存放的是低速设备上的某些数据的复制数据,即高速缓存上有的,低速设备上面必然有 | 存放的是低速设备传递给高速设备的数据(或相反),而这些数据在低速设备(或高速设备)上却不一定有备份,这些数据再从缓冲区传送到高速设备(或低速设备) |
| **目的** | 高速缓存存放的是高速设备经常要访问的数据,若高速设备要访问的数据不在高速缓存中,则高速设备就需要访问低速设备 | 高速设备和低速设备的通信都要经过缓冲区,高速设备永远不会直接去访问低速设备 |

### 5.2.3 设备分配与回收
**1. 设备分配概述**
设备分配是指根据用户的I/O 请求分配所需的设备。分配的总原是充分发挥设备的使用效率,尽可能地既让设备忙碌,又避免由于不合理的分配方法造成进程死锁。

**2. 设备分配的数据结构**
在系统中,可能存在多个通道,每个通道可以连接多个控制器,每个控制器可以连接多个物理设备。设备分配的数据结构要能体现出这种从属关系,各数据结构的介绍如下。
1) **设备控制表(DCT)**: 系统为每个设备配置一张DCT,表中的表项就是设备的各个属性,如图5.11所示。在DCT中,应该有下列字段:
设备控制表集合
- DCT 1
- DCT 2
- ...
- DCT n

每个DCT包含:
- 设备类型type
- 设备标识符:deviceid
- 设备状态:等待/不等待 忙/闲
- 指向控制器表的指针
- 重复执行次数或时间
- 设备队列的队首指针

**图5.11 DCT**
**设备类型**:表示设备类型,如打印机、扫描仪、键盘等。
**设备标识符**:即物理设备名,每个设备在系统中的物理设备名是唯一的。
**设备状态**:表示当前设备的状态(忙/闲)。
**指向控制器表的指针**:每个设备由一个控制器控制,该指针指向对应的控制器表。
**重复执行次数或时间**:重复执行次数达到规定值仍不成功时,才认为此次I/O 失败。
**设备队列的队首指针**:指向正在等待该设备的进程队列(由进程PCB组成)的队首。

**注**
当某进程释放某个设备,且无其他进程请求该设备时,系统将该设备DCT中的设备状态改为空闲,即可实现“设备回收”。

2) **控制器控制表(COCT)**: 每个设备控制器都对应一张 COCT,如图5.12(a)所示。操作系统根据 COCT的信息对控制器进行操作和管理。每个控制器由一个通道控制,通过表项“与控制器连接的通道表指针”可以找到相应通道的信息。
3) **通道控制表(CHCT)**: 每个通道都对应一张CHCT,如图5.12(b)所示。操作系统根据CHCT 的信息对通道进行操作和管理。一个通道可为多个控制器服务,通过表项“与通道连接的控制器表首址”可以找到该通道管理的所有控制器的信息。
4) **系统设备表(SDT)**: 整个系统只有一张 SDT,如图5.12(c)所示。它记录已连接到系统中的所有物理设备的情况,每个物理设备对应一个表目。

(a)控制器控制表COCT
- 控制器标识符:controllerid
- 控制器状态:忙/闲
- 与控制器连接的通道表指针
- 控制器队列的队首指针
- 控制器队列的队尾指针

(b) 通道控制表CHCT
- 通道标识符:channelid
- 通道状态:忙/闲
- 与通道连接的控制器表首址:
- 通道队列的队首指针
- 通道队列的队尾指针

(c)系统设备表SDT
- 表目I: 设备类, 设备标识符, DCT, 驱动程序入口
- 表目II: ...
- ...

**图5.12 COCT、CHCT和SDT**

在多道程序系统中,进程数多于资源数,因此要有一套合理的分配原则,主要考虑的因素有设备的固有属性、设备的分配算法、设备分配的安全性以及设备的独立性。

**3. 设备分配时应考虑的因素**
**命题追踪** 设备分配需要考虑的因素 (2023)
(1) 设备的固有属性
设备的固有属性可分成三种,对它们应采取不同的分配策略。
1) 独占设备:将它分配给某个进程后,便由该进程独占,直至进程完成或释放该设备。
2) 共享设备:可将它同时分配给多个进程,需要合理调度各个进程访问该设备的先后次序。
3) 虚拟设备:虚拟设备属于可共享设备,可将它同时分配给多个进程使用。
(2) 设备分配算法
针对设备分配,通常只采用以下两种分配算法:
1) FCFS算法。该算法根据各个进程对某个设备提出请求的先后次序,将这些进程排成一个设备请求队列,设备分配程序总是将设备首先分配给队首进程。
2) 最高优先级优先算法。在利用该算法形成设备队列时,优先级高的进程排在设备队列前面,而对于优先级相同的I/O请求,则按FCFS 原则排队。
(3) 设备分配中的安全性
设备分配中的安全性是指在设备分配中应防止发生进程死锁。
1) **安全分配方式**。每当进程发出I/O请求后,便进入阻塞态,直到其 I/O操作完成时才被唤醒。这样,进程一旦获得某种设备后便会阻塞,不能再请求任何资源,而在它阻塞时也不保持任何资源。其优点是设备分配安全,缺点是CPU和I/O 设备是串行工作的。
2) **不安全分配方式**。进程在发出 I/O 请求后仍继续运行,需要时又会发出第二个、第三个I/O请求等。仅当进程所请求的设备已被另一进程占用时,才进入阻塞态。优点是一个进程可同时操作多个设备,使进程推进迅速;缺点是有可能造成死锁。

**4. 设备分配的步骤**
下面以独占设备为例,介绍设备分配的过程。
1) 分配设备。首先根据I/O 请求中的物理设备名,查找SDT,从中找出该设备的DCT,再根据 DCT 中的设备状态字段,可知该设备的状态。若忙,则将进程 PCB 挂到设备等待队列中;若不忙,则根据一定的策略将设备分配给该进程。
2) 分配控制器。设备分配后,根据DCT找到COCT,查询控制器的状态。若忙,则将进程PCB 挂到控制器等待队列中;若不忙,则将控制器分配给该进程。
3) 分配通道。控制器分配后,根据COCT找到CHCT,查询通道的状态。若忙,则将进程PCB 挂到通道等待队列中;若不忙,则将通道分配给该进程。只有设备、控制器和通道都分配成功时,这次的设备分配才算成功,之后便可启动设备进行数据传送。
在上面的例子中,进程是以物理设备名提出I/O请求的。若指定设备已分配给其他进程,则该进程分配失败;或者说上面的设备分配程序不具有与设备无关性。为了获得设备的独立性,进程应使用逻辑设备名。这样,系统首先从SDT 中找出第一个该类设备的DCT。若该设备忙,则查找第二个该类设备的DCT,仅当所有该类设备都忙时,才将进程挂到该类设备的等待队列上。而只要有一个该类设备可用,系统便进入进一步的分配操作。

**5. 逻辑设备名到物理设备名的映射**
**命题追踪** 逻辑设备名和物理设备名的使用 (2009)
为了实现设备的独立性,进程中应使用逻辑设备名来请求某类设备。但是,系统只能识别物理设备名,因此在系统中需要配置一张逻辑设备表,用于将逻辑设备名映射为物理设备名。
**逻辑设备表(Logical Unit Table, LUT)**的每个表项中包含3项内容:逻辑设备名、物理设备名和设备驱动程序的入口地址。当进程用逻辑设备名来请求分配设备时,系统会为它分配一台相应的物理设备,并在LUT中建立一个表目,填上相应的信息,当以后进程再利用该逻辑设备名请求 I/O 操作时,系统通过查找LUT来寻找对应的物理设备及其驱动程序。
在系统中,可采取两种方式设置逻辑设备表:
1) 整个系统中只设置一张LUT。如图5.13(a)所示。所有进程的设备分配情况都记录在同一张LUT中,这就要求所有用户不能使用相同的逻辑设备名,主要适用于单用户系统。
2) 为每个用户设置一张LUT。如图5.13(b)所示。系统为每个用户设置一张LUT,同时在多用户系统中都配置系统设备表。因此,不同用户可以使用相同的逻辑设备名。

(a)整个系统的LUT
| 逻辑设备名 | 物理设备名 | 驱动程序入口地址 |
| :--- | :--- | :--- |
| /dev/printer | 3 | 2014 |
| /dev/tty | 5 | 2046 |
| ... | ... | ... |

(b)每个用户设置一张 LUT
| 逻辑设备名 | 系统设备指针 |
| :--- | :--- |
| /dev/printer | 3 |
| /dev/tty | 5 |
| ... | ... |

**图5.13 逻辑设备表 LUT**
### 5.2.4 SPOOLing 技术 (假脱机技术)
为了缓和CPU的高速性与I/O 设备的低速性之间的矛盾,引入了假脱机技术,它是操作系统中采用的一项将独占设备改造成共享设备的技术。该技术利用专门的外围控制机,先将低速 I/O设备上的数据传送到高速磁盘上,或者相反。当CPU需要输入数据时,便可直接从磁盘中读取数据;反之,当CPU 需要输出数据时,也能以很快的速度将数据先输出到磁盘上。引入多道程序技术后,系统便可利用程序来模拟脱机输入/输出时的外围控制机,在主机的直接控制下实现脱机输入/输出功能。SPOOLing 系统的组成如图5.14所示。

内存 <-> 磁盘
内存中包含:
- 输入进程SP; 输出进程SP。
- 输入缓冲区B_i
- 输出缓冲区B_o
磁盘中包含:
- 输入井
- 输出井

输入设备 -> 输入缓冲区B_i -> 输入井
输出井 -> 输出缓冲区B_o -> 输出设备

**图5.14 SPOOLing 系统的组成**

**命题追踪** SPOOLing 技术的特点 (2016)
(1) 输入井和输出井
在磁盘上开辟出的两个存储区域。输入井模拟脱机输入时的磁盘,用于收容 I/O 设备输入的数据。输出井模拟脱机输出时的磁盘,用于收容用户程序的输出数据。一个进程的输入(或输出)数据保存为一个文件,所有进程的输入(或输出)文件链接成一个输入(或输出)队列。
(2) 输入缓冲区和输出缓冲区
在内存中开辟的两个缓冲区。输入缓冲区用于暂存由输入设备送来的数据,以后再传送到输入井。输出缓冲区用于暂存从输出井送来的数据,以后再传送到输出设备。
(3) 输入进程和输出进程
输入进程用于模拟脱机输入时的外围控制机,将用户要求的数据从输入设备传送到输入缓冲区,再存放到输入井中。当CPU 需要输入数据时,直接从输入井读入内存。输出进程用于模拟脱机输出时的外围控制机,将用户要求输出的数据从内存传送到输出井,待输出设备空闲时,再将输出井中的数据经输出缓冲区输出至输出设备。
(4) 井管理程序
用于控制作业与磁盘井之间信息的交换。
打印机是典型的独占设备,利用SPOOLing 技术可将它改造为一台可供多个用户共享的打印设备。当多个用户进程发出打印输出请求时,SPOOLing 系统同意它们的请求,但并不真正立即将打印机分配给它们,而由假脱机管理进程为每个进程做如下两项工作:
1) 在磁盘缓冲区中为进程申请一个空闲盘块,并将要打印的数据送入其中暂存。
2) 为用户进程申请一张空白的用户请求打印表,并将用户的打印要求填入其中,再将该表挂到假脱机文件队列上。
对每个用户进程而言,系统并非即时执行真实的打印操作,而只是即时将数据输出到缓冲区,这时的数据并未被真正打印,而只让用户感觉系统已为它打印,真正的打印操作是在打印机空闲且该打印任务在等待队列中已排到队首时进行的。以上过程用户是不可见的。虽然系统中只有一台打印机,但当进程提出打印请求时,系统都会在输出井中为其分配一个缓冲区(相当于分配一
台逻辑设备),使每个进程都觉得自己在独占一台打印机,从而实现对打印机的共享。
SPOOLing 系统的特点:①提高了 I/O速度,将对低速 I/O 设备执行的操作演变为对磁盘缓冲区中数据的存取操作,如同脱机输入/输出一样,缓和了CPU和低速 I/O 设备之间速度不匹配的矛盾;②将独占设备改造为共享设备,在假脱机打印机系统中,实际上并没有为任何进程分配设备;③实现了虚拟设备功能,对每个进程而言,它们都认为自己独占了一台设备。
SPOOLing 技术是一种以空间换时间的技术,我们很容易理解它牺牲了空间,因为它开辟了磁盘上的空间作为输入井和输出井,但它又是如何节省时间的呢?
从前述内容我们了解到,磁盘是一种高速设备,在与内存交换数据的速度上优于打印机、键盘、鼠标等中低速设备。试想一下,若没有SPOOLing技术,CPU要向打印机输出要打印的数据,打印机的打印速度比较慢,CPU就必须迁就打印机,在打印机将数据打印完后才能继续做其他的工作,浪费了CPU的不少时间。在SPOOLing 技术下,CPU要向打印机打印的数据可以先输出到磁盘的输出井中(这个过程由假脱机进程控制),然后做其他的事情。若打印机此时被占用,则SPOOLing 系统就会将这个打印请求挂到等待队列上,待打印机有空时再将数据打印出来。向磁盘输出数据的速度比向打印机输出数据的速度快,因此就节省了时间。

### 5.2.5 设备驱动程序接口
设备驱动程序是I/O 系统的上层与设备控制器之间的通信程序,其主要任务是接收上层应用发来的抽象 I/O 请求,如read 或write命令,将它们转换为具体要求后发送给设备控制器,进而使其启动设备去执行任务;反之,它也将设备控制器发来的信号传送给上层应用。
**命题追踪** 设备驱动程序的功能 (2013、2019、2023)
为了实现上层应用与设备控制器之间的通信,设备驱动程序应具有以下功能:①接收由上层软件发来的命令和参数,并将抽象要求转换为与设备相关的具体要求。例如,将抽象要求中的盘块号转换为磁盘的盘面号、磁道号和扇区号。②检查用户I/O 请求的合法性,了解设备的工作状态,传递与设备操作有关的参数,设置设备的工作方式。③发出I/O命令,若设备空闲,则立即启动它,完成指定的I/O操作;若设备忙,则将请求者的PCB挂到设备队列上等待。④及时响应由设备控制器发来的中断请求,并根据其中断类型,调用相应的中断处理程序进行处理。
**命题追踪** 设备驱动程序的特点 (2022)
相比于普通的应用程序和系统程序,设备驱动程序具有以下差异:①设备驱动程序将抽象的I/O 请求转换成具体的I/O 操作后,传送给设备控制器,并将设备控制器中记录的设备状态和I/O 操作的完成情况及时地反馈给请求进程。②设备驱动程序与设备采用的I/O 控制方式紧密相关,常用的I/O 控制方式是中断驱动方式和DMA方式。③设备驱动程序与硬件密切相关,对于不同类型的设备,应配置不同的设备驱动程序。④由于设备驱动程序与硬件紧密相关,目前很多设备驱动程序的基本部分已固化在ROM中。⑤设备驱动程序应允许同时多次调用执行。
为了使所有的设备驱动程序都有统一的接口,一方面,要求每个设备驱动程序与操作系统之间都有相同或相近的接口,以便更容易地添加一个新的设备驱动程序,同时更容易地编制设备驱动程序;另一方面,要将抽象的设备名转换为具体的物理设备名,并且进一步找到相应的设备驱动程序入口。此外,还应对设备进行保护,防止无权访问的用户使用设备。

### 5.2.6 I/O 操作举例
由于统考对I/O 的考查越来越深入,本节以C语言中的库函数scanf为例,从执行“`scanf("%c", &d)`”开始,到最终键盘输入的字符存入变量d,来介绍 I/O操作的具体执行细节。
在讨论之前,先补充两个概念:内核缓冲区(就是处于内核空间的缓冲区)和与之对应的用户缓冲区。在scanf函数执行的第一阶段,就申请了用户缓冲区buf,由于buf位于用户空间中,而接下来需要在内核空间中运行,这两个空间是相互独立的。内核空间用来存放操作系统的代码和数据,是供所有进程共享的。系统调用函数运行在内核态,因此要先把 I/O端口中的数据复制到内核空间中,在系统调用返回前,再将数据从内核空间复制到用户空间。
程序调用“`scanf("%c", &d)`”时,尝试用键盘输入对变量d进行赋值。scanf()会关联一个用户缓冲区 buf,这个缓冲区是C语言函数库在用户空间中管理的。
scanf()的第一阶段的工作是在C语言函数库中完成的:
1) 检查与 scanf函数关联的用户缓冲区buf,若缓冲区中已有数据,则直接读取。若缓冲区为空,则触发系统调用read,以从内核缓冲区中读取数据。
2) 执行系统调用read,read 调用会在执行 trap 指令前传入本次调用的三个参数。
*   fd:文件描述符,指明输入设备。
*   buf:用户空间的缓冲区,数据从内核缓冲区被复制到这个缓冲区。
*   count: 读取的最大字节数。
第二阶段的工作是系统调用,read 调用会展开成一段包含陷阱指令的代码,从用户态陷入内核态以执行相关的操作。系统调用的大致过程如图5.15所示。

传递系统调用参数 -> 执行trap指令陷入到内核态 -> 执行相应的服务程序 -> 返回用户态
**图5.15 系统调用的大致过程**

进入内核态后,系统调用服务例程申请一个内核缓冲区,并且最终转到真正执行I/O操作的设备驱动层。在中断方式下,系统调用服务例程执行过程的大致描述如下:
1) 设置相应的I/O参数后,发起I/O的进程P进入阻塞态,CPU调度其他进程运行。
2) 用户通过键盘输入字符,字符被送到键盘I/O接口的数据端口。
3) 键盘I/O接口向CPU发出中断请求。
4) CPU 响应中断并执行键盘中断处理程序,将字符从I/O接口的数据端口送入内核缓冲区。
5) 进程P被唤醒,插入就绪队列,等待被调度。
6) 进程P再次获得CPU后,系统调用服务例程将字符从内核缓冲区复制到用户缓冲区。
7) 进程P从系统调用返回,之后scanf函数进行字符解析,最终将该字符存储到变量d中。
整个I/O 操作过程的工作流程如图5.16所示。

用户进程P发起scanf请求 -> 进程P触发系统调用进入内核态 -> 进程P执行驱动程序,初始化I/O参数 -> 进程P进入阻塞态 -> 用户从键盘输入数据到I/O接口 -> I/O接口向CPU发起中断 -> 中断过程调用驱动程序把数据送到内核缓冲区 -> 进程P被中断处理程序唤醒，进入就绪态 -> 进程P上处理机运行，进入运行态 -> 系统调用服务例程把数据从内核缓冲区复制到用户缓冲区 -> 进程P进行系统调用的返回 -> 进程P在用户态完成字符的解析

**图5.16 整个I/O操作过程的工作流程**
值得注意的是,在整个过程中,用户输入的字符首先从键盘I/O接口中的数据端口送到内核缓冲区,然后从内核缓冲区送到用户缓冲区,其中第一个过程是由设备驱动程序完成的,因为这需要和I/O 端口直接打交道,第二个过程是由普通的内核程序(属于设备无关软件层)完成的,因为这和具体的硬件没有关系。数据的流向如图5.17所示。

键盘 -> 用户进行输入 -> 键盘I/O接口 -> 设备驱动程序 -> 内核缓冲区 -> 系统调用内核代码 -> 用户缓冲区 -> scanf格式解析 -> 变量d的内存

**图5.17 数据的流向**

### 5.2.7 本节小结
本节开头提出的问题的参考答案如下。
1) 当处理机和外部设备的速度差距较大时,有什么办法可以解决问题?
可采用缓冲技术来缓解 CPU与外设速度上的矛盾,即在某个地方(一般为主存)设立一片缓冲区,外设与CPU的输入/输出都经过缓冲区,这样外设和CPU就都不用互相等待。
2) 什么是设备的独立性?引入设备的独立性有什么好处?
设备独立性是指用户在编程序时使用的设备与实际设备无关。一个程序应独立于分配给它的某类设备的具体设备,即在用户程序中只指明I/O使用的设备类型即可。
设备独立性有以下优点:①方便用户编程。②使程序运行不受具体机器环境的限制。③便于程序移植。

### 5.2.8 本节习题精选
**一、单项选择题**
**01. 设备的独立性是指( )。**
A. 设备独立于计算机系统
B. 系统对设备的管理是独立的
C. 用户编程时使用的设备与实际使用的设备无关
D. 每台设备都有一个唯一的编号

**02. 引入高速缓冲的主要目的是()。**
A. 提高CPU的利用率
B. 提高I/O设备的利用率
C. 改善CPU与I/O设备速度不匹配的问题 
D. 节省内存

**03. 为了使多个并发进程能有效地进行输入和输出,最好采用( )结构的缓冲技术。**
A. 缓冲池
B. 循环缓冲
C. 单缓冲
D. 双缓冲

**04. 缓冲技术中的缓冲池在()中。**
A. 主存
B. 外存
C. ROM
D. 寄存器

**05. 支持双向传送的设备应使用()。**
A. 单缓冲区
B. 双缓冲区
C. 多缓冲区
D. 缓冲池

**06. 下列关于缓冲区的描述中,正确的是( )。**
A. 缓冲区是一种专门的硬件缓冲器,不能用内存来实现
B. 缓冲区的作用是提高CPU和I/O设备之间的速度匹配
C. 缓冲区只能用于输入设备,不能用于输出设备
D. 缓冲区只能用于块设备,不能用于字符设备

**07. 使用单缓冲或双缓冲进行通信时,( )可以实现数据的双向并行传输。**
A. 只有单缓冲
B. 只有双缓冲
C. 都
D. 都不

**08. 下列各种算法中,( )是设备分配常用的一种算法。**
A. 首次适应
B. 时间片分配
C. 最佳适应
D. 先来先服务

**09. 设从磁盘将一块数据传送到缓冲区所用的时间为80µs,将缓冲区中的数据传送到用户区所用的时间为40µs,CPU处理一个数据块所用的时间为30µs。若有多块数据需要处理,并采用单缓冲区传送某磁盘数据,则处理一块数据所用的总时间为( )。**
Α. 120µs
Β. 110µs
C. 150µs
D. 70µs

**10. 某操作系统采用双缓冲区传送磁盘上的数据。设从磁盘将数据传送到缓冲区所用的时间为$T_1$,将缓冲区中的数据传送到用户区所用的时间为$T_2$,CPU处理一块数据所用的时间为$T_3$,假设一个磁盘块和一个缓冲区的大小相等,某系统在一段时间内连续处理一大批数据,则平均处理一个磁盘块数据的时间为( )。**
A. $T_1+T_2+T_3$
B. $max(T_2, T_3) + T_1$ 
C. $max(T_1, T_3) + T_2$
D. $max(T_1, T_2+T_3)$

**11. 若I/O所花费的时间比CPU的处理时间短得多,则缓冲区()。**
A. 最有效
B. 几乎无效
C. 均衡
D. 以上答案都不对

**12. 缓冲区管理者重要考虑的问题是( )。**
A. 选择缓冲区的大小
B. 决定缓冲区的数量
C. 实现进程访问缓冲区的同步
D. 限制进程的数量

**13. 考虑单用户计算机上的下列I/O操作,需要使用缓冲技术的是( )。**
Ⅰ. 图形用户界面下使用鼠标
II. 多任务操作系统下的磁盘驱动器(假设没有设备预分配)
III. 包含用户文件的磁盘驱动器
IV. 使用存储器映射I/O,直接和总线相连的图形卡
A. I. III
B. II. IV
C. II、III、IV
D. 全选

**14. 以下()不属于设备管理数据结构。**
A. PCB
B. DCT
C. COCT
D. CHCT

**15. 下列( )不是设备的分配方式。**
A. 独享分配
B. 共享分配
C. 虚拟分配
D. 分区分配

**16. 设备分配程序需要访问一系列的数据结构来给进程分配设备,这些数据结构有:设备控制表(DCT),控制器控制表(COCT),通道控制表(CHCT),系统设备表(SDT)。在设备分配的过程中,访问这些数据结构的正确顺序是( )。**
A. SDT, DCT, COCT, CHCT
B. DCT, COCT, CHCT, SDT
C. SDT, COCT, CHCT, DCT
D. COCT, CHCT, SDT, DCT

**17. 下面设备中属于共享设备的是( )。**
A. 打印机
B. 磁带机
C. 磁盘
D. 磁带机和磁盘

**18. 提高单机资源利用率的关键技术是()。**
A. SPOOLing 技术
B. 虚拟技术
C. 交换技术
D. 多道程序设计技术

**19. 虚拟设备是靠( )技术来实现的。**
A. 通道
B. 缓冲
C. SPOOLing
D. 控制器

**20. SPOOLing 技术的主要目的是( )。**
A. 提高CPU和设备交换信息的速度
B. 提高独占设备的利用率
C. 减轻用户编程负担
D. 提供主、辅存接口

**21. 在采用 SPOOLing技术的系统中,用户的打印结果首先被送到( )。**
A. 磁盘固定区域 
B. 内存固定区域 
C. 终端
D. 打印机

**22. 采用SPOOLing技术的计算机系统,外围计算机需要( )。**
A. 一台
B. 多台
C. 至少一台
D. 0台

**23. SPOOLing 系统由()组成。**
A. 预输入程序、井管理程序和缓输出程序
B. 预输入程序、井管理程序和井管理输出程序
C. 输入程序、井管理程序和输出程序
D. 预输入程序、井管理程序和输出程序

**24. 在SPOOLing系统中,用户进程实际分配到的是( )。**
A. 用户所要求的外设
B. 外存区,即虚拟设备
C. 设备的一部分存储区
D. 设备的一部分空间

**25. 下面关于SPOOLing系统的说法中,正确的是( )。**
A. 构成 SPOOLing系统的基本条件是有外围输入机与外围输出机
B. 构成 SPOOLing系统的基本条件仅是要有高速的大容量硬盘作为输入井和输出井
C. 当输入设备忙时,SPOOLing系统中的用户程序暂停执行,待I/O 空闲时再被唤醒执行输出操作
D. SPOOLing 系统中的用户程序可以随时将输出数据送到输出井中,待输出设备空闲时再由SPOOLing 系统完成数据的输出操作

**26. 下面关于SPOOLing的叙述中,不正确的是()。**
A. SPOOLing 系统中不需要独占设备
B. SPOOLing 系统加快了作业执行的速度
C. SPOOLing 系统使独占设备变成共享设备
D. SPOOLing 系统提高了独占设备的利用率

**27. ()是操作系统中采用的以空间换取时间的技术。**
A. SPOOLing 技术 
B. 虚拟存储技术 
C. 覆盖与交换技术 
D. 通道技术

**28. 采用假脱机技术,将磁盘的一部分作为公共缓冲区以代替打印机,用户对打印机的操作实际上是对磁盘的存储操作,用以代替打印机的部分由( )完成。**
A. 独占设备
B. 共享设备
C. 虚拟设备
D. 一般物理设备

**29. 下面关于独占设备和共享设备的说法中,不正确的是()。**
A. 打印机、扫描仪等属于独占设备
B. 对独占设备往往采用静态分配方式
C. 共享设备是指一个作业尚未撤离,另一个作业即可使用,但每个时刻只有一个作业使用
D. 对共享设备往往采用静态分配方式

**30. 当用户要求使用打印机打印某文件时,用户的要求是由操作系统的( )实现的。**
A. 文件系统
B. 设备管理程序
C. 文件系统和设备管理程序
D. 打印机启动程序和设备管理程序

**31. 下列设备管理工作中,适合由设备独立性软件来完成的有()。**
I. 向设备寄存器写命令
II. 检查用户是否有权使用设备
III. 将二进制整数转换成 ASCII码格式打印 IV. 缓冲区管理
A. I、II和III
B. II、III和IV
C. II 和IV
D. I、III和IV

**32. 下列关于设备驱动程序的说法中,正确的是()。**
I. 设备驱动程序负责处理与设备相关的中断处理过程
II. 驱动程序全部使用汇编语言编写,没有使用高级语言编写
III. 设备驱动程序负责处理磁盘调度
IV. 设备驱动程序与设备密切相关,可以在任意操作系统运行
A. II、III、IV
B. I. III
C. III、IV
D. I、II、III

**33. 下列选项中,( )不属于设备驱动程序的功能。**
A. 接收进程发来的I/O命令和参数,并检查其合法性
B. 查询I/O 设备的状态
C. 发出I/O命令,启动I/O设备
D. 对I/O设备传回的数据进行分析和缓冲

**34. 对设备驱动程序的处理过程进行排序,正确的处理顺序是()。**
①对服务请求进行校验
②传送必要的参数
③启动I/O设备
④将抽象要求转化为具体要求
⑤检查设备的状态
A. ①④⑥②⑧
B. ④①⑥②③
C. ①④②⑥③
D. ④①②⑤③

**35. `printf()`是C语言中进行格式化输出的库函数,该函数最终会转到内核态执行相应的系统调用服务例程,进程是通过执行()从用户态进入内核态的。**
A. 陷入指令
B. 关中断指令 
C. 无条件跳转指令 
D. 输出指令

**36. 【2009统考真题】程序员利用系统调用打开I/O设备时,通常使用的设备标识是( )。**
A. 逻辑设备名
B. 物理设备名
C. 主设备号
D. 从设备号

**37. 【2011统考真题】某文件占10个磁盘块,现要把该文件的磁盘块逐个读入主存缓冲区,并且送到用户区进行分析,假设一个缓冲区与一个磁盘块大小相同,把一个磁盘块读入缓冲区的时间为100µs,将缓冲区的数据传送到用户区的时间是50µs,CPU对一块数据进行分析的时间为50µs。在单缓冲区和双缓冲区结构下,读入并分析完该文件的时间分别是()。**
Α. 1500µs, 1000µs 
B. 1550µs, 1100µs 
C. 1550µs, 1550µs 
D. 2000µs, 2000µs

**38. 【2013统考真题】设系统缓冲区和用户工作区均采用单缓冲,从外设读入一块数据到系统缓冲区的时间为100,从系统缓冲区读入一块数据到用户工作区的时间为5,对用户工作区中的一块数据进行分析的时间为90(见下图)。进程从外设读入并分析2个数据块的最短时间是( )。**
90 用户工作区
↑ 5
系统缓冲区
↑ 100
外设
A. 200
Β. 295
C. 300
D. 390

**39. 【2013统考真题】用户程序发出磁盘I/O请求后,系统的处理流程是:用户程序→系统调用处理程序→设备驱动程序→中断处理程序。其中,计算数据所在磁盘的柱面号、磁头号、扇区号的程序是()。**
A. 用户程序
B. 系统调用处理程序
C. 设备驱动程序
D. 中断处理程序

**40. 【2015统考真题】在系统内存中设置磁盘缓冲区的主要目的是()。**
A. 减少磁盘I/O次数
B. 减少平均寻道时间
C. 提高磁盘数据可靠性
D. 实现设备无关性

**41. 【2016统考真题】下列关于SPOOLing技术的叙述中,错误的是( )。**
A. 需要外存的支持
B. 需要多道程序设计技术的支持
C. 可以让多个作业共享一台独占式设备
D. 由用户作业控制设备与输入/输出井之间的数据传送

**42. 【2020统考真题】对于具备设备独立性的系统,下列叙述中,错误的是()。**
A. 可以使用文件名访问物理设备
B. 用户程序使用逻辑设备名访问物理设备
C. 需要建立逻辑设备与物理设备之间的映射关系
D. 更换物理设备后必须修改访问该设备的应用程序

**43. 【2022统考真题】下列关于驱动程序的叙述中,不正确的是()。**
A. 驱动程序与I/O控制方式无关
B. 初始化设备是由驱动程序控制完成的
C. 进程在执行驱动程序时可能进入阻塞态
D. 读/写设备的操作是由驱动程序控制完成的

**44. 【2023统考真题】下列因素中,设备分配需要考虑的是()。**
I. 设备的类型
II. 设备的访问权限
III. 设备的占用状态
IV. 逻辑设备与物理设备的映射关系
A. 仅I、II
B. 仅II、III
C. 仅III、IV
D. I、II、III、IV

**45. 【2024统考真题】当键盘中断服务例程执行结束时,所输入数据的存放位置是( )。**
A. 用户缓冲区
B. CPU中的通用寄存器
C. 内核缓冲区
D. 键盘控制器的数据寄存器

### 二、综合应用题
**01. 输入/输出软件一般分为4个层次:用户层、与设备无关的软件层、设备驱动程序和中断处理程序。请说明以下各工作是在哪一层完成的:**
1) 为磁盘读操作计算磁道、扇区和磁头。
2) 向设备寄存器写命令。
3) 检查用户是否有权使用设备。
4) 将二进制整数转换成ASCII码以便打印。

**02. 在某系统中,若采用双缓冲区(每个缓冲区可存放一块数据),将一块数据从磁盘传送到缓冲区的时间为80µs,从缓冲区传送到用户的时间为20µs,CPU计算一块数据的时间为50µs。总共处理4个数据块,每个数据块的平均处理时间是多少?**

**03. 【2023统考真题】进程P通过执行系统调用从键盘接收一个字符的输入,已知此过程中与进程P相关的操作包括:①将进程P插入就绪队列;②将进程P插入阻塞队列;③将字符从键盘控制器读入系统缓冲区;④启动键盘中断处理程序;⑤进程P从系统调用返回;⑥用户在键盘上输入字符。以上编号①~⑥仅用于标记操作,与操作的先后顺序无关。请回答下列问题。**
1) 按照正确的操作顺序,操作①的前一个和后一个操作分别是上述操作中的哪一个?操作⑥的后一个操作是上述操作中的哪一个?
2) 在上述哪个操作之后CPU一定从进程P切换到其他进程?在上述哪个操作之后 CPU调度程序才能选中进程P执行?
3) 完成上述哪个操作的代码属于键盘驱动程序?
4) 键盘中断处理程序执行时,进程P处于什么状态?CPU是处于内核态还是处于用户态?

### 5.2.9 答案与解析
**一、单项选择题**
**01. C**
设备的独立性主要是指用户使用设备的透明性,即使用户程序和实际使用的物理设备无关。
**02. C**
CPU与I/O 设备执行速度通常是不对等的,前者快、后者慢,通过高速缓冲技术来改善两者不匹配的问题。
**03. A**
缓冲池是系统的共用资源,可供多个进程共享,并且既能用于输入又能用于输出。其一般包含三种类型的缓冲:①空闲缓冲区;②装满输入数据的缓冲区;③装满输出数据的缓冲区。为了管理上的方便,可将相同类型的缓冲区链成一个队列。选项B、C、D属专用缓冲。
**04. A**
输入井和输出井是在磁盘上开辟的存储空间,而输入/输出缓冲区是在内存中开辟的,因为CPU 速度比 I/O 设备高很多,缓冲池通常在主存中建立。
**05. B**
支持双向发送和接收数据的设备(如网卡等)应使用双缓冲区,双缓冲区可以实现同一时刻的双向数据传输,提高设备的效率和利用率。单缓冲区只能实现单向数据传输。多缓冲区和缓冲池用于提高I/O性能的技术,但不是必需的,也不一定适合所有的双向设备。
**06. B**
缓冲区是一个存储区域,可由专门的硬件寄存器组成,也可利用内存来实现。缓冲区的作用是提高CPU和I/O 设备之间的速度匹配,因为CPU的速度远高于I/O 设备的速度,若没有缓冲区,则CPU就要等待I/O 设备完成操作,造成资源浪费。缓冲区可用于输入设备和输出设备,如键盘、打印机等。缓冲区也可用于块设备和字符设备,如磁盘、串口等。
**07. B**
两个进程之间若只设置单缓冲区,则同一时刻只能实现单向传输,但可在一段时间内用于发送数据,另一段时间内用于接收数据。若设置双缓冲区,则可以实现双向同时的并行传输。
**08. D**
选项A和C都是动态分区分配的常用算法,选项B是进程调度的常用算法,设备分配的常用算法主要有先来先服务算法和最高优先级优先算法。
**09. A**
采用单缓冲区传送数据时,设备与处理机对缓冲区的操作是串行的,当进行第$i$次读磁盘数据送至缓冲区时,系统再同时读出用户区中第$i-1$次数据进行计算,此两项操作可以并行,并与数据从缓冲区传送到用户区的操作串行进行,所以系统处理一块数据所用的总时间为$max(80µs, 30µs) + 40µs = 120µs$。
**10. D**
计算处理一个磁盘块数据的平均时间,可以假定系统的初始状态:用户区为空,缓冲区1为满,缓冲区2为空,如下图所示。①$0 \sim T_2$时段,将缓冲区1中的数据送入用户区;②$T_2 \sim T_2+T_3$时段,处理用户区中的数据,③$0 \sim T_1$时段,将数据从磁盘送入缓冲区2,只有②和③都完成后,系统才重新回到初始状态,因此平均处理一个磁盘块数据的时间为$max(T_1, T_2+T_3)$。
T_1: 磁盘 -> 缓冲区2
T_2: 缓冲区1 -> 用户区; T_3: (处理)用户区
**11. B**
缓冲区主要解决输入/输出速度比CPU处理的速度慢而造成数据积压的矛盾。所以当 I/O 花费的时间比 CPU处理时间短很多时,缓冲区没有必要设置。
**12. C**
在缓冲机制中,无论是单缓冲、多缓冲还是缓冲池,因为缓冲区是一种临界资源,所以在 使用缓冲区时都有一个申请和释放(互斥)的问题需要考虑。
**13. D**
在鼠标移动时,若有高优先级的操作产生,为了记录鼠标活动的情况,必须使用缓冲技术,选项I正确。由于磁盘驱动器和目标或源 I/O 设备间的吞吐量不同,必须采用缓冲技术,选项 II正确。为了能使数据从用户作业空间传送到磁盘或从磁盘传送到用户作业空间,必须采用缓冲技术,选项 III 正确。为了便于多幅图形的存取及提高性能,缓冲技术是可以采用的,特别是在显示当前一幅图形又要得到下一幅图形时,应采用双缓冲技术,选项IV 正确。
**14. A**
DCT 是设备控制表;COCT是控制器控制表;CHCT 是通道控制表;PCB是进程控制块,不属于设备管理的数据结构。
**15. D**
设备的分配方式主要有独享分享、共享分配和虚拟分配,选项D是内存的分配方式。
**16. A**
在设备分配的过程中,访问数据结构的顺序通常是按设备管理的逻辑层次来安排的。设备分配过程通常从系统设备表(SDT)开始,然后依次获取设备控制表(DCT)、控制器控制表(COCT)和通道控制表(CHCT)中的信息,最终完成对设备的分配。
**17. C**
共享设备是指在一个时间间隔内可被多个进程同时访问的设备,只有磁盘满足。打印机在一个时间间隔内被多个进程访问时打印出来的文档就会乱;磁带机旋转到所需的读/写位置需要较长时间,若一个时间间隔内被多个进程访问,磁带机就只能一直旋转,没时间读/写。
**18. D**
在单机系统中,最关键的资源是处理器资源,最大化地提高处理器利用率,就是最大化地提高系统效率。多道程序设计技术是提高处理器利用率的关键技术,其他均为设备和内存的相关技术。
**19. C**
SPOOLing 技术是操作系统中采用的一种将独占设备改造为共享设备的技术。通过这种技术处理后的设备通常称为虚拟设备。
**20. B**
SPOOLing 技术将一台物理设备虚拟为多台逻辑设备,以减少设备的闲置时间,提高设备的并发度和吞吐量,因此 SPOOLing 技术的主要目的是提高独占设备的利用率。
**21. A**
输入井和输出井是在磁盘上开辟的两大存储空间。输入井模拟脱机输入时的磁盘设备,用于暂存 I/O 设备输入的数据;输出井模拟脱机输出时的磁盘,用于暂存用户程序的输出数据。为了缓和CPU,打印结果首先送到位于磁盘固定区域的输出井。
**22. D**
SPOOLing 技术需要使用磁盘空间(输入井和输出井)和内存空间(输入/输出缓冲区),不需要外围计算机的支持。
**23. A**
SPOOLing 系统主要包含三部分,即输入井和输出井、输入缓冲区和输出缓冲区以及输入进程和输出进程。这三部分由预输入程序、井管理程序和缓输出程序管理,以保证系统正常运行。
**24. B**
通过 SPOOLing 技术可将一台物理 I/O 设备虚拟为I/O 设备,同样允许多个用户共享一台物理 I/O 设备,所以SPOOLing 并不是将物理设备真的分配给用户进程。
**25. D**
构成 SPOOLing 系统的基本条件是不仅要有大容量、高速度的外存作为输入井和输出井,还要有 SPOOLing 软件,因此选项A错误、选项B不够全面,同时利用 SPOOLing 技术提高了系统和I/O 设备的利用率,进程不必等待I/O操作的完成,因此选项C也不正确。
**26. A**
SPOOLing 技术将独占设备虚拟成共享设备,因此必须先有独占设备才行。下面说明SPOOLing 技术如何加快作业执行的速度,提高独占设备的利用率。进程不需要等待打印机空闲,只需将输出数据送到输出井,然后继续执行其他操作,这样就提高了进程的效率。打印机不需要空闲等待进程的输出,只需从输出井中读取数据进行打印,然后读取下一个数据,这样就提高了打印机的效率。输出进程可以根据输出井中的数据量和优先级来安排打印顺序,从而平衡各个进程的等待时间和响应时间,这样就提高了系统的性能。输出进程可以减少对打印机的切换次数,从而减少系统的开销,进而提高打印机的利用率。
**27. A**
SPOOLing 技术需有高速大容量且可随机存取的外存支持,通过预输入和缓输出来减少 CPU等待慢速设备的时间,将独享设备改造成共享设备。
**28. C**
利用假脱机技术可将独享设备改造为可供多个用户共享的虚拟设备,各作业在执行期间只使用虚拟设备。采用假脱机技术,将磁盘的一部分作为公共缓冲区以代替打印机,用户对打印机的操作实际上是对磁盘的存储操作,用以代替打印机的部分由虚拟设备完成。
**29. D**
独占设备采用静态分配方式,而共享设备采用动态分配方式。
**30. C**
当用户使用打印机打印某文件时,需要通过文件系统来访问磁盘上的数据,然后通过设备管理程序来控制打印机的输出。打印机启动程序只能初始化打印机,不能直接访问磁盘上的数据。
**31. C**
设备寄存器写命令是由设备驱动程序完成的。检查用户是否有权使用设备属于设备保护,是由设备独立性软件完成的。将二进制整数转换成ASCII码的格式打印是通过I/O库函数完成的,属于用户层软件。缓冲区管理属于输入/输出的共有操作,是由设备独立性软件完成的。缓冲区是内存中的区域,显然不是由设备驱动程序完成的。
**32. B**
不同厂家的设备通常提供不同的驱动程序,对应不同的中断处理,因此需要驱动程序来完成,选项I正确。驱动程序仅有一部分需要用汇编语言编写,其余部分可用高级语言编写,选项II 错误。不同型号的磁盘的调度方式不一定相同,磁盘调度由磁盘驱动程序完成,选项III 正确。不同的操作系统有不同的驱动程序接口,因此驱动程序要根据操作系统的要求进行定制,选项IV 错误。
**33. D**
设备驱动程序的功能是管理I/O设备。数据的分析和缓冲是由进程或操作系统完成的。
**34. B**
用户层软件发出的命令是设备独立性软件提供的统一接口,需要由驱动程序将这些抽象要求转化为具体要求,才能被设备控制器识别,例如将抽象要求中的盘块号转化为磁盘的柱面号、盘面号和扇区号。然后,驱动程序会检查服务请求是不是该设备可以执行的,之后检查设备的状态,只有设备处于就绪状态时才能启动,最后传送此次I/O的参数并启动设备。
**35. A**
`printf()`是进行输出的函数,最终需要在内核态执行 I/O 指令来完成输出的功能。系统调用函数中包含一条陷入指令,也称trap 指令,会让CPU陷入内核态执行相应的服务程序。
传递系统调用参数 -> 执行trap指令陷入内核态 -> 执行相应的服务程序 -> 返回用户态
**36. A**
用户程序对 I/O 设备的请求采用逻辑设备名,而程序实际执行时使用物理设备名,它们之间的转换是由设备无关软件层完成的。主设备和从设备是总线仲裁中的概念。
**37. B**
在单缓冲区中,当上一个磁盘块从缓冲区读入用户区完成时,下一磁盘块才能开始读入,也就是当最后一个磁盘块读入用户区完毕时所用的时间为$150×10=1500µs$,加上处理最后一个磁盘块的时间50µs,得1550µs。双缓冲区中,不存在等待磁盘块从缓冲区读入用户区的问题,10个磁盘块可以连续从外存读入主存缓冲区,加上将最后一个磁盘块从缓冲区送到用户区的传输时间50µs 及处理时间 50µs,也就是$100×10+50+50 = 1100µs$。
**38. C**
数据块1从外设到用户工作区的总时间为105,在这段时间中,数据块2未进行操作。在数据块1进行分析处理时,数据块2从外设到用户工作区的总时间为105,这段时间是并行的。再加上数据块2进行处理的时间90,总共是300。
**39. C**
计算柱面号、磁头号和扇区号的工作是由设备驱动程序完成的。题中的功能因设备硬件的不同而不同,因此应由厂家提供的设备驱动程序实现。
**40. A**
磁盘和内存的速度差异,决定了可以将内存经常访问的文件调入磁盘缓冲区,从高速缓存中复制的访问比磁盘I/O的机械操作要快很多。
**41. D**
SPOOLing 利用专门的外围控制机,将低速 I/O 设备上的数据传送到高速磁盘上,或者相反。SPOOLing 的意思是外部设备同时联机操作,也称假脱机输入/输出操作,是操作系统中采用的一项将独占设备改造成共享设备的技术。高速磁盘即外存,选项A正确。SPOOLing 技术建立在多道程序设计技术的基础上,在一个时间段内,输入进程、输出进程是可以和运行的作业进程并发执行的,选项B正确。SPOOLing 技术实现了将独占设备改造成共享设备的技术,选项C正确。设备与输入井/输出井之间数据的传送是由系统实现的,选项D错误。
**42. D**
设备可视为特殊文件,选项A正确。用户使用逻辑设备名来访问物理文件,有利于设备独立性,选项B正确。通过逻辑设备名访问物理设备时,需要建立逻辑设备和物理设备之间的映射关系,选项C正确。应用程序按逻辑设备名访问设备,再经驱动程序的处理来控制物理设备,若更换物理设备,则只需更换驱动程序,而无须修改应用程序,选项D错误。
**43. A**
厂家在设计一个设备时,通常会为该设备编写驱动程序,主机需要先安装驱动程序,才能使用设备。当一个设备被连接到主机时,驱动程序负责初始化设备(如将设备控制器中的寄存器初始化),选项B正确。若采用程序直接控制方式,进程不会被阻塞,进程会处于等待状态;若采用中断控制方式,则驱动程序启动 I/O 操作后,将调出其他进程执行,而当前用户进程被阻塞;若采用DMA控制方式,则驱动程序对DMA 控制器初始化后,便发送“启动DMA传送”命令,外设开始传送数据,同时CPU 执行处理器调度程序,当前用户进程被阻塞,选项C正确。设备的读/写操作本质就是在设备控制器和主机之间传送数据,而只有厂家知道设备控制器的内部实现,因此也只有厂家提供的驱动程序能控制设备的读/写操作,选项 D 正确。厂家会根据设备特性,在驱动程序中实现一种合适的I/O控制方式,不同的I/O 控制方式需要不同的驱动程序来实现数据的传输和控制,例如,中断驱动方式需要驱动程序能够响应中断信号,DMA方式需要驱动程序能够设置 DMA 控制器的寄存器,通道控制方式需要驱动程序能够执行通道指令等,选项A错误。
**44. D**
设备的类型决定了设备的固有属性,如独占性、共享性、可虚拟性等,不同类型的设备需要采用不同的分配方式,如独占分配、共享分配、虚拟分配等。设备的访问权限决定了哪些进程可以使用哪些设备,以保证系统的安全性和保密性,通常系统设备只能由系统进程或特权进程访问,用户设备只能由用户进程或授权进程访问。设备的占用状态决定了设备是否可以被分配给请求进程,以及如何处理等待进程,若设备空闲,则通常可以直接分配给请求进程;若设备忙,则需要将请求进程排入设备队列,并按照一定的算法进行调度。逻辑设备与物理设备的映射关系决定了如何通过逻辑地址访问物理地址,以提高系统的灵活性和可扩展性,通常系统会为每个物理设备分配一个逻辑名,并建立一个系统设备表来记录逻辑名与物理名之间的对应关系。
**45. C**
以C语言中 scanf()函数的执行过程为例,用户请求通过键盘输入数据,当程序执行到 scanf()时,触发系统调用,CPU切换到内核态,执行系统调用服务例程,系统调用的内核程序进行一些初始化操作,启动外设,同时阻塞该用户进程,直到键盘输入数据,键盘中断服务例程(实际上是驱动程序)将数据从I/O接口(键盘控制器)中的数据寄存器取出并送至内核缓冲区,然后唤醒用户进程,用户进程被唤醒后进入就绪队列,等得到CPU运行后,再将内核缓冲区中的数据送至用户缓冲区,最后进行系统调用的返回。综上所述,键盘中断服务例程执行结束后,所输入数据的存放位置是内核缓冲区,当系统调用结束时,所输入数据才被送至用户缓冲区。

### 二、综合应用题
**01. 【解答】**
分析:首先,我们来看这些功能是不是应该由操作系统来完成。操作系统是一个代码相对稳定的软件,它很少发生代码的变化。若1)由操作系统完成,则操作系统就必须记录逻辑块和磁盘细节的映射,操作系统的代码会急剧膨胀,而且对新型介质的支持也会引起代码的变动。若2)也由操作系统完成,则操作系统需要记录不同生产厂商的不同数据,而且后续新厂商和新产品也无法得到支持。
因为1)和2)都与具体的磁盘类型有关,因此为了能够让操作系统尽可能多地支持各种不同型号的设备,1)和2)应由厂商所编写的设备驱动程序完成。3)涉及安全与权限问题,应由与设备无关的操作系统完成。4)应由用户层来完成,因为只有用户知道将二进制整数转换为ASCII码的格式(使用二进制还是十进制、有没有特别的分隔符等)。
**02. 【解答】**
4个数据块的处理过程如下图所示,总耗时390µs,每块的平均处理时间为$390µs/4 = 97.5µs$。
进程A:
缓冲区-用户区: T1, T2, T3, T4 (时间: 20, 50, 20, 50)
磁盘-缓冲区: M1, M2, M3, M4 (时间: 80, 80, 80, 80)
CPU: C1, C2, C3, C4 (时间: 50, 50, 50, 50)
时间轴(µs):
0 --80 (M1)
80 --100 (T1)
100--150 (C1)
150--160 (M2)
160--180 (T2)
180--230 (C2)
230--240 (M3)
240--260 (T3)
260--310 (C3)
310--320 (M4)
320--340 (T4)
340--390 (C4)
从上图可以看出,处理n个数据块的总耗时为$(80n+20+50)µs = (80n+70)µs$,每个数据块的平均处理时间为$(80n+70)/n µs$,当n较大时,平均时间近似为$max(C, T) = 80µs$。
**03. 【解析】**
1) 正确的执行顺序为②⑥④③①⑤,因此操作①的前一个操作是③,后一个操作是⑤;操作⑥的后一个操作是④。下面以 scanf()函数的执行过程为例分析相关操作顺序。首先用户进程 P调用I/O 标准库的 scanf()函数,scanf()调用系统调用封装函数 read(),在这个函数中有一条陷阱指令,通过它陷入内核,内核调出 read()对应的 sys_read()系统调用服务例程进行执行,进入内核后,在设备无关层进行若干调用,最终到设备驱动层进行处理,设备驱动程序对本次I/O进行初始化操作,然后执行操作系统的进程调度程序 scheduler(),由调度程序 scheduler()来阻塞用户进程P,并调度其他进程执行。此后,在用户输入字符之前,CPU运行其他进程,键盘等待用户输入数据,当用户在键盘上输入字符后,外设向CPU发出相应的中断请求,CPU响应中断后,启动键盘中断处理程序,该处理程序对I/O 中断进行简单通用的处理后,唤醒具体的处理键盘输入的驱动程序,该驱动程序将字符从键盘控制器读入系统缓冲区,数据传输完成后,唤醒P并将其插入就绪队列,进行中断返回,进程P也从系统调用返回。相关过程如下图所示。
外设: 外设工作 -> 完成 -> P进入就绪队列
CPU: (进程Q运行) -> 中断处理 -> 响应 -> 返回 -> (进程P运行)
(P被阻塞)
2) 在操作②之后CPU一定从进程P切换到其他进程。在操作①之后CPU 调度程序才能选中进程P执行。
3) 设备驱动程序负责驱动I/O 设备工作,I/O 操作初始化,执行具体的I/O指令。将字符从键盘控制器读入系统缓冲区是和键盘直接相关的具体操作,完成操作③的代码属于键盘驱动程序。
4) 键盘中断处理程序执行时,进程P还在阻塞队列,处于阻塞态。中断处理程序、设备驱动程序、设备独立性软件都属于内核 I/O软件层,执行相关代码时,CPU处于内核态。

## 5.3 磁盘和固态硬盘[^1]
在学习本节时,请读者思考以下问题:
1) 在磁盘上进行一次读/写操作需要哪几部分时间?其中哪部分时间最长?
2) 存储一个文件时,当一个磁道存储不下时,剩下的部分是存在同一个盘面的不同磁道好,还是存在同一个柱面上的不同盘面好?
本节主要介绍磁盘管理的方式。学习本节时,要重点掌握计算一次磁盘操作的时间,以及对于给定访盘的磁道序列,按照特定算法求出磁头通过的总磁道数及平均寻道数。

### 5.3.1 磁盘
**命题追踪** 磁盘容量的计算 (2019)
磁盘(Disk)是表面涂有磁性物质的物理盘片,通过一个称为磁头的导体线圈从磁盘存取数据。在读/写操作期间,磁头固定,磁盘在下面高速旋转。如图5.18所示,磁盘盘面上的数据存储在一组同心圆中,称为磁道。每个磁道与磁头一样宽,一个盘面有上千个磁道。磁道又划分为几百个扇区,每个扇区固定存储大小(如1KB),一个扇区称为一个盘块。相邻磁道及相邻扇区间通过一定的间隙分隔开,以避免精度错误。注意,因为扇区按固定圆心角度划分,所以密度从最外道向里道增加,磁盘的存储能力受限于最内道的最大记录密度。

**注意**
为了提高磁盘的存储容量,充分利用磁盘外层磁道的存储能力,现代磁盘不再将内外磁道划分为相同数目的扇区,而将盘面划分为若干环带,同一环带内的所有磁道具有相同的扇区数,显然,外层环带的磁道拥有较内层环带的磁道更多的扇区。

**命题追踪** 将簇号转化为磁盘物理地址的过程 (2019)
磁盘安装在一个磁盘驱动器中,它由磁头臂、用于旋转磁盘的转轴和用于数据输入/输出的电子设备组成。如图5.19所示,多个盘片垂直堆叠,组成磁盘组,每个盘面对应一个磁头,所有磁头固定在一起,与磁盘中心的距离相同且只能“共进退”。所有盘片上相对位置相同的磁道组成柱面。扇区是磁盘可寻址的最小单位,磁盘上能存储的物理块数目由扇区数、磁道数及磁盘面数决定,磁盘地址用“柱面号·盘面号·扇区号”表示。

[^1]: 本节内容与《计算机组成原理考研复习指导》，书中的3.4节联系密切，建议结合复习。