③若在处理C的最后一个编码位时创建了新结点,则继续验证下一个编码。
若所有编码均通过验证,则编码具有前缀特性。

### 归纳总结

本章的内容较多,其中二叉树是极其重要的考查点。关于二叉树的有关操作,在 2014 年的统考中首次出现了线性表以外的算法设计题,需要引起读者的注意。
遍历是二叉树的各种操作的基础,统考时会考查遍历过程中对结点的各种其他操作,而且容易结合递归算法和利用栈或队列的非递归算法。读者需重点掌握各种遍历方法的代码书写,并学会在遍历的基础上,进行一些其他的相关操作。其中递归算法短小精悍,出现的概率较大,请读者不要掉以轻心,要做到对几种遍历方式的程序模板烂熟于心,并结合一定数量的习题,才可以在考试中快速地写出漂亮的代码。
二叉树遍历算法的递归程序:
```c
void Track(BiTree *p){
    if(p!=NULL){
        //(1)
        Track(p->lchild);
        //(2)
        Track(p->rchild);
        //(3)
    }
}
```
访问函数 visit()位于(1)、(2)、(3)的位置,分别对应于先序、中序、后序遍历。但对于具体题目来说,设计算法时要灵活应用。请读者认真练习下面的例题。
**例题：**设二叉树的存储结构为二叉链表,编写有关二叉树的递归算法。
1) 统计二叉树中度为1的结点个数。
2) 统计二叉树中度为2的结点个数。
3) 统计二叉树中度为0的结点个数。
4) 统计二叉树的高度。
5) 统计二叉树的宽度。
6) 从二叉树中删去所有叶结点。
7) 计算指定结点*p所在的层次。
8) 计算二叉树中各结点中的最大元素的值。
9) 交换二叉树中每个结点的两个子女。
10) 以先序次序输出一颗二叉树中所有结点的数据值及结点所在的层次。

### 思维拓展

输入一个整数`data`和一棵二元树。从树的根结点开始往下访问一直到叶结点,所经过的所有结点形成一条路径。打印出路径及与`data`相等的所有路径。例如,输入整数 22 和下图所示的二元树,则打印出两条路径 10, 12 和 10, 5, 7。