先从前往后,再从后往前。算法的代码如下:
```c
int Partition(ElemType K[], int n) {
//交换序列K[1..n]中的记录,使枢轴到位,并返回其所在位置
    int i=1,j=n;             //设置两个交替变量初值分别为1和n
    ElemType pivot=K[j];     //枢轴
    while(i<j){              //循环跳出条件
        while(i<j&&K[i]<=pivot)
            i++;             //从前往后找比枢轴大的元素
        if(i<j)
            K[j]=K[i];       //移动到右端
        while(i<j&&K[j]>=pivot)
            j--;             //从后往前找比枢轴小的元素
        if(i<j)
            K[i]=K[j];       //移动到左端
    } //while
    K[i]=pivot;              //枢轴存放在最终位置
    return i;                //返回存放枢轴的位置
}
```

## 8.7 外部排序

外部排序可能会考查相关概念、方法和排序过程,外部排序的算法比较复杂,不会在算法设计上进行考查。本节的主要内容有:
① 外部排序指的是大文件的排序,即待排序的记录存储在外存中,待排序的文件无法一次性装入内存,需要在内存和外存之间进行多次数据交换,以达到排序整个文件的目的。
② 为减少平衡归并中外存读/写次数所采取的方法:增大归并路数和减少归并段个数。
③ 利用败者树增大归并路数。
④ 利用置换-选择排序增大归并段长度来减少归并段个数。
⑤ 由长度不等的归并段进行多路平衡归并,需要构造最佳归并树。

### 8.7.1 外部排序的基本概念

前面介绍过的排序算法都是在内存中进行的(称为内部排序)。而在许多应用中,经常需要对大文件进行排序,因为文件中的记录很多,无法将整个文件复制进内存中进行排序。因此,需要将待排序的记录存储在外存上,排序时再把数据一部分一部分地调入内存进行排序,在排序过程中需要多次进行内存和外存之间的交换。这种排序算法就称为外部排序。

### 8.7.2 外部排序的方法

文件通常是按块存储在磁盘上的,操作系统也是按块对磁盘上的信息进行读/写的。因为磁盘读/写的机械动作所需的时间远远超过在内存中进行运算的时间(相比而言可以忽略不计),因此在外部排序过程中的时间代价主要考虑访问磁盘的次数,即I/O次数。

**命题追踪**
> 对大文件排序时使用的排序算法(2016)

外部排序通常采用归并排序算法。它包括两个阶段:①根据内存缓冲区大小,将外存上的文件分成若干长度为$l$的子文件,依次读入内存并利用内部排序算法对它们进行排序,并将排序后得到的有序子文件重新写回外存,称这些有序子文件为归并段或顺串;②对这些归并段进行逐趟归并,使归并段(有序子文件)逐渐由小到大,直至得到整个有序文件为止。

例如,一个含有2000个记录的文件,每个磁盘块可容纳125个记录,首先通过8次内部排序得到8个初始归并段R1~R8,每段都含250条记录。然后对该文件做如图8.15所示的两两归并,直至得到一个有序文件。可以把内存工作区等分为三个缓冲区,如图8.14所示,其中的两个为输入缓冲区,一个为输出缓冲区。首先,从两个输入归并段R1和R2中分别读入一个块,放在输入缓冲区1和输入缓冲区2中。然后,在内存中进行二路归并,归并后的对象顺序存放在输出缓冲区中。若输出缓冲区中对象存满,则将其顺序写到输出归并段(R1')中,再清空输出缓冲区,继续存放归并后的对象。若某个输入缓冲区中的对象取空,则从对应的输入归并段中再读取下一块,继续参加归并。如此继续,直到两个输入归并段中的对象全部读入内存并都归并完成为止。当R1和R2归并完后,再归并R3和R4、R5和R6、最后归并R7和R8,这是一趟归并。再把上趟的结果R1'和R2'、R3'和R4'两两归并,这又是一趟归并。最后把R1''和R2''两个归并段归并,得到最终的有序文件,一共进行了3趟归 př。

*图8.14 二路归并*
```
+--------------+      +--------------+
| 输入缓冲区1  | ---> |              |
+--------------+      | 输出缓冲区   |
| 输入缓冲区2  | ---> |              |
+--------------+      +--------------+
```

*图8.15 二路归并的排序过程*
```
R1  R2  R3  R4  R5  R6  R7  R8
 \ /   \ /   \ /   \ /
 R1'   R2'   R3'   R4'
  \   /     \   /
   R1''      R2''
     \      /
      有序文件
```

在外部排序中实现两两归并时,不可能将两个有序段及归并结果段同时存放在内存中,因此需要不停地将数据读出、写入磁盘,而这会耗费大量的时间。一般情况下:
外部排序的总时间 = 内部排序的时间 + 外存信息读/写的时间 + 内部归并的时间

显然,外存信息读/写的时间远大于内部排序和内部归并的时间,因此应着力减少I/O次数。外存信息的读/写是以“磁盘块”为单位的,因此可知每趟归并需进行16次读和16次写,3趟归并加上内部排序时所需进行的读/写,使得总共需进行$32 \times 3 + 32 = 128$次读/写。

若改用4路归并排序,则只需2趟归并,外部排序时的总读/写次数便减至$32 \times 2 + 32 = 96$。因此,增大归并路数,可减少归并趟数,进而减少总的磁盘I/O次数,如图8.16所示。

*图8.16 4路归并的排序过程*
```
R1  R2  R3  R4      R5  R6  R7  R8
 \  |   |  /        \  |   |  /
    R1'               R2'
      \              /
         有序文件
```
一般地,对$r$个初始归并段,做$k$路归并(每趟将$k$个或$k$个以下的有序子文件归并成一个有序子文件)。第一趟可将$r$个初始归并段归并为$\lceil r/k \rceil$个归并段,以后每趟归并将$m$个归并段归并成$\lceil m/k \rceil$个归并段,直至最后形成一个大的归并段为止。树的高度-1 = $\lceil \log_k r \rceil$ = 归并趟数$S$。可见,只要增大归并路数$k$,或减少初始归并段个数$r$,都能减少归并趟数$S$,进而减少读/写磁盘的次数,达到提高外部排序速度的目的。

### 8.7.3 多路平衡归并与败者树

增加归并路数$k$能减少归并趟数$S$,进而减少I/O次数。然而,增加归并路数$k$时,内部归并的时间将增加。做内部归并时,在$k$个元素中选择关键字最小的元素需要$k-1$次比较。每趟归并$n$个元素需要做$(n-1)(k-1)$次比较,$S$趟归并总共需要的比较次数为①
$$
S(n-1)(k-1) = \lceil \log_k r \rceil(n-1)(k-1) = \lceil \log_2 r \rceil(n-1)\frac{k-1}{\lceil \log_2 k \rceil}
$$
式中,$(k-1)/\lceil \log_2 k \rceil$随$k$增长而增长,因此内部归并时间亦随$k$的增长而增长。这将抵消因增大$k$而减少外存访问次数所得到的效益。因此,不能使用普通的内部归并排序算法。

为了使内部归并不受$k$的增大的影响,引入了败者树。败者树是树形选择排序的一种变体,可视为一棵完全二叉树。$k$个叶结点分别存放$k$个归并段在归并过程中当前参加比较的元素,内部结点用来记忆左右子树中的“失败者”,而让胜利者往上继续进行比较,一直到根结点。若比较两个数,大的为失败者、小的为胜利者,则根结点指向的数为最小数。

**命题追踪**
> 败者树的实现原理(2024)

如图8.17(a)所示,b3与b4比较,b4是败者,将段号4写入父结点ls[4]。b1与b2比较,b2是败者,将段号2写入ls[3]。b3与b4的胜者b3与b0比较,b0是败者,将段号0写入ls[2]。最后两个胜者b3与b1比较,b1是败者,将段号1写入ls[1]。而将胜者b3的段号3写入ls[0]。此时,根结点ls[0]所指的段的关键字最小。对于$k$路归并,初始构造败者树需要$k-1$次比较。b3中的6输出后,将下一关键字填入b3,继续比较。

*图8.17 实现5路归并的败者树*

(a) 初始状态
```
        ls[0] (3 冠军)
            |
        ls[1] (1)
        /       \
    ls[2](0)   ls[3](2)
    /      \
ls[4](4)   ...
/
b3(6) b4(12)  b0(10) b1(9) b2(20)
...   ...     ...    ...   ...
```
(b) b3的6输出后,从b3输入15,重新调整
```
        ls[0] (1)
            |
        ls[1] (4)
        /       \
    ls[2](0)   ls[3](2)
    /      \
ls[4](3)   ...
/
b3(15) b4(12)  b0(10) b1(9) b2(20)
...    ...     ...    ...   ...
```
(图中只示意了部分节点和值)

因为$k$路归并的败者树深度为$\lceil \log_2 k \rceil + 1$,所以从$k$个记录中选择最小关键字,仅需进行$\lceil \log_2 k \rceil$次比较。因此总的比较次数约为
$$
S(n-1)\lceil \log_2 k \rceil = \lceil \log_k r \rceil(n-1)\lceil \log_2 k \rceil = (n-1)\lceil \log_2 r \rceil
$$
可见,使用败者树后,内部归并的比较次数与$k$无关了。因此,只要内存空间允许,增大归并路数$k$将有效地减少归并树的高度,从而减少I/O次数,提高外部排序的速度。

值得说明的是,归并路数$k$并不是越大越好。归并路数$k$增大时,相应地需要增加输入缓冲区的个数。若可供使用的内存空间不变,势必要减少每个输入缓冲区的容量,使得内存、外存交换数据的次数增大。当$k$值过大时,虽然归并趟数会减少,但读/写外存的次数仍会增加。

### 8.7.4 置换-选择排序(生成初始归并段)

从8.7.2节的讨论可知,减少初始归并段个数$r$也可以减少归并趟数$S$。若总的记录个数为$n$,每个归并段的长度为$l$,则归并段的个数$r=\lceil n/l \rceil$。采用内部排序算法得到的各个初始归并段长度都相同(除最后一段外),它依赖于内部排序时可用内存工作区的大小。因此,必须探索新的方法,用来产生更长的初始归并段,这就是本节要介绍的置换-选择算法。
> ①本节中出现的关于比较次数的公式只是为了帮助读者理解相关原理,无须死记硬背。

**命题追踪**
> 置换-选择排序生成初始归并段的实例(2023)

设初始待排文件为FI,初始归并段输出文件为FO,内存工作区为WA,FO和WA的初始状态为空,WA可容纳$w$个记录。置换-选择算法的步骤如下:
1) 从FI输入$w$个记录到工作区WA。
2) 从WA中选出其中关键字取最小值的记录,记为MINIMAX记录。
3) 将MINIMAX记录输出到FO中去。
4) 若FI不空,则从FI输入下一个记录到WA中。
5) 从WA中所有关键字比MINIMAX记录的关键字大的记录中选出最小关键字记录,作为新的MINIMAX记录。
6) 重复3)～5),直至在WA中选不出新的MINIMAX记录为止,由此得到一个初始归并段,输出一个归并段的结束标志到FO中去。
7) 重复2)～6),直至WA为空。由此得到全部初始归并段。

设待排文件FI = {17, 21, 05, 44, 10, 12, 56, 32, 29}, WA容量为3, 排序过程如表8.2所示。

**表8.2 置换-选择排序过程示例**

| 输出文件 FO | 工作区 WA | 输入文件 FI |
| :--- | :--- | :--- |
| | | 17,21,05, 44, 10, 12, 56, 32, 29 |
| | 17 21 **05** | 44, 10, 12, 56, 32, 29 |
| 05 | **17** 21 44 | 10, 12, 56, 32, 29 |
| 05 17 | **10** 21 44 | 12, 56, 32, 29 |
| 05 17 21 | 10 **12** 44 | 56, 32, 29 |
| 05 17 21 44 | 10 12 **56** | 32, 29 |
| 05 17 21 44 56 | 10 12 **32** | 29 |
| 05 17 21 44 56 # | **10** 12 32 | 29 |
| 10 | 29 **12** 32 | |
| 10 12 | **29** 32 | |
| 10 12 29 | **32** | |
| 10 12 29 32 | | |
| 10 12 29 32 # | | |

上述算法,在WA中选择MINIMAX记录的过程需利用败者树来实现。

### 8.7.5 最佳归并树

文件经过置换-选择排序后,得到的是长度不等的初始归并段。下面讨论如何组织长度不等的初始归并段的归并顺序,使得I/O次数最少。假设由置换-选择排序得到9个初始归并段,其长度(记录数)依次为9, 30, 12, 18, 3, 17, 2, 6, 24。现做3路平衡归并,其归并树如图8.18所示。

在图8.18中,各叶结点表示一个初始归并段,上面的权值表示该归并段的长度,叶结点到根的路径长度表示其参加归并的趟数,各非叶结点代表归并成的新归并段,根结点表示最终生成的归并段。树的带权路径长度WPL为归并过程中的总读记录数,所以$I/O次数 = 2 \times WPL = 484$。

*图8.18 3路平衡归并的归并树*
```
        121
       / | \
     51  38  32
    /|\  /|\
   9 30 12 18 3 17 ...
```
(示意图，结构非精确)

**命题追踪**
> 构造三叉哈夫曼树及相关的分析和计算(2013)

显然,归并方案不同,所得归并树不同,树的带权路径长度(I/O次数)亦不同。为了优化归并树的WPL,可以将哈夫曼树的思想推广到$m$叉树的情形,在归并树中,让记录数少的初始归并段最先归并,记录数多的初始归并段最晚归并,就可以建立总的I/O次数最少的最佳归并树。上述9个初始归并段可构造成一棵如图8.19所示的归并树,按此树进行归并,仅需对外存进行446次读/写,这棵归并树称为最佳归并树。

*图8.19 3路平衡归并的最佳归并树*
```
           121
          / | \
         30 32 59
            /|\  /|\
           9 11 12 17 18 24
          /|\
         2 3 6
```
(示意图，结构非精确)

图8.19中的哈夫曼树是一棵严格3叉树,即树中只有度为3或0的结点。若只有8个初始归并段,如上例中少了一个长度为30的归并段。若在设计归并方案时,缺额的归并段留在最后,即除最后一次做二路归并外,其他各次归并仍是3路归并,此归并方案的I/O次数为386。显然,这不是最佳方案。正确的做法是:若初始归并段不足以构成一棵严格$k$叉树(也称正则$k$叉树)时,则需添加长度为0的“虚段”,按照哈夫曼树的原则,权为0的叶子应离树根最远。因此,最佳归并树应如图8.20所示,此时的I/O次数仅为326。

*图8.20 8个归并段的最佳归并树*
```
         91
        / | \
       20 24 47
      /|\    /|\
     5 6 9  12 17 18
    /|\
   0 2 3
```
(示意图，结构非精确)

如何判定添加虚段的数目?
设度为0的结点有$n_0$个,度为$k$的结点有$n_k$个,归并树的结点总数为$n$,则有:
*   $n = n_k + n_0$ (总结点数 = 度为$k$的结点数 + 度为0的结点数)
*   $n = kn_k + 1$ (总结点数 = 所有结点的度数之和 + 1)
因此,对严格$k$叉树有$n_0 = (k-1)n_k+1$,由此可得$n_k = (n_0-1)/(k-1)$。
*   若$(n_0-1)\%(k-1)=0$ (%为取模运算),则说明这$n_0$个叶结点(初始归并段)正好可以构造$k$叉归并树。此时,内结点有$n_k$个。
*   若$(n_0-1)\%(k-1)=u \ne 0$,则说明对于这$n_0$个叶结点,其中有$u$个多余,不能包含在$k$叉归并树中。为构造包含所有$n_0$个初始归并段的$k$叉归并树,应在原有$n_k$个内结点的基础上再增加1个内结点。它在归并树中代替了一个叶结点的位置,被代替的叶结点加上刚才多出的$u$个叶结点,即再加上$k-u-1$个空归并段,就可以建立归并树。

**命题追踪**
> 实现最佳归并时需补充的虚段数量的分析(2019)

以图8.20为例,用8个归并段构成3叉树,$(n_0-1)\%(k-1) = (8-1)\%(3-1)=1$,说明7个归并段刚好可以构成一棵严格3叉树(假设把以5为根的树视为一个叶子)。为此,将叶子5变成一个内结点,再添加$3-1-1=1$个空归并段,就可以构成一棵严格3叉树。

### 8.7.6 本节试题精选

**一、单项选择题**
**01.** 外部排序和内部排序的主要区别是( )。
A. 内部排序的数据量小,而外部排序的数据量大
B. 内部排序不涉及内、外存数据交换,而外部排序涉及内、外存数据交换
C. 内部排序的速度快,而外部排序的速度慢
D. 内部排序所需的内存小,而外部排序所需的内存大

**02.** 下列关于外部排序的说法中,正确的是( )。
A. 置换选择排序得到的初始归并段的长度一定相等
B. 内外存交换数据的时间只占总排序时间的一小部分
C. 败者树是一棵完全二叉树
D. 外部排序不涉及对文件的读/写操作

**03.** 多路平衡归并的作用是( )。
A. 减少归并趟数
B. 减少初始归并段的个数
C. 便于实现败者树
D. 以上都对

**04.** 设在磁盘上存放有375000个记录,做5路平衡归并排序,内存工作区能容纳600个记录,为把所有记录排好序,需要做( )趟归并排序。
A. 3
B. 4
C. 5
D. 6

**05.** 在下列关于外部排序过程输入/输出缓冲区作用的叙述中,不正确的是( )。
A. 暂存输入/输出记录
B. 内部归并的工作区
C. 产生初始归并段的工作区
D. 传送用户界面的消息

**06.** 在做$m$路平衡归并排序的过程中,为实现输入/内部归并/输出的并行处理,需要设置(①)个输入缓冲区和(②)个输出缓冲区。
① A. 2 B. m C. 2m-1 D. 2m
② A. 2 B. m C. 2m-1 D. 2m

**07.** 若只需3趟排序就可完成64个元素的多路归并排序,则选取的归并路数最少是( )。
A. 2
B. 3
C. 4
D. 5

**08.** 置换-选择排序的作用是( )。
A. 用于生成外部排序的初始归并段
B. 完成将一个磁盘文件排序成有序文件的有效的外部排序算法
C. 生成的初始归并段的长度是内存工作区的2倍
D. 对外部排序中输入/归并/输出的并行处理

**09.** 一个无序文件的$n$个记录采用置换选择排序产生$m$个有序段,则$m$和$n$的关系是( )。
A. $m$与$n$成正比 B. $m=\log_2 n$ C. $m$与$n$成反比 D. 以上都不对

**10.** 在由$k$路归并构建的败者树中选取一个关键字最小的记录,则所需时间为( )。
Α. $O(1)$
B. $O(k)$
C. $O(\log_2 k)$
D. 以上都不对

**11.** 下列关于小顶堆和败者树的说法中,正确的是( )。
I. 败者树从下往上维护,每上一层,只需和失败结点比较1次
II. 败者树的每次维护,必定要从叶结点一直走到根结点,不可能从中间停止
III. 堆从上往下维护,每下一层,若其左右孩子均不为空,则需比较2次
IV. 堆的每次维护,必定要从根结点一直走到叶结点,不可能从中间停止
A. I、III
B. II、III
C. I、II、III
D. I、III、IV

**12.** 最佳归并树在外部排序中的作用是( )。
A. 完成$m$路归并排序
B. 设计$m$路归并排序的优化方案
C. 产生初始归并段
D. 与锦标赛树的作用类似

**13.** 在由$m$个初始归并段构建的$k$阶最佳归并树中,不需要补充虚段,则度为$k$的结点个数是( )。
A. $(m-1)/k$
B. $m/k$
C. $(m-1)/(k-1)$
D. 无法确定

**14.** 【2013统考真题】已知三叉树T中6个叶结点的权分别是2,3,4,5,6,7,T的带权(外部)路径长度最小是( )。
A. 27
Β. 46
C. 54
D. 56

**15.** 【2016统考真题】对10TB的数据文件进行排序,应使用的方法是( )。
A. 希尔排序
B. 堆排序
C. 快速排序
D. 归并排序

**16.** 【2019统考真题】设外存上有120个初始归并段,进行12路归并时,为实现最佳归并,需要补充的虚段个数是( )。
A. 1
B. 2
C. 3
D. 4

**17.** 【2024统考真题】在外排序中,利用败者树对初始为升序的归并段进行多路归并,败者树中记录“冠军”的结点保存的是( )。
A. 最大关键字
B. 最小关键字
C. 最大关键字所在的归并段号
D. 最小关键字所在的归并段号

### 二、综合应用题

**01.** 若某个文件经内部排序得到80个初始归并段,试问:
1) 若使用多路平衡归并执行3趟完成排序,则应取得的归并路数至少应为多少?
2) 若操作系统要求一个程序同时可用的输入/输出文件的总数不超过15个,则按多路归并至少需要几趟可以完成排序?若限定这个趟数,可取的最低路数是多少?

**02.** 假设文件有4500个记录,在磁盘上每个块可放75个记录。计算机中用于排序的内存区可容纳450个记录。试问:
1) 可以建立多少个初始归并段?每个初始归 př段有多少记录?存放于多少个块中?
2) 应采用几路归并?请写出归并过程及每趟需要读/写磁盘的块数。

**03.** 设初始归并段为(10, 15, 31), (9, 20), (22, 34, 37), (6, 15, 42), (12, 37), (84, 95)。试利用败者树进行m路归并,手工执行选择最小的5个关键字的过程。

**04.** 给出12个初始归并段,其长度分别为30, 44, 8, 6, 3, 20, 60, 18, 9, 62, 68, 85。现要做4路外归并排序,试画出表示归并过程的最佳归并树,并计算该归并树的带权路径长度WPL。

**05.** 【2023统考真题】对含有$n(n>0)$个记录的文件进行外部排序,采用置换-选择排序生成初始归并段时需要使用一个工作区,工作区中能保存$m$个记录。请回答:
1) 若文件中含有19个记录,其关键字依次是51, 94, 37, 92, 14, 63, 15, 99, 48, 56, 23, 60, 31, 17, 43, 8, 90, 166, 100, 则当$m=4$时,可生成几个初始归并段?各是什么?
2) 对任意的$m(n \gg m>0)$,生成的第一个初始归并段的长度最大值和最小值分别是多少?

### 8.7.7 答案与解析

#### 一、单项选择题

**01. B**
外部排序和内部排序最主要的区别是是否涉及内存、外存的数据交换。

**02. C**
置换选择排序得到的是长度不一定相等的归并段,选项A错误。外部排序的主要时间消耗在内外存之间的数据交换上,选项B错误。败者树是一棵完全二叉树,选项C正确。外部排序包括两个阶段:生成初始归并段和对初始归并段进行归并,这两个阶段都涉及对文件的读/写操作,选项D错误。

**03. A**
多路平衡归并的目的是减少归并趟数,因为当$m$个初始归并段采用$k$路平衡归并时,所需趟数$s=\lceil \log_k m \rceil$,若不采用多路平衡归并,则其归并趟数大于$s$。

**04. B**
初始归并段的个数$r=375000/600=625$,因此,归并趟数$S=\lceil \log_r r \rceil=\lceil \log_5 625 \rceil=4$。第一趟把625个归并段归并成$625/5=125$个;第二趟把125个归并段归并成$125/5=25$个;第三趟把25个归并段归并成$25/5=5$个;第四趟把5个归并段归并成$5/5=1$个。

**05. D**
在外部排序过程中输入/输出缓冲区就是排序的内存工作区,例如做$m$路平衡归并需要$m$个输入缓冲区和1个输出缓冲区,用以存放参加归并的和归并完成的记录。在产生初始归并段时也可用作内部排序的工作区。它没有传送用户界面的消息的任务。

**06. D, A**
相比普通的$m$路归并:需增加一个输出缓冲区,当一个输出缓冲区满时,输出一个缓冲区的同时归并程序可向另一个输出缓冲区填充数据,这就实现了内部归并和输出的并行。需增加$m$个输入缓冲区,当$m$个输入缓冲区正在运行时,外部可向新增的$m$个缓冲区写入数据,这就实现了输入和内部归并的并行。综上,需设置2个输出缓冲区,$2m$个输入缓冲区。

**07. C**
归并趟数$=\lceil \log_k n \rceil$,其中$k$表示归并的路数,$n$表示元素个数,当$k=4$、$n=64$时,归并趟数恰好等于3,因此选取的归并路数至少是4。

**08. A**
置换-选择排序是外部排序中生成初始归并段的方法,用此方法得到的初始归并段的长度是不等长的,其长度平均是传统等长初始归并段的2倍,从而使得初始归并段数减少到原来的近二分之一。但是,置换-选择排序不是一种完整的生成有序文件的外部排序算法。

**09. D**
设内存工作区$w=1$,则文件{1,2,3,4,5}产生1个有序段,而文件{5,4,3,2,1}产生5个有序段,因此$m$与待排文件、内存工作区大小$w$和$n$都有关,但不是选项A、B、C描述的直接关系。

**10. C**
在败者树中选取最小关键字的时间复杂度取决于败者树的高度,所需时间为$O(\log_2 k)$。

**11. C**
选项I正确,是败者树的性质。在败者树的维护过程中,会让胜利者一直调整到根结点,选项II正确。以小根堆为例,每次调整时,先比较下一层的两个元素(1次),找出较小值,然后比较当前元素和下一层的较小元素(1次),以决定是否向下交换位置,选项III正确。堆在维护时,可能会在中间某层停止(若此处无须调整),而不一定要走到叶结点,选项IV错误。

**12. B**
最佳归并树在外部排序中的作用是设计$m$路归并排序的优化方案,仿照构造哈夫曼树的方法,以初始归并段的长度为权值,构造具有最小带权路径长度的$m$叉哈夫曼树,可以有效地减少归并过程中的读/写记录数,加快外部排序的速度。

**13. C**
$k$阶最佳归并树中只有度为0和$k$的结点。设结点总数为$n$,度为0的结点数为$n_0$,度为$k$的结点数为$n_k$,则$n_0=m$、$n-1=k \times n_k, n = n_0+n_k$,因此$k \times n_k = m+n_k-1$,求得$n_k=(m-1)/(k-1)$。

**14. B**
题中的三叉树为使WPL最小,必须构造三叉哈夫曼树,应满足$(n_0-1)\%(3-1)=0$的条件,因此需添加1个权值为0的虚叶结点,说明7个叶结点刚好可构成一棵严格的三叉树。按照哈夫曼树的原则,权为0的叶结点应离树根最远,构造三叉哈夫曼树的过程如下:
① 合并权值最小的三个结点0,2,3,得到新结点的权值=5,剩下5,4,5,6,7。
② 合并权值最小的三个结点4,5,5,得到新结点的权值=14,剩下14,6,7。
③ 合并权值最小的三个结点6,7,14,得到新结点的权值=27,仅有27,建树完成。
WPL = $\sum_{i=1}^n \text{权值}_{\text{叶结点}i} \times \text{深度}_{D_{\text{叶结点}i}} = (2+3)\times3+(4+5)\times2+(6+7)\times1 = 46$
或
WPL = $\sum_{i=1}^m \text{权值}_{W_{\text{分支结点}i}} = 27+14+5 = 46$
每个分支结点的权值都累加了其下面所有分支结点的权值,因此采用第二种方法更方便。

**15. D**
外部排序指的是大文件的排序,即待排序的记录存储在外存中,待排序的文件无法一次性装入内存,需要在内存和外存之间进行多次数据交换,以达到排序整个文件的目的。外部排序通常采用归并排序算法。选项A、B、C都是内部排序的方法。

**16. B**
在12路归并树中只存在度为0和度为12的结点,设度为0的结点数、度为12的结点数和要补充的结点数分别为$n_0, n_{12}$和$n_补$,则有$n_0=120+n_补, n_0=(12-1)n_{12}+1$,可得$n_{12}=(120-1+n_补)/(12-1)$。因为结点数$n_{12}$为整数,所以$n_补$是使上式整除的最小整数,求得$n_补=2$。
此外,题中为实现最佳归并,应满足12叉哈夫曼树,$n=120, m=12$,不满足$(n-1)\%(m-1)=0$的条件,因此需要添加两个权值为0的叶结点,使得$n=122$,才能满足条件。

**17. D**
利用败者树归并升序归并段,每次需要得到当前的最小关键字,因此记录“冠军”的结点保存的只能是最小关键字或最小关键字所在的归并段号。通过败者树找出最小关键字后,还需要找到该关键字所在的归并段,并移动段内元素,以继续比较下一个元素,下面分别进行讨论:①假设记录“冠军”的结点保存的是最小关键字所在的归并段号,则能直接得到最小关键字及其所在的段,时间复杂度低。②假设记录“冠军”的结点保存的是最小关键字,则查找其所在的段需要检索所有段的首元素是否与该最小关键字相等,时间复杂度高。为了提高效率,记录“冠军”的结点保存的是最小关键字所在的归并段号,而不是最小关键字。

#### 二、综合应用题

**01.【解答】**
1) 设归并路数为$m$,初始归并段个数$r=80$,根据归并趟数计算公式$S=\lceil \log_m r \rceil = \lceil \log_m 80 \rceil = 3$,得$\log_m 80 \le 3, m^3 \ge 80$。由此解得$m \ge 5$,即应取的归并路数至少为5。
2) 设多路归并的归并路数为$m$,需要$m$个输入缓冲区和1个输出缓冲区。一个缓冲区对应一个文件,有$m+1=15$,因此$m=14$,可做14路归并。由$S=\lceil \log_m r \rceil = \lceil \log_{14} 80 \rceil = 2$,即至少需要2趟归并可完成排序。若限定趟数为2,由$S=\lceil \log_m 80 \rceil=2$,有$80 \le m^2$,可取的最低路数为9。即要在2趟内完成排序,进行9路归并排序即可。

**02.【解答】**
1) 文件有4500个记录,用于排序的内存区可容纳450个记录,可建立的初始归并段有$4500/450=10$个。每个初始归并段中有450个记录,存于$450/75=6$个块中。
2) 内存区可容纳6个块,可建立6个缓冲区,其中5个缓冲区用于输入,1个缓冲区用于输出,因此可采用5路归并,归并过程如下图所示。
```
+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+
| 450 | 450 | 450 | 450 | 450 | 450 | 450 | 450 | 450 | 450 |
+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+
 \- - - - - - 2250 - - - - - -/ \- - - - - - 2250 - - - - - -/
        \ - - - - - - - 4500 - - - - - - /
```
共做了2趟归并,每趟需要读60块、写60块。

**03.【解答】**
做6路归并排序,选择最小的5个关键字的败者树如下图所示。
(由于复杂性，此处仅展示第一步)
*   **初始状态**:
    叶子节点关键字: (10), (9), (22), (6), (12), (84)
    败者树选择过程:
    - 12 vs 84 -> 败者 84 (段5)
    - 6 vs (胜者12) -> 败者 12 (段4)
    - 22 vs (胜者6) -> 败者 22 (段2)
    - 9 vs (胜者6) -> 败者 9 (段1)
    - 10 vs (胜者6) -> 败者 10 (段0)
    - 冠军: 6 (段3)
*   **输出6(3号段)**，从3号段读入下一个数15。
*   **重新调整**... (重复此过程)

**04.【解答】**
设初始归并段个数$n=12$,外归并路数$k=4$,计算$(n-1)\%(k-1) = 11\%3=2 \ne 0$,说明不能做完全的4路归并,因为多出了两个初始归并段,必须添加$k-2-1=1$个长度为0的空归并段,才能构成严格的4路归并树,即每次归并都有$k$个归并段参加归并。
此时,归并树的内结点应有$(n-1+1)/(k-1) = 12/3 = 4$个,如下图所示。
(a) 初始权值(包括一个权为0的虚段): 0, 3, 6, 8, 9, 18, 20, 30, 44, 60, 62, 68, 85
(b) 第一趟归并:
   - (0,3,6,8) -> 17
   - (9,18,20,30) -> 77 (此处图示与计算有出入，按图示) (9,18,20) -> 47 -> 归并(9,17,18,20)
   - (44,60,62,68)
(c) 第二趟归并
...
(d) 最终树
WPL=$(3+6+8)\times3 + (9+18+20+30+44+60+62)\times2 + (68+85)\times1 = 51+486+153=690$。

**05.【解答】**
1) 当文件中的$n$个记录升序排列时,只生成一个归并段,长度达到最大,为$n$。若初始工作区内的$m$个元素都大于输入文件中剩下的所有记录,则第一个归并段的长度就为$m$,此时为第一个归并段长度最小的情况。排序过程如下表所示。

| 输出文件 FO | 工作区 WA | 输入文件 FI |
| :--- | :--- | :--- |
| | | 51,94,37,92,14,63,15,99,48,56,23,60,31,17,43,8,90,166,100 |
| | 51,94,37,92 | 14,63,15,99,48,56,23,60,31,17,43,8,90,166,100 |
| 37 | 51,94,14,92 | 63,15,99,48,56,23,60,31,17,43,8,90,166,100 |
| 37,51 | 63,94,14,92 | 15,99,48,56,23,60,31,17,43,8,90,166,100 |
| 37,51,63 | 15,94,14,92 | 99,48,56,23,60,31,17,43,8,90,166,100 |
| 37,51,63,92 | 15,94,14,99 | 48,56,23,60,31,17,43,8,90,166,100 |
| 37,51,63,92,94 | 15,14,99,48 | 56,23,60,31,17,43,8,90,166,100 |
| 37,51,63,92,94,99 | 15,14,56,48 | 23,60,31,17,43,8,90,166,100 |
| 37,51,63,92,94,99# | 15,14,56,48 | 23,60,31,17,43,8,90,166,100 |
| 14 | 15,23,56,48 | 60,31,17,43,8,90,166,100 |
| 14,15 | 60,23,56,48 | 31,17,43,8,90,166,100 |
| 14,15,23 | 60,31,56,48 | 17,43,8,90,166,100 |
| 14,15,23,31 | 60,17,56,48 | 43,8,90,166,100 |
| 14,15,23,31,48 | 60,17,56,43 | 8,90,166,100 |
| 14,15,23,31,48,56 | 60,17,8,43 | 90,166,100 |
| 14,15,23,31,48,56,60 | 90,17,8,43 | 166,100 |
| 14,15,23,31,48,56,60,90 | 166,17,8,43 | 100 |
| 14,15,23,31,48,56,60,90,166 | 100,17,8,43 | |
| 14,15,23,31,48,56,60,90,166# | 100,17,8,43 | |
| 8 | 100,17,43 | |
| 8,17 | 100,43 | |
| 8,17,43 | 100 | |
| 8,17,43,100 | | |
| 8,17,43,100# | | |

生成三个初始归并段,分别是37, 51, 63, 92, 94, 99; 14, 15, 23, 31, 48, 56, 60, 90, 166; 8, 17, 43, 100。

2) 最大值为$n$,最小值为$m$。

### 归纳总结

下面对本章所介绍的排序算法进行一次系统的比较和复习。

1.  直接插入排序、冒泡排序和简单选择排序是基本的排序算法,它们主要用于元素个数$n$不是很大($n<10000$)的情形。
    它们的平均时间复杂度均为$O(n^2)$,实现也都非常简单。直接插入排序对于规模很小的元素序列($n \le 25$)非常有效。它的时间复杂度与待排序元素序列的初始排列有关。在最好情况下,直接插入排序只需要$n-1$次比较操作就可以完成,且不需要交换操作。在平均情况下和最差情况下,直接插入排序的比较和交换操作都是$O(n^2)$。冒泡排序在最好情况下只需要一趟排序过程就可以完成,此时也只需要$n-1$次比较操作,不需要交换操作。简单选择排序的关键字比较次数与待排序元素序列的初始排列无关,其比较次数总是$O(n^2)$,但元素移动次数则与待排序元素序列的初始排列有关,最好情况下数据不需要移动,最坏情况下元素移动次数不超过$3(n-1)$。
    从空间复杂度来看,这三种基本的排序算法除一个辅助元素外,都不需要其他额外空间。从稳定性来看,直接插入排序和冒泡排序都是稳定的,但简单选择排序不是。

2.  对于中等规模的元素序列($n \le 1000$),希尔排序是一种很好的选择。
    在希尔排序中,开始时增量较大,分量较多,每个组内的记录数较少,因而记录的比较和移动次数较少,且移动距离较远;到后来步长越来越小(最后一步为1),分组越少,每个组内的记录数越多,但同时记录次序也越来越接近有序,因而记录的比较和移动次数也都比较少。从理论上和实验上都已证明,在希尔排序中,记录的总比较次数和总移动次数比直接插入排序时少得多,特别是当$n$越大时效果越明显。而且,希尔排序代码简单,基本上不需要什么额外内存,但希尔排序是一种不稳定的排序算法。

3.  对于元素个数$n$很大的情况,可以采用快速排序、堆排序、归并排序或基数排序,其中快速排序和堆排序都是不稳定的,而归并排序和基数排序是稳定的排序算法。
    快速排序是最通用的高效内部排序算法,特别是它的划分思想经常在很多算法设计题中出现。平均情况下它的时间复杂度为$O(n\log_2 n)$,一般情况下所需要的额外空间也是$O(\log_2 n)$。但是快速排序在有些情况下也可能会退化(如元素序列已经有序时),时间复杂度会增加到$O(n^2)$,空间复杂度也会增加到$O(n)$。但我们可以通过“三者取中”法来避免最坏情况的发生。
    堆排序也是一种高效的内部排序算法,它的时间复杂度是$O(n\log_2 n)$,而且没有什么最坏情况会导致堆排序的运行明显变慢,并且堆排序基本上不需要额外的空间。但堆排序不大可能提供比快速排序更好的平均性能。
    归并排序也是一个重要的高效排序算法,它的一个重要特性是性能与输入元素序列无关,时间复杂度总是$O(n\log n)$。归并排序的主要缺点是需要$O(n)$的额外存储空间。
    基数排序是一种相对特殊的排序算法,这类算法不仅是对元素序列的关键字进行比较,更重要的是它们对关键字的不同位部分进行处理和比较。虽然基数排序具有线性增长的时间复杂度,但由于在常规编程环境中,基数排序的线性时间开销实际上并不比快速排序的时间开销小很多,并且由于基数排序基于的关键字抽取算法受到操作系统和排序元素的影响,其适应性远不如普通