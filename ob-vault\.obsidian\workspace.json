{"main": {"id": "c5309c23639f6433", "type": "split", "children": [{"id": "271a419edb146a0b", "type": "tabs", "children": [{"id": "d468fc94a288b92a", "type": "leaf", "state": {"type": "pdf", "state": {"file": "26王道数据结构/11_3.2 队列 75.pdf", "page": 1, "left": -4, "top": 845, "zoom": 0.8311655917043794}, "icon": "lucide-file-text", "title": "11_3.2 队列 75"}}, {"id": "63c645f162cea6fa", "type": "leaf", "state": {"type": "pdf", "state": {"file": "26王道数据结构/10_3.1 栈 62.pdf", "page": 9, "left": 3, "top": 845, "zoom": 0.8311655917043794}, "icon": "lucide-file-text", "title": "10_3.1 栈 62"}}, {"id": "22fc12312b379ffb", "type": "leaf", "state": {"type": "pdf", "state": {"file": "26王道数据结构/16_4.1 串的定义和实现 109.pdf", "page": 2, "left": 419, "top": 846, "zoom": 1}, "icon": "lucide-file-text", "title": "16_4.1 串的定义和实现 109"}}], "currentTab": 2}], "direction": "vertical"}, "left": {"id": "656191f2e6401125", "type": "split", "children": [{"id": "ae47b76ab87bd0fd", "type": "tabs", "children": [{"id": "c7b2959d6175ca8c", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "637f9c386e4e7689", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "d45fac5cac7e4929", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "right": {"id": "d7307f9e5722c485", "type": "split", "children": [{"id": "4487c097bfc2a072", "type": "tabs", "children": [{"id": "502fa8ef876c7525", "type": "leaf", "state": {"type": "backlink", "state": {"file": "高等代数2/5多项式.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "5多项式 的反向链接列表"}}, {"id": "069c5321bcc649bb", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "高等代数II/6特征值/6.1特征值和特征向量.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from 6.1特征值和特征向量"}}, {"id": "e572d6b2ad1f0c3b", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "2d175bce662bd1b8", "type": "leaf", "state": {"type": "outline", "state": {"file": "26王道数据结构/16_4.1 串的定义和实现 109.pdf", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "16_4.1 串的定义和实现 109 的大纲"}}, {"id": "179036768ee44fb9", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "3ae7d4af484349e7", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "7a5495fafd75000d", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "99960bf36047b840", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "23d118b83ee7833b", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "c44516f4fd6ae13d", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "0bd62aa7bf6c970d", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "5a1c81819a394f49", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "8fd2720169338c8d", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "b4ad65ea4e445e64", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "6d9dde9faf7705e3", "type": "leaf", "state": {"type": "SC-custom-variables-view", "state": {}, "icon": "code-glyph", "title": "Shell commands: Custom variables"}}], "currentTab": 3}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "math-indicator-changer:Change math indicator": false, "pdf-plus:PDF++: Toggle auto-copy": false, "pdf-plus:PDF++: Toggle auto-focus": false, "pdf-plus:PDF++: Toggle auto-paste": false, "cmdr:Copy file to clipboard": false, "obsidian-shellcommands:Shell commands: Custom variables": false}}, "active": "22fc12312b379ffb", "lastOpenFiles": ["26王道数据结构/13_3.4 数组和特殊矩阵 100.pdf", "26王道数据结构/12_3.3 栈和队列的应用 89.pdf", "26王道数据结构/11_3.2 队列 75.pdf", "26王道数据结构/07_2.3 线性表的链式表示 29.pdf", "26王道数据结构/10_3.1 栈 62.pdf", "26王道数据结构/06_2.2 线性表的顺序表示 14.pdf", "26王道数据结构/08_归纳总结 61.pdf", "26王道数据结构/07_2.3 线性表的链式表示 29 1.pdf", "26王道数据结构/09_思维拓展 61.pdf", "26武忠祥高等数学基础篇/47_8.3 多元函数的极值与最值 206.pdf", "26王道数据结构/05_2.1 线性表的定义和基本操作 12.pdf", "Demo.canvas", "高等代数2/7相似标准型.md", "react-foundations/7_updating-state.md", "react-foundations/1_what-is-react-and-nextjs.md", "React-Foundations/1_About-React-and-Next.js.md", "react-foundations/9_installation.md", "react-foundations/8_from-react-to-nextjs.md", "react-foundations/6_displaying-data-with-props.md", "react-foundations/5_building-ui-with-components.md", "react-foundations/4_getting-started-with-react.md", "react-foundations/3_updating-ui-with-javascript.md", "react-foundations/2_rendering-ui.md", "react-foundations/11_next-steps.md", "react-foundations/10_server-and-client-components.md", "dashboard-app/9_streaming.md", "dashboard-app/8_static-and-dynamic-rendering.md", "dashboard-app/7_fetching-data.md", "dashboard-app/6_setting-up-your-database.md", "dashboard-app/5_navigating-between-pages.md", "dashboard-app/4_creating-layouts-and-pages.md", "dashboard-app/3_optimizing-fonts-images.md", "dashboard-app/2_css-styling.md", "dashboard-app/1_getting-started.md", "dashboard-app/17_next-steps.md", "dashboard-app/16_adding-metadata.md", "dashboard-app/15_adding-authentication.md"]}