| 散列地址 | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| 关键字 | 11 | | 14 | 7 | | 20 | 9 | | 3 | 18 |

装填因子$\alpha=7/11$。
2) $H(14)=9$, 和关键字3比较, 不命中; $H_1(14)=(9+1)\%11=10$, 和18比较, 不命中; $H_2(14)=(9+4)\%11=2$, 和14比较, 命中。因此, 关键字比较序列是3, 18, 14。
3) $H(8)=2$, 和关键字14比较, 不命中: $H_1(8)=(2+1)\%11=3$, 和7比较, 不命中; $H_2(8)=(2+4)\%11=6$, 和9比较, 不命中; $H_3(8)=(2+9)\%11=0$, 和11比较, 不命中; $H_4(8)=(2+16)\%11=7$, 是空位置, 确认查找失败。因此, 确认查找失败时的散列地址是7。

### 归纳总结

本章的核心考查点是求平均查找长度(ASL), 以度量各种查找算法的性能。查找算法本身依托于查找结构, 查找结构又是由相同数据类型的记录或结点构成的, 所以最终落脚于数据结构类型的区别。不管是何种查找算法, 其平均查找长度的计算公式都是一样的。

查找成功的平均查找长度$ASL_{成功}=\sum_{i=1}^{n}P_i C_i$。

查找失败的平均查找长度$ASL_{不成功}=\sum_{j=0}^{n}q_j c'_j$。

设一个查找集合中已有$n$个数据元素, 每个元素的查找概率为$p_i$, 查找成功的数据比较次数为$c_i(i=1,2,\dots,n)$; 不在此集合中的数据元素分布在由这$n$个元素的间隔构成的$n+1$个子集合内, 每个子集合元素的查找概率为$q_j$, 查找不成功的数据比较次数为$c'_j(j=0,1,\dots,n)$。因此, 对某一特定查找算法的查找成功的$ASL_{成功}$和查找失败的$ASL_{不成功}$, 是综合考虑还是分开考虑呢?
若综合考虑, 即$\sum_{i=1}^{n}p_i+\sum_{j=0}^{n}q_j=1$, 若所有元素查找概率相等, 则有$p_i=q_j=\frac{1}{2n+1}$; 若分开考虑, 即$\sum_{i=1}^{n}p_i=1, \sum_{j=0}^{n}q_j=1$, 若所有元素查找概率相等, 则有$p_i=\frac{1}{n}, q_j=\frac{1}{n+1}$。

虽然综合考虑更为理想, 但在实际应用中多数是分开考虑的, 因为对于查找不成功的情况, 很多场合下没有明确给出, 往往会被忽略掉。不过读者仍要注意的是, 这两种考虑的计算结果是不同的, 考试中一定要仔细阅读题目的要求, 以免失误。

### 思维拓展

本章介绍了几种基本的查找算法, 在实际中又会碰到怎样的查找问题呢?
**题目**：数组中有一个数字出现的次数超过了数组长度的一半, 请找出这个数字。读者也许会想到先进行排序, 位于位置$(n+1)/2$的数即要找的数, 这样最小时间复杂度就为$O(n\log_2 n)$; 若进行散列查找, 数字的范围又未知, 则应如何将时间复杂度控制在$O(n)$内呢?

**提示**
出现的次数超过数组长度的一半, 表明这个数字出现的次数比其他数字出现的次数的总和还多。所以我们可以考虑每次删除两个不同的数, 则在剩下的数中, 待查找数字出现的次数仍然超过总数的一半。通过不断重复这个过程, 不断排除其他的数字, 最终剩下的都为同一个数字, 即要找的数字。

---
## 第八章 排序

**【考纲内容】**
(一) 排序的基本概念
(二) 插入排序
直接插入排序; 折半插入排序; 希尔排序(shell sort)
(三) 交换排序
冒泡排序(bubble sort); 快速排序
(四) 选择排序
简单选择排序; 堆排序
(五) 二路归并排序(merge sort)
(六) 基数排序
(七) 外部排序
(八) 排序算法的分析和应用

**【知识框架】**
*   **基本概念**: 稳定性、衡量标准：时、空复杂度
*   **内部排序**
    *   **插入排序**: 直接插入排序、折半插入排序、希尔排序
    *   **交换排序**: 冒泡排序、快速排序
    *   **选择排序**: 简单选择排序、堆排序
    *   **归并排序**
    *   **基数排序**
*   **外部排序** — 多路归并排序

**【复习提示】**
堆排序、快速排序和归并排序是本章的重难点。读者应深入掌握各种排序算法的思想、排序过程(能动手模拟)和特征(初态的影响、复杂度、稳定性、适用性等), 通常以选择题的形式考查不同算法之间的对比。此外, 对于一些常用排序算法的关键代码, 要达到熟练编写的程度; 看到某特定序列, 读者应具有选择最优排序算法(根据排序算法特征)的能力。

---
### 8.1 排序的基本概念

**8.1.1 排序的定义**

排序, 就是重新排列表中的元素, 使表中的元素满足按关键字有序的过程。为了查找方便, 通常希望计算机中的表是按关键字有序的。排序的确切定义如下:
输入：$n$个记录$R_1, R_2, \dots, R_n$, 对应的关键字为$k_1, k_2, \dots, k_n$。
输出：输入序列的一个重排$R'_{1}, R'_{2}, \dots, R'_{n}$, 使得$k'_{1} \le k'_{2} \le \dots \le k'_{n}$ (其中“$\le$”可以换成其他的比较大小的符号)。
算法的稳定性。若待排序表中有两个元素$R_i$和$R_j$, 其对应的关键字相同, 即$key_i = key_j$, 且在排序前$R_i$在$R_j$的前面, 若使用某一排序算法排序后, $R_i$仍然在$R_j$的前面, 则称这个排序算法是稳定的, 否则称这个排序算法是不稳定的。需要注意的是, 算法是否具有稳定性并不能衡量一个算法的优劣, 它主要是对算法的性质进行描述。若待排序表中的关键字不允许重复, 排序结果是唯一的, 则对于排序算法的选择, 稳定与否无关紧要。

**注意**
对于不稳定的排序算法, 只需举出一组关键字的实例, 说明它的不稳定性即可。

在排序过程中, 根据数据元素是否完全存放在内存中, 可将排序算法分为两类: ①内部排序, 是指在排序期间元素全部存放在内存中的排序; ②外部排序, 是指在排序期间元素无法全部同时存放在内存中, 必须在排序的过程中根据要求不断地在内、外存之间移动的排序。
一般情况下, 内部排序算法在执行过程中都要进行两种操作: 比较和移动。通过比较两个关键字的大小, 确定对应元素的前后关系, 然后通过移动元素以达到有序。当然, 并非所有的内部排序算法都要基于比较操作, 事实上, 基数排序就不基于比较操作。
每种排序算法都有各自的优缺点, 适合在不同的环境下使用, 就其全面性能而言, 很难提出一种被认为是最好的算法。通常可以将排序算法分为插入排序、交换排序、选择排序、归并排序和基数排序五大类, 后面几节会分别进行详细介绍。内部排序算法的性能取决于算法的时间复杂度和空间复杂度, 而时间复杂度一般是由比较和移动的次数决定的。

**注意**
大多数的内部排序算法都更适用于顺序存储的线性表。

### 8.1.2 本节试题精选

**单项选择题**
01. 下述排序算法中, 不属于内部排序算法的是( )。
    A. 插入排序
    B. 选择排序
    C. 拓扑排序
    D. 冒泡排序
02. 排序算法的稳定性是指( )。
    A. 经过排序后, 能使关键字相同的元素保持原顺序中的相对位置不变
    B. 经过排序后, 能使关键字相同的元素保持原顺序中的绝对位置不变
    C. 排序算法的性能与被排序元素个数关系不大