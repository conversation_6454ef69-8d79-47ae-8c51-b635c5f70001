## 第5章 连续随机变量的产生

### 引言

对于连续随机变量的模拟，都有类似于产生离散随机变量的各种方法与之对应。生成连续随机变量的逆变换法和筛选法将在5.1节和5.2节介绍。在5.3节，我们考虑生成正态随机变量的有效方法，即，极坐标法。最后，在5.4节和5.5节，我们考虑泊松过程和非齐次泊松过程的模拟问题。

### 5.1 逆变换法

设$F$为某连续随机变量的分布函数。我们将基于如下命题给出生成此随机变量的逆变换法。

**命题** 设$U$是$(0,1)$上均匀分布的随机变量，对于任一连续分布函数$F$，随机变量

$$X = F^{-1}(U)$$

的分布函数为$F$。\[$F^{-1}(u)$被定义为满足$F(x) = u$的$x$。]

**证明** 以$F_X$记$X = F^{-1}(U)$的分布函数，则

$$F_X(x) = P\{X \leq x\} = P\{F^{-1}(U) \leq x\} \tag{5.1}$$

由于$F(x)$是一分布函数，故它是$x$的单调增函数，且不等式"$a < b$"等价于不等式"$F(a) < F(b)$"。于是，由等式(5.1)知

$$F_X(x) = P\{F(F^{-1}(U)) \leq F(x)\}$$
$$= P\{U \leq F(x)\}$$
$$= F(x)$$

由上一命题知，如果随机变量$X$的分布函数$F(x)$连续，则我们可以如下模拟它：生成随机数$U$后，取$X = F^{-1}(U)$。

**例5a** 如果随机变量$X$的分布函数为

$$F(x) = x^n, 0 < x < 1$$

则用上述方法模拟生成$X$。

如果记$x = F^{-1}(u)$，则

$$u = F(x) = x^n$$

或等价地有

$$x = u^{1/n}$$

于是，我们可以通过生成随机数$U$后，令$X = U^{1/n}$来得到随机变量$X$的模拟值。

逆变换方法是一种非常好的产生指数随机变量的方法，参见下面的例子。

**例5b** 设$X$是参数为1的指数随机变量，则其分布函数为

$$F(x) = 1 - e^{-x}$$

如果设$x = F^{-1}(u)$，则

$$u = F(x) = 1 - e^{-x}$$

或

$$1 - u = e^{-x}$$

或取对数后有

$$x = -\ln(1 - u)$$

于是，参数为1的指数分布的随机变量$X$可由下式产生：

$$X = F^{-1}(U) = -\ln(1 - U)$$

其中$U$为随机数。

由于$1-U$也是$(0,1)$上均匀分布的随机变量，故$-\ln(1-U)$与$-\ln(U)$同分布。于是，由上式可知，一个随机数的负对数是参数为1的指数随机变量。利用这一点进行随机模拟可以节省一些计算时间。

另外，我们注意到，如果$X$是均值为1的指数随机变量，则对于任一正常数$c$, $cX$是均值为$c$的指数随机变量。于是，参数为$\lambda$(均值为$1/\lambda$)的指数随机变量$X$可以由

$$X = -\frac{1}{\lambda}\ln U$$

产生，其中$U$为随机数。

注：上述方法也可用来生成泊松随机变量。前面讲过，当两个相继事件的间隔时间是独立的参数为$\lambda$的指数随机变量时，这些事件就构成一个参数为$\lambda$的泊松过程（见第2章的2.9节）。由此可知，到时间1出现的事件数$N(1)$服从参数为$\lambda$的泊松分布。如果以$X_i$, $i = 1,2,\cdots$记相继两个事件(第$i$个与第$i-1$个事件)的间隔时间，则第$n$个事件发生的时间为$\sum_{i=1}^n X_i$。于是，到时刻1发生的事件数可以表示为

$$N(1) = \max\left\{n: \sum_{i=1}^n X_i \leq 1\right\}$$

这就是说，到时刻1发生的事件数等于时刻1时发生的事件序数的最大值（例如，如果时刻1时第4个事件发生，而第5个事件没有发生，故时刻1时总共有4个事件发生）。于是，利用例5b的结果，我们可以证明参数为$\lambda$的泊松随机变量$N = N(1)$满足

$$N = \max\left\{n: \sum_{i=1}^n -\frac{1}{\lambda}\ln U_i \leq 1\right\}$$
$$= \max\left\{n: \sum_{i=1}^n \ln U_i \geq -\lambda\right\}$$
$$= \max\{n: \ln(U_1 \cdots U_n) \geq -\lambda\}$$
$$= \max\{n: U_1 \cdots U_n \geq e^{-\lambda}\}$$

其中$U_1,\cdots,U_n,\cdots$为随机数。于是，参数为$\lambda$的泊松随机变量$N$可以如下产生：相继生成随机数直至它们的乘积小于$e^{-\lambda}$，则$N$等于这些随机数的个数减1，即

$$N = \min\{n: U_1 \cdots U_n < e^{-\lambda}\} - 1$$

基于伽玛分布和指数分布间的关系，我们可以有效利用例5b的结果来生成$\Gamma(n,\lambda)$随机变量。

**例5c** 假设我们希望产生$\Gamma(n,\lambda)$的随机变量。因为此随机变量的分布函数

$$F(x) = \int_0^x \frac{\lambda e^{-\lambda y}(\lambda y)^{n-1}}{(n-1)!}dy$$

故其逆函数不可能显示地表出。但是，由于$\Gamma(n,\lambda)$随机变量$X$可以表示成$n$个独立的参数为$\lambda$的指数随机变量的和（见第2章的2.3节），故我们可以利用例5b的结果来生成$X$。特别地，参数为$(n,\lambda)$的伽玛随机变量$X$可以如下产生：

$$X = -\frac{1}{\lambda}\ln U_1 - \cdots - \frac{1}{\lambda}\ln U_n$$
$$= -\frac{1}{\lambda}\ln(U_1 \cdots U_n)$$

其中$U_1,\cdots,U_n$为$n$个随机数。等式$\sum_{i=1}^n \ln x_i = \ln(x_1 \cdots x_n)$的应用可以节省一些计算时间，其原因是右端仅一次对数计算，而左端却需要$n$次对数计算。

我们可以利用例5c的方法来有效地生成一组指数随机变量：先生成它们和的随机变量值，之后再生成每个随机变量在其和给定条件下的单个值。例如，为了生成两个独立同分布的参数为1的指数随机变量$X$和$Y$，我们先生成其和$X + Y$的值，并记$t = X + Y$，然后，再利用第2章习题36的结论：在给定$X + Y = t$下，$X$的条件分布是$(0,t)$上的均匀分布。基于上述思想，生成一对参数为1的指数随机变量的算法如下：

步骤1：生成随机数$U_1$和$U_2$。
步骤2：令$t = -\ln(U_1 U_2)$。
步骤3：生成随机数$U_3$。
步骤4：$X = tU_3, Y = t - X$。

若利用例5c的方法直接生成，则$X = -\ln U_1, Y = -\ln U_2$。比较二者的异同，我们知道上面的算法比直接算法少了一次对数运算，但多了两次乘法和多生成了一个随机数。

我们也可以运用上述算法生成$k$个独立的参数为1的指数随机变量：先利用$-\ln(U_1 \cdots U_k)$生成它们的和，且记$t = -\ln(U_1 \cdots U_k)$；之后，再生成$k-1$个新的随机数，且把它们由小到大排列成$U_{(1)} < U_{(2)} < \cdots < U_{(k-1)}$；最后，所求的$k$个指数随机变量为

$$t[U_{(i)} - U_{(i-1)}], i = 1,2,\cdots,k$$

其中$U_{(0)} \equiv 0, U_{(k)} \equiv 1$。

### 5.2 筛选法

假设我们已有一种生成密度函数为$g(x)$的随机变量的方法，现在将利用这种方法生成密度函数为$f(x)$的随机变量。为此，我们先生成来自$g$的随机变量$Y$，然后以正比例于$f(Y)/g(Y)$的概率接受此值。

特别地，设$c$为一常数且满足

$$\frac{f(y)}{g(y)} \leq c, 对任意 y$$

于是，我们有如下的生成密度函数为$f$的随机变量的方法（其图示见图5-1）。

**筛选法**
步骤1：生成密度函数为$g$的随机变量$Y$。
步骤2：生成随机数$U$。
步骤3：如果$U \leq \frac{f(Y)}{cg(Y)}$，则令$X = Y$。否则，转至步骤1。

有些读者会注意到，除了用密度函数替代概率分布函数外，产生连续随机变量的筛选法与离散情形完全一样。于是，我们可以利用完全类似于离散情形的方法证明下面的结论。

**定理**
(i) 利用筛选法生成的随机变量的密度函数为$f$。
(ii) 此算法所需的迭代次数是均值为$c$的几何随机变量。

与离散情形一样，以概率$f(Y)/cg(Y)$接受$Y$可以如下进行：产生一个随机数$U$后，如果$U \leq f(Y)/cg(Y)$，则接受$Y$。

**例5d** 用筛选法生成密度函数为

$$f(x) = 20x(1 - x)^3, 0 < x < 1$$

的随机变量。

由于此随机变量\[其分布为参数$(2,4)$的贝塔分布]集中在区间$(0,1)$里，于是，我们选用

$$g(x) = 1, 0 < x < 1$$

的筛选法。为求取满足$f(x)/g(x) \leq c$的最小的$c$，我们利用微分法以确定

$$\frac{f(x)}{g(x)} = 20x(1 - x)^3$$

的最大值。上式两边关于$x$求导，有

$$\frac{d}{dx}\left(\frac{f(x)}{g(x)}\right) = 20[(1 - x)^3 - 3x(1 - x)^2]$$

令上式等于零，则得到其最大值在点$x = 1/4$处达到。于是

$$\frac{f(x)}{g(x)} \leq 20\left(\frac{1}{4}\right)\left(\frac{3}{4}\right)^3 = \frac{135}{64} \equiv c$$

于是，

$$\frac{f(x)}{cg(x)} = \frac{256}{27}x(1 - x)^3$$

这样，其筛选算法如下：

步骤1：生成随机数$U_1$和$U_2$。
步骤2：如果$U_2 \leq \frac{256}{27}U_1(1-U_1)^3$，则令$X = U_1$且停止。否则，转至步骤1。

步骤1的平均运行次数是$c = \frac{135}{64} \approx 2.11$。

**例5e** 假设我们希望产生密度函数为

$$f(x) = Kx^{1/2}e^{-x}, x > 0$$

的$\Gamma(3/2,1)$的随机变量，其中$K = 1/\Gamma(3/2) = 2/\sqrt{\pi}$。由于此随机变量的取值为正且均值为$3/2$，故我们自然想到采用均值也为$3/2$的指数随机变量的密度作为$g(x)$，即

$$g(x) = \frac{2}{3}e^{-2x/3}, x > 0$$

此时

$$\frac{f(x)}{g(x)} = \frac{3K}{2}x^{1/2}e^{-x/3}$$

在上式两边求导且令其为零，则求得此比值的最大值在点

$$\frac{1}{2}x^{-1/2}e^{-x/3} = \frac{1}{3}x^{1/2}e^{-x/3}$$

即$x = 3/2$处达到。于是，

$$c = \max\frac{f(x)}{g(x)} = \frac{3K}{2}\left(\frac{3}{2}\right)^{1/2}e^{-1/2}$$
$$= \frac{3^{3/2}}{(2\pi e)^{1/2}}$$

因为

$$\frac{f(x)}{cg(x)} = (2e/3)^{1/2}x^{1/2}e^{-x/3}$$

故$\Gamma(3/2,1)$的随机变量可以如下生成：

步骤1：生成一个随机数$U_1$，并令$Y = -\frac{3}{2}\ln U_1$。
步骤2：生成一个随机数$U_2$。
步骤3：如果$U_2 < (2eY/3)^{1/2}e^{-Y/3}$，则令$X = Y$。否则，转至步骤1。

所需的平均选代次数为

$$c = 3\left(\frac{3}{2\pi e}\right)^{1/2} \approx 1.257$$

在上面的例子中，我们应用与伽玛分布具有同一均值的指数分布作为$g(x)$的筛选法生成了伽玛随机变量。注意到，用指数分布来生成伽玛随机变量是最有效的。

为证明这一点，假设我们想生成概率密度函数为

$$f(x) = Ke^{-\lambda x}x^{\alpha-1}, x > 0$$

的随机变量，其中$\lambda > 0, \alpha > 0, K = \lambda^{\alpha}/\Gamma(\alpha)$。上述$f(x)$是参数为$\alpha$和$\lambda$的伽玛随机变量的密度函数，且其均值为$\alpha/\lambda$。

假设想利用基于参数为$\mu$的指数密度函数的筛选法生成上述随机变量，因为

$$\frac{f(x)}{g(x)} = \frac{Ke^{-\lambda x}x^{\alpha-1}}{\mu e^{-\mu x}} = \frac{K}{\mu}x^{\alpha-1}e^{(\mu-\lambda)x}$$

且当$0 < \alpha < 1$时

$$\lim_{x \to 0}\frac{f(x)}{g(x)} = \infty$$

故此时我们无法利用基于指数密度函数的筛选法。因为$\alpha = 1$的伽玛密度函数即为指数分布密度函数，故假设$\alpha > 1$。又因为当$\mu \geq \lambda$时，

$$\lim_{x \to \infty}\frac{f(x)}{g(x)} = \infty$$

故我们限制$\mu$严格小于$\lambda$。此时，该算法所需的平均选代次数为

$$c(\mu) = \max_x \frac{f(x)}{g(x)} = \max_x \frac{K}{\mu}x^{\alpha-1}e^{(\mu-\lambda)x}$$

为求使上式达到最大的$x$值，求导令其导数等于0，则有

$$0 = (\alpha - 1)x^{\alpha-2}e^{(\mu-\lambda)x} - (\lambda - \mu)x^{\alpha-1}e^{(\mu-\lambda)x}$$

由此得到其最大值点为

$$x = \frac{\alpha - 1}{\lambda - \mu}$$

把它带回去，则

$$c(\mu) = \frac{K}{\mu}\left(\frac{\alpha - 1}{\lambda - \mu}\right)^{\alpha-1}e^{(\mu-\lambda)(\alpha-1)/(\lambda-\mu)} = \frac{K}{\mu}\left(\frac{\alpha - 1}{\lambda - \mu}\right)^{\alpha-1}e^{1-\alpha}$$

于是，最小化$c(\mu)$的$\mu$值使$\mu(\lambda - \mu)^{\alpha-1}$达到最大。其导数为

$$\frac{d}{d\mu}\{\mu(\lambda - \mu)^{\alpha-1}\} = (\lambda - \mu)^{\alpha-1} - (\alpha - 1)\mu(\lambda - \mu)^{\alpha-2}$$

令上式为0，则得到最优的$\mu$满足

$$\lambda - \mu = (\alpha - 1)\mu$$

或

$$\mu = \lambda/\alpha$$

这就是说，用基于均值为$\alpha/\lambda$的指数密度函数的筛选法生成参数为$\alpha, \lambda$的伽玛随机变量所需要的迭代次数最小，且指数密度函数的均值与伽玛密度的均值相同。

下一个例子将指出如何利用筛选法生成正态随机变量。

**例5f 正态随机变量的生成** 为生成标准正态随机变量$Z$（即均值为0，方差为1的正态），我们首先注意到其绝对值$|Z|$的概率密度函数为

$$f(x) = \frac{1}{\sqrt{2\pi}}e^{-x^2/2}, 0 < x < \infty \tag{5.2}$$

我们首先利用筛选法生成具有上述密度函数的随机变量，密度$g(x)$采用均值为1的指数密度，即

$$g(x) = e^{-x}, 0 < x < \infty$$

此时

$$\frac{f(x)}{g(x)} = \sqrt{2/\pi}e^{x-x^2/2}$$

且其最大值在使得$x - x^2/2$达到最大值处取得。由微分法可知最大值点为$x = 1$。于是，我们取

$$c = \max\frac{f(x)}{g(x)} = \frac{f(1)}{g(1)} = \sqrt{2e/\pi}$$

由于

$$\frac{f(x)}{cg(x)} = \exp\left\{x - \frac{x^2}{2} - \frac{1}{2}\right\}$$
$$= \exp\left\{-\frac{(x - 1)^2}{2}\right\}$$

故生成$|Z|$的算法如下：

步骤1：生成参数为1的指数随机变量$Y$。
步骤2：生成一个随机数$U$。
步骤3：如果$U \leq \exp\{-(Y - 1)^2/2\}$，则令$X = Y$。否则，转至步骤1。

当我们模拟得到密度函数为(5.2)式的随机变量$X$后（此随机变量的分布等同于标准正态随机变量的绝对值的分布），则令标准正态随机变量$Z$以等概率取$X$或$-X$即可。

由于在步骤3中的$U \leq \exp\{-(Y - 1)^2/2\}$等价于$-\ln U \geq (Y - 1)^2/2$，且例5b证明$-\ln U$是参数为1的指数随机变量，故上述算法等价于下面的算法：

步骤1：生成两个独立的参数为1的指数随机变量$Y_1, Y_2$。
步骤2：如果$Y_2 \geq (Y_1 - 1)^2/2$，则令$X = Y_1$。否则，转至步骤1。

接受上述算法中的$Y_1$就意味着$Y_2$大于$(Y_1 - 1)^2/2$，那这种大小关系大概有多少个？为回答这一问题，我们注意到$Y_2$是参数为1的指数随机变量，并且由指数分布的无记忆性可知，在给定它大于某值\[此值取为$Y_2$超过$(Y_1 - 1)^2/2$的部分]下的条件分布仍是参数为1的指数分布。于是，在步骤2中接受$Y_1$不仅意味着我们得到了标准正态随机变量的绝对值$X$，而且通过计算$Y_2 - (Y_1 - 1)^2/2$，我们还得到了一个参数为1的指数随机变量（且与$X$独立）。

综上所述，下述算法可同时生成一个参数1的指数随机变量和独立的标准正态随机变量：

步骤1：生成参数1的指数随机变量$Y_1$。
步骤2：生成参数1的指数随机变量$Y_2$。
步骤3：如果$Y_2 - (Y_1 - 1)^2/2 > 0$，则令$Y = Y_2 - (Y_1 - 1)^2/2$且转至步骤4。否则，转至步骤1。
步骤4：生成一个随机数$U$，且令

$$Z = \begin{cases}
Y_1 & 如果\ U \leq \frac{1}{2} \\
-Y_1 & 如果\ U > \frac{1}{2}
\end{cases}$$

由上一算法生成的$Z$与$Y$是独立的，且$Z$为一均值0方差1的正态随机变量，$Y$是一参数为1的指数随机变量（如果希望生成一个均值$\mu$方差$\sigma^2$的正态随机变量，则可取$\mu + \sigma Z$）。

**注1** 因为$c = \sqrt{2e/\pi} \approx 1.32$，故在上一算法的步骤2中所需的选代次数是一均值为1.32的几何随机变量。

**注2** 如果我们希望生成多个标准正态随机变量，则我们可以用步骤3中得到的指数随机变量$Y$作为下一轮步骤1中所需的初始指数随机变量。于是，平均来讲，模拟一个标准正态随机变量需要生成1.64(=2 $\times$ 1.32 - 1)个指数随机变量和1.32$^2$次计算。

**注3** 为确定标准正态随机变量的符号（如步骤4），不必再生成一个新的随机数，而可以利用前面生成的第一个随机数。也就是说，如前面用过的随机数为$r_1, r_2, \cdots, r_k$，则可用$r_1$来确定标准正态随机变量的符号。

当我们想模拟落在某个特定区域的随机变量时，筛选法是非常有用的。下面这个例子将说明这一点。

**例5g** 假设我们想生成取值大于5的$\Gamma(2,1)$的随机变量。即，我们想生成的随机变量的概率密度函数为

$$f(x) = \frac{xe^{-x}}{\int_5^{\infty}xe^{-x}dx} = \frac{xe^{-x}}{6e^{-5}}, x \geq 5$$

其中的积分是由分部积分法求得的。由于$\Gamma(2,1)$的期望为2，故我们利用基于取值不小于5的均值为2的指数密度函数的筛选法，即我们取

$$g(x) = \frac{1}{2}e^{-x/2}/e^{-5/2}, x \geq 5$$

于是，

$$\frac{f(x)}{g(x)} = \frac{e^{5/2}}{3}xe^{-x/2}, x \geq 5$$

由于当$x \geq 5$时，$xe^{-x/2}$是$x$的递减函数，故此算法所需的迭代次数是均值为

$$c = \max_{x \geq 5}\left\{\frac{f(x)}{g(x)}\right\} = \frac{f(5)}{g(5)} = 5/3$$

的几何分布随机变量。想生成取值超过5的参数为1/2的指数随机变量，我们应用如下事实：由指数随机变量的无记忆性知，此时取值超过5的指数随机变量仍是参数为1/2的指数随机变量。即，如果$X$是一参数为1/2的指数随机变量，则5 + $X$与$X$具有相同的分布且超过5。于是，我们有如下来模拟具有密度函数$f$的随机变量的算法：

步骤1：生成一个随机数$U$。
步骤2：令$Y = 5 - 2 \log(U)$。
步骤3：生成一个随机数$U$。
步骤4：如果$U \leq \frac{e^{5/2}}{5}Ye^{-Y/2}$，则令$X = Y$且停止；否则回到步骤1。

正如在例5f中我们利用基于指数密度函数的筛选法模拟正态随机变量一样，我们也可以利用基于指数密度函数的筛选法很有效地模拟落在某个特定区间内的正态随机变量。细节（包括指数分布最佳均值的确定）将在8.8节给出。

### 5.3 生成正态随机变量的极坐标法

设$X$和$Y$是两个独立的标准正态随机变量，以$R$和$\Theta$记向量$(X,Y)$的极坐标。则有（见图5-2）

因为$X$与$Y$是独立的，故它们的联合密度为二者密度的乘积，即

$$f(x,y) = \frac{1}{\sqrt{2\pi}}e^{-x^2/2}\frac{1}{\sqrt{2\pi}}e^{-y^2/2}$$
$$= \frac{1}{2\pi}e^{-(x^2+y^2)/2} \tag{5.3}$$

为确定$R^2$和$\Theta$的联合密度$f(d,\theta)$，我们做下面变量替换：

$$d = x^2 + y^2, \theta = \arctan\left(\frac{y}{x}\right)$$

容易验证此变换的雅可比式，即上述变换关于$x$和$y$的偏导数矩阵的行列式等于2。于是，由等式(5.3)知，$R^2$和$\Theta$的联合密度为

$$f(d,\theta) = \frac{1}{2\pi}e^{-d/2}, 0 < d < \infty, 0 < \theta < 2\pi$$

由于上式等于均值2的指数密度$(\frac{1}{2}e^{-d/2})$和$(0,2\pi)$上均匀密度$[(2\pi)^{-1}]$的乘积，故有

$R^2$与$\Theta$独立，且$R^2$是均值2的指数随机变量，$\Theta$是$(0,2\pi)$上的均匀随机变量。(5.4)

于是，我们可以先用等式(5.4)生成极坐标，之后把它们变成直角坐标就得到了两个独立的标准正态随机变量。这即是下面的算法：

步骤1：生成随机数$U_1, U_2$。
步骤2：$R^2 = -2 \ln U_1$($R^2$是均值2的指数随机变量)，$\Theta = 2\pi U_2$\[$\Theta$是$(0,2\pi)$上均匀随机变量\]。
步骤3：令

$$X = R \cos \Theta = \sqrt{-2 \ln U_1} \cos(2\pi U_2), \quad Y = R \sin \Theta = \sqrt{-2 \ln U_1} \sin(2\pi U_2) \tag{5.5}$$

等式(5.5)的变换被称为Box-Muller变换。

然而，利用(5.5)式的Box-Muller变换生成两个独立标准正态的计算效率不高，其原因是它需要计算正余弦两个三角函数。但是，我们可以通过直接计算随机角度的正余弦（而不是对于某随机数$U$，直接计算$2\pi U$的正余弦）来克服其费时的不足。实际上，我们注意到，如果$U$是$(0,1)$上的随机数，则$2U$是$(0,2)$上的随机数，$2U - 1$是$(-1,1)$上的随机数。于是，如果$U_1, U_2$是两个随机数，且令

$$V_1 = 2U_1 - 1$$
$$V_2 = 2U_2 - 1$$

则$(V_1, V_2)$在中心为原点、面积为4的正方形中均匀分布（见图5-3）。

现假设我们连续产生随机数对$(V_1, V_2)$，直至得到一对落入中心在原点半径为1的圆内，即满足$V_1^2 + V_2^2 \leq 1$。注意到满足此条件的$(V_1, V_2)$在此圆内均匀分布。如以$R$和$\Theta$记此点的极坐标，则不难验证，$R$与$\Theta$是独立的，且$R^2$在$(0,1)$上均匀分布（见习题21），$\Theta$在$(0,2\pi)$上均匀分布。因为此角度$\Theta$是随机的，故由上述可知，一个随机角度$\Theta$的正余弦函数可以如下计算：

$$\sin \Theta = \frac{V_2}{R} = \frac{V_2}{\sqrt{(V_1^2 + V_2^2)^{1/2}}}$$

$$\cos \Theta = \frac{V_1}{R} = \frac{V_1}{\sqrt{(V_1^2 + V_2^2)^{1/2}}}$$

其中$(V_1, V_2)$为满足条件的随机点。于是，综合Box-Muller变换(5.5)和上式，一对独立的标准正态随机变量可以如下生成：生成一个随机数$U$后，令

$$X = (-2 \ln U)^{1/2}\frac{V_1}{(V_1^2 + V_2^2)^{1/2}} \tag{5.6}$$

$$Y = (-2 \ln U)^{1/2}\frac{V_2}{(V_1^2 + V_2^2)^{1/2}}$$

另外，由于$R^2 = V_1^2 + V_2^2$在$(0,1)$上均匀分布且它与随机角度$\Theta$独立，故它可以替代(5.6)式中的随机数$U$。于是，如令$S = R^2$，则我们知

$$X = (-2 \ln S)^{1/2}\frac{V_1}{S^{1/2}} = V_1\left(\frac{-2 \ln S}{S}\right)^{1/2}$$

$$Y = (-2 \ln S)^{1/2}\frac{V_2}{S^{1/2}} = V_2\left(\frac{-2 \ln S}{S}\right)^{1/2}$$

是独立的标准正态随机变量，其中$(V_1, V_2)$是中心在原点半径为1的圆内的随机点，且$S = V_1^2 + V_2^2$。

综上所述，我们有如下的产生一对独立的标准正态随机变量的极坐标算法：

步骤1：生成随机数$U_1, U_2$。
步骤2：令$V_1 = 2U_1 - 1, V_2 = 2U_2 - 1, S = V_1^2 + V_2^2$。
步骤3：如果$S > 1$，则转至步骤1。
步骤4：产生独立的标准正态随机变量：

$$X = \sqrt{\frac{-2 \ln S}{S}}V_1, \quad Y = \sqrt{\frac{-2 \ln S}{S}}V_2$$

由于正方形内的随机点落入圆内的概率为$\pi/4$（圆与正方形面积之比），故极坐标法中步骤1所需的平均迭代次数为$4/\pi = 1.273$。于是，为生成两个独立的标准正态随机变量，平均来讲，共需2.546个随机数、一次对数、一次平方根、一次除法和4.546次乘法。

### 5.4 泊松过程的生成

如果我们希望生成一个参数$\lambda$的泊松过程的前$n$个事件发生的时间，则我们可以利用如下结论：此过程两个相继事件发生的间隔时间为参数$\lambda$的指数随机变量。于是，生成此过程的一种方法就是先生成这些间隔时间。如以$U_1, U_2, \cdots, U_n$记$n$个随机数，且令$X_i = -\frac{1}{\lambda}\ln U_i$，则$X_i$即可作为此泊松过程第$i-1$个和第$i$个事件发生的间隔时间。由于第$j$个事件发生的时间等于前$j$个间隔时间之和，则前$n$个事件发生的时间分别为$\sum_{i=1}^j X_i$, $j = 1, \cdots, n$。

如果我们想模拟此泊松过程时刻$T$前的状态，则我们可以利用上述方法相继生成其间隔时间，直至其和超过$T$为止。也就是说，我们可以利用下述算法模拟一个参数为$\lambda$的泊松过程在$(0,T)$时间段内的事件发生时间，其中$t$表示时间，$I$表示时间$t$内发生的事件数，$S(I)$表示最近事件发生的时间。

**一个参数为$\lambda$的泊松过程在时刻$T$前的状态模拟**

步骤1：$t = 0, I = 0$。
步骤2：生成一个随机数$U$。
步骤3：$t = t - \frac{1}{\lambda}\ln U$。如果$t > T$，停止。
步骤4：$I = I + 1, S(I) = t$。
步骤5：转至步骤2。

上述算法中$I$的最终值表示时间$T$内发生的事件数，$S(1), \cdots, S(I)$递增且表示$I$个事件发生的时间。

另一种模拟参数为$\lambda$的泊松过程时刻$T$前状态的方法是先模拟时刻$T$发生的事件总数$N(T)$。由于$N(T)$是一均值为$\lambda T$的泊松随机变量，这可以利用第4章所给的方法模拟它。如果$N(T)$的模拟值为$n$，则生成$n$个随机数$U_1, \cdots, U_n$，且取$\{TU_1, \cdots, TU_n\}$为时刻$T$时此泊松过程的事件时间集。

为验证上述方法的正确性，以$N(t)$记集合$\{TU_1, \cdots, TU_n\}$中小于$t$的元素个数，则我们必须说明$N(t), 0 \leq t \leq T$是一个泊松过程。为证明它是一个具有独立增量的过程，记$I_1, \cdots, I_r$为区间$[0,T]$中的$r$个不相交的时间区间。如果$TU_i$属于区间$I_i(i = 1, \cdots, r)$，则称第$i$个泊松事件为类型$i$。如果$p_i, i \leq r$表示区间$I_i$的长度与$T$之商，$p_{r+1} = 1 - \sum_{i=1}^r p_i$，由于$U_i, i \geq 1$独立，则$N(T)$个泊松事件中的每一个都可以独立地以概率$p_i$被归类到类型$i, i = 1, \cdots, r + 1$。于是，由2.8节的结论知，落在$r$个不相交区间的事件数$N_1, \cdots, N_r$是独立的泊松随机变量，且期望$E[N_i]$等于$\lambda$与区间$I_i$的长度之积。由此可知，$N(T), 0 \leq t \leq T$是一个具有独立增量的平稳过程。由于落在任一长度为$h$的区间内的事件数服从均值为$\lambda h$的泊松分布，故我们有

$$\lim_{h \to 0}\frac{P\{N(h) = 1\}}{h} = \lim_{h \to 0}\frac{\lambda h e^{-\lambda h}}{h} = \lambda$$

和

$$\lim_{h \to 0}\frac{P\{N(h) \geq 2\}}{h} = \lim_{h \to 0}\frac{1 - e^{-\lambda h} - \lambda h e^{-\lambda h}}{h} = 0$$

由此结论得证。

如果我们仅想模拟一个泊松随机变量过程事件发生时间的集合，则上述方法比利用其间隔时间服从指数分布的模拟方法更有效。然而，我们一般均希望事件发生时间是递增排序的，故我们还要将$TU_i, i = 1, \cdots, n$排序。

### 5.5 非齐次泊松过程的产生

如放松泊松过程中的平稳增量条件，则得到非齐次泊松过程。非齐次泊松过程是建模中常用计数过程中非常重要的一种。从概率的角度看，非齐次泊松过程的参数，即单位时间内事件出现的个数，并不需要是常数，而可以随时间变化。一般来说，对于一个用非齐次泊松过程来刻划的数学模型，我们很难得到它的解析解。于是，非齐次泊松过程的应用并不非常广泛。然而，由于我们可以用模拟方法分析这样的模型，故我们期望这样的数学模型会得到广泛的应用。

假设我们希望模拟强度函数为$\lambda(t)$的非齐次泊松过程时刻$T$前的状态。被称为稀松(thinning)或随机稀释法是我们给出的第一种方法。在选取满足

$$\lambda(t) \leq \lambda, \forall t \leq T$$

的$\lambda$之后，如在第2章证明的那样，我们可以通过随机选取参数为$\lambda$的泊松过程的事件发生时间来生成非齐次泊松过程。也就是说，如果一个参数为$\lambda$的泊松过程在时刻$t$发生的事件以概率$\lambda(t)/\lambda$被记录，则被记录到的事件列就构成一个强度为$\lambda(t), 0 \leq t \leq T$的非齐次泊松过程。于是，我们可以通过对泊松过程的事件进行随机计数来产生所需的非齐次泊松过程，其算法如下：

**在时刻$T$前一个泊松过程的产生**
步骤1：$t = 0, I = 0$。
步骤2：生成一个随机数$U$。
步骤3：$t = t - \frac{1}{\lambda}\ln U$。如果$t > T$，则停止。
步骤4：生成一个随机数$U$。
步骤5：如果$U \leq \lambda(t)/\lambda$，则令$I = I + 1, S(I) = t$。
步骤6：转至步骤2。

在上述算法中，$\lambda(t)$是强度函数，$\lambda$满足$\lambda(t) \leq \lambda, I$的最终值表示时刻$T$前发生的事件数，$S(1), \cdots, S(I)$是事件发生的时刻。

上述算法之所以被称为稀松法，是由于它仅取齐次泊松过程的部分事件。注意到，用指数分布来生成伽玛随机变量是最有效的。为证明这一点，假设我们想生成概率密度函数为

$$\lambda(s) \leq \lambda, 如果t_{i-1} \leq s < t_i, i = 1, \cdots, k + 1$$

为生成区间$(t_{i-1}, t_i)$上的非齐次泊松过程，我们先生成参数为$\lambda_i$的指数随机变量，然后以概率$\lambda(s)/\lambda_i$接受在$s \in (t_{i-1}, t_i)$时刻发生的事件。由指数分布的无记忆性可知，从一个子区间到另一个子区间，其效率并没有损失。也就是说，如果现在我们考虑的区间为$(t_{i-1}, t_i)$且以$X$记参数为$\lambda_i$的指数分布随机变量，则$t + X > t_i$，则$\lambda_i [X - (t_i - t)]/\lambda_{i+1}$可作为下一区间的参数为$\lambda_{i+1}$的指数分布随机变量。

对于强度$\lambda(s)$满足关系式(5.7)的非齐次泊松过程，我们可用下述算法来生成参数为$\lambda_i$的指数随机变量，其中$t$表示当前时刻，$J$表示区间（如$t_{j-1} \leq t < t_j$，则$J = j$），$I$表示前面发生的事件数，$S(1), \cdots, S(I)$表示事件发生的时间。

**在时刻$T$前一个非齐次泊松过程的产生**
步骤1：$t = 0, J = 1, I = 0$。
步骤2：生成一个随机数$U$且令$X = -\frac{1}{\lambda_J}\ln U$。
步骤3：如果$t + X > t_J$，则转至步骤8。
步骤4：$t = t + X$。
步骤5：生成一个随机数$U$。
步骤6：如果$U \leq \lambda(t)/\lambda_J$，则$I = I + 1, S(I) = t$。
步骤7：转至步骤2。
步骤8：如果$J = k + 1$，则停止。
步骤9：$X = (X - t_J + t)\lambda_J/\lambda_{J+1}, t = t_J, J = J + 1$。
步骤10：转至步骤3。

对于区间$(t_{i-1}, t_i)$，记

$$\lambda_i \equiv \inf\{\lambda(s): t_{i-1} \leq s < t_i\}$$

且假设$\lambda_i > 0$。此时，我们将不直接应用稀松算法，而是先在第$i$个区间产生参数为$\lambda_i$的泊松过程，之后再模拟强度为$\lambda(s) = \lambda(s) - \lambda_i[s \in (t_{i-1}, t_i)]$的非齐次泊松过程。（为模拟此泊松过程而生成的最后一个指数随机变量，虽然它超出了$T$要求区间，但也不必浪费，因为它经适当变换后还可重新再用。）把上面产生的两个过程叠加起来，则得到了此区间所需的过程。我们这样做的原因是它将节省产生均值为$\lambda_i(t_i - t_{i-1})$的泊松分布随机变量所需的均匀分布随机变量。例如，我们考虑

$$\lambda(s) = 10 + s, \quad 0 < s < 1$$

利用$\lambda = 11$的稀松法，可能生成其期望数为11的事件列，其中每一个事件都需用一个随机数来确定此事件是否被接受。换言之，为产生参数为10的泊松过程与参数为$\lambda(s) = s$, $0 < s < 1$的非齐次泊松过程，把二者合并后得到的事件列与所要求的同分布，但需验证被接受事件数的期望等于11。

产生强度为$\lambda(t), t > 0$的非齐次泊松过程的另一个方法是直接产生相继事件的发生时间。以$S_1, S_2, \cdots$记此过程相继事件的发生时间。由于事件在时间$s$发生，故它独立于时刻$s$前出现的事件，且到下一个事件发生的时间间隔的分布$F_s$为

$$F_s(x) = P\{从s到下一事件的时间间隔小于x|在时刻s发生一事件\}$$
$$= P\{下一事件在时间s和s+x间发生|在时刻s发生一事件\}$$
$$= P\{下一事件在时间s和s+x间发生\}由独立增量$$
$$= 1 - P\{在时间s和s+x间没有事件发生\}$$
$$= 1 - \exp\left(-\int_s^{s+x}\lambda(y)dy\right) \tag{5.8}$$

于是，我们可以由分布$F_0$产生$S_1$，如记其值为$s_1$，则$S_2$的模拟值为$s_2$加上由分布$F_{s_1}$产生的随机数，依此类推，就得到了事件发生时间$S_1, S_2, \cdots$的模拟值。上述由分布进行随机模拟的方法背定依赖其分布形式。在下面的例子中，分布$F_s$的逆函数容易求得，故我们可以利用逆变换法。

**例5h** 设$\lambda(t) = 1/(t + a), t \geq 0$，其中$a$为正常数。则

$$\int_0^x \lambda(s + y)dy = \int_0^x \frac{1}{s + y + a}dy = \ln\left(\frac{x + s + a}{s + a}\right)$$

于是，由等式(5.8)有

$$F_s(x) = 1 - \frac{s + a}{x + s + a}$$

为求其逆，设$x = F_s^{-1}(u)$，则

$$u = F_s(x) = 1 - \frac{s + a}{x + s + a}$$

或等价地有

$$x = \frac{u(s + a)}{1 - u}$$

即

$$F_s^{-1}(u) = (s + a)\frac{u}{1 - u}$$

因此，相继事件的发生时间$S_1, S_2, \cdots$可如下产生：生成随机数$U_1, U_2, \cdots$后，依次取

$$S_1 = \frac{aU_1}{1 - U_1}$$

$$S_2 = S_1 + (S_1 + a)\frac{U_2}{1 - U_2} = \frac{S_1 + aU_2}{1 - U_2}$$

一般地有

$$S_j = S_{j-1} + (S_{j-1} + a)\frac{U_j}{1 - U_j} = \frac{S_{j-1} + aU_j}{1 - U_j}, \quad j \geq 2$$

### 习题

1. 给出具有下述生成密度函数的随机变量的方法.

$$f(x) = e^x/(e - 1), 0 \leq x \leq 1$$

2. 给出具有如下生成密度函数的随机变量的方法.

$$f(x) = \begin{cases}
\frac{x - 2}{2} & 如果2 \leq x \leq 3 \\
\frac{2 - x/3}{2} & 如果3 \leq x \leq 6
\end{cases}$$

3. 应用逆变换法生成如下分布函数的随机变量.

$$F(x) = \frac{x^2 + x}{2}, 0 \leq x \leq 1$$

4. 给出生成分布函数为

$$F(x) = 1 - \exp(-\alpha x^{\beta}), 0 < x < \infty$$

的随机变量方法. (具有上述分布函数的随机变量称为威布尔随机变量.)

5. 给出具有如下生成密度函数的随机变量的方法.

$$f(x) = \begin{cases}
e^{2x} & -\infty < x < 0 \\
e^{-2x} & 0 < x < \infty
\end{cases}$$

6. 设$X$为均值1的指数随机变量. 给出模拟分布为给定$X < 0.05$下$X$的条件分布的随机变量的有效算法, 其密度为

$$f(x) = \frac{e^{-x}}{1 - e^{-0.05}}, 0 < x < 0.05$$

生成1000个这样的随机变量, 并用它们估计$E[X|X < 0.05]$. 另外, 求$E[X|X < 0.05]$的精确值.

7. (复合法) 设产生分布为$F_i$, $i = 1,2,\cdots,n$的随机变量相对容易. 我们如何产生分布函数为

$$F(x) = \sum_{i=1}^n p_i F_i(x)$$

的随机变量? 其中$p_i$, $i = 1,\cdots,n$是和为1的非负数.

8. 利用习题7的结论, 给出生成具有如下分布函数的随机变量的算法:
(a) $F(x) = \frac{x+x^3+x^5}{3}$, $0 \leq x \leq 1$.
(b) $F(x) = \begin{cases}\frac{1-e^{-2x}+2x}{3} & 如果0 < x < 1 \\ \frac{3-e^{-2x}}{3} & 如果1 < x < \infty\end{cases}$
(c) $F(x) = \sum_{i=1}^n \alpha_i x^i$, $0 \leq x \leq 1$, 其中$\alpha_i \geq 0, \sum_{i=1}^n \alpha_i = 1$.

9. 给出生成分布函数为

$$F(x) = \int_0^{\infty} x^y e^{-y}dy, 0 \leq x \leq 1$$

的随机变量的算法. \[提示: 考虑利用习题7中的复合法. 特别地, 以$F$记$X$的分布函数, 且假设给定$Y = y$下$X$的条件分布为

$$P\{X \leq x|Y = y\} = x^y, 0 \leq x \leq 1$$
\]
10. 设一个火灾保险公司有1000个客户, 且每个客户在下月提出索赔的概率为0.05, 并相互独立, 其索赔金额是独立的均值为800美元的指数随机变量. 请用模拟方法估计其索赔金额之和超过5万美元的概率.

11. 给出一种生成3个指数随机变量的算法, 并与例5c后的成对生成随机变量的算法进行计算量的比较.

12. 假设容易生成分布为$F_i$, $i = 1,\cdots,n$的随机变量. 我们如何生成来自如下分布的随机变量?
(a) $F(x) = \prod_{i=1}^n F_i(x)$.
(b) $F(x) = 1 - \prod_{i=1}^n [1 - F_i(x)]$.
[提示: 如果$X_i, i = 1,\cdots,n$独立, 且$X_i$的分布为$F_i$, 则什么样的随机变量的分布为$F$?]

13. 利用筛选法和习题12的结果, 给出两种有别于逆变换法的方法生成具有如下分布函数的随机变量.

$$F(x) = x^n, 0 \leq x \leq 1$$

14. 设$G$为一分布函数, $g$为其密度. 对于常数$a < b$, 我们希望生成具有如下分布函数的随机变量.

$$F(x) = \frac{G(x) - G(a)}{G(b) - G(a)}, a \leq x \leq b$$

(a) 如果$X$的分布函数为$G$, 则$F$是给定什么条件下$X$的条件分布?
(b) 证明此时的筛选法为: 生成分布函数为$G$的随机变量$X$后, 如果它落在$a$与$b$之间, 则接受它.

15. 给出生成密度函数为

$$f(x) = xe^{-x}, 0 \leq x < \infty$$

的随机变量的两种方法, 并比较二者的效率.

16. 设随机变量$X$的密度函数为

$$f(x) = \frac{1}{2}x^2e^{-x}, x > 0$$

现用带有参数为$\lambda$的指数密度的筛选法生成此模拟值. 并求最优的$\lambda$值使此算法的选代次数最少.

17. 给出生成密度函数为

$$f(x) = 30(x^2 - 2x^3 + x^4), 0 \leq x \leq 1$$

的随机变量的方法, 并讨论此方法的效率.

18. 给出生成密度函数为

$$f(x) = \frac{1}{0.000~336}x(1 - x)^3, 0.8 < x < 1$$

的随机变量$X$的有效方法.

19. 在例5f中, 我们用带有参数为1的指数密度函数的筛选法给出了生成一个正态随机变量的方法. 证明在所有的指数密度函数$g(x) = \lambda e^{-\lambda x}$中, $\lambda = 1$使上述算法的选代次数最少.

20. 编写用例5f中的方法生成正态随机变量的程序.

21. 设$(X,Y)$在半径为1的圆上均匀分布, 证明: 如果$R$是点$(X,Y)$到圆心的距离, 则$R^2$在$(0,1)$上均匀分布.

22. 编写生成参数为$\lambda$的泊松过程在时刻$T$前状态的程序.

23. 某项工作的完成必须依次经历$k$道工序, 一名工人完成第$i$道工序所需的时间是一个参数为$\lambda_i$的指数随机变量, $i = 1,\cdots,k$. 然而, 一名工人在完成第$i$道工序后接着从事下一道工序的概率仅为$\alpha_i$, $i = 1,2,\cdots,k - 1$, 即一名工人在完成第$i$道工序后将以概率$1 - \alpha_i$不再从事这项工作. 如以$X$记一名工人从事此项工作的时间总量, 则$X$被称为Cox随机变量. 编写模拟此随机变量的程序.

24. 设公共汽车到达某项比赛场所的时间服从参数5(即每小时有5个事件发生)的泊松过程, 每辆公共汽车均等可能地有20,21,\cdots,40个球迷乘坐, 且不同公共汽车上的球迷数量是相互独立的. 编写模拟$t = 1$时刻抵达此比赛场所的球迷数的程序.

25. (a) 利用稀松法编写模拟前10个时间单元内强度为

$$\lambda(t) = 3 + \frac{4}{t + 1}$$

的非齐次泊松过程的程序.
(b) 给出一种改进本例稀松法的方法.

26. 给出有效模拟前10个时间单元内强度为

$$\lambda(t) = \begin{cases}
\frac{t}{5} & 0 < t < 5 \\
1 + 5(t - 5) & 5 < t < 10
\end{cases}$$

的非齐次泊松过程的算法.

### 参考文献

[1] Dagpunar, T., Principles of Random Variate Generation. Clarendon Press, Oxford, 1988.

[2] Devroye, L., Nonuniform Random Variate Generation. Springer-Verlag, New York, 1986.

[3] Fishman, G. S., Principles of Discrete Event Simulation. Wiley, New York, 1978.

[4] Knuth, D., The Art of Computer Programming, Vol.2, 2nd ed., Seminumerical Algorithms, Addison-Wesley, Reading, MA, 2000.

[5] Law, A. M., and W. D. Kelton, Simulation Modelling and Analysis, 3rd ed. McGraw-Hill, New York, 1997.

[6] Lewis, P. A. W., and G. S. Shedler, Simulation of Nonhomogeneous Poisson Processes by Thinning, Nav. Res. Log. Quart., 26, 403-413, 1979.

[7] Marsaglia, G., Generating Discrete Random Variables in a Computer, Commun. Assoc. Comput. Mach., 6, 37-38, 1963.

[8] Morgan, B. J. T., Elements of Simulation. Chapman and Hall, London, 1983.

[9] Ripley, B. D., Computer Generation of Random Variables:A Tutorial, Inst. Statist. Rev., 51, 301-319, 1983.

[10] Ripley, B. D., Stochastic Simulation. Wiley, New York, 1986.

[11] Rubenstein, R. Y., Simulation and the Monte Carlo Method. Wiley, New York, 1981.

[12] Schmeiser, B. W., Random Variate Generation, a Survey, Proc. 1980 Winter Simulation Conf., Orlando, FL: pp.79-104, 1980.