位图用1位来表示一个页框是否空闲,所以占用的空间大小为$2^{22}b=2^{19}B=512KB$。
17. A
位图法利用一个二进制位来表示磁盘中一个盘块的使用情况,磁盘上的所有盘块都有一个二进制位与之对应。当其值为“0”时,表示对应的盘块空闲;当其值为“1”时,表示已分配。位图所占空间的大小只取决于外存空间的总大小(盘块数量),而与当前空闲块数量无关。

### 二、综合应用题
**01.【解答】**
1) 根据位示图的位置($i$,$j$),得出盘块的序号$b=i×16+j$;用$C$表示柱面号,$H$表示磁头号,$S$表示扇区号,则有
$C = b/(20×8), H = (b\%(20×8))/8, S=b\%8$
2) 分配:顺序扫描位示图,找出1个其值为“0”的二进制位(“0”表示空闲),利用上述公式将其转换成相应的序号$b$,并修改位示图,置($i$,$j$)=1。
回收:将回收盘块的盘块号换算成位示图中的$i$和$j$,转换公式为
$b = C×20×8+ H×8+S, i=b/16, j=b\%16$
最后将计算出的($i$,$j$)在位示图中置“0”。
**02.【解答】**
1) 整个磁盘空间的存储块(扇区)数量为$4×16×100=6400$个。
2) 位示图应为6400个位,若用字长为32位($n=32$)的单元来构造位示图,则需要$6400/32=200$个字。
3) 位示图中第18个字的第16位($i=18,j=16$)对应的块号为$32×(18-1)+16=560$。

### 4.4 本章疑难点
1. 文件的物理分配方式的比较
文件的三种物理分配方式的比较如表4.3所示。

表4.3 文件三种分配方式的比较
| | **访问第n条记录** | **优点** | **缺点** |
| :--- | :--- | :--- | :--- |
| 连续分配 | 需访问磁盘1次 | 顺序存取时速度快,文件定长时可根据文件起始地址及记录长度进行随机访问 | 文件存储要求连续的存储空间,会产生碎片,不利于文件的动态扩充 |
| 链接分配 | 需访问磁盘$n$次 | 可解决外存的碎片问题,提高外存空间的利用率,动态增长较方便 | 只能按照文件的指针链顺序访问,查找效率低,指针信息存放消耗外存空间 |
| 索引分配 | $m$级需访问磁盘$m+1$次 | 可以随机访问,文件易于增删 | 索引表增加存储空间的开销,索引表的查找策略对文件系统效率影响较大 |

2. 文件打开的过程描述
① 检索目录,要求打开的文件应该是已经创建的文件,它应登记在文件目录中,否则会出错。在检索到指定文件后,就将其磁盘 iNode 复制到活动 iNode 表中。
② 将参数 mode所给出的打开方式与活动iNode中在创建文件时所记录的文件访问权限相比较,若合法,则此次打开操作成功。
③ 当打开合法时,为文件分配用户打开文件表表项和系统打开文件表表项,并为后者设置初值,通过指针建立表项与活动 iNode 之间的联系,再将文件描述符fd返回给调用者。

---
## 第5章 输入/输出管理

**【考纲内容】**
(一) 输入/输出(I/O)管理基础
设备:设备的基本概念,设备的分类,I/O接口
I/O 控制方式:轮询方式,中断方式,DMA 方式
I/O 软件层次结构:中断处理程序,驱动程序,设备独立性软件,用户层 I/O 软件
输入/输出应用程序接口:字符设备接口,块设备接口,网络设备接口,阻塞/非阻塞 I/O
(二) 设备独立软件
缓冲区管理;设备分配与回收;假脱机技术(SPOOLing);设备驱动程序接口
(三) 外存管理
磁盘:磁盘结构,格式化,分区,磁盘调度算法
固态硬盘:读/写性能特效,磨损均衡

**【复习提示】**
本章的内容较为分散,重点掌握I/O接口、I/O软件、三种 I/O 控制方式、高速缓存与缓冲区、SPOOLing 技术,磁盘特性和调度算法。本章很多知识点与硬件高度相关,建议与计算机组成原理的对应章节结合复习。已复习过计算机组成原理的读者遇到比较熟悉的内容时也可适当跳过。另外,未复习过计算机组成原理的读者可能觉得本章的习题较难,但无须担心。
本章内容在历年统考真题中所占的比重不大,若统考中出现本章的题目,则基本上可以断定一定较为简单,看过相关内容的读者就一定会做,而未看过的读者基本上只能靠“蒙”。考研成功的秘诀是复习要反复多次且全面,偷工减料是要吃亏的,希望读者重视本章的内容。

### 5.1 I/O 管理概述
在学习本节时,请读者思考I/O 管理要完成哪些功能。

### 5.1.1 I/O 设备
I/O 设备管理是操作系统设计中最凌乱也最具挑战性的部分。它包含了很多领域的不同设备及与设备相关的应用程序,因此很难有一个通用且一致的设计方案。

1. 设备的分类
I/O设备是指可以将数据输入计算机的外部设备,或者可以接收计算机输出数据的外部设备。I/O 设备的类型繁多,从不同的角度可将它们分为不同的类型。
按信息交换的单位分类,I/O 设备可分为: