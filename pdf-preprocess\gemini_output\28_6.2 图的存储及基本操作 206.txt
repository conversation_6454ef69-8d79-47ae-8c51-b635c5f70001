成,且其中之一只含一个顶点,另一个为完全图。其中只含一个顶点的子图没有边,另一个完全图的边数为$n(n-1)/2=28$,得$n=8$。所以该图至少有$1+8=9$个顶点。

## 6.2 图的存储及基本操作
图的存储必须要完整、准确地反映顶点集和边集的信息。根据不同图的结构和算法,采用不同的存储方式将对程序的效率产生相当大的影响,因此所选的存储结构应适合于待求解的问题。

### 6.2.1 邻接矩阵法
所谓邻接矩阵存储,是指用一个一维数组存储图中顶点的信息,用一个二维数组存储图中边的信息(各顶点之间的邻接关系),存储顶点之间邻接关系的二维数组称为邻接矩阵。
顶点数为$n$的图$G=(V,E)$的邻接矩阵$A$是$n \times n$的,将$G$的顶点编号为$v_1, v_2, \dots, v_n$,则
$$
A[i][j] = \begin{cases} 1, & (v_i, v_j) \text{或} <v_i, v_j> \text{是} E(G) \text{中的边} \\ 0, & (v_i, v_j) \text{或} <v_i, v_j> \text{不是} E(G) \text{中的边} \end{cases}
$$

**命题追踪** 图的邻接矩阵存储及相互转换(2011、2015、2018)

对带权图而言,若顶点$v_i$和$v_j$之间有边相连,则邻接矩阵中对应项存放着该边对应的权值,若顶点$v_i$和$v_j$不相连,则通常用$0$或$\infty$来代表这两个顶点之间不存在边:
$$
A[i][j] = \begin{cases} W_{ij}, & (v_i, v_j) \text{或} <v_i, v_j> \text{是} E(G) \text{中的边} \\ 0 \text{或} \infty, & (v_i, v_j) \text{或} <v_i, v_j> \text{不是} E(G) \text{中的边} \end{cases}
$$
有向图、无向图和网对应的邻接矩阵示例如图6.5所示。

(a) 有向图$G_1$及其邻接矩阵
$$
A_1 = \begin{vmatrix} 0 & 1 & 1 & 0 \\ 0 & 0 & 0 & 0 \\ 0 & 0 & 0 & 1 \\ 1 & 0 & 0 & 0 \end{vmatrix}
$$

(b) 无向图$G_2$及其邻接矩阵
$$
A_2 = \begin{vmatrix} 0 & 1 & 0 & 1 & 1 \\ 1 & 0 & 1 & 0 & 1 \\ 0 & 1 & 0 & 1 & 1 \\ 1 & 0 & 1 & 0 & 0 \\ 1 & 1 & 1 & 0 & 0 \end{vmatrix}
$$

(c) 网及其邻接矩阵(对角线元素也经常用0表示)
$$
A_3 = \begin{vmatrix} \infty & 5 & \infty & \infty & \infty \\ \infty & \infty & 4 & \infty & \infty \\ \infty & \infty & \infty & 8 & 9 \\ 6 & \infty & \infty & \infty & 5 \\ \infty & 3 & \infty & \infty & \infty \end{vmatrix}
$$

图6.5 有向图、无向图及网的邻接矩阵

**命题追踪** (算法题)邻接矩阵的遍历及顶点的度的计算(2021、2023)

图的邻接矩阵存储结构定义如下:
```c
#define MaxVertexNum 100            //顶点数目的最大值
typedef char VertexType;            //顶点对应的数据类型
typedef int EdgeType;               //边对应的数据类型
typedef struct {
    VertexType vex[MaxVertexNum];   //顶点表
    EdgeType edge[MaxVertexNum][MaxVertexNum]; //邻接矩阵, 边表
    int vexnum, arcnum;             //图的当前顶点数和边数
} MGraph;
```

**注意**
1. 在简单应用中,可直接用二维数组作为图的邻接矩阵(顶点信息等均可省略)。
2. 当邻接矩阵的元素仅表示相应边是否存在时,EdgeType可用值为0和1的枚举类型。
3. 无向图的邻接矩阵是对称矩阵,对规模特大的邻接矩阵可采用压缩存储。
4. 邻接矩阵表示法的空间复杂度为$O(n^2)$,其中$n$为图的顶点数。

**命题追踪** 邻接矩阵的遍历的时间复杂度(2021)

图的邻接矩阵存储表示法具有以下特点:
1. 无向图的邻接矩阵一定是一个对称矩阵(并且唯一)。因此,在实际存储邻接矩阵时只需存储上(或下)三角矩阵的元素。

**命题追踪** 基于邻接矩阵的顶点的度的计算(2013、2021、2023)
2. 对于无向图,邻接矩阵的第$i$行(或第$i$列)非零元素(或非$\infty$元素)的个数正好是顶点$i$的度$TD(v_i)$。
3. 对于有向图,邻接矩阵的第$i$行非零元素(或非$\infty$元素)的个数正好是顶点$i$的出度$OD(v_i)$;第$i$列非零元素(或非$\infty$元素)的个数正好是顶点$i$的入度$ID(v_i)$。
4. 用邻接矩阵存储图,很容易确定图中任意两个顶点之间是否有边相连。但是,要确定图中有多少条边,则必须按行、按列对每个元素进行检测,所花费的时间代价很大。
5. 稠密图(边数较多的图)适合采用邻接矩阵的存储表示。

**命题追踪** 计算$A^2$并说明$A^n[i][j]$的含义(2015)
6. 设图$G$的邻接矩阵为$A$,$A^n$的元素$A^n[i][j]$等于由顶点$i$到顶点$j$的长度为$n$的路径的数目。该结论了解即可,证明方法可参考离散数学教材。

### 6.2.2 邻接表法
当一个图为稀疏图时,使用邻接矩阵法显然会浪费大量的存储空间,而图的邻接表法结合了顺序存储和链式存储方法,大大减少了这种不必要的浪费。
所谓邻接表,是指对图$G$中的每个顶点$v_i$建立一个单链表,第$i$个单链表中的结点表示依附于顶点$v_i$的边(对于有向图则是以顶点$v_i$为尾的弧),这个单链表就称为顶点$v_i$的边表(对于有向图则称为出边表)。边表的头指针和顶点的数据信息采用顺序存储,称为顶点表,所以在邻接表中存在两种结点:顶点表结点和边表结点,如图6.6所示。

| 顶点域 | 边表头指针 |
| --- | --- |
| data | firstarc |
| **顶点表结点** |

| 邻接点域 | 指针域 |
| --- | --- |
| adjvex | nextarc |
| **边表结点** |

图6.6 顶点表和边表结点结构

顶点表结点由两个域组成:顶点域(data)存储顶点的相关信息,边表头指针域(firstarc)指向第一条边的边表结点。边表结点至少由两个域组成:邻接点域(adjvex)存储与头结点顶点$v_i$邻接的顶点编号,指针域(nextarc)指向下一条边的边表结点。

**命题追踪** 图的邻接表存储的应用(2014)

无向图和有向图的邻接表的实例分别如图6.7和图6.8所示。

(a)无向图G
(b)无向图G的邻接表的表示
1 -> 2 -> 5 -> 4
2 -> 1 -> 5 -> 3 -> 4
3 -> 2 -> 4
4 -> 2 -> 5 -> 3
5 -> 1 -> 2

图6.7 无向图邻接表表示法实例

(a)有向图G
(b)有向图G的邻接表的表示
1 -> 2 -> 4
2 -> 5
3 -> 5
4 -> 2
5 -> 4

图6.8 有向图邻接表表示法实例

图的邻接表存储结构定义如下:
```c
#define MaxVertexNum 100        //图中顶点数目的最大值
typedef struct ArcNode {        //边表结点
    int adjvex;                 //该弧所指向的顶点的位置
    struct ArcNode *nextarc;    //指向下一条弧的指针
    //InfoType info;            //网的边权值
} ArcNode;
typedef struct VNode {          //顶点表结点
    VertexType data;            //顶点信息
    ArcNode *firstarc;          //指向第一条依附该顶点的弧的指针
} VNode, AdjList[MaxVertexNum];
typedef struct {                //邻接表
    AdjList vertices;           //图的顶点数和弧数
    int vexnum, arcnum;         //ALGraph 是以邻接表存储的图类型
} ALGraph;
```
图的邻接表存储方法具有以下特点:
1. 若$G$为无向图,则所需的存储空间为$O(|V|+2|E|)$;若$G$为有向图,则所需的存储空间为$O(|V|+|E|)$。前者的倍数2是因为在无向图中,每条边在邻接表中出现了两次。

**命题追踪** 邻接矩阵法和邻接表法的适用性差异(2011)
2. 对于稀疏图(边数较少的图),采用邻接表表示将极大地节省存储空间。
3. 在邻接表中,给定一个顶点,能很容易地找出它的所有邻边,因为只需要读取它的邻接表。在邻接矩阵中,相同的操作则需要扫描一行,花费的时间为$O(n)$。但是,若要确定给定的两个顶点间是否存在边,则在邻接矩阵中可以立刻查到,而在邻接表中则需要在相应结点对应的边表中查找另一结点,效率较低。
4. 在无向图的邻接表中,求某个顶点的度只需计算其邻接表中的边表结点个数。在有向图的邻接表中,求某个顶点的出度只需计算其邻接表中的边表结点个数;但求某个顶点$x$的入度则需遍历全部的邻接表,统计邻接点(adjvex)域为$x$的边表结点个数。
5. 图的邻接表表示并不唯一,因为在每个顶点对应的边表中,各边结点的链接次序可以是任意的,它取决于建立邻接表的算法及边的输入次序。

### 6.2.3 十字链表
十字链表是有向图的一种链式存储结构。在十字链表中,有向图的每条弧用一个结点(弧结点)来表示,每个顶点也用一个结点(顶点结点)来表示。两种结点的结构如下所示。

**弧结点**
| tailvex | headvex | hlink | tlink | (info) |
|---|---|---|---|---|

**顶点结点**
| data | firstin | firstout |
|---|---|---|

弧结点中有5个域: $tailvex$域和$headvex$域分别存放弧尾和弧头这两个顶点的编号;头链域$hlink$指向弧头相同的下一条弧;尾链域$tlink$指向弧尾相同的下一条弧; $info$域存放该弧的相关信息。这样,弧头相同的弧在同一个链表上,弧尾相同的弧也在同一个链表上。
顶点结点中有3个域: $data$域存放该顶点的数据信息,如顶点名称; $firstin$域指向以该顶点为弧头的第一条弧; $firstout$域指向以该顶点为弧尾的第一条弧。
图6.9为有向图的十字链表表示法。

(a) (图示为一个有向图 V1,V2,V3,V4)
(b) (图示为十字链表的表示)

图6.9 有向图的十字链表表示(弧结点省略 info 域)

注意,顶点结点之间是顺序存储的,弧结点省略了 info 域。
在十字链表中,既容易找到以$V_i$为尾的弧,也容易找到以$V_j$为头的弧,因而容易求得顶点的出度和入度。图的十字链表表示是不唯一的,但一个十字链表表示唯一确定一个图。

### 6.2.4 邻接多重表
邻接多重表是无向图的一种链式存储结构。在邻接表中,容易求得顶点和边的各种信息,但求两个顶点之间是否存在边而执行删除边等操作时,需要分别在两个顶点的边表中遍历,效率较低。与十字链表类似,在邻接多重表中,每条边用一个结点表示,其结构如下所示。

| ivex | ilink | jvex | jlink | (info) |
|---|---|---|---|---|

其中, $ivex$域和$jvex$域存放该边依附的两个顶点的编号; $ilink$域指向依附于顶点$ivex$的下一条边; $jlink$域指向依附于顶点$jvex$的下一条边, $info$域存放该边的相关信息。
每个顶点也用一个结点表示,它由如下所示的两个域组成。

| data | firstedge |
|---|---|

其中, $data$域存放该顶点的相关信息, $firstedge$域指向依附于该顶点的第一条边。
在邻接多重表中,所有依附于同一顶点的边串联在同一链表中,因为每条边依附于两个顶点,所以每个边结点同时链接在两个链表中。对无向图而言,其邻接多重表和邻接表的差别仅在于,同一条边在邻接表中用两个结点表示,而在邻接多重表中只有一个结点。

**命题追踪** 图的邻接多重表表示的分析(2024)

图6.10为无向图的邻接多重表表示法。邻接多重表的各种基本操作的实现和邻接表类似。
(图6.10 无向图的邻接多重表表示 (边结点省略 info 域))

图的四种存储方式的总结如表6.1所示。

**表6.1 图的四种存储方式的总结**
| | 邻接矩阵 | 邻接表 | 十字链表 | 邻接多重表 |
|:---|:---|:---|:---|:---|
| 空间复杂度 | $O(|V|^2)$ | 无向图: $O(|V|+2|E|)$ <br> 有向图: $O(|V|+|E|)$ | $O(|V|+|E|)$ | $O(|V|+|E|)$ |
| 找相邻边 | 遍历对应行或列的时间复杂度为$O(|V|)$ | 找有向图的入度必须遍历整个邻接表 | 很方便 | 很方便 |
| 删除边或顶点 | 删除边很方便,删除顶点需要大量移动数据 | 无向图中删除边或顶点都不方便 | 很方便 | 很方便 |
| 适用于 | 稠密图 | 稀疏图和其他 | 只能存有向图 | 只能存无向图 |
| 表示方式 | 唯一 | 不唯一 | 不唯一 | 不唯一 |

### 6.2.5 图的基本操作
图的基本操作是独立于图的存储结构的。而对于不同的存储方式,操作算法的具体实现会有着不同的性能。在设计具体算法的实现时,应考虑采用何种存储方式的算法效率会更高。
图的基本操作主要包括(仅抽象地考虑,所以忽略各变量的类型):
*   `Adjacent(G,x,y)`: 判断图$G$是否存在边$<x,y>$或$(x,y)$。
*   `Neighbors(G,x)`: 列出图$G$中与结点$x$邻接的边。
*   `InsertVertex(G,x)`: 在图$G$中插入顶点$x$。
*   `DeleteVertex(G,x)`: 从图$G$中删除顶点$x$。
*   `AddEdge(G,x,y)`: 若无向边$(x,y)$或有向边$<x,y>$不存在,则向图$G$中添加该边。
*   `RemoveEdge(G,x,y)`: 若无向边$(x,y)$或有向边$<x,y>$存在,则从图$G$中删除该边。
*   `FirstNeighbor(G,x)`: 求图$G$中顶点$x$的第一个邻接点,若有则返回顶点号。若$x$没有邻接点或图中不存在$x$,则返回-1。
*   `NextNeighbor(G,x,y)`: 假设图$G$中顶点$y$是顶点$x$的一个邻接点,返回除$y$外顶点$x$的下一个邻接点的顶点号,若$y$是$x$的最后一个邻接点,则返回-1。
*   `Get_edge_value(G,x,y)`: 获取图$G$中边$(x,y)$或$<x,y>$对应的权值。
*   `Set_edge_value(G,x,y,v)`: 设置图$G$中边$(x,y)$或$<x,y>$对应的权值为$v$。

此外,还有图的遍历算法:按照某种方式访问图中的每个顶点且仅访问一次。图的遍历算法包括深度优先遍历和广度优先遍历,具体见下一节的内容。

### 6.2.6 本节试题精选
**一、单项选择题**
01. 下列关于图的存储结构的说法中,错误的是()。
    A. 使用邻接矩阵存储一个图时,在不考虑压缩存储的情况下,所占用的存储空间大小只与图中的顶点数有关,与边数无关
    B. 邻接表只用于有向图的存储,邻接矩阵适用于有向图和无向图
    C. 若一个有向图的邻接矩阵的对角线以下的元素为0,则该图的拓扑序列必定存在
    D. 存储无向图的邻接矩阵是对称的,所以只需存储邻接矩阵的下(或上)三角部分
02. 若图的邻接矩阵中主对角线上的元素皆为0,其余元素全为1,则该图一定()。
    A. 是无向图 B. 是有向图 C. 是完全图 D. 不是带权图
03. 在含有$n$个顶点和$e$条边的无向图的邻接矩阵中,零元素的个数为()。
    A. $e$ B. $2e$ C. $n^2-e$ D. $n^2-2e$
04. 带权有向图$G$用邻接矩阵存储,则$v_i$的入度等于邻接矩阵中()。
    A. 第$i$行非$\infty$的元素个数
    B. 第$i$列非$\infty$的元素个数
    C. 第$i$行非$\infty$且非0的元素个数
    D. 第$i$列非$\infty$且非0的元素个数
05. 一个有$n$个顶点的图用邻接矩阵$A$表示,若图为有向图,顶点$v_j$的入度是();若图为无向图,顶点$v_i$的度是()。
    A. $\sum_{i=1}^{n}A[i][j]$ B. $\sum_{j=1}^{n}A[i][j]$
    C. $\sum_{i=1}^{n}A[j][i]$ D. $\sum_{j=1}^{n}A[j][i]$或$\sum_{j=1}^{n}A[i][j]$
06. 从邻接矩阵$A=\begin{bmatrix} 0 & 1 & 0 \\ 1 & 0 & 1 \\ 0 & 1 & 0 \end{bmatrix}$可以看出,该图共有(①)个顶点;若是有向图,则该图共有(②)条弧;若是无向图,则共有(③)条边。
    ① A. 9 B. 3 C. 6 D. 1 E. 以上答案均不正确
    ② A. 5 B. 4 C. 3 D. 2 E. 以上答案均不正确
    ③ A. 5 B. 4 C. 3 D. 2 E. 以上答案均不正确
07. 以下关于图的存储结构的叙述中,正确的是()。
    A. 一个图的邻接矩阵表示唯一,邻接表表示唯一
    B. 一个图的邻接矩阵表示唯一,邻接表表示不唯一
    C. 一个图的邻接矩阵表示不唯一,邻接表表示唯一
    D. 一个图的邻接矩阵表示不唯一,邻接表表示不唯一
08. 矩阵$A$是有向图$G$的邻接矩阵,若矩阵$A^2$的某元素$a_{ij}^2=3$,则说明()。
    A. 从顶点$i$到$j$存在3条长度为2的路径
    B. 从顶点$i$到$j$存在3条长度不超过2的路径
    C. 从顶点$i$到$j$存在2条长度为3的路径
    D. 从顶点$i$到$j$存在2条长度不超过3的路径
09. 用邻接表法存储图所用的空间大小()。
    A. 与图的顶点数和边数有关 B. 只与图的边数有关
    C. 只与图的顶点数有关 D. 与边数的平方有关
10. 若邻接表中有奇数个边表结点,则()。
    A. 图中有奇数个结点 B. 图中有偶数个结点
    C. 图为无向图 D. 图为有向图
11. 在有向图的邻接表存储结构中,顶点$v$在边表中出现的次数是()。
    A. 顶点$v$的度 B. 顶点$v$的出度
    C. 顶点$v$的入度 D. 依附于顶点$v$的边数
12. $n$个顶点的无向图的邻接表最多有()个边表结点。
    A. $n^2$ B. $n(n-1)$ C. $n(n+1)$ D. $n(n-1)/2$
13. 设某无向图中有$n$个顶点和$e$条边,则建立该图的邻接表的时间复杂度是()。
    A. $O(n+e)$ B. $O(n^2)$ C. $O(ne)$ D. $O(n^3)$
14. 假设有$n$个顶点、$e$条边的有向图用邻接表表示,则删除与某个顶点$v$相关的所有边的时间复杂度为()。
    A. $O(n)$ B. $O(e)$ C. $O(n+e)$ D. $O(ne)$
15. 设$n$个顶点、$e$条边的有向图用邻接表表示,则某个顶点$v$的入度的时间复杂度为()。
    A. $O(n)$ B. $O(e)$ C. $O(n+e)$ D. $O(ne)$
16. 对邻接表的叙述中,()是正确的。
    A. 无向图的邻接表中,第$i$个顶点的度为第$i$个链表中结点数的两倍
    B. 邻接表比邻接矩阵的操作更简便
    C. 邻接矩阵比邻接表的操作更简便
    D. 求有向图结点的度,必须遍历整个邻接表
17. 邻接多重表是()的存储结构。
    A. 无向图 B. 有向图 C. 无向图和有向图 D. 都不是
18. 十字链表是()的存储结构。
    A. 无向图 B. 有向图 C. 无向图和有向图 D. 都不是
19. 【2013统考真题】设图的邻接矩阵$A$如下所示,各顶点的度依次是()。
    $$
    A = \begin{bmatrix} 0 & 1 & 0 & 1 \\ 0 & 0 & 1 & 1 \\ 0 & 1 & 0 & 0 \\ 1 & 0 & 0 & 0 \end{bmatrix}
    $$
    A. 1,2,1,2 B. 2,2,1,1 C. 3,4,2,3 D. 4,4,2,2
20. 【2024统考真题】若无向图$G=(V, E)$的邻接多重表如下图所示,则$G$中顶点$b$与$d$的度分别是()。
    (图示为邻接多重表)
    A. 0, 2 B. 2, 4 C. 2, 5 D. 3, 4

**二、综合应用题**
01. 已知带权有向图$G$的邻接矩阵如下图所示,请画出该带权有向图$G$。
    $$
    \begin{vmatrix}
    0 & 15 & 2 & 12 & \infty & \infty \\
    \infty & 0 & \infty & \infty & 6 & \infty \\
    \infty & \infty & 0 & \infty & \infty & 8 \\
    \infty & \infty & \infty & 0 & \infty & 4 \\
    \infty & \infty & 8 & \infty & 0 & 3 \\
    \infty & \infty & 5 & \infty & \infty & 0 \\
    \infty & 4 & \infty & \infty & \infty & 10
    \end{vmatrix}
    $$
02. 设图$G=(V, E)$以邻接表存储,如下图所示。画出其邻接矩阵存储及图$G$。
    1 -> 2 -> 3 -> 4
    2 -> 1 -> 3 -> 4 -> 5
    3 -> 1 -> 2 -> 4
    4 -> 1 -> 2 -> 3 -> 5
    5 -> 2 -> 4
03. 对$n$个顶点的无向图和有向图,分别采用邻接矩阵和邻接表表示时,试问:
    1) 如何判别图中有多少条边?
    2) 如何判别任意两个顶点$i$和$j$是否有边相连?
    3) 任意一个顶点的度是多少?
04. 如何对无环有向图中的顶点重新编号,使得该图的邻接矩阵中所有的1都集中到对角线以上?
05. 写出从图的邻接表表示转换成邻接矩阵表示的算法。
06. 【2015统考真题】已知含有5个顶点的图$G$如下图所示。
    (图示为一个无向图,顶点0,1,2,3,4)
    请回答下列问题:
    1) 写出图$G$的邻接矩阵$A$(行、列下标从0开始)。
    2) 求$A^2$,矩阵$A^2$中位于0行3列元素值的含义是什么?
    3) 若已知具有$n(n \ge 2)$个顶点的图的邻接矩阵为$B$,则$B^m(2 \le m \le n)$中非零元素的含义是什么?
07. 【2021统考真题】已知无向连通图$G$由顶点集$V$和边集$E$组成,$|E|>0$,当$G$中度为奇数的顶点个数为不大于2的偶数时,$G$存在包含所有边且长度为$|E|$的路径(称为EL路径)。设图$G$采用邻接矩阵存储,类型定义如下:
```c
typedef struct {                    //图的定义
    int   numVertices, numEdges;    //图中实际的顶点数和边数
    char  VerticesList[MAXV];       //顶点表。MAXV为已定义常量
    int   Edge[MAXV][MAXV];         //邻接矩阵
} MGraph;
```
    请设计算法`int IsExistEL(MGraph G)`,判断$G$是否存在EL路径,若存在,则返回1,否则返回0。要求:
    1) 给出算法的基本设计思想。
    2) 根据设计思想,采用C或C++语言描述算法,关键之处给出注释。
    3) 说明你所设计算法的时间复杂度和空间复杂度。
08. 【2023统考真题】已知有向图$G$采用邻接矩阵存储,类型定义如下:
```c
typedef struct {                    //图的类型定义
    int numVertices, numEdges;      //图的顶点数和有向边数
    char VerticesList[MAXV];        //顶点表, MAXV为已定义常量
    int Edge[MAXV][MAXV];           //邻接矩阵
} MGraph;
```
    将图中出度大于入度的顶点称为K顶点。例如,在下图中,顶点$a$和$b$为K顶点。
    (图示为一个有向图 a,b,c,d)
    请设计算法`int printVertices(MGraph G)`,对给定的任意非空有向图$G$,输出$G$中所有的K顶点,并返回K顶点的个数。要求:
    1) 给出算法的基本设计思想。
    2) 根据设计思想,采用C或C++语言描述算法,关键之处给出注释。

### 6.2.7 答案与解析
**一、单项选择题**
01. B
$n$个顶点的图,若采用邻接矩阵表示,不考虑压缩存储,则存储空间大小为$O(n^2)$,选项A正确。邻接表可用于存储无向图,只是把每条边都视为两条方向相反的有向边,因此需要存储两次,选项B错误。因为邻接矩阵中对角线以下的元素全为0,所以若存在$<i,j>$,则必有$i<j$,由传递性可知图中路径的顶点编号是依次递增的,假设存在环$k \to \dots \to j \to k$,由题设可知$k < j < k$,矛盾,所以不存在环,拓扑序列必定存在,选项C正确。选项D显然正确。

**注意**
若邻接矩阵对角线以下(或以上)的元素全为0,则图中必然不存在环,即拓扑序列一定存在,但这并不能说明拓扑序列是唯一的。

02. C
除主对角线上的元素外,其余元素全为1,说明任意两个顶点之间都有边相连,因此该图一定-是完全图。
03. D
在无向图的邻接矩阵中,矩阵大小为$n^2$,非零元素的个数为$2e$,所以零元素的个数为$n^2-2e$。读者应掌握此题的变体,即当无向图变为有向图时,能够求出零的个数和非零的个数。
04. D
带权有向图的邻接矩阵中,0和$\infty$表示的都不是有向边,而入度是由邻接矩阵的列中元素计算出来的;出度是由邻接矩阵的行中元素计算出来的。
05. B、D
有向图的入度是其第$j$列的非0元素之和,无向图的度是第$i$行或第$i$列的非0元素之和。
06. B、B、D
邻接矩阵的顶点数等于矩阵的行(列)数,有向图的边数等于矩阵中非零元素的个数,无向图的边数等于矩阵中非零元素个数的一半。

**注意**
本题中所给的矩阵为对称矩阵,若不是对称矩阵,则必然不可能是无向图。

07. B
邻接矩阵表示唯一是因为图中边的信息在矩阵中有确定的位置,邻接表不唯一是因为邻接表的建立取决于读入边的顺序和边表中的插入算法。
08. A
设图$G$的邻接矩阵为$A$,$A^n$的元素$a_{ij}^n$等于从顶点$i$到$j$的长度为$n$的路径的数目,因此$a_{ij}^2=3$表示从顶点$i$到$j$存在3条长度为2的路径。该结论记住即可。
09. A
邻接表存储时,顶点数$n$决定了顶点表的大小,边数$e$决定了边表结点的个数,且无向图的每条边存储两次,总存储空间为$O(n+2e)$。而邻接矩阵只与图的顶点数有关,为$O(n^2)$。
10. D
无向图采用邻接表表示时,每条边存储两次,所以其边表结点的个数为偶数。题中边表结点为奇数个,所以必然是有向图,且有奇数条边。
11. C
题中的边表是不包括顶点表的。因为任何顶点$u$对应的边表中存放的都是以$u$为起点的边所对应的另一个顶点$v$。从而$v$在边表中出现的次数也就是它的入度。
12. B
最多有$n(n-1)/2$条边,每条边在邻接表中存储两次,因此边表结点最多为$n(n-1)$个。
13. A
建立图的邻接表需要遍历所有的顶点和边,每个顶点有一个顶点表结点,每条边需要创建一个边表结点并插入到相应的链表中。因此,共需$n+2e$次操作,时间复杂度为$O(n+e)$。
14. C
与顶点$v$相关的边包括出边和入边,对于出边,只需遍历$v$的顶点表结点和其指向的边表;对于入边,则需遍历整个边表。先删除出边:删除$v$的顶点表结点的单链表,出边数最多为$n-1$,时间复杂度为$O(n)$;再删除入边:扫描整个边表(扫描剩余全部顶点表结点及其指向的边表),删除所有$v$的顶点$v$的入边,时间复杂度为$O(n+e)$。因此总时间复杂度为$O(n+e)$。
15. C
为了求顶点$v$的入度,只需要遍历邻接表中的所有边表,检查每条边是否指向顶点$v$,这相当于遍历整个邻接表,因此算法的时间复杂度为$O(n+e)$。
16. D
无向图的邻接表中,第$i$个顶点的度为第$i$个链表中的结点数,所以选项A错。邻接表和邻接矩阵对于不同的操作各有优势,选项B和C都不准确。有向图结点的度包括出度和入度,对于出度,需要遍历顶点表结点所对应的边表;对于入度,则需要遍历剩下的全部边表。
17. A
邻接多重表是无向图的存储结构。
18. B
十字链表是有向图的存储结构。
19. C
邻接矩阵$A$为非对称矩阵,说明图是有向图,度为入度与出度之和。各顶点的度是矩阵中此结点对应的行(对应出度)和列(对应入度)的非零元素之和。
20. B
在邻接多重表中,统计一个结点的入度、出度或度数(入度+出度)时,只需考虑所有的边结点。下图是邻接多重表的边结点的结构:
| ivex | ilink | jvex | jlink | info |
|---|---|---|---|---|
其中,$ivex$表示弧头的编号,$ilink$域指向下一个弧头相同的边结点,$jvex$表示弧尾的编号,$jlink$域指向下一个弧尾相同的边结点。根据定义可知:求$x$号结点的入度时,只需统计$jvex$域等于$x$的边结点个数;求$x$号结点的出度时,只需统计$ivex$域等于$x$的边结点个数;$x$号结点的度数为入度和出度之和。顶点$b$的编号为1,统计可知:入度为1,出度为1,度数为2。顶点$d$的编号为3,统计可知:入度为1,出度为3,度数为4。

**二、综合应用题**
01. **【解答】**
带权有向图$G$如下图所示。
(图示：顶点a,b,c,d,e,f。边和权值：a->b(15), a->c(2), a->d(12), b->e(6), c->f(8), d->f(4), e->c(8), e->f(3), f->c(5), f->b(4))
02. **【解答】**
其邻接矩阵存储如下所示。
$$
\begin{vmatrix}
0 & 1 & 1 & 1 & 0 \\
1 & 0 & 1 & 1 & 1 \\
1 & 1 & 0 & 1 & 0 \\
1 & 1 & 1 & 0 & 1 \\
0 & 1 & 0 & 1 & 0
\end{vmatrix}
$$
在邻接表中,每条边存储了2次,在没有特殊说明时,通常默认其为无向图(当然,无向图也可视为具有对边的有向图)。该邻接表对应的图G如下图所示。
(图示：顶点1,2,3,4,5。边：(1,2), (1,3), (1,4), (2,3), (2,4), (2,5), (3,4), (4,5))
03. **【解答】**
1) 对于邻接矩阵表示的无向图,边数等于矩阵中1的个数除以2;对于邻接表表示的无向图,边数等于边结点的个数除以2。对于邻接矩阵表示的有向图,边数等于矩阵中1的个数;对于邻接表表示的有向图,边数等于边结点的个数。
2) 在邻接矩阵表示的无向图或有向图中,对于任意两个顶点$i$和$j$,邻接矩阵中$arcs[i][j]$或$arcs[j][i]$为1表示有边相连,否则表示无边相连。在邻接表表示的无向图或有向图中,对于任意两个顶点$i$和$j$,若从顶点表结点$i$出发找到编号为$j$的边表结点或从顶点表结点$j$出发找到编号为$i$的边表结点,表示有边相连;否则为无边相连。
3) 对于邻接矩阵表示的无向图,顶点$i$的度等于第$i$行中1的个数;对于邻接矩阵表示的有向图,顶点$i$的出度等于第$i$行中1的个数,入度等于第$j$列中1的个数;度数等于它们的和。对于邻接表表示的无向图,顶点$i$的度等于顶点表结点$i$的单链表中边表结点的个数;对于邻接表表示的有向图,顶点$i$的出度等于顶点表结点$i$的单链表中边表结点的个数,顶点$i$的入度等于邻接表中所有编号为$i$的边表结点数;度数等于入度与出度之和。
04. **【解答】**
按各顶点的出度进行排序。$n$个顶点的有向图,其顶点的最大出度是$n-1$,最小出度为0。这样排序后,出度最大的顶点编号为1,出度最小的顶点编号为$n$。之后,进行调整,即只要存在弧$<i,j>$,就不管顶点$j$的出度是否大于顶点$i$的出度,都把$i$编号在顶点$j$的编号之前,因为只有$i \le j$,弧$<i,j>$对应的1才能出现在邻接矩阵的上三角。
通过后面小节的学习,会发现采用拓扑排序并依次编号是一种更为简便的方法。
05. **【解答】**
**算法思想**：设图的顶点分别存储在数组$v[n]$中。首先初始化邻接矩阵。遍历邻接表,在依次遍历顶点$v[i]$的边链表时,修改邻接矩阵的第$i$行的元素值。若链表边结点的值为$j$,则置$arcs[i][j]=1$。遍历完邻接表时,整个转换过程结束。此算法对于无向图、有向图均适用。
算法的实现如下:
```c
void Convert(ALGraph &G, int arcs[M][N]) {
    //此算法将邻接表方式表示的图G转换为邻接矩阵arcs
    for (i=0; i<n; i++) {           //依次遍历各顶点表结点为头的边链表
        p = (G->v[i]).firstarc;     //取出顶点i的第一条出边
        while (p != NULL) {         //遍历边链表
            arcs[i][p->adjvex] = 1;
            p = p->nextarc;         //取下一条出边
        } //while
    } //for
}
```
06. **【解答】**
考查图的邻接矩阵的性质。
1) 图$G$的邻接矩阵$A$如下:
$$
A = \begin{bmatrix}
0 & 1 & 1 & 0 & 1 \\
1 & 0 & 0 & 1 & 1 \\
1 & 0 & 0 & 1 & 0 \\
0 & 1 & 1 & 0 & 1 \\
1 & 1 & 0 & 1 & 0
\end{bmatrix}
$$
2) $A^2$如下:
$$
A^2 = \begin{bmatrix}
3 & 1 & 0 & 3 & 1 \\
1 & 3 & 2 & 1 & 2 \\
0 & 2 & 2 & 0 & 2 \\
3 & 1 & 0 & 3 & 1 \\
1 & 2 & 2 & 1 & 3
\end{bmatrix}
$$
0行3列的元素值3表示从顶点0到顶点3之间长度为2的路径共有3条。
3) $B^m(2 \le m \le n)$中位于$i$行$j$列($0 \le i,j \le n-1$)的非零元素的含义是,图中从顶点$i$到顶点$j$的长度为$m$的路径条数。
07. **【解答】**
1) **算法的基本设计思想**：
本算法题属于送分题,题干已经告诉我们算法的思想。对于采用邻接矩阵存储的无向图,在邻接矩阵的每一行(列)中,非零元素的个数为本行(列)对应顶点的度。可以依次计算连通图$G$中各顶点的度,并记录度为奇数的顶点个数,若个数为0或2,则返回1,否则返回0。
2) **算法实现**
```c
int IsExistEL(MGraph G) {
    //采用邻接矩阵存储,判断图是否存在EL路径
    int degree, i, j, count=0;
    for (i=0; i<G.numVertices; i++) {
        degree=0;
        for (j=0; j<G.numVertices; j++)
            degree += G.Edge[i][j];   //依次计算各个顶点的度
        if (degree%2 != 0)
            count++;                  //对度为奇数的顶点计数
    }
    if (count==0 || count==2)
        return 1;                     //存在EL路径,返回1
    else
        return 0;                     //不存在EL路径,返回0
}
```
3) **时间复杂度和空间复杂度**
算法需要遍历整个邻接矩阵,所以时间复杂度是$O(n^2)$,空间复杂度是$O(1)$。
08. **【解答】**
1) **算法的基本设计思想**:
采用邻接矩阵表示有向图时,一行中1的个数为该行对应顶点的出度,一列中1的个数为该列对应顶点的入度。使用一个初值为零的计数器记录K顶点的个数。对图$G$的每个顶点,根据邻接矩阵计算其出度`outdegree`和入度`indegree`。若`outdegree-indegree>0`,则输出该顶点且计数器加1。最后返回计数器的值。
2) **用C语言描述的算法**:
```c
int printVertices(MGraph G) {
    //采用邻接矩阵存储,输出K顶点,返回个数
    int indegree, outdegree, k, m, count=0;
    for (k=0; k<G.numVertices; k++) {
        indegree = outdegree = 0;
        for (m=0; m<G.numVertices; m++)    //计算顶点的出度
            outdegree += G.Edge[k][m];
        for (m=0; m<G.numVertices; m++)    //计算顶点的入度
            indegree += G.Edge[m][k];
        if (outdegree > indegree) {
            printf("%c", G.VerticesList[k]);
            count++;
        }
    }
    return count;                         //返回K顶点的个数
}
```

## 6.3 图的遍历
图的遍历是指从图中的某一顶点出发,按照某种搜索方法沿着图中的边对图中的所有顶点访问一次,且仅访问一次。注意到树是一种特殊的图,所以树的遍历实际上也可视为一种特殊的图的遍历。图的遍历算法是求解图的连通性问题、拓扑排序和求关键路径等算法的基础。
图的遍历比树的遍历要复杂得多,因为图的任意一个顶点都可能和其余的顶点相邻接,所以在访问某个顶点后,可能沿着某条路径搜索又回到该顶点。为避免同一顶点被访问多次,在遍历图的过程中,必须记下每个已访问过的顶点,为此可以设一个辅助数组`visited[]`来标记顶点是否被访问过。图的遍历算法主要有两种:广度优先搜索和深度优先搜索。

### 6.3.1 广度优先搜索
广度优先搜索(Breadth-First-Search, BFS)类似于树的层序遍历。基本思想是:首先访问起始顶点$v$,接着由$v$出发,依次访问$v$的各个未访问过的邻接顶点$w_1, w_2, \dots, w_i$,然后依次访问$w_1, w_2, \dots, w_i$的所有未被访问过的邻接顶点;再从这些访问过的顶点出发,访问它们所有未被访问过的邻接顶点,直至图中所有顶点都被访问过为止。若此时图中尚有顶点未被访问,则另选图中一个未曾被访问的顶点作为始点,重复上述过程,直至图中所有顶点都被访问到为止。Dijkstra单源最短路径算法和Prim最小生成树算法也应用了类似的思想。
换句话说,广度优先搜索遍历图的过程是以$v$为起始点,由近至远依次访问和$v$有路径相通且路径长度为$1,2,\dots$的顶点。广度优先搜索是一种分层的查找过程,每向前走一步可能访问一批顶点,不像深度优先搜索那样有往回退的情况,因此它不是一个递归的算法。为了实现逐层的访问,算法必须借助一个辅助队列,以记忆正在访问的顶点的下一层顶点。
广度优先搜索算法的伪代码如下:
```
bool visited[MAX_VERTEX_NUM];       //访问标记数组
void BFSTraverse(Graph G) {         //对图G进行广度优先遍历
    for(i=0; i<G.vexnum; ++i)
        visited[i] = FALSE;         //访问标记数组初始化
    InitQueue(Q);                   //初始化辅助队列Q
    for(i=0; i<G.vexnum; ++i)        //从0号顶点开始遍历
        if(!visited[i])             //对每个连通分量调用一次BFS()
            BFS(G, i);              //若v_i未访问过,从v_i开始调用BFS()
}
```
用邻接表实现广度优先搜索的算法如下:
```c
void BFS(ALGraph G, int i) {
```