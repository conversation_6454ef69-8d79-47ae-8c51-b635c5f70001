## 第6章 图

**【考纲内容】**
(一)图的基本概念
(二)图的存储及基本操作
邻接矩阵;邻接表;邻接多重表;十字链表
(三)图的遍历
深度优先搜索;广度优先搜索
(四)图的基本应用
最小(代价)生成树;最短路径;拓扑排序;关键路径

**【知识框架】**

图
- 图的定义
- 图结构的存储
  - 邻接矩阵法、邻接表法
  - 邻接多重表、十字链表
- 图的遍历
  - 深度优先遍历
  - 广度优先遍历
- 图的相关应用
  - 最小生成树: Prim算法、Kruskal 算法
  - 最短路径: Dijkstra算法、Floyd 算法
  - 拓扑排序: AOV 网
  - 关键路径: AOE网

**【复习提示】**
图算法的难度较大,主要掌握深度优先搜索与广度优先搜索。掌握图的基本概念及基本性质、图的存储结构(邻接矩阵、邻接表、邻接多重表和十字链表)及特性、存储结构之间的转化、基于存储结构上的各种遍历操作和各种应用(拓扑排序、最小生成树、最短路径和关键路径)等。图的相关算法较多,通常只需掌握其基本思想和实现步骤,而实现代码不是重点。

### 6.1 图的基本概念

#### 6.1.1 图的定义
图$G$由顶点集$V$和边集$E$组成,记为$G = (V, E)$,其中$V(G)$表示图$G$中顶点的有限非空集;$E(G)$表示图$G$中顶点之间的关系(边)集合。若$V = \{v_1, v_2, ..., v_n\}$,则用$|V|$表示图$G$中顶点的个数,$E = \{(u, v) | u \in V, v \in V\}$,用$|E|$表示图$G$中边的条数。

***

**注**
线性表可以是空表,树可以是空树,但图不可以是空图。也就是说,图中不能一个顶点也没有,图的顶点集$V$一定非空,但边集$E$可以为空,此时图中只有顶点而没有边。

***

下面是图的一些基本概念及术语。
1.  **有向图**
    若$E$是有向边(也称弧)的有限集合,则图$G$为有向图。弧是顶点的有序对,记为$<v, w>$,其中$v, w$是顶点,$v$称为弧尾,$w$称为弧头,$<v, w>$称为从$v$到$w$的弧,也称$v$邻接到$w$。
    图6.1(a)所示的有向图$G_1$可表示为
    $G_1 = (V_1, E_1)$
    $V_1 = \{1, 2, 3\}$
    $E_1 = \{<1, 2>, <2, 1>, <2, 3>\}$

2.  **无向图**
    若$E$是无向边(简称边)的有限集合,则图$G$为无向图。边是顶点的无序对,记为$(v, w)$或$(w, v)$,可以说$w$和$v$互为邻接点。边$(v,w)$依附于$w$和$v$,或称边$(v,w)$和$v, w$相关联。
    图6.1(b)所示的无向图$G_2$可表示为
    $G_2 = (V_2, E_2)$
    $V_2 = \{1, 2, 3, 4\}$
    $E_2 = \{(1, 2), (1, 3), (1, 4), (2, 3), (2, 4), (3, 4)\}$

(a)有向图$G_1$
(b)无向图$G_2$
(c)有向完全图$G_3$
图6.1 图的示例

3.  **简单图、多重图**
    一个图$G$若满足:①不存在重复边;②不存在顶点到自身的边,则称图$G$为简单图。图6.1中$G_1$和$G_2$均为简单图。若图$G$中某两个顶点之间的边数大于1条,又允许顶点通过一条边和自身关联,则称图$G$为多重图。多重图和简单图的定义是相对的。本书中仅讨论简单图。

4.  **顶点的度、入度和出度**
    **命题追踪** 无向图中顶点和边的关系(2009、2017)
    在无向图中,顶点$v$的度是指依附于顶点$v$的边的条数,记为$TD(v)$。在图6.1(b)中,每个顶点的度均为3。无向图的全部顶点的度之和等于边数的2倍,因为每条边和两个顶点相关联。
    在有向图中,顶点$v$的度分为入度和出度,入度是以顶点$v$为终点的有向边的数目,记为$ID(v)$;而出度是以顶点$v$为起点的有向边的数目,记为$OD(v)$。在图6.1(a)中,顶点2的出度为2、入度为1。顶点$v$的度等于其入度与出度之和,即$TD(v) = ID(v) + OD(v)$。有向图的全部顶点的入度之和与出度之和相等,并且等于边数,这是因为每条有向边都有一个起点和终点。

***

5.  **路径、路径长度和回路**
    顶点$v_p$到顶点$v_q$之间的一条路径是指顶点序列$v_p, v_{i_1}, v_{i_2}, \dots, v_{i_m}, v_q$,当然关联的边也可理解为路径的构成要素。路径上的边的数目称为路径长度。第一个顶点和最后一个顶点相同的路径称为回路或环。若一个图有$n$个顶点,且有大于$n-1$条边,则此图一定有环。

6.  **简单路径、简单回路**
    **命题追踪** 路径、回路、简单路径、简单回路的定义(2011)
    在路径序列中,顶点不重复出现的路径称为简单路径。除第一个顶点和最后一个顶点外,其余顶点不重复出现的回路称为简单回路。

7.  **距离**
    从顶点$u$出发到顶点$v$的最短路径若存在,则此路径的长度称为从$u$到$v$的距离。若从$u$到$v$根本不存在路径,则记该距离为无穷($\infty$)。

8.  **子图**
    设有两个图$G = (V, E)$和$G' = (V', E')$,若$V'$是$V$的子集,且$E'$是$E$的子集,则称$G'$是$G$的子图。若有满足$V(G') = V(G)$的子图$G'$,则称其为$G$的生成子图。图6.1中$G_3$为$G_1$的子图。

**注**
并非$V$和$E$的任何子集都能构成$G$的子图,因为这样的子集可能不是图,即$E$的子集中的某些边关联的顶点可能不在这个$V$的子集中。

9.  **连通、连通图和连通分量**
    **命题追踪** 图的连通性与边和顶点的关系(2010、2022)
    在无向图中,若从顶点$v$到顶点$w$有路径存在,则称$v$和$w$是连通的。若图$G$中任意两个顶点都是连通的,则称图$G$为连通图,否则称为非连通图。无向图中的极大连通子图称为连通分量,在图6.2(a)中,图$G_4$有3个连通分量如图6.2(b)所示。假设一个图有$n$个顶点,若边数小于$n-1$,则此图必是非连通图;思考,若该图是非连通图,则最多可以有多少条边?①

(a)无向图$G_1$
(b) $G_1$的三个连通分量
图6.2 无向图及其连通分量

10. **强连通图、强连通分量**
    在有向图中,若有一对顶点$v$和$w$,从$v$到$w$和从$w$到$v$之间都有路径,则称这两个顶点
***
①非连通情况下边最多的情况:由$n-1$个顶点构成一个完全图,此时再加入一个顶点则变成非连通图。

***

是强连通的。若图中任意一对顶点都是强连通的,则称此图为强连通图。有向图中的极大强连通子图称为有向图的强连通分量,图$G_1$的强连通分量如图6.3所示。思考,假设一个有向图有$n$个顶点,若该图是强连通图,则最少需要有多少条边?①

**注**
在无向图中讨论连通性,在有向图中讨论强连通性。

11. **生成树、生成森林**
    连通图的生成树是包含图中全部顶点的一个极小连通子图。若图中顶点数为$n$,则它的生成树含有$n-1$条边。包含图中全部顶点的极小连通子图,只有生成树满足这个极小条件,对生成树而言,若砍去它的一条边,则会变成非连通图,若加上一条边则会形成一个回路。在非连通图中,连通分量的生成树构成了非连通图的生成森林。图$G_2$的一个生成树如图6.4所示。

图6.3 图$G_1$的强连通分量
图6.4 图$G_2$的一个生成树

**注**
区分极大连通子图和极小连通子图。极大连通子图要求子图必须连通,而且包含尽可能多的顶点和边;极小连通子图是既要保持子图连通又要使得边数最少的子图。

12. **边的权、网和带权路径长度**
    在一个图中,每条边都可以标上具有某种含义的数值,该数值称为该边的权值。这种边上带有权值的图称为带权图,也称网。路径上所有边的权值之和,称为该路径的带权路径长度。

13. **完全图(也称简单完全图)**
    对于无向图,$|E|$的取值范围为$0$到$n(n-1)/2$,有$n(n-1)/2$条边的无向图称为完全图,在完全图中任意两个顶点之间都存在边。对于有向图,$|E|$的取值范围为$0$到$n(n-1)$,有$n(n-1)$条弧的有向图称为有向完全图,在有向完全图中任意两个顶点之间都存在方向相反的两条弧。图6.1中$G_2$为无向完全图,而$G_3$为有向完全图。

14. **稠密图、稀疏图**
    边数很少的图称为稀疏图,反之称为稠密图。稀疏和稠密本身是模糊的概念,稀疏图和稠密图常常是相对而言的。一般当图$G$满足$|E|<|V|\log_2|V|$时,可以将$G$视为稀疏图。

15. **有向树**
    一个顶点的入度为0、其余顶点的入度均为1的有向图,称为有向树。

### 6.1.2 本节试题精选
**一、单项选择题**
01. 图中有关路径的定义是()。
    A. 由顶点和相邻顶点序偶构成的边所形成的序列
    B. 由不同顶点所形成的序列
    C. 由不同边所形成的序列
    D. 上述定义都不是
***
①有向图强连通情况下边最少的情况:至少需要$n$条边,构成一个环路。

***

02. 一个有$n$个顶点和$n$条边的无向图一定是()。
    A. 连通的
    B. 不连通的
    C. 无环的
    D. 有环的
03. 若从无向图的任意顶点出发进行一次深度优先搜索即可访问所有顶点,则该图一定是()。
    A. 强连通图
    B. 连通图
    C. 有回路
    D. 一棵树
04. 以下关于图的叙述中,正确的是()。
    A. 图与树的区别在于图的边数大于或等于顶点数
    B. 假设有图$G=(V, E)$,顶点集$V'\subseteq V$, $E'\subseteq E$,则$V'$和$E'$构成$G$的子图
    C. 无向图的连通分量是指无向图中的极大连通子图
    D. 图的遍历就是从图中某一顶点出发访遍图中其余顶点
05. 以下关于图的叙述中,正确的是()。
    A. 强连通有向图的任何顶点到其他所有顶点都有弧
    B. 图的任意顶点的入度等于出度
    C. 有向完全图一定是强连通有向图
    D. 有向图的边集的子集和顶点集的子集都构成原有向图的子图
06. 一个有28条边的非连通无向图至少有()个顶点。
    A. 7
    B. 8
    C. 9
    D. 10
07. 对于一个有$n$个顶点的图:若是连通无向图,其边的个数至少为();若是强连通有向图,则其边的个数至少为()。
    A. $n-1, n$
    B. $n-1, n(n-1)$
    C. $n, n$
    D. $n, n(n-1)$
08. 无向图$G$有23条边,度为4的顶点有5个,度为3的顶点有4个,其余都是度为2的顶点,则图$G$有()个顶点。
    A. 11
    B. 12
    C. 15
    D. 16
09. 在有$n$个顶点的有向图中,顶点的度最大可达()。
    A. $n$
    B. $n-1$
    C. $2n$
    D. $2n-2$
10. 具有6个顶点的无向图,当有()条边时能确保是一个连通图。
    A. 8
    B. 9
    C. 10
    D. 11
11. 设有无向图$G=(V,E)$和$G'=(V', E')$,若$G'$是$G$的生成树,则下列不正确的是()。
    I. $G'$为$G$的连通分量
    II. $G'$为$G$的无环子图
    III. $G'$为$G$的极小连通子图且$V=V'$
    A. I、II
    B. 只有III
    C. II、III
    D. 只有I
12. 具有51个顶点和21条边的无向图的连通分量最多为()。
    A. 33
    B. 34
    C. 45
    D. 32
13. 在如下图所示的有向图中,共有()个强连通分量。

***

A. 1
B. 2
C. 3
D. 4
14. 若具有$n$个顶点的图是一个环,则它有()棵生成树。
    A. $n^2$
    B. $n$
    C. $n-1$
    D. 1
15. 若一个具有$n$个顶点、$e$条边的无向图是一个森林,则该森林中必有()棵树。
    A. $n$
    B. $e$
    C. $n-e$
    D. 1
16. **【2009统考真题】**下列关于无向连通图特性的叙述中,正确的是()。
    I. 所有顶点的度之和为偶数
    II. 边数大于顶点个数减1
    III. 至少有一个顶点的度为1
    A. 只有I
    B. 只有II
    C. I和II
    D. I和III
17. **【2010统考真题】**若无向图$G = (V, E)$中含有7个顶点,要保证图$G$在任何情况下都是连通的,则需要的边数最少是()。
    A. 6
    B. 15
    C. 16
    D. 21
18. **【2017统考真题】**已知无向图$G$含有16条边,其中度为4的顶点个数为3,度为3的顶点个数为4,其他顶点的度均小于3。图$G$所含的顶点个数至少是()。
    A. 10
    B. 11
    C. 13
    D. 15
19. **【2022统考真题】**对于无向图$G=(V,E)$,下列选项中,正确的是()。
    A. 当$|V|>|E|$时,$G$一定是连通的
    B. 当$|V|<|E|$时,$G$一定是连通的
    C. 当$|V|=|E|-1$时,$G$一定是不连通的
    D. 当$|V|>|E|+1$时,$G$一定是不连通的

### 二、综合应用题
01. 图$G$是一个非连通无向图,共有28条边,该图至少有多少个顶点?

### 6.1.3 答案与解析
**一、单项选择题**
**01. A**
本题是北京交通大学考研真题,不同教材对路径的定义可能略有不同,顶点之间关联的边也可理解为路径的构成要素。对于选项B,路径的定义中并没有要求是不同顶点,比如简单回路的第一个顶点和最后一个顶点是可以相同的,此外选项B也没有说明这些顶点之间有边相联。
**02. D**
若一个无向图有$n$个顶点和$n-1$条边,可以使它连通但没有环(生成树),但若再加一条边,在不考虑重边的情形下,则必然会构成环。
**03. B**
强连通图是有向图,与题意矛盾,选项A错误;对无向连通图做一次深度优先搜索,可以访问到该连通图的所有顶点,选项B正确;有回路的无向图不一定是连通图,因为回路不一定

***

包含图的所有结点,选项C错误;连通图可能是树,也可能存在环,选项D错误。
**04. C**
图与树的区别是逻辑上的区别,而不是边数的区别,图的边数也可能小于树的边数,选项A错误;若$E'$中的边对应的顶点不是$V'$的元素,$V'$和$E'$无法构成图,选项B错误;无向图的极大连通子图称为连通分量,选项C正确;图的遍历要求每个结点只能被访问一次,且若图非连通,则从某一顶点出发无法访问到其他全部顶点,选项D的说法不准确。
**05. C**
强连通有向图的任何顶点到其他所有顶点都有路径,但未必有弧;无向图任意顶点的入度等于出度,但有向图未必满足;若边集中的某条边对应的某个顶点不在对应的顶点集中,则有向图的边集的子集和顶点集的子集无法构成子图。
**06. C**
考查至少有多少个顶点的情形,我们考虑该非连通图最极端的情况,即它由一个完全图加一个独立的顶点构成,此时若再加一条边,则必然使图变成连通图。在$28=n(n-1)/2=8 \times 7/2$条边的完全无向图中,总共有8个顶点,再加上1个不连通的顶点,共9个顶点。
**07. A**
对于连通无向图,边最少即构成一棵树的情形;对于强连通有向图,边最少即构成一个有向环的情形。
**08. D**
因为在具有$n$个顶点、$e$条边的无向图中,有$\sum_{i=1}^n TD(v_i)=2e$,所以求得度为2的顶点数为7,从而共有16个顶点。
**09. D**
在有向图中,顶点的度等于入度与出度之和。$n$个顶点的有向图中,任意一个顶点最多还可以与其他$n-1$个顶点有一对指向相反的边相连。**注意**,数据结构中仅讨论简单图。
**10. D**
5个顶点构成一个完全无向图,需要$n(n-1)/2=10$条边;再加上1条边后,能保证第6个顶点必然与此完全无向图构成一个连通图,所以共需11条边。
**11. D**
一个连通图的生成树是一个极小连通子图,显然它是无环的,因此选项II、III正确。极大连通子图称为连通分量,$G'$连通但非连通分量。这里再补充一下“极大连通子图”:若图本来就是连通的,且每个子部分包含其本身的所有顶点和边,则它就是极大连通子图。
**12. C**
初始考虑只有51个顶点的无向图$G$,此时$G$中每个顶点都是连通分量,问题转化为向$G$中添加21条边,如何添加这21条边使得连通分量数目最多。若向两个不同的连通分量之间添加边,则连通分量数目会减1,所以应尽可能地将这21条边加入同一个连通分量且让其接近完全图,含有7个顶点的完全图有21条边,所以用7个顶点构成一个含有21条边的连通分量,剩下$51-7=44$个顶点对应44个连通分量,共有45个连通分量。
**13. B**
强连通分量是极大强连通子图,任意两个顶点之间有方向相反的两条路径。由定义不难得出,若一个顶点只有出边或入边,则该顶点必定单独构成一个连通分量。图中,顶点$B$只有出边,其他所有顶点都不可能有到顶点$B$的路径,所以顶点$B$单独构成一个强连通分量。在顶点$A、C、D、E$中,任意两个顶点之间都有方向相反的两条路径,所以可构成一个强连通分量。
**14. B**

***

$n$个顶点的生成树是具有$n-1$条边的极小连通子图,因为$n$个顶点构成的环共有$n$条边,去掉任意一条边就是一棵生成树,所以共有$n$种情况,所以可以有$n$棵不同的生成树。
**15. C**
$n$个结点的树有$n-1$条边,假设森林中有$x$棵树,将每棵树的根连到一个添加的结点,则成为一棵树,结点数是$n+1$,边数是$e+x$,从而可知$x=n-e$。
**【另解】**设森林中有$x$棵树,则再用$x-1$条边就可将所有的树连接成一棵树,此时边数+1=顶点数,即$e+(x-1)+1=n$,所以$x=n-e$。
**16. A**
每条边都连接了两个顶点,在计算顶点的度之和时每条边都被计算了两次,所以所有顶点的度之和偶数。无向连通图对应的生成树也是无向连通图,但此时边数等于顶点数减1,选项II错误。考虑2个或以上的顶点恰好构成一个环的情况,此时每个顶点的度都是2,选项III错误。
**17. C**
题干要求无论如何分配边,都能使7个顶点连通,这不同于只要6条边两两相连就能构成一个连通图的情形。考虑最极端的情形,即图$G$的某6个顶点构成一个完全无向图,此时若再添加一条边,则都将连通第7个顶点,使该图变成一个连通图。所以最少边数$=6 \times 5/2+1=16$。若边数$n$小于或等于15,可以使这$n$条边仅连接图$G$中的某6个顶点,从而导致第7个顶点无法与这6个顶点构成连通图(不满足“在任何情况下”)。
为简单起见,以5个顶点为例,左边4个顶点和$4 \times 3/2=6$条边构成一个完全图,此时若再添加一条边(可以是虚线中的任意一条),则能保证这5个顶点在任何情况下都是连通的,如下图所示。若边数小于7,则不能保证5个顶点在任何情况下都是连通的。
[Image of a complete graph K4 with a 5th vertex connected by dashed lines to two vertices of K4]
**18. B**
无向图边数的2倍等于各顶点度数的总和。要求至少的顶点数,应使每个顶点的度取最大,而其他顶点的度均小于3,因此可设它们的度都为2,并设它们的数量是$x$,列出方程$4 \times 3 + 3 \times 4 + 2x=16 \times 2$,解得$x=4$。因此至少包含$4+4+3=11$个顶点。
**19. D**
对于此类分析图的边数、顶点数与连通性问题,思路是寻找临界情况,在临界情况下任意增加或减少一条边,都会改变图的连通性。第一种临界情况如图1所示,此时若减少任意一条边,图就由连通变为不连通,即无向图连通的最小边数是$|V|-1$,因此,当$|E|<|V|-1$时,图一定不连通,选项C错误,选项D正确。第二种临界情况如图2所示,此时若增加任意一条边,则图就由不连通变为连通,即无向图不连通的最大边数是$(|V|-1)(|V|-2)/2$(此时$|V|-1$个顶点构成一个完全图),因此,仅当$|E|\ge(|V|-1)(|V|-2)/2+1$时才能保证无向图一定连通,选项A、B错误。
图1
图2
**二、综合应用题**
**01.【解答】**
图$G$是一个非连通无向图,当边数固定时,顶点数最少的情况是该图由两个连通子图构成,且其中之一只含一个顶点,另一个为完全图。其中只含一个顶点的子图没有边,另一个完全图的边数为$n(n-1)/2=28$,得$n=8$。所以该图至少有$1+8=9$个顶点。

### 6.2 图的存储及基本操作
图的存储必须要完整、准确地反映顶点集和边集的信息。根据不同图的结构和算法,采用不同的存储方式将对程序的效率产生相当大的影响,因此所选的存储结构应适合于待求解的问题。

### 6.2.1 邻接矩阵法
所谓邻接矩阵存储,是指用一个一维数组存储图中顶点的信息,用一个二维数组存储图中边的信息(各顶点之间的邻接关系),存储顶点之间邻接关系的二维数组称为邻接矩阵。
顶点数为$n$的图$G=(V,E)$的邻接矩阵$A$是$n \times n$的,将$G$的顶点编号为$v_1, v_2, \dots, v_n$,则
$$
A[i][j] =
\begin{cases}
1, & (v_i, v_j)\text{或}<v_i, v_j>\text{是}E(G)\text{中的边} \\
0, & (v_i, v_j)\text{或}<v_i, v_j>\text{不是}E(G)\text{中的边}
\end{cases}
$$
**命题追踪** 图的邻接矩阵存储及相互转换(2011、2015、2018)
对带权图而言,若顶点$v_i$和$v_j$之间有边相连,则邻接矩阵中对应项存放着该边对应的权值,若顶点$v_i$和$v_j$不相连,则通常用$0$或$\infty$来代表这两个顶点之间不存在边:
$$
A[i][j]=
\begin{cases}
w_{ij}, & (v_i,v_j)\text{或}<v_i,v_j>\text{是}E(G)\text{中的边} \\
0\text{或}\infty, & (v_i,v_j)\text{或}<v_i,v_j>\text{不是}E(G)\text{中的边}
\end{cases}
$$
有向图、无向图和网对应的邻接矩阵示例如图6.5所示。
(a)有向图$G_1$及其邻接矩阵
(b)无向图$G_2$及其邻接矩阵
(c)网及其邻接矩阵(对角线元素也经常用0表示)
图6.5 有向图、无向图及网的邻接矩阵

**命题追踪** (算法题)邻接矩阵的遍历及顶点的度的计算(2021、2023)
图的邻接矩阵存储结构定义如下:
```c
#define MaxVertexNum 100 //顶点数目的最大值
typedef char VertexType; //顶点对应的数据类型
typedef int EdgeType; //边对应的数据类型
typedef struct {
    VertexType vex[MaxVertexNum]; //顶点表
    EdgeType edge[MaxVertexNum][MaxVertexNum]; //邻接矩阵, 边表
```