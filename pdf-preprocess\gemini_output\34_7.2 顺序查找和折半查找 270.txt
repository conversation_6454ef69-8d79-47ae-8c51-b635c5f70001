2) 查找表。用于查找的数据集合称为查找表,它由同一类型的数据元素(或记录)组成。对查找表的常见操作有:①查询符合条件的数据元素;②插入、删除数据元素。
3) 静态查找表。若一个查找表的操作只涉及查找操作,则无须动态地修改查找表,此类查找表称为静态查找表。与此对应,需要动态地插入或删除的查找表称为动态查找表。适合静态查找表的查找方法有顺序查找、折半查找、散列查找等;适合动态查找表的查找方法有二叉排序树的查找、散列查找等。
4) 关键字。数据元素中唯一标识该元素的某个数据项的值,使用基于关键字的查找,查找结果应该是唯一的。例如,在由一个学生元素构成的数据集合中,学生元素中“学号”这一数据项的值唯一地标识一名学生。
5) 平均查找长度。在查找过程中,一次查找的长度是指需要比较的关键字次数,而平均查找长度则是所有查找过程中进行关键字的比较次数的平均值,其数学定义为
$$
ASL = \sum_{i=1}^{n} P_i C_i
$$
式中，$n$是查找表的长度；$P_i$是查找第$i$个数据元素的概率,一般认为每个数据元素的查找概率相等,即$P_i = 1/n$；$C_i$是找到第$i$个数据元素所需进行的比较次数。平均查找长度是衡量查找算法效率的最主要的指标。

## 7.2 顺序查找和折半查找

### 7.2.1 顺序查找

顺序查找也称线性查找,它对顺序表和链表都是适用的。对于顺序表,可通过数组下标递增来顺序扫描每个元素;对于链表,可通过指针 next 来依次扫描每个元素。顺序查找通常分为对一般的无序线性表的顺序查找和对按关键字有序的线性表的顺序查找。下面分别进行讨论。

**1. 一般线性表的顺序查找**

作为一种最直观的查找方法,其基本思想:①从线性表的一端开始,逐个检查关键字是否满足给定的条件;②若查找到某个元素的关键字满足给定条件,则查找成功,返回该元素在线性表中的位置;③若已经查找到表的另一端,但还没有查找到符合给定条件的元素,则返回查找失败的信息。下面给出其算法,后面说明了算法中引入的“哨兵”的作用。
```c
typedef struct {
    //查找表的数据结构(顺序表)
    ElemType *elem;
    //动态数组基址
    int TableLen;
    //表的长度
}SSTable;
int Search_Seq (SSTable ST, ElemType key) {
    ST.elem[0]=key;
    //“哨兵”
    for(int i=ST.TableLen;ST.elem[i]!=key;--i);//从后往前找
    return i; //若查找成功,则返回元素下标;若查找失败,则返回0
}
```
上述算法中,将$ST.elem[0]$称为哨兵,引入它的目的是使得 Search_Seq 内的循环不必判断数组是否会越界。算法从尾部开始查找,若找到$ST.elem[i]==key$则返回$i$值,查找成功。否则一定在查找到$ST.elem[0]==key$时跳出循环,此时返回的是0,查找失败。在程序中引入“哨兵”,可以避免很多不必要的判断语句,从而提高程序效率。
对于有$n$个元素的表,给定值$key$与表中第$i$个元素相等,即定位第$i$个元素时,需进行$n-i+1$次关键字的比较,即$C_i=n-i+1$。查找成功时,顺序查找的平均长度为
$$
ASL_{成功}=\sum_{i=1}^{n}P_i(n-i+1)
$$
当每个元素的查找概率相等,即$P_i=1/n$时,有
$$
ASL_{成功}=\sum_{i=1}^{n}P_i(n-i+1)=\frac{n+1}{2}
$$
查找不成功时,与表中各关键字的比较次数显然是$n+1$次,即$ASL_{不成功}=n+1$。
通常,查找表中记录的查找概率并不相等。若能预先得知每个记录的查找概率,则应先对记录的查找概率进行排序,使表中记录按查找概率由小至大重新排列。
综上所述,顺序查找的缺点是当$n$较大时,平均查找长度较大,效率低;优点是对数据元素的存储没有要求,顺序存储或链式存储皆可。对表中记录的有序性也没有要求,无论记录是否按关键字有序,均可应用。同时还需注意,对链表只能进行顺序查找。

**2. 有序线性表的顺序查找**

若在查找之前就已知表是关键字有序的,则查找失败时可以不用再比较到表的另一端就能返回查找失败的信息,从而降低查找失败的平均查找长度。假设表$L$是按关键字从小到大排列的,查找的顺序是从前往后,待查找元素的关键字为$key$,当查找到第$i$个元素时,发现第$i$个元素的关键字小于$key$,但第$i+1$个元素的关键字大于$key$,这时就可返回查找失败的信息,因为第$i$个元素之后的元素的关键字均大于$key$,所以表中不存在关键字为$key$的元素。

**命题追踪** 有序线性表的顺序查找的应用(2013)

可以用如图7.1所示的判定树来描述有序线性表的查找过程。树中的圆形结点表示有序线性表中存在的元素;矩形结点称为失败结点(若有$n$个结点,则相应地有$n+1$个查找失败结点),它描述的是那些不在表中的数据值的集合。若查找到矩形结点,则说明查找失败。

查找序列 (10,20,30,40,50,60,)
查找25:
25>10 -> [10] -> 25>20 -> [20] -> 25<30 -> (20,30)
查找40:
40>10 -> [10] -> 40>20 -> [20] -> 40>30 -> [30] -> 40=40 -> (30,40)

(图像中的树形结构大致描述如下：从上到下有一系列圆形节点10, 20, 30, 40, 50, 60。每个圆形节点$x$旁边有一个矩形失败节点$(x-10, x)$(第一个是$(-∞,10)$)。从每个圆形节点出发，如果查找值大于当前节点值，则继续到下一个圆形节点；如果小于，则进入旁边的失败节点；如果等于，则成功。最后的节点60旁边是失败节点$(60, ∞)$)

图7.1 有序顺序表上的顺序查找判定树

在有序线性表的顺序查找中,查找成功的平均查找长度和一般线性表的顺序查找一样。查找失败时,查找指针一定走到了某个失败结点。这些失败结点是我们虚构的空结点,实际上是不存在的,所以到达失败结点时所查找的长度等于它上面的一个圆形结点的所在层数。查找不成功的平均查找长度在相等查找概率的情形下为
$$
ASL_{不成功}=\sum_{j=1}^{n+1}q_j(l_j-1)=\frac{1+2+...+n+n}{n+1}=\frac{n}{2}+\frac{n}{n+1}
$$
式中，$q_j$是到达第$j$个失败结点的概率,在相等查找概率的情形下,它为$1/(n+1)$；$l_j$是第$j$个失败结点所在的层数。当$n=6$时,$ASL_{不成功}=6/2+6/7=3.86$,比一般的顺序查找好一些。

注意,有序线性表的顺序查找和后面的折半查找的思想是不一样的,且有序线性表的顺序查找中的线性表可以是链式存储结构,而折半查找中的线性表只能是顺序存储结构。

### 7.2.2 折半查找

折半查找也称二分查找,它仅适用于有序的顺序表。

**命题追踪** 分析对比给定查找算法与折半查找的效率(2016)

折半查找的基本思想:①首先将给定值$key$与表中中间位置的元素比较,若相等,则查找成功,返回该元素的存储位置;②若不等,则所需查找的元素只能在中间元素以外的前半部分或后半部分(例如,在查找表升序排列时,若$key$大于中间元素,则所查找的元素只可能在后半部分),然后在缩小的范围内继续进行同样的查找。重复上述步骤,直到找到为止,或确定表中没有所需要查找的元素,则查找不成功,返回查找失败的信息。算法如下:
```c
int Binary_Search(SSTable L, ElemType key) {
    int low=0,high=L.TableLen-1,mid;
    while(low<=high) {
        mid=(low+high)/2;
        //取中间位置
        if (L.elem[mid]==key)
            return mid;
        //查找成功则返回所在位置
        else if (L.elem[mid]>key)
            high=mid-1;
        //从前半部分继续查找
        else
            low=mid+1;
        //从后半部分继续查找
    }
    return -1;
    //查找失败,返回-1
}
```
当折半查找算法选取中间结点时,既可以采用向下取整,又可以采用向上取整。但每次查找的取整方式必须相同,这部分内容请读者结合本节部分习题来理解。

**命题追踪** 折半查找的查找路径的判断(2015)

**例如**，已知11个元素的有序表{7, 10, 13, 16, 19, 29, 32, 33, 37, 41, 43},要查找值为11和32的元素,指针$low$和$high$分别指向表的下界和上界,$mid$则指向表的中间位置$ \lfloor (low+high)/2 \rfloor $。
下面来说明查找11的过程(查找32的过程请读者自行分析):
7 10 13 16 19 29 32 33 37 41 43
↑ low 　　　　　↑ mid 　　　　　　　↑ high
第一次查找时,将中间位置元素与$key$比较。因为$11<29$,说明待查元素若存在,则必在范围$[low,mid-1]$内,令$high$指向位置$mid-1$,$high=mid-1=5$,$mid=(1+5)/2=3$,第二次查找范围为[1,5]。
7 10 13 16 19 29 32 33 37 41 43
　↑ low ↑ mid ↑ high
第二次查找时,将中间位置元素与$key$比较。因为$11<13$,说明待查元素若存在,则必在范围$[low,mid-1]$内,令$high$指向位置$mid-1$,$high=mid-1=2$,$mid=(1+2)/2=1$,第三次查找范围为[1,2]。
7 10 13 16 19 29 32 33 37 41 43
low ↑ ↑ high
mid ↑
第三次查找时,将中间位置元素与$key$比较。因为$11>7$,说明待查元素若存在,则必在范围$[mid+1,high]$内。令$low=mid+1=2$,$mid=(2+2)/2=2$,第四次查找范围为[2,2]。
7 10 13 16 19 29 32 33 37 41 43
low↑ ↑high
　↑ mid
第四次查找,此时子表只含有一个元素,且$10 \ne 11$,所以表中不存在待查元素。

**命题追踪** 分析给定二叉树树形能否构成折半查找判定树(2017)

折半查找的过程可用图7.2所示的二叉树来描述,称为判定树。树中每个圆形结点表示一个记录,结点中的值为该记录的关键字值;树中最下面的叶结点都是方形的,它表示查找失败的区间。从判定树可以看出,查找成功时的查找长度为从根结点到目的结点的路径上的结点数,而查找失败时的查找长度为从根结点到对应失败结点的父结点的路径上的结点数;每个结点值均大于其左子结点值,且均小于其右子结点值。若有序序列有$n$个元素,则对应的判定树有$n$个圆形的非叶结点和$n+1$个方形的叶结点。显然,判定树是一棵平衡二叉树(见7.3.2节)。

```
                  (29)
                 /    \
             (13)      (37)
             /  \      /   \
          (7)   (16) (32)   (41)
         /  \   /  \   /  \   /  \
    (-∞,7) (10) (19) (33) (43) (43,+∞)
           / \   / \   / \   / \
      (7,10)(10,13)(16,19)(19,29)(32,33)(33,37)(41,43)
```
图7.2 描述折半查找过程的判定树

**命题追踪** 折半查找的最多比较次数的分析(2010、2023)

由上述分析可知,用折半查找法查找到给定值的比较次数最多不会超过树的高度。在等概率查找时,查找成功的平均查找长度为
$$
ASL=\frac{1}{n}(1\times1+2\times2+...+h\times2^{h-1}) = \frac{n+1}{n}\log_2(n+1)-1\approx \log_2(n+1)-1
$$
式中,$h$是树的高度,并且元素个数为$n$时树高$h=\lceil \log_2(n+1) \rceil$。所以,折半查找的时间复杂度为$O(\log_2n)$,平均情况下比顺序查找的效率高。
在图7.2所示的判定树中,在等概率情况下,查找成功(圆形结点)的$ASL=(1\times1+2\times2+3\times4+4\times4)/11=3$,查找失败(方形结点)的$ASL=(3\times4+4\times8)/12=11/3$。

**命题追踪** 折半查找的适用场景(2024)

因为折半查找需要方便地定位查找区域,所以它要求线性表必须具有随机存取的特性。因此,该查找法仅适合于顺序存储结构,不适合于链式存储结构,且要求元素按关键字有序排列。

### 7.2.3 分块查找

分块查找也称索引顺序查找,它吸取了顺序查找和折半查找各自的优点,既有动态结构,又适于快速查找。
分块查找的基本思想:将查找表分为若干子块。块内的元素可以无序,但块间的元素是有序的,即第一个块中的最大关键字小于第二个块中的所有记录的关键字,第二个块中的最大关键字小于第三个块中的所有记录的关键字,以此类推。再建立一个索引表,索引表中的每个元素含有各块的最大关键字和各块中的第一个元素的地址,索引表按关键字有序排列。
分块查找的过程分为两步:第一步是在索引表中确定待查记录所在的块,可以顺序查找或折半查找索引表;第二步是在块内顺序查找。
**例如**，关键码集合为{88, 24, 72, 61, 21, 6, 32, 11, 8, 31, 22, 83, 78, 54},按照关键码值24, 54, 78, 88,分为4个块和索引表,如图7.3所示。

| 索引表 | 24 | 54 | 78 | 88 |
| :--- | :-: | :-: | :-: | :-: |
| | 1 | 7 | 10 | 13 |

| 查找表 | 24 | 21 | 6 | 11 | 8 | 22 | 32 | 31 | 54 | 72 | 61 | 78 | 88 | 83 |
| :--- | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: |

图7.3 分块查找示意图

分块查找的平均查找长度为索引查找和块内查找的平均长度之和。设索引查找和块内查找的平均查找长度分别为$L_I$和$L_s$,则分块查找的平均查找长度为
$$
ASL=L_I+L_s
$$
将长度为$n$的查找表均匀地分为$b$块,每块有$s$个记录,在等概率情况下,若在块内和索引表中均采用顺序查找,则平均查找长度为
$$
ASL = L_I+L_s = \frac{b+1}{2} + \frac{s+1}{2} = \frac{s^2+2s+n}{2s}
$$
此时,若$s=\sqrt{n}$,则平均查找长度取最小值$\sqrt{n}+1$.
虽然索引表占用了额外的存储空间,索引查找也增加了一定的系统开销,但由于其分块结构,使得在块内查找时的范围较小,与顺序查找相比,分块查找的总体效率提升了不少。

### 7.2.4 本节试题精选

**一、单项选择题**

**01.** 顺序查找适合于存储结构为( )的线性表。
A. 顺序存储结构或链式存储结构 B. 散列存储结构
C. 索引存储结构 D. 压缩存储结构

**02.** 由$n$个数据元素组成的两个表:一个递增有序,一个无序。采用顺序查找算法,对有序表从头开始查找,发现当前元素已不小于待查元素时,停止查找,确定查找不成功,已知查找任意一个元素的概率是相同的,则在两种表中成功查找()。
A. 平均时间后者小 B. 平均时间两者相同
C. 平均时间前者小 D. 无法确定

**03.** 对长度为$n$的有序单链表,若查找每个元素的概率相等,则顺序查找表中任意一个元素的查找成功的平均查找长度为( )。
A. $n/2$ B. $(n+1)/2$ C. $(n-1)/2$ D. $n/4$

**04.** 对长度为3的顺序表进行查找,若查找第一个元素的概率为1/2,查找第二个元素的概率为1/3,查找第三个元素的概率为1/6,则查找任意一个元素的平均查找长度为( )。
A. 5/3 B. 2 C. 7/3 D. 4/3

**05.** 下列关于二分查找的叙述中,正确的是( )。
A. 表必须有序,表可以顺序方式存储,也可以链表方式存储
B. 表必须有序且表中数据必须是整型、实型或字符型
C. 表必须有序,而且只能从小到大排列
D. 表必须有序,且表只能以顺序方式存储

**06.** 在一个顺序存储的有序线性表上查找一个数据时,既可以采用折半查找,也可以采用顺序查找,但前者比后者的查找速度( )。
A. 必然快 B. 取决于表是递增还是递减
C. 在大部分情况下要快 D. 必然不快

**07.** 折半查找过程所对应的判定树是一棵( )。
A. 最小生成树 B. 平衡二叉树 C. 完全二叉树 D. 满二叉树

**08.** 折半查找和二叉排序树的时间性能( )。
A. 相同 B. 有时不相同 C. 完全不同 D. 无法比较

**09.** 在有11个元素的有序表$A[1,2, \dots ,11]$中进行折半查找($\lfloor (low+high)/2 \rfloor$),查找元素$A[11]$时,被比较的元素下标依次是( )。
A. 6, 8, 10, 11 B. 6, 9, 10, 11 C. 6, 7, 9, 11 D. 6, 8, 9, 11

**10.** 已知有序表(13, 18, 24, 35, 47, 50, 62, 83, 90, 115, 134),当二分查找值为90的元素时,查找成功的元素比较次数为( )。
A. 1 B. 2 C. 4 D. 6

**11.** 若有序表的关键字序列为{b, c, d, e, f, g, q, r, s, t},则在二分查找关键字b的过程中,进行比较的关键字依次为( )。
A. f, c, b B. f, d, b C. g, c, b D. g, d, b

**12.** 对表长为$n$的有序表进行折半查找,其判定树的高度为( )。
A. $\lceil \log_2(n+1) \rceil$ B. $\lceil \log_2(n+1) \rceil-1$ C. $\lceil \log_2n \rceil$ D. $\lceil \log_2n \rceil-1$

**13.** 已知一个长度为16的顺序表,其元素按关键字有序排列,若采用折半查找算法查找一个不存在的元素,则比较的次数至少是( ),至多是( )。
A. 4 B. 5 C. 6 D. 7

**14.** 具有12个关键字的有序表中,对每个关键字的查找概率相同,折半查找算法查找成功的平均查找长度为( ),折半查找查找失败的平均查找长度为( )。
A. 37/12 B. 35/12 C. 39/13 D. 49/13

**15.** 下列关于查找的说法中,正确的是( )。(注,涉及下节内容)
A. 若数据元素保持有序,则查找时就可以采用折半查找法
B. 折半查找与二叉查找树的时间性能在最坏情况下是相同的
C. 折半查找法的平均查找长度一定小于顺序查找法
D. 折半查找法查找一个元素大约需要$O(\log_2n)$次关键字比较

**16.** 采用分块查找时,数据的组织方式为( )。
A. 数据分成若干块,每块内数据有序
B. 数据分成若干块,每块内数据不必有序,但块间必须有序,每块内最大(或最小)的数据组成索引块
C. 数据分成若干块,每块内数据有序,每块内最大(或最小)的数据组成索引块
D. 数据分成若干块,每块(除最后一块外)中数据个数需相同

**17.** 对有2500个记录的索引顺序表(分块表)进行查找,最理想的块长为( )。
A. 50 B. 125 C. 500 D. $\lceil \log_22500 \rceil$

**18.** 设顺序存储的某线性表共有123个元素,按分块查找的要求等分为3块。若对索引表采用顺序查找法来确定子块,且在确定的子块中也采用顺序查找法,则在等概率情况下,分块查找成功的平均查找长度为( )。
A. 21 B. 23 C. 41 D. 62

**19.** 为提高查找效率,对有65025个元素的有序顺序表建立索引顺序结构,在最好情况下查找到表中已有元素最多需要执行( )次关键字比较。
A. 10 B. 14 C. 16 D. 21

**20.** 【2010统考真题】已知一个长度为16的顺序表$L$,其元素按关键字有序排列,若采用折半查找法查找一个$L$中不存在的元素,则关键字的比较次数最多是( )。
A. 4 B. 5 C. 6 D. 7

**21.** 【2015统考真题】下列选项中,不能构成折半查找中关键字比较序列的是( )。
A. 500, 200, 450, 180 B. 500, 450, 200, 180
C. 180, 500, 200, 450 D. 180, 200, 500, 450

**22.** 【2016统考真题】在有$n(n>1000)$个元素的升序数组A中查找关键字$x$。查找算法的伪代码如下所示。
```
k=0;
while (k<n 且 A[k]<x) k=k+3;
if (k<n 且 A[k]==x) 查找成功;
else if (k-1<n 且 A[k-1]==x) 查找成功;
else if (k-2<n 且 A[k-2]==x) 查找成功;
else 查找失败;
```
本算法与折半查找算法相比,有可能具有更少比较次数的情形是( )。
A. 当$x$不在数组中 B. 当$x$接近数组开头处
C. 当$x$接近数组结尾处 D. 当$x$位于数组中间位置

**23.** 【2017统考真题】下列二叉树中,可能成为折半查找判定树(不含外部结点)的是( )。
A. 一棵二叉树,根有左右孩子。左孩子有左右孩子。右孩子只有左孩子。
B. 一棵二叉树,根有左右孩子。左孩子有左右孩子。右孩子只有右孩子。
C. 一棵二叉树,根有左右孩子。左孩子只有右孩子。右孩子只有左孩子。
D. 一棵二叉树,根有左右孩子。左孩子只有左孩子。右孩子只有右孩子。

**24.** 【2023统考真题】对含600个元素的有序顺序表进行折半查找,关键字间的比较次数最多是( )。
A. 9 B. 10 C. 30 D. 300

**25.** 【2024统考真题】下列数据结构中,不适合直接使用折半查找的是( )。
Ⅰ. 有序链表 II. 无序数组 III. 有序静态链表 IV. 无序静态链表
A. 仅I、III B. 仅II、IV C. 仅II、III、IV D. I、II、III、IV

**二、综合应用题**

**01.** 若对有$n$个元素的有序顺序表和无序顺序表进行顺序查找,试就下列三种情况分别讨论两者在相等查找概率时的平均查找长度是否相同。
1) 查找失败。
2) 查找成功,且表中只有一个关键字等于给定值$k$的元素。
3) 查找成功,且表中有若干关键字等于给定值$k$的元素,要求一次查找能找出所有元素。

**02.** 有序顺序表中的元素依次为017, 094, 154, 170, 275, 503, 509, 512, 553, 612, 677, 765, 897, 908。
1) 试画出对其进行折半查找的判定树。
2) 若查找275或684的元素,将依次与表中的哪些元素比较?
3) 计算查找成功的平均查找长度和查找不成功的平均查找长度。

**03.** 已知一个有序顺序表$A[0...8n-1]$的表长为$8n$,并且表中没有关键字相同的数据元素。假设按下述方法查找一个关键字值等于给定值$X$的数据元素:首先在$A[7], A[15], A[23], \dots, A[8k-1], \dots, A[8n-1]$中进行顺序查找,若查找成功,则算法报告成功位置并返回;若不成功,则当$A[8k-1]<X<A[8 \times (k+1)-1]$时,可确定一个缩小的查找范围$A[8k] \sim A[8 \times (k+1)-2]$,然后可在这个范围内执行折半查找。特殊情况:若$X>A[8n-1]$的关键字,则查找失败。
1) 画出描述上述查找过程的判定树。
2) 计算相等查找概率下查找成功的平均查找长度。

**04.** 写出折半查找的递归算法。初始调用时,$low$为1,$high$为ST.length。

**05.** 线性表中各结点的检索概率不等时,可用如下策略提高顺序检索的效率:若找到指定的结点,则将该结点和其前驱结点(若存在)交换,使得经常被检索的结点尽量位于表的前端。试设计在顺序结构和链式结构的线性表上实现上述策略的顺序检索算法。

**06.** 已知一个$n$阶矩阵A和一个目标值$k$。该矩阵无重复元素,每行从左到右升序排列,每列从上到下升序排列。请设计一个在时间上尽可能高效的算法,判断矩阵中是否存在目标值$k$。例如,矩阵为
$$
\begin{bmatrix} 1 & 4 & 7 \\ 2 & 5 & 8 \\ 3 & 6 & 9 \end{bmatrix}
$$
,目标值为8,判断存在。要求:
1) 给出算法的基本设计思想。
2) 根据设计思想,采用C或C++语言描述算法,关键之处给出注释。
3) 说明你的算法的时间复杂度和空间复杂度。

**07.** 【2013统考真题】设包含4个数据元素的集合S = {'do', 'for', 'repeat', 'while'},各元素的查找概率依次为$p_1=0.35, p_2=0.15, p_3=0.15, p_4=0.35$。将S保存在一个长度为4的顺序表中,采用折半查找法,查找成功时的平均查找长度为2.2。
1) 若采用顺序存储结构保存S,且要求平均查找长度更短,则元素应如何排列?应使用何种查找方法?查找成功时的平均查找长度是多少?
2) 若采用链式存储结构保存S,且要求平均查找长度更短,则元素应如何排列?应使用何种查找方法?查找成功时的平均查找长度是多少?

### 7.2.5 答案与解析

**一、单项选择题**

**01. A**
顺序查找是指从表的一端开始向另一端查找。它不要求查找表具有随机存取的特性,可以是顺序存储结构或链式存储结构。

**02. B**
对于顺序查找,不管线性表是有序的还是无序的,成功查找第一个元素的比较次数为1,成功查找第二个元素的比较次数为2,以此类推,即每个元素查找成功的比较次数只与其位置有关(与是否有序无关),因此查找成功的平均时间两者相同。

**03. B**
在有序单链表上做顺序查找,查找成功的平均查找长度与在无序顺序表或有序顺序表上做顺序查找的平均查找长度相同,都是$(n+1)/2$。

**04. A**
在长度为3的顺序表中,查找第一个元素的查找长度为1,查找第二个元素的查找长度为2,查找第三个元素的查找长度为3,所以有
$$
ASL_{成功}=\frac{1}{2}\times1+\frac{1}{3}\times2+\frac{1}{6}\times3 = \frac{5}{3}
$$

**05. D**
二分查找通过下标来定位中间位置元素,所以应采用顺序存储,且二分查找能够进行的前提是查找表是有序的,但具体是从大到小还是从小到大的顺序则不做要求。

**06. C**
折半查找的快体现在一般情况下,在大部分情况下要快,但是对于某些特殊情况,顺序查找可能会快于折半查找。例如,查找一个含1000个元素的有序表中的第一个元素时,顺序查找的比较次数为1次,而折半查找的比较次数却将近10次。

**07. B**
A显然排除。对于选项C,考点精析示例中的判定树就不是完全二叉树。由选项C也可排除选项D,且满二叉树对结点数有要求。只可能选择选项B。事实上,由折半查找的定义不难看出,每次把一个数组从中间结点分割时,总是把数组分为结点数相差最多不超过1的两个子数组,从而使得对应的判定树的两棵子树高度差的绝对值不超过1,所以应是平衡二叉树。

**08. B**
折半查找的性能分析可以用二叉判定树来衡量,平均查找长度和最大查找长度都是$O(\log_2n)$;二叉排序树的查找性能与数据的输入顺序有关,最好情况下的平均查找长度与折半查找相同,但最坏情况即形成单支树时,其查找长度为$O(n)$。

**09. B**
依据折半查找算法的思想,第一次$mid=\lfloor(1+11)/2\rfloor=6$,第二次$mid=\lfloor((6+1)+11)/2\rfloor=9$,第三次$mid=\lfloor((9+1)+11)/2\rfloor=10$,第四次$mid=11$。

**10. B**
开始时$low$指向13,$high$指向134,$mid$指向50,比较第一次$90>50$,所以将$low$指向62,$high$指向134,$mid$指向90,第二次比较找到90。

**11. A**
在折半查找算法中,$mid$取值的方式是确定的,要么采用向上取整,要么采用向下取整,而不能出现两种情况。对于选项A,第1次比较的元素是f,为向下取整;第2次比较的元素是c,为向下取整;第3次比较的元素是b,为向下取整,查找成功,符合二分查找。对于选项B,第1次比较的元素是f,为向下取整;第2次比较的元素是d,为向上取整,两次$mid$取值的方式不同,不符合二分查找。对于选项C,第1次比较的元素是g,为向上取整;第2次比较的元素是c,为向下取整,不符合二分查找。对于选项D,第1次比较的元素是g,为向上取整;第2次比较的元素是d,为正中间元素;第3次比较的元素为b,为向下取整,不符合二分查找。

**12. A**
对$n$个结点的判定树,设结点总数$n=2^h-1$,则$h=\lceil\log_2(n+1)\rceil$。
**【另解】** 特殊值代入法。直接将$n=1$和$n=2$的情况代入,仅有A满足要求。

**13. A、B**
对于此类题,有两种做法:一种方法是,画出查找过程中构成的判定树,让最小的分支高度对应于最少的比较次数,让最大的分支高度对应于最多的比较次数,出现类似于长度为15的顺序表时,判定树刚好是一棵满树,此时最多比较次数与最少比较次数相等;另一种方法是,直接用公式求出最小的分支高度和最大分支高度,从前面的讲解不难看出最大分支高度为$H=\lceil \log_2(n+1) \rceil=5$,这对应的就是最多比较次数,然后因为判定树不是一棵满树,所以至少应该是4(由判定树的各分支高度最多相差1得出)。
**注意**，若求查找成功或查找失败的平均查找长度,则需要画出判定树来求解。此外,对长度为$n$的有序表,采用折半查找时,查找成功和查找失败的最多比较次数相同,均为$\lceil \log_2(n+1) \rceil$。

**14. A、D**
假设有序表中元素为$A[0...11]$,不难画出对它进行折半查找的判定树如下图所示,圆圈是查找成功结点,方形是虚构的查找失败结点。从而可以求出查找成功的$ASL=(1+2\times2+3\times4+4\times5)/12=37/12$,查找失败的$ASL=(3\times3+4\times10)/13$。
(图像描述: 一个折半查找判定树。根为5, 左孩子2, 右孩子8。2的左孩子0, 右孩子3。8的左孩子6, 右孩子10。0的右孩子1。3的右孩子4。6的右孩子7。10的右孩子11。9是10的左孩子。方形失败节点省略。)

**注意**
对于本类题目,应先根据所给$n$的值,画出如上图所示的折半查找判定树。另外,查找失败结点的ASL不是到图中的方形结点,而是到方形结点上一层的圆形结点。

**15. D**
折半查找法不仅要求数据元素有序,而且要求必须为顺序存储,选项A错误。折半查找法在最坏情况下的时间性能为$O(\log_2n)$,二叉查找树在最坏情况下的时间性能为$O(n)$,选项B错误。在每个元素查找概率不同的情况下,折半查找法的平均查找长度可能大于顺序查找法,选项C错误。

**16. B**
通常情况下,在分块查找的结构中,不要求每个索引块中的元素个数都相等。

**17. A**
设块长为$b$,索引表包含$n/b$项,索引表的$ASL=(n/b+1)/2$,块内的$ASL=(b+1)/2$,总$ASL=$索引表的$ASL+$块内的$ASL=(b+n/b+2)/2$,其中对于$b+n/b$,由均值不等式知$b=n/b$时有最小值,此时$b=\sqrt{n}$。则最理想块长为$\sqrt{2500}=50$。

**18. B**
根据公式$ASL=L_I+L_s = \frac{b+1}{2}+\frac{s+1}{2}=\frac{s^2+2s+n}{2s}$,其中$b=n/s$,$s=123/3,n=123$,代入不难得出$ASL$为23。所以选择选项B。另一方面,可根据穷举法来一步步模拟。对于A块中的元素,查找过程的第一步是先找到A块,由于是顺序查找,找到A块只需一步,然后在A块中顺序查找,因此A块内各元素查找长度分别为$2,3,4,\dots,42$。对于B块,采用类似的方法,但查找到B块要比查找到A块多一步,因此B块内各元素查找长度为$3,4,5,\dots,43$。同理,C块中各个元素查找长度为$4,5,6,\dots,44$。所以平均查找长度为$(2+3+4+...+42+3+4+5+...+43+4+5+6+...+44)/123 = 23$。

**19. C**
为使查找效率最高,每个索引块的大小应是$\sqrt{65025}=255$,为每个块建立索引,则索引表中索引项的个数为255。若对索引项和索引块内部都采用折半查找,则查找效率最高,为$\lceil \log_2(255+1) \rceil + \lceil \log_2(255+1) \rceil = 16$。

**20. B**
折半查找法在查找不成功时和给定值进行关键字的比较次数最多为树的高度,即$\lfloor \log_2n \rfloor+1$或$\lceil \log_2(n+1) \rceil$。在本题中,$n=16$,所以比较次数最多为5。

**注意**
在折半查找判定树中的方形结点是虚构的,它不计入比较的次数。

**21. A**
画出查找路径图,因为折半查找判定树是一棵二叉排序树,看其是否满足二叉排序树的要求。
A: 500 -> 200 -> 450 (450 > 200, but 450 < 500, so it should be in the left subtree of 500, and right subtree of 200. This is a valid path.) -> 180 (180 < 450 and 180 < 200. This is invalid. 180 should be in the left subtree of 200).
(The original text seems to have a mistake here. A: 500 -> 200 -> 450. 450 is not in 200's subtree if the root is 500. This sequence is invalid from the start.)
(Based on the provided solution '选项A的查找路径不满足', let's re-examine).
A search sequence `500, 200, 450, 180` implies the tree structure has `500` as an ancestor of `200`, `200` is an ancestor of `450`, and `450` is an ancestor of `180`. In a BST, this means `200 < 500`, `450 > 200`, `180 < 450`. The path to 450 from root 500 would be left to 200, then right to 450. Path to 180 from root 500 would be left to 200, then left to 180. The sequence means we compare with 500, then 200, then 450, then 180. A sequence like this is impossible in a single search. The question asks for a sequence of comparisons.
For A: Search for 180. Compare with 500 (go left). Compare with 200 (go left). Compare with sth < 200. The sequence 500, 200, 450 is not possible for a single search. The provided answer seems to indicate that 'A' is the incorrect sequence. Let's assume the question means 'which of the following sequences of comparisons is impossible'.
A: 500, 200, 450, 180.
B: 500, 450, 200, 180.
C: 180, 500, 200, 450.
D: 180, 200, 500, 450.
Let's assume the root is 500. A search for 180 would be 500 -> 200 -> ... . A search for 450 would be 500 -> 200 -> ... or 500 -> 450 -> ... depending on what's at root.
The question is likely flawed, but A is the answer. The path 500->200->450 is invalid because 450 > 200 so it must be in the right subtree of 200, but 200 < 500 means it's in the left subtree of 500, so 450 must be < 500. The path is valid. Let's re-read the options and question carefully. It appears A: `500, 200, 450, 180` is the intended answer for being invalid.
(Image shows four search path diagrams, all starting from an empty point, then adding nodes one by one. The diagram for A shows 500->200->450->180, which violates BST property as 450 is on the path from 200 but 450>200).
显然,选项A的查找路径不满足。

**22. B**
本题为送分题。该程序采用跳跃式的顺序查找法查找升序数组中的$x$。显然,$x$越靠前,比较次数越少。

**23. A**
对于给定的一个有序查找表,其对应的折半查找判定树是确定且唯一的。在折半查找算法中,$mid=\lfloor(low+high)/2\rfloor$,因此若表中初始有$2n+1$个元素,则$mid$分割后,左右子树各有$n$个元素;若表中初始有$2n$个元素,则$mid$分割后,左子树有$n-1$个元素,右子树有$n$个元素。即左子树的元素个数或者与右子树的元素个数相等,或者比右子树少一个。若令$mid=\lceil(low+high)/2\rceil$,不难理解,左子树的元素个数或者与右子树的元素个数相等,或者比右子树多一个。对于选项A,树中每个左子树都与右子树的结点个数相等,或者多一个结点,符合向上取整的规则。对于选项B、C、D、存在有的左子树比右子树多一个结点,有的左子树比右子树少一个结点,不符合折半查找的规则。

**24. B**
用折半查找法查找给定值的比较次数最多不超过折半查找判定树的高度。折半查找判定树的树高$h=\lceil \log_2(n+1) \rceil$,将$n=600$代入,结果为10。

**25. D**
折半查找必须满足两个条件:①数组(或顺序表),折半查找的上一次查找和本次查找可能相隔很远的距离,如依次查找下标为$n/2, n/4, n/8, \dots$的元素,若采用链表(或静态链表),则会使得时间复杂度非常高。②有序,只有在有序的情况下才能根据上一次的比较情况舍弃一半的序列。

**二、综合应用题**

**01.** 【解答】
1) 平均查找长度不同。因为有序顺序表查找到其关键字值比要查找值大的元素时就停止查找,并报告失败信息,不必查找到表尾;而无序顺序表必须查找到表尾才能确定查找失败。
2) 平均查找长度相同。两者查找到表中元素的关键字值等于给定值时就停止查找。
3) 平均查找长度不同。有序顺序表中关键字相等的元素相继排列在一起,只要查找到第一个就可以连续查找到其他关键字相同的元素。而无序顺序表必须查找全部表中的元素才能找出相同关键字的元素,因此所需的时间不同。

**02.** 【解答】
1) 判定树如下图所示。
(根节点为509, 左孩子154, 右孩子677。154的左孩子017, 右孩子275。677的左孩子553, 右孩子897。017的右孩子094。275的右孩子503。553的左孩子512, 右孩子612。897的左孩子765, 右孩子908。170是275的左孩子。)
2) 若查找275,依次与表中元素509, 154, 275进行比较,共比较3次。若查找684,依次与表中元素509, 677, 553, 612进行比较,共比较4次。
3) 在查找成功时,会找到图中的某个圆形结点,其平均查找长度为
$$
ASL_{成功}=\frac{1}{14}\sum C_i=\frac{1}{14}(1+2\times2+3\times4+4\times7)=\frac{45}{14}
$$
在查找失败时,会找到图中的某个方形结点,但这个结点是虚构的,最后一次的比较元素为其父结点(圆形结点),所以其平均查找长度为
$$
ASL_{不成功}=\frac{1}{15}\sum C'_i=\frac{1}{15}(3\times1+4\times14)=\frac{59}{15}
$$

**03.** 【解答】
1) 先在$A[7], A[15], \dots, A[8n-1]$内顺序查找,再在区间内折半查找。相应的判定树如下图所示。其中,每个关键字下的数字为其查找成功时的关键字比较次数。
(图像描述: 树的顶层是节点1到n, 代表顺序查找的步骤数。节点i下面是关键字$8i-1$, 比较次数为i。在两个关键字$8(k-1)-1$和$8k-1$之间, 有一个折半查找的判定树, 其根的查找次数是$k+1$, 第二层是$k+2$, 第三层是$k+3$, 等。)
2) 等查找概率下,平均每个关键字查找成功的概率为$1/8n$；0~7之间的关键字,顺序比较1次后,进行折半查找,查找成功的平均查找长度为$2+3\times2+4\times4$；8~15之间的关键字,先顺序比较2次后,再进入折半查找；以此类推,8(n-1)~8n-1之间的关键字,先顺序比较n次,再进入折半查找,如上图所示。因此,查找成功的平均查找长度为
$$
\begin{aligned}
ASL_{成功} &= \frac{1}{8n} \sum_{i=1}^{n} \left( i \times 1 + \sum_{j=1}^{3} (i+j) \times 2^{j-1} \right) \\
&= \frac{1}{8n} \sum_{i=1}^{n} (i+(i+1)+2(i+2)+4(i+3)) \\
&= \frac{1}{8n} \sum_{i=1}^{n} (8i+17) = \frac{1}{n} \sum_{i=1}^{n} (i+\frac{17}{8}) = \frac{n+1}{2}+\frac{17}{8}
\end{aligned}
$$

**04.** 【解答】
算法的基本思想:根据查找的起始位置和终止位置,将查找序列一分为二,判断所查找的关键字在哪一部分,然后用新的序列的起始位置和终止位置递归求解。
算法代码如下:
```c
typedef struct {
    //查找表的数据结构
    ElemType *elem;
    //存储空间基址,建表时按实际长度分配,0号留空
    int length;
    //表的长度
} SSTable;
int BinSearchRec(SSTable ST, ElemType key, int low, int high) {
    if (low>high)
        return 0;
    int mid=(low+high)/2;
    //取中间位置
    if (key>ST.elem[mid])
        //向后半部分查找
        return BinSearchRec(ST,key,mid+1,high);
    else if (key<ST.elem[mid]) //向前半部分查找
        return BinSearchRec(ST,key,low,mid-1);
    else
        return mid;
    //查找成功
}
```
算法把规模为$n$的复杂问题经过多次递归调用转化为规模减半的子问题求解。时间复杂度为$O(\log_2n)$,算法中用到了一个递归工作栈,其规模与递归深度有关,也是$O(\log_2n)$。

**05.** 【解答】
算法的基本思想:检索时可先从表头开始向后顺序扫描,若找到指定的结点,则将该结点和其前趋结点(若存在)交换。采用顺序表存储结构的算法实现如下:
```c
int SeqSrch(RcdType R[], ElemType k, int n) {
    //顺序查找线性表,找到后和其前面的元素交换
    int i=0;
    while((R[i].key!=k)&&(i<n))
        //从前向后顺序查找指定结点
        i++;
    if(i<n&&i>0) {
        //若找到,则交换
        RcdType temp=R[i];R[i]=R[i-1];R[i-1]=temp;
        return --i;
        //交换成功,返回交换后的位置
    }
    else if (i == 0) return i; // 如果在第一个位置找到
    else return -1;
    //交换失败
}
```
链表的实现方式请读者自行思考。注意,链表方式实现的基本思想与上述思想相似,但要注意用链表实现时,在交换两个结点之前需要保存指向前一结点的指针。

**06.** 【解析】
1) 算法的基本设计思想:
从矩阵A的右上角(最右列)开始比较,若当前元素小于目标值,则向下寻找下一个更大的元素;若当前元素大于目标值,则从右往左依次比较,若目标值存在,则只可能在该行中。
2) 算法的实现:
```cpp
bool findkey(int A[][], int n, int k) {
    int i=0, j=n-1;
    while(i<n && j>=0) { //离开边界时查找结束
        if (A[i][j]==k) return true; //查找成功
        else if (A[i][j]>k)
            j--; //向左移动,在该行内寻找目标值
        else i++; //向下移动,查找下一个更大的元素
    }
    return false; //查找失败
}
```
3) 比较次数不超过$2n$次,时间复杂度为$O(n)$;空间复杂度为$O(1)$。

**07.** 【解答】
1) 折半查找要求元素有序顺序存储,字符串默认按字典序排序(字典序是一种比较两个字符串大小的方法,它按字母顺序从左到右逐个比较对应的字符,若某一位可比较出大小,则不再继续比较后面的字符,如 abd<acd、abc<abcd 等),对本题来说 do<for<repeat<while。若各个元素的查找概率不同,折半查找的性能不一定优于顺序查找。采用顺序查找时,元素按其查找概率的降序排列时查找长度最小。
采用顺序存储结构,数据元素按其查找概率降序排列。采用顺序查找方法。
查找成功时的平均查找长度 = $0.35 \times 1 + 0.35 \times 2 + 0.15 \times 3 + 0.15 \times 4 = 2.1$。
此时,显然查找长度比折半查找的更短。
2) 答案1:采用链式存储结构时,只能采用顺序查找,其性能和顺序表一样,类似于上题。数据元素按其查找概率降序排列,构成单链表。采用顺序查找方法。
查找成功时的平均查找长度 = $0.35 \times 1 + 0.35 \times 2 + 0.15 \times 3 + 0.15 \times 4 = 2.1$。
答案2:还可以构造成二叉排序树的形式。采用二叉链表的存储结构,构造二叉排序树,元素的存储方式见下图。采用二叉排序树的查找方法。
(二叉排序树1: 根 for, 左孩子 do, 右孩子 repeat, repeat的右孩子 while)
(二叉排序树2: 根 repeat, 左孩子 for, 右孩子 while, for的左孩子 do)
查找成功时的平均查找长度 = $0.15 \times 1 + 0.35 \times 2 + 0.35 \times 2 + 0.15 \times 3 = 2.0$。

## 7.3 树形查找

### 7.3.1 二叉排序树(BST)

构造一棵二叉排序树的目的并不是排序,而是提高查找、插入和删除关键字的速度,二叉排序树这种非线性结构也有利于插入和删除的实现。

**1. 二叉排序树的定义**

**命题追踪** 二叉排序树的应用(2013)

二叉排序树(也称二叉查找树)或者是一棵空树,或者是具有下列特性的二叉树:
1) 若左子树非空,则左子树上所有结点的值均小于根结点的值。
2) 若右子树非空,则右子树上所有结点的值均大于根结点的值。