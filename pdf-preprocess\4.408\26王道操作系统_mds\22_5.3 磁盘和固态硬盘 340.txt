2) 在操作②之后CPU一定从进程P切换到其他进程。在操作①之后CPU 调度程序才能选中进程P执行。
3) 设备驱动程序负责驱动I/O 设备工作, I/O 操作初始化, 执行具体的I/O指令。将字符从键盘控制器读入系统缓冲区是和键盘直接相关的具体操作, 完成操作③的代码属于键盘驱动程序。
4) 键盘中断处理程序执行时, 进程P还在阻塞队列, 处于阻塞态。中断处理程序、设备驱动程序、设备独立性软件都属于内核 I/O软件层, 执行相关代码时, CPU处于内核态。

## 5.3 磁盘和固态硬盘Ⓡ

在学习本节时,请读者思考以下问题:
1) 在磁盘上进行一次读/写操作需要哪几部分时间?其中哪部分时间最长?
2) 存储一个文件时,当一个磁道存储不下时,剩下的部分是存在同一个盘面的不同磁道好,还是存在同一个柱面上的不同盘面好?
本节主要介绍磁盘管理的方式。学习本节时,要重点掌握计算一次磁盘操作的时间,以及对于给定访盘的磁道序列,按照特定算法求出磁头通过的总磁道数及平均寻道数。

### 5.3.1 磁盘

**命题追踪** 磁盘容量的计算 (2019)

磁盘(Disk)是表面涂有磁性物质的物理盘片,通过一个称为磁头的导体线圈从磁盘存取数据。在读/写操作期间,磁头固定,磁盘在下面高速旋转。如图5.18所示,磁盘盘面上的数据存储在一组同心圆中,称为磁道。每个磁道与磁头一样宽,一个盘面有上千个磁道。磁道又划分为几百个扇区,每个扇区固定存储大小(如1KB),一个扇区称为一个盘块。相邻磁道及相邻扇区间通过一定的间隙分隔开,以避免精度错误。注意,因为扇区按固定圆心角度划分,所以密度从最外道向里道增加,磁盘的存储能力受限于最内道的最大记录密度。

**注意**
为了提高磁盘的存储容量,充分利用磁盘外层磁道的存储能力,现代磁盘不再将内外磁道划分为相同数目的扇区,而将盘面划分为若干环带,同一环带内的所有磁道具有相同的扇区数,显然,外层环带的磁道拥有较内层环带的磁道更多的扇区。

**命题追踪** 将簇号转化为磁盘物理地址的过程 (2019)

磁盘安装在一个磁盘驱动器中,它由磁头臂、用于旋转磁盘的转轴和用于数据输入/输出的电子设备组成。如图5.19所示,多个盘片垂直堆叠,组成磁盘组,每个盘面对应一个磁头,所有磁头固定在一起,与磁盘中心的距离相同且只能“共进退”。所有盘片上相对位置相同的磁道组成柱面。扇区是磁盘可寻址的最小单位,磁盘上能存储的物理块数目由扇区数、磁道数及磁盘面数决定,磁盘地址用“柱面号·盘面号·扇区号”表示。

①本节内容与《计算机组成原理考研复习指导》,书中的3.4节联系密切,建议结合复习。

---

读/写磁盘数据块的过程如下:①根据柱面号移动磁头臂,让磁头移动到对应的柱面;②激活对应盘面的磁头;③当磁盘旋转时,磁头从对应扇区上划过,从而完成对指定扇区的读/写。

磁盘按不同的方式可分为若干类型:磁头相对于盘片的径向方向固定的称为固定头磁盘,这种磁盘中的每个磁道有一个磁头。磁头可移动的称为活动头磁盘,磁头臂可来回伸缩定位磁道。盘片永久固定在磁盘驱动器内的称为固定盘磁盘。盘片可移动和替换的称为可换盘磁盘。最早的磁盘由IBM公司研发,称为温彻斯特磁盘,它是一种磁头活动而盘片固定的磁盘存储器。

操作系统中几乎每介绍一类资源及其管理时,都要涉及一类调度算法。用户访问文件,需要操作系统的服务,文件实际上存储在磁盘中,操作系统接收用户的命令后,经过一系列的检验访问权限和寻址过程后,最终都会到达磁盘,控制磁盘将相应的数据信息读出或修改。当有多个请求同时到达时,操作系统就要决定先为哪个请求服务,这就是磁盘调度算法要解决的问题。

### 5.3.2 磁盘的管理

**命题追踪** 新磁盘安装操作系统的过程 (2021)

**1. 磁盘初始化**

**命题追踪** 物理格式化的内容 (2017、2021)

一个新的磁盘只是一个磁性记录材料的空白盘。在磁盘可以存储数据之前,必须将它分成扇区,以便磁盘控制器能够进行读/写操作,这个过程称为低级格式化(也称物理格式化)。每个扇区通常由头部、数据区域和尾部组成。头部和尾部包含了一些磁盘控制器的使用信息,其中利用磁道号、磁头号和扇区号来标志一个扇区,利用CRC字段对扇区进行校验。

大多数磁盘在工厂时作为制造过程的一部分就已低级格式化,这种格式化能够让制造商测试磁盘,并且初始化逻辑块号到无损磁盘扇区的映射。对于许多磁盘,当磁盘控制器低级格式化时,还能指定在头部和尾部之间留下多长的数据区,通常选择256字节或512字节等。

**2. 分区**

**命题追踪** 逻辑格式化的内容 (2017、2021)

在可以使用磁盘存储文件之前,还要完成两个步骤。第一步是,将磁盘分区(我们熟悉的C盘、D盘等形式的分区),每个分区由一个或多个柱面组成,每个分区的起始扇区和大小都记录在磁盘主引导记录的分区表中。第二步是,对物理分区进行逻辑格式化(也称高级格式化),将初始的文件系统数据结构存储到磁盘上,这些数据结构包括空闲的空间和已分配的空间,以及一个初始为空的目录,建立根目录、对保存空闲磁盘块信息的数据结构进行初始化。

因为扇区的单位太小,为了提高效率,操作系统将多个相邻的扇区组合在一起,形成一簇(在Linux中称为块)。为了更高效地管理磁盘,一簇只能存放一个文件的内容,文件所占用的空间只能是簇的整数倍;若文件大小小于一簇(甚至是0字节),则也要占用一簇的空间。

**3. 引导块**

计算机启动时需要运行一个初始化程序(自举程序),它初始化CPU、寄存器、设备控制器和内存等,接着启动操作系统。为此,自举程序找到磁盘上的操作系统内核,将它加载到内存,并转到起始地址,从而开始操作系统的运行。

自举程序通常存放在ROM中,为了避免改变自举代码而需要改变 ROM 硬件的问题,通常只在ROM中保留很小的自举装入程序,而将完整功能的引导程序保存在磁盘的启动块上,启动块位于磁盘的固定位置。具有启动分区的磁盘称为启动磁盘或系统磁盘。

引导 ROM 中的代码指示磁盘控制器将引导块读入内存,然后开始执行,它可以从非固定的磁盘位置加载整个操作系统,并且开始运行操作系统。下面以 Windows 为例来分析引导过程。Windows 允许将磁盘分为多个分区,有一个分区为引导分区,它包含操作系统和设备驱动程序。Windows 系统将引导代码存储在磁盘的第0号扇区,它称为主引导记录(MBR)。引导首先运行 ROM中的代码,这个代码指示系统从 MBR 中读取引导代码。除了包含引导代码,MBR还包含一个磁盘分区表和一个标志(以指示从哪个分区引导系统),如图5.20所示。当系统找到引导分区时,读取分区的第一个扇区,称为引导扇区,并继续余下的引导过程,包括加载各种系统服务。

图5.20 Windows 磁盘的引导

**4. 坏块**

磁盘有移动部件且容错能力弱,因此容易导致一个或多个扇区损坏。部分磁盘甚至在出厂时就有坏块。根据所用的磁盘和控制器,对这些块有多种处理方式。

对于简单磁盘,如采用IDE控制器的磁盘,坏块可手动处理,如MS-DOS的Format 命令执行逻辑格式化时会扫描磁盘以检查坏块。坏块在FAT表上会标明,因此程序不会使用它们。

对于复杂的磁盘,控制器维护磁盘内的坏块列表。这个列表在出厂低级格式化时就已初始化,并在磁盘的使用过程中不断更新。低级格式化将一些块保留作为备用,操作系统看不到这些块。控制器可以采用备用块来逻辑地替代坏块,这种方案称为扇区备用。

对坏块的处理实质上就是用某种机制使系统不去使用坏块。

### 5.3.3 磁盘调度算法

**1. 磁盘的存取时间**

一次磁盘读/写操作的时间由寻找(寻道)时间、旋转延迟时间和传输时间决定。
1) 寻道时间$T_s$。活动头磁盘在读/写信息前,将磁头移动到目的磁道所需的时间。这个时间除跨越$n$条磁道的时间外,还包括启动磁头臂的时间$s$,则
$$
T_s = mn+s
$$
式中,$m$是每跨越一个磁道所需的时间,约为$0.2$ms;磁头臂的启动时间约为$2$ms。
2) 旋转延迟时间$T_r$。磁头定位到要读/写扇区所需的时间,设磁盘的旋转速度为$r$,则
$$
T_r = \frac{1}{2r}
$$
对于硬盘,典型的旋转速度为$5400$转/分,相当于一周$11.1$ms,则$T_r$为$5.55$ms;对于软盘,其旋转速度为$300 \sim 600$转/分,则$T_r$为$50 \sim 100$ms。
3) 传输时间$T_t$。从磁盘读出或向磁盘写入数据所需的时间,这个时间取决于每次所读/写的字节数$b$和磁盘的旋转速度$r$,则
$$
T_t = \frac{b}{rN}
$$
式中,$r$为磁盘每秒的转数,$N$为一个磁道上的字节数。总平均存取时间$T_a$可以表示为
$$
T_a = T_s + \frac{1}{2r} + \frac{b}{rN}
$$
在磁盘的存取时间中,寻道时间占大头,它与磁盘调度算法密切相关;而延迟时间和传输时间都与磁盘旋转速度线性相关,所以转速是磁盘性能的一个非常重要的硬件参数,也很难从操作系统层面进行优化。因此,磁盘调度的主要目标是减少磁盘的平均寻道时间。

**2. 磁盘调度算法**

目前常用的磁盘调度算法有以下几种。

**命题追踪** 各种磁盘调度算法的比较 (2010、2018)

**(1) 先来先服务 (First Come First Served, FCFS) 算法**
FCFS 算法根据进程请求访问磁盘的先后顺序进行调度,这是一种最简单的调度算法,如图5.21所示。该算法的优点是具有公平性。若只有少量进程需要访问,且大部分请求都是访问簇聚的文件扇区,则有望达到较好的性能;若有大量进程竞争使用磁盘,则这种算法在性能上往往接近于随机调度。所以,实际磁盘调度中会考虑一些更为复杂的调度算法。

**例** 磁盘请求队列中的请求顺序分别为55, 58, 39, 18, 90, 160, 150, 38, 184, 磁头的初始位置是磁道100, 采用FCFS算法时磁头的运动过程如图5.21所示。磁头共移动了$(45+3+19+21+72+70+10+112+146)=498$个磁道,平均寻道长度$=498/9 = 55.3$。

图5.21 FCFS 磁盘调度算法

**(2) 最短寻道时间优先 (Shortest Seek Time First, SSTF) 算法**

**命题追踪** 磁盘调度 SSTF 算法的应用 (2019、2021)

SSTF 算法每次选择调度的是离当前磁头最近的磁道,使每次的寻道时间最短。每次选择最小寻道时间并不能保证平均寻道时间最小,但能提供比FCFS 算法更好的性能。这种算法会产生“饥饿”现象,例如图5.22中,某时刻磁头正在18号磁道,假设在18号磁道附近频繁地出现新的请求,则磁头在18号磁道附近来回移动,使较远的磁道(如184号)长期得不到访问。

图5.22 SSTF 磁盘调度算法

**例** 磁盘请求队列中的请求顺序分别为55, 58, 39, 18, 90, 160, 150, 38, 184, 磁头初始位置是磁道100, 采用SSTF 算法时磁头的运动过程如图5.22所示。磁头共移动了$10+32+3+16+1+20+132+10+24=248$个磁道,平均寻道长度$=248/9=27.5$。

**(3) 扫描 (SCAN) 算法**
SSTF 算法产生饥饿的原因是“磁头可能在一个小范围内来回地移动”。为了防止这个问题,可以规定:只有磁头移动到最外侧磁道时才能向内移动,移动到最内侧磁道时才能向外移动,这就是 SCAN 算法的思想。它是在SSTF算法的基础上规定了磁头移动的方向,如图5.23所示。磁头移动规律与电梯运行相似,因此也称电梯调度算法。SCAN 算法对最近扫描过的区域不公平,因此它在访问局部性方面不如FCFS算法和SSTF 算法好。

图5.23 SCAN 磁盘调度算法

**命题追踪** 磁盘调度 SCAN 算法的应用 (2009、2010、2015)

**例** 磁盘请求队列中的请求顺序分别为55, 58, 39, 18, 90, 160, 150, 38, 184, 磁头初始位置是磁道100。采用SCAN算法时,不但要知道磁头的当前位置,而且要知道磁头的移动方向,假设磁头沿磁道号增大的顺序移动,则磁头的运动过程如图5.23所示。移动磁道的顺序为100, 150, 160, 184, 200, 90, 58, 55, 39, 38, 18。磁头共移动了$(50+10+24+16+110+32+3+16+1+20) = 282$个磁道,平均寻道长度 = $282/9 = 31.33$。

**(4) 循环扫描 (Circular SCAN, C-SCAN) 算法**

**命题追踪** 磁盘调度 C-SCAN 算法的应用 (2024)

在SCAN 算法的基础上规定磁头单向移动来提供服务,返回时直接快速移动至起始端而不服务任何请求。因为SCAN 算法偏向于处理那些接近最里或最外的磁道的访问请求,所以使用改进型的C-SCAN 算法来避免这个问题,如图5.24所示。

图5.24 C-SCAN 磁盘调度算法

---

**例** 磁盘请求队列中的请求顺序分别为55, 58, 39, 18, 90, 160, 150, 38, 184, 磁头初始位置是磁道100。采用C-SCAN 算法时,假设磁头沿磁道号增大的顺序移动,则磁头的运动过程如图5.24所示。移动磁道的顺序为100, 150, 160, 184, 200, 0, 18, 38, 39, 55, 58, 90。磁头共移动$50+10+24+16+200+18+20+1+16+3+32=390$个磁道,平均寻道长度$=390/9=43.33$。

采用 SCAN 算法和C-SCAN 算法时,磁头总是严格地遵循从盘面的一端到另一端,显然,在实际使用时还可以改进,即磁头只需移动到最远端的一个请求即可返回,不需要到达磁盘端点。这种改进后的 SCAN 算法和C-SCAN 算法称为LOOK调度(见图5.25)和C-LOOK调度(见图5.26),因为它们在朝一个给定方向移动前会查看是否有请求。

图5.25 LOOK 磁盘调度算法

图5.26 C-LOOK 磁盘调度算法

**注** 若无特别说明,也可默认 SCAN 算法和C-SCAN 算法为LOOK 调度和C-LOOK 调度。

以上四种磁盘调度算法的优缺点见表5.2。

**表5.2 四种磁盘调度算法的优缺点**

| 算法 | 优点 | 缺点 |
| :--- | :--- | :--- |
| FCFS 算法 | 公平、简单 | 平均寻道距离大,仅应用在磁盘I/O较少的场合 |
| SSTF 算法 | 性能比“先来先服务”好 | 不能保证平均寻道时间最短,可能出现“饥饿”现象 |
| SCAN 算法 | 寻道性能较好,可避免“饥饿”现象 | 不利于远离磁头一端的访问请求 |
| C-SCAN 算法 | 消除了对两端磁道请求的不公平 | |

**3. 减少延迟时间的方法**

除减少寻道时间外,减少延迟时间也是提高磁盘传输效率的重要因素。
磁盘是连续自转设备,磁头读入一个扇区后,需要经过短暂的处理时间,才能开始读入下一个扇区。若逻辑上相邻的块在物理上也相邻,则读入几个连续的逻辑块可能需要很长的延迟时间。为此,可对一个盘面的扇区进行交替编号[假设盘面有8个扇区,见图5.27(b)],即让逻辑上相邻的块在物理上保持一定的间隔,则读入多个连续块时能够减少延迟时间。

图5.27 盘面扇区的交替编号
(a)连续编号
(b)交替编号

---

此外,由于磁盘的所有盘面是同步转动的,逻辑块在相同柱面上也是按盘面号连续存放的,即按0号盘0号扇区、0号盘1号扇区……0号盘7号扇区、1号盘0号扇区………1号盘7号扇区、2号盘0号扇区……的顺序存放。要读入不同盘面上的连续块,如在读完0号盘7号扇区后,还需要一段处理时间,而盘面又在不停地转动,因此当磁头首次划过1号盘0号扇区(下一次要读的块)时并不能读取,只能等磁头再次划过该扇区时才能读取。为此,可对不同的盘面进行错位命名[假设有2个盘面,且已采用交替编号,见图5.28(b)],读完0号盘7号扇区后,还有一段时间处理,当磁头首次划过1号盘0号扇区时能直接读取,从而减少了延迟时间。

图5.28 磁盘盘面的错位命名
(a)普通命名
(b)错位命名

在磁盘的存取时间中,寻道时间和延迟时间属于“找”的时间,凡是“找”的时间都可以通过一定的方法优化,但传输时间是磁盘本身性质所决定的,不能通过一定的措施减少。

**4. 提高磁盘I/O速度的方法**

文件的访问速度是衡量文件系统性能最重要的因素,可从以下三个方面来优化:①改进文件的目录结构和检索目录的方法,以减少对目录的查找时间;②选取好的文件存储结构,以提高对文件的访问速度;③提高磁盘I/O速度,以实现文件中的数据在磁盘和内存之间快速传送。其中,①和②已在第4章中介绍,这里主要介绍如何提高磁盘I/O 的速度。

**命题追踪** 改善磁盘I/O性能的方法 (2012、2018)

1) 采用磁盘高速缓存。上节介绍了磁盘高速缓存的概念。
2) 调整磁盘请求顺序。即上面介绍的各种磁盘调度算法。
3) 提前读。在读磁盘当前块时,将下一磁盘块也读入内存缓冲区。
4) 延迟写。仅在缓冲区首部设置延迟写标志,然后释放此缓冲区并将其链入空闲缓冲区链表的尾部,当其他进程申请到此缓冲区时,才真正将缓冲区信息写入磁盘块。
5) 优化物理块的分布。除了上面介绍的扇区编号优化,当文件采用链接方式和索引方式组织时,应尽量将同一个文件的盘块安排在一个磁道或相邻的磁道上,以减少寻道时间。另外,将若干盘块组成簇,按簇对文件进行分配,也可减少磁头的平均移动距离。
6) 虚拟盘。是指用内存空间去仿真磁盘,又叫RAM盘。常用于存放临时文件。
7) 采用磁盘阵列RAID。因为可以采用并行交叉存取,所以能大幅提高磁盘I/O 速度。

### 5.3.4 固态硬盘

**1. 固态硬盘的特性**

**固态硬盘**(Solid State Disk, SSD)是一种基于闪存技术的存储器。它与U盘并无本质差别,只是容量更大,存取性能更好。一个SSD由一个或多个闪存芯片和闪存翻译层组成,如图5.29所示。闪存芯片替代传统磁盘中的机械驱动器,而闪存翻译层将来自 CPU的逻辑块读/写请求翻译成对底层物理设备的读/写控制信号,因此闪存翻译层相当于扮演了磁盘控制器的角色。

---

图5.29 固态硬盘(SSD)

在图5.29中,一个闪存由$B$块组成,每块由$P$页组成。通常,页的大小是$512$B~$4$KB,每块由$32 \sim 128$页组成,块的大小为$16$KB~$512$KB。数据是以页为单位读/写的。以块为单位擦除,只有在一页所属的块整个被擦除后,才能写这一页。一旦一个块被擦除,块中的每个页就都可以直接再写一次。某个块进行了若干重复写后,就会磨损坏,不能再使用。

随机写很慢,有两个原因。首先,擦除块比较慢,通常比访问页高一个数量级。其次,若写操作试图修改一个包含已有数据的页$P_i$,则该块中所有含有用数据的页都必须被复制到一个新(擦除过的)块中,然后才能进行对页$P_i$的写操作。

比起传统磁盘,SSD有很多优点,它由半导体存储器构成,没有移动的部件,因此随机访问速度比机械磁盘要快很多,也没有任何机械噪声和震动,能耗更低、抗震性好、安全性高等。
随着技术的不断发展,价格也不断下降,SSD 有望逐步取代传统机械硬盘。

**2. 磨损均衡(Wear Leveling)**

固态硬盘也有缺点,闪存的擦写寿命是有限的,一般是几百次到几千次。若直接用普通闪存组装 SSD,则实际的寿命表现可能非常令人失望————读/写数据时会集中在SSD的一部分闪存,这部分闪存的寿命会损耗得特别快。一旦这部分闪存损坏,整块SSD也就损坏了。这种磨损不均衡的情况,可能导致一块256GB的SSD只因数兆空间的闪存损坏而整块损坏。

为了弥补 SSD的寿命缺陷,引入了磨损均衡。SSD磨损均衡技术大致分为两种:
1) **动态磨损均衡**。写入数据时,优先选择擦除次数少的新闪存块。老的闪存块先歇一歇。
2) **静态磨损均衡**。这种技术更为先进,就算没有数据写入,SSD 也会监测并自动进行数据分配,让老的闪存块承担以读为主的存储任务。同时让较新的闪存块腾出空间,以承担更多以写为主的存储任务。如此一来,各闪存块的寿命损耗就都差不多。

有了这种算法加持,SSD的寿命就比较可观了。例如,对于一个256GB的SSD,若闪存的擦写寿命是500次,则需要写入125TB数据,才寿终正寝。就算每天写入10GB数据,也要三十多年才能将闪存磨损坏,更何况很少有人每天往SSD中写入10GB数据。

### 5.3.5 本节小结

本节开头提出的问题的参考答案如下。
1) 在磁盘上进行一次读/写操作需要哪几部分时间?其中哪部分时间最长?
在磁盘上进行一次读/写操作花费的时间由寻道时间、延迟时间和传输时间决定。其中寻道时间是将磁头移动到指定磁道所需要的时间,延迟时间是磁头定位到某一磁道的扇区(块号)所需要的时间,传输时间是从磁盘读出或向磁盘写入数据所经历的时间。一般来说,寻道时间因为要移动磁头臂,所以占用时间最长。
2) 存储一个文件时,当一个磁道存储不下时,剩下部分是存在同一个盘面的不同磁道好,还是存在同一个柱面上的不同盘面好?
上一问已经说到,寻道时间对于一次磁盘访问的影响是最大的,若存在同一个盘面的不同磁道,则磁头臂势必要移动,这样会大大增加文件的访问时间,而存在同一个柱面上的不同盘面就不需要移动磁道,所以一般情况下存在同一个柱面上的不同盘面更好。

### 5.3.6 本节习题精选

**一、单项选择题**

01. 文件系统和整个磁盘的关系是( )。
A. 没有磁盘就没有文件系统
B. 文件系统的组织信息放在磁盘上,这些信息和代码合在一起形成文件系统
C. 文件系统就是整个磁盘
D. 没有关系

02. 磁盘是可共享设备,但在每个时刻( )作业启动它。
A. 可以由任意多个 B. 能限定多个 C. 至少能由一个 D. 至多能由一个

03. 既可以顺序读/写,又可以按任意次序读/写的存储器有( )。
I. 光盘 II. 磁带 III. U盘 IV. 磁盘
A. II、III、IV B. I、III、IV C. III、IV D. 仅IV

04. 磁盘调度的目的是缩短( )时间。
A. 寻道 B. 延迟 C. 传送 D. 启动

05. 下列各种算法中,( )算法和其他算法存在根本的不同。
A. SCAN B. FCFS C. C-LOOK D. CLOCK

06. 磁盘上的文件以( )为单位读/写。
A. 块 B. 记录 C. 柱面 D. 磁道

07. 在磁盘中读取数据的下列时间中,影响最大的是( )。
A. 处理时间 B. 延迟时间 C. 传送时间 D. 寻道时间

08. 硬盘的操作系统引导扇区产生在( )。
A. 对硬盘进行分区时 B. 对硬盘进行低级格式化时
C. 硬盘出厂时自带 D. 对硬盘进行高级格式化时

09. 在磁盘中,每个扇区的头部和尾部都包含一些磁盘控制器的使用信息,如扇区号等,这些磁盘控制器的使用信息是在( )阶段被创建的。
A. 低级格式化 B. 分区 C. 高级格式化 D. 系统引导

10. 在下列有关旋转延迟的叙述中,不正确的是( )。
A. 旋转延迟的大小与磁盘调度算法无关
B. 旋转延迟的大小取决于磁盘空闲空间的分配程序
C. 旋转延迟的大小与文件的物理结构有关
D. 扇区数据的处理时间对旋转延迟的影响较大

11. 当设计针对传统机械式硬盘的磁盘调度算法时,主要考虑下列哪种因素对磁盘I/O 的性能影响最为显著?( )。
A. 移动磁头的延迟 B. 单个磁盘块的读/写时间
C. 磁盘平均旋转延迟 D. 磁盘最大旋转延迟

12. 下列算法中,用于磁盘调度的是( )。
A. 时间片轮转调度算法 B. LRU 算法
C. 最短寻道时间优先算法 D. 优先级高者优先算法

13. 以下算法中,( )可能出现“饥饿”现象。
A. 电梯调度 B. 最短寻道时间优先 C. 循环扫描算法 D. 先来先服务

14. 在以下算法中,( )可能随时改变磁头的运动方向。
A. 电梯调度 B. 先来先服务 C. 循环扫描算法 D. 以上答案都不对

15. 假设磁盘有256个柱面,4个磁头(盘面),每个磁道有8个扇区(编号均从0开始)。文件A在磁盘上连续存放。若文件A中的一个块存放在5号柱面、1号磁头下的7号扇区,则文件A的下一块应存放在( )。
A. 5号柱面、2号磁头下的7号扇区 B. 5号柱面、2号磁头下的0号扇区
C. 6号柱面、1号磁头下的7号扇区 D. 6号柱面、1号磁头下的0号扇区

16. 假设磁盘有100个柱面,每个柱面上有8个磁道,每个磁道有8个扇区。文件A含有6400个逻辑记录,逻辑记录大小与扇区大小一致,该文件以顺序结构的形式存放在磁盘上。文件的第0个逻辑记录存放在磁盘地址(0号柱面、0号盘面、0号扇区)中,则磁盘地址(78号柱面、6号盘面、6号扇区)中存放了该文件的第()个逻辑记录。
A. 5045 B. 5046 C. 5047 D. 5048

17. 已知某磁盘的平均转速为$r$秒/转,平均寻道时间为$T$秒,每个磁道可以存储的字节数为$N$,现向该磁盘读/写$b$字节的数据,采用随机寻道的方法,每道的所有扇区组成一个簇,其平均访问时间是( )。
A. $(r + T)b/N$ B. $b/NT$ C. $(b/N+ T)r$ D. $bT/N+r$

18. 设磁盘的转速为3000转/分,盘面划分为10个扇区,则读取一个扇区的时间为( )。
A. 20ms B. 5ms C. 2ms D. 1ms

19. 一个磁盘的转速为7200转/分,每个磁道有160个扇区,每扇区有512B,那么理想情况下,其数据传输率为( )。
A. 7200×160KB/s B. 7200KB/s C. 9600KB/s D. 19200KB/s

20. 设一个磁道访问请求序列为55, 58, 39, 18, 90, 160, 150, 38, 184, 磁头的起始位置为100,若采用SSTF(最短寻道时间优先)算法,则磁头移动( )个磁道。
A. 55 B. 184 C. 200 D. 248

21. 若当前磁头在67号磁道,依次有4个磁道号请求为35, 77, 55, 121,则当采用( )调度算法时,下一次磁头才可能到达55号磁道。
A. 循环扫描(向大磁道号方向移动) B. 最短寻道时间优先
C. 电梯调度(向小磁道号方向移动) D. 先来先服务

22. 某磁盘有1000个磁道,编号从0到999,当前磁头正在734号磁道,且向磁道号增大的方向移动。磁道请求依次为164, 845, 911, 165, 788, 432, 396, 700, 25,若分别用SCAN算法(非LOOK调度)和SSTF算法完成上述请求后,磁头移过的磁道数分别是( )。
A. 1865, 1543 B. 1688, 1738 C. 1239, 1131 D. 1239, 1738

23. 假定磁带的记录密度为400字符/英寸($1$in=$0.0254$m),每条逻辑记录为80字符,块间隙(每条逻辑记录之间的间隙)为0.4英寸,现有3000个逻辑记录需要存储,存储这些记录需要长度为( )的磁带,磁带利用率是( )。
A. 1500英寸,33.3% B. 1500英寸,43.5%
C. 1800英寸,33.3% D. 1800英寸,43.5%

24. 下列关于固态硬盘(SSD)的说法中,错误的是( )。
A. 基于闪存的存储技术 B. 随机读/写性能明显高于磁盘
C. 随机写比较慢 D. 不易磨损

25. 下列关于固态硬盘的说法中,正确的是( )。
A. 固态硬盘的写速度比较慢,性能甚至弱于常规硬盘
B. 相比常规硬盘,固态硬盘优势主要体现在连续存取的速度
C. 静态磨损均衡算法通常比动态磨损均衡算法的表现更优秀
D. 写入时,静态磨损均衡算法每次选择使用长期存放数据而很少擦写的存储块

26. 下列关于固态硬盘的说法中,错误的是( )。
A. 常规硬盘需要采用磁盘调度算法,而固态硬盘不需要
B. 固态硬盘需要进行磨损均衡,而常规硬盘不需要
C. 反复写同一个块会减少固态硬盘的寿命
D. 磨损均衡机制的目的是加快固态硬盘读/写速度

27. 【2009统考真题】假设磁头当前位于第105道,正在向磁道序号增加的方向移动。现有一个磁道访问请求序列为35, 45, 12, 68, 110, 180, 170, 195,采用SCAN调度(电梯调度)算法得到的磁道访问序列是( )。
A. 110, 170, 180, 195, 68, 45, 35, 12 B. 110, 68, 45, 35, 12, 170, 180, 195
C. 110, 170, 180, 195, 12, 35, 45, 68 D. 12, 35, 45, 68, 110, 170, 180, 195

28. 【2012统考真题】下列选项中,不能改善磁盘设备I/O性能的是( )。
A. 重排 I/O请求次序 B. 在一个磁盘上设置多个分区
C. 预读和滞后写 D. 优化文件物理块的分布

29. 【2015统考真题】某硬盘有200个磁道(最外侧磁道号为0),磁道访问请求序列为130,42, 180, 15, 199,当前磁头位于第58号磁道并从外侧向内侧移动。按照SCAN 调度方法处理完上述请求后,磁头移过的磁道数是( )。
A. 208 B. 287 C. 325 D. 382

30. 【2017统考真题】下列选项中,磁盘逻辑格式化程序所做的工作是( )。
I. 对磁盘进行分区
II. 建立文件系统的根目录
III. 确定磁盘扇区校验码所占位数
IV. 对保存空闲磁盘块信息的数据结构进行初始化
A. 仅II B. 仅II、IV C. 仅III、IV D. 仅I、II、IV

31. 【2018统考真题】下列优化方法中,可以提高文件访问速度的是( )。
I. 提前读 II. 为文件分配连续的簇
III. 延迟写 IV. 采用磁盘高速缓存
A. 仅I、II B. 仅II、III C. 仅I、III、IV D. I、II、III、IV

32. 【2018统考真题】系统总是访问磁盘的某个磁道而不响应对其他磁道的访问请求,这种现象称为磁头臂黏着。下列磁盘调度算法中,不会导致磁头臂黏着的是( )。
A. 先来先服务(FCFS) B. 最短寻道时间优先(SSTF)
C. 扫描算法(SCAN) D. 循环扫描算法(C-SCAN)

33. 【2021统考真题】某系统中磁盘的磁道数为200(0~199),磁头当前在184号磁道上。用户进程提出的磁盘访问请求对应的磁道号依次为184, 187, 176, 182, 199。若采用最短寻道时间优先调度算法(SSTF)完成磁盘访问,则磁头移动的距离(磁道数)是( )。
A. 37 B. 38 C. 41 D. 42

34. 【2024 统考真题】某个磁盘的磁道数为400(磁道号为0~399),采用循环扫描算法(C-SCAN)进行磁盘调度,完成对200号磁道的请求后,磁头向磁道号减小的方向移动。若还有7个磁盘请求,对应的磁道号分别为300, 120, 110, 0, 160, 210, 399,则完成上述磁盘访问请求后磁头移动的距离是( )。
A. 599 B. 619 C. 788 D. 799

**二、综合应用题**

01. 假定有一个磁盘组共有100个柱面,每个柱面有8个磁道,每个磁道划分成8个扇区。现有一个5000 条逻辑记录的文件,逻辑记录的大小与扇区大小相等,该文件以顺序结构存放在磁盘组上,柱面、磁道、扇区均从0开始编址,逻辑记录的编号从0开始,文件信息从0柱面、0磁道、0扇区开始存放。试问,该文件编号为3468的逻辑记录应存放在哪个柱面的第几个磁道的第几个扇区上?

02. 假设磁盘的每个磁道分成9个块,现在一个文件有A, B,…,I共9条记录,每条记录的大小与块的大小相等,设磁盘转速为27毫秒/转,每读出一块后需要2ms的处理时间。若忽略其他辅助时间,且一开始磁头在即将要读A记录的位置,试问:
1) 若将这些记录顺序存放在一个磁道上,则顺序读取该文件要多少时间?
2) 若要求顺序读取的时间最短,则应该如何安排文件的存放位置?

03. 在一个磁盘上,有1000个柱面,编号为0~999,用下面的算法计算为满足磁盘队列中的所有请求,磁头臂必须移过的磁道的数目。假设最后服务的请求是在磁道345上,并且读/写头正在朝磁道0移动。在按FCFS顺序排列的队列中包含了如下磁道上的请求:
123, 874, 692, 475, 105, 376.
1) FCFS; 2) SSTF; 3) SCAN; 4) LOOK; 5) C-SCAN; 6) C-LOOK.

04. 某软盘有40个磁道,磁头从一个磁道移至相邻磁道需要6ms。文件在磁盘上非连续存放,逻辑上相邻数据块的平均距离为13磁道,每块的旋转延迟时间及传输时间分别为100ms和25ms,问读取一个100块的文件需要多少时间?若系统对磁盘进行了整理,让同一文件的磁盘块尽可能靠拢,从而使逻辑上相邻数据块的平均距离降为2磁道,这时读取一个100块的文件需要多少时间?

05. 有一个交叉存放信息的磁盘,信息在其上的存放方法如下图所示。每个磁道有8个扇区,每个扇区大小为512B,旋转速度为3000转/分,顺时针读扇区。假定磁头已在读取信息的磁道上,0扇区转到磁头下需要1/2转,且设备对应的控制器不能同时进行输入/输出,在数据从控制器传送至内存的这段时间内,从磁头下通过的扇区数为2,请回答:
1) 依次读取一个磁道上的所有扇区需要多少时间?
2) 该磁盘的数据传输速率是多少?

06. 【2010统考真题】如下图所示,假设计算机系统采用C-SCAN(循环扫描)磁盘调度策略,使用2KB的内存空间记录16384个磁盘块的空闲状态。

1) 请说明在上述条件下如何进行磁盘块空闲状态的管理。
2) 设某单面磁盘的旋转速度为6000转/分,每个磁道有100个扇区,相邻磁道间的平均移动时间为1ms。若在某时刻,磁头位于100号磁道处,并沿着磁道号增大的方向移动(见上图),磁道号请求队列为50, 90, 30, 120,对请求队列中的每个磁道需读取1个随机分布的扇区,则读完这4个扇区点共需要多少时间?要求给出计算过程。
3) 若将磁盘替换为随机访问的Flash半导体存储器(如U盘、固态硬盘等),是否有比C-SCAN 更高效的磁盘调度策略?若有,给出磁盘调度策略的名称并说明理由;若无,说明理由。

07. 【2019统考真题】某计算机系统中的磁盘有300个柱面,每个柱面有10个磁道,每个磁道有200个扇区,扇区大小为512B。文件系统的每簇包含2个扇区。请回答下列问题:
1) 磁盘的容量是多少?
2) 设磁头在85号柱面上,此时有4个磁盘访问请求,簇号分别为100260, 60005, 101660和110560。采用最短寻道时间优先SSTF调度算法,系统访问簇的先后次序是什么?
3) 簇号100530 在磁盘上的物理地址是什么?将簇号转换成磁盘物理地址的过程由 I/O系统的什么程序完成?

08. 【2021统考真题】某计算机用硬盘作为启动盘,硬盘的第一个扇区存放主引导记录,其中包含磁盘引导程序和分区表。磁盘引导程序用于选择引导哪个分区的操作系统,分区表记录硬盘上各分区的位置等描述信息。硬盘被划分成若干分区,每个分区的第一个扇区存放分区引导程序,用于引导该分区中的操作系统。系统采用多阶段引导方式,除了执行磁盘引导程序和分区引导程序,还需要执行ROM中的引导程序。回答下列问题:
1) 系统启动过程中操作系统的初始化程序、分区引导程序、ROM中的引导程序、磁盘引导程序的执行顺序是什么?
2) 将硬盘制作为启动盘时,需要完成操作系统的安装、磁盘的物理格式化、逻辑格式化、对磁盘进行分区,执行这4个操作的正确顺序是什么?
3) 磁盘扇区的划分和文件系统根目录的建立分别是在第2)问的哪个操作中完成的?

### 5.3.7 答案与解析

**一、单项选择题**

**01. B**
文件系统不一定依赖于磁盘,也可存在于其他存储介质上,如光盘、闪存、网络等。文件系统可以只占用磁盘的一部分空间,而不是整个磁盘;一个磁盘上可以有多个文件系统,也可以没有文件系统的空间。文件系统和磁盘之间的联系密切,文件系统需要用磁盘来存储数据,而磁盘需要用文件系统来组织数据。因此,选项B正确,选项A、C和D错误。

**02. D**
磁盘是可共享设备(分时共享),是指某段时间内可以有多个用户进行访问。但某一时刻只能有一个作业可以访问。

**03. B**
顺序访问按从前到后的顺序对数据进行读/写操作,如磁带。直接访问或随机访问,则可以按任意的次序对数据进行读/写操作,如光盘、磁盘、U盘等。

**04. A**
磁盘调度是对访问磁道次序的调度,若没有合适的磁盘调度,则寻道时间会大大增加。

**05. D**
SCAN、FCFS、C-LOOK 算法都可以是磁盘调度算法,CLOCK 算法是页面置换算法。

**06. A**
文件以块为单位存放于磁盘中,文件的读/写也以块为单位。

**07. D**
对磁盘读/写时间影响最大的是寻道时间,寻道过程为机械运动,时间较长,影响较大。

**08. D**
操作系统的引导程序位于磁盘活动分区的引导扇区,因此必然产生在分区之后。分区是将磁盘分为由一个或多个柱面组成的分区(C盘、D盘等形式),每个分区的起始扇区和大小都记录在磁盘主引导记录的分区表中。而对于高级格式化(创建文件系统),操作系统将初始的文件系统数据结构存储到磁盘上,文件系统在磁盘上布局介绍详见第4章。

**09. A**
低级格式化是磁盘制造商在生产磁盘时进行的操作,它负责创建磁盘的物理结构,包括磁道、扇区的划分,并将一些控制信息(如扇区号、磁道号等)写入每个扇区的头部和尾部。这些信息在低级格式化阶段被写入磁盘,因此磁盘控制器能够理解如何访问各个扇区。

**10. D**
磁盘调度算法是为了减少寻道时间。扇区数据的处理时间主要影响传输时间。选项B、C均与旋转延迟有关,文件的物理结构与磁盘空间的分配方式相对应,包括连续分配、链接分配和索引分配。连续分配的磁盘中,文件的物理地址连续;而链接分配方式的磁盘中,文件的物理地址不连续,因此与旋转延迟都有关。

**11. A**
磁盘存取时间由寻道时间、旋转延迟时间、传输时间决定,寻道时间占的比例最大,磁盘的调度算法主要是优化寻道时间,而旋转延迟时间和传输时间难以从操作系统层面优化。

**12. C**
选项A和D可是进程调度算法。选项B可是页面淘汰算法。只有选项C是磁盘调度算法。

**13. B**
最短寻道时间优先算法中,当新的距离磁头比较近的磁盘访问请求不断被满足时,可能导致较远的磁盘访问请求被无限延迟,从而导致“饥饿”现象。

**14. B**
先来先服务算法根据磁盘请求的时间先后进行调度,因此可能随时改变磁头方向。而电梯调度、循环扫描算法均限制磁头的移动方向。

**15. B**
文件A采用连续存放方式,按照磁盘的地址结构(柱面号,磁头号,扇区号),文件A的下一块应存放在同一个柱面的同一个磁道的下一个扇区中,7号扇区已是本磁道的最后一个扇区,因此应存放在同一个柱面的下一个磁头的0号扇面,即5号柱面、2号磁头下的0号扇区。由此可见,文件A的数据是连续存储在磁盘的一组或相邻几组同心圆中的。

**16. B**
每个柱面上有8个磁道(表示有8个磁头),每个磁道有8个扇区,因此每个柱面有$8×8=64$个扇区。由题意可知,柱面号、盘面号、扇区号和逻辑记录编号都是从0开始的,因此78号柱面的6号磁道的6号扇区存放的是文件的第$78×64+6×8+6=5046$个逻辑记录。

**17. A**
将每道的所有扇区组成一个簇,意味着可以将一个磁道的所有存储空间组织成一块数据组,这样有利于提高存储速度。读/写磁盘时,磁头首先找到磁道,称为寻道,然后才可以将信息从磁道里读出或写入。读/写完一个磁道后,磁头会继续寻找下一个磁道,完成剩余的工作,所以在随机寻道的情况下,读/写一个磁道的时间要包括寻道时间和读/写磁道时间,即$T+r$秒。因为总的数据量是$b$字节,它要占用的磁道数为$b/N$个,所以总平均读/写时间为$(r+T)b/N$秒。

**18. C**
访问每条磁道的时间为$60/3000$s $= 0.02$s $=20$ms,即磁盘旋转一圈的时间为$20$ms,每个盘面10个扇区,因此读取一个扇区的时间为$20$ms/$10 = 2$ms。

**19. C**
磁盘的转速为$7200$转/分$=120$转/秒,转一圈经过160个扇区,每个扇区为$512$B,所以数据传输率$=120×160×512/1024$KB/s $= 9600$KB/s。

**20. D**
对于SSTF 算法,寻道序列应为100, 90, 58, 55, 39, 38, 18, 150, 160, 184;移动磁道次数分别为10, 32, 3, 16, 1, 20, 132, 10, 24,所以磁头移动总次数为248。另外也可以画出草图来解答,从100寻道到18需要82次,然后加上从18到184需要的$184-18=166$次,共移动$166+82=248$次。

**21. C**
当采用循环扫描算法时,磁头当前位于67号磁道,磁头正在向磁道号增大的方向移动,因此下一次处理的是77号磁道,选项A错误。当采用最短寻道时间优先算法时,77号磁道距离当前67号磁道最近,因此下一次处理的是77号磁道,选项B错误。当采用电梯调度算法时,磁头当前位于67号磁道,磁头正在向磁道号减少的方向移动,因此下一次处理的是55号磁道,选项C正确。当采用先来先服务算法时,根据请求的先后顺序进行调度,下一次处理的是35号磁道,选项D错误。

**22. C**
采用 SCAN 算法时,依次访问的磁道是788, 845, 911, 999, 700, 432, 396, 165, 164, 25,磁头移动的距离是$(999-734)+(999-25)=1239$。采用SSTF算法时,依次访问的磁道是700, 788, 845, 911, 432, 396, 165, 164, 25,磁头移动的距离是$(734-700)+(911-700)+(911-25) = 1131$。

**23. C**
一个逻辑记录所占的磁带长度为$80/400=0.2$英寸,因此存储3000条逻辑记录需要的磁带长度为$(0.2+0.4)×3000=1800$英寸,利用率为$0.2/(0.2+0.4) = 33.3\%$。

**24. D**
固态硬盘基于闪存技术,没有机械部件,随机读/写不需要机械操作,因此速度明显高于磁盘,选项A和B正确。选项C已在考点讲解中解释过。SSD的缺点是容易磨损,选项D错误。

**25. C**
SSD的写速度慢于读速度,但不至于比常规机械硬盘差,选项A错误。SSD 基于闪存技术,没有机械部件,随机存取速度很快,传统机械硬盘因为需要寻道和找扇区的时间,所以随机存取速度慢;传统机械硬盘转速很快,连续存取比随机存取快得多,因此SSD的优势主要体现在随机存取的速度上,选项B错误。静态磨损算法在没有写入数据时,SSD 监测并自动进行数据分配,因此通常表现更优秀,选项C正确。因为闪存的擦除速度较慢,若每次都选择写入存放有数据的块,会极大地降低写入速度,选项D混淆了静态磨损均衡,静态磨损均衡是指在没有写入数据时,SSD 监测并自动进行数据分配,从而使得各块的擦写更加均衡,并不是说写入时每次都选择存放老数据的块。

**26. D**
磨损均衡机制的目的是延长固态硬盘的寿命,而不是加快固态硬盘读/写速度。

**27. A**
SCAN 算法的原理类似于电梯。首先,当磁头从105道向序号增加的方向移动时,便会按照从小到大的顺序服务所有大于105的磁道号(110, 170, 180, 195);往回移动时又会按照从大到小的顺序进行服务(68, 45, 35, 12),结果如下图所示。

**28. B**
对于选项A,重排 I/O 请求次序也就是进行I/O 调度,使进程之间公平地共享磁盘访问,减少I/O 完成所需要的平均等待时间。对于选项C,缓冲区结合预读和滞后写技术对于具有重复性及阵发性的I/O 进程改善磁盘I/O性能很有帮助。对于选项D,优化文件物理块的分布可以减少寻道时间与延迟时间,从而提高磁盘性能。在一个磁盘上设置多个分区与改善设备 I/O性能并无多大联系,相反还会带来处理的复杂性,降低利用率。

**29. C**
SCAN 算法就是电梯调度算法。顾名思义,若开始时磁头向外移动,就一直要到最外侧,然后返回向内侧移动,就像电梯若往下,则一直要下到底层才会再上升一样。当前磁头位于58号并从外侧向内侧移动,先依次访问130、180和199,然后返回向外侧移动,依次访问42和15,因此磁头移过的磁道数是$(199-58) + (199-15)=325$。

**30. B**
新磁盘是空白盘,必须分成扇区以便磁盘控制器能进行读/写操作,这个过程称为低级格式化(或物理格式化)。低级格式化为每个扇区使用特别的数据结构,选项III 错误。为了使用磁盘存储文件,操作系统还需要将自己的数据结构记录在磁盘上。这分为两步。第一步是将磁盘分为由一个或多个柱面组成的分区,每个分区可以作为一个独立的磁盘,选项I错误。在分区之后,第二步是逻辑格式化(创建文件系统)。在这一步,操作系统将初始的文件系统数据结构存储到磁盘上。这些数据结构包括空闲和已分配的空间及一个初始为空的目录,选项II、IV 正确。

**31. D**
选项 II 和IV显然均能提高文件访问速度。对于选项I,提前读是指在读当前盘块时,将下一个盘块提前读入缓冲区,以便需要时直接从缓冲区中读取,提高了文件的访问速度。对于选项 III,延迟写是指先将数据写入缓冲区,并置上“延迟写”标志,以备不久之后访问,当缓冲区需要再次被分配出去时,才将缓冲区数据写入磁盘,减少了访问磁盘的次数,提高了文件的访问速度。

**32. A**
当系统中总是持续存在某个磁道的访问请求时,均持续满足最短寻道时间优先、扫描算法和循环扫描算法的访问条件,会一直服务该访问请求,尽管系统中还存在其他磁道的访问请求,但却得不到响应。而先来先服务按照请求次序进行调度,比较公平。

**33. C**
最短寻道时间优先算法总是选择调度与当前磁头所在磁道距离最近的磁道。可以得出访问序列184, 182, 187, 176, 199,从而求出移动距离之和是$0+2+5+11+23=41$。

**34. C**
采用C-SCAN 算法进行磁盘调度,完成200号磁道的请求后,磁头向磁道号减小的方向移动,依次处理160, 120, 110, 0号磁道的请求;然后,磁头向磁道号增大的方向快速移动至399号磁道,在快速移动的过程中不处理任何请求;完成399号磁道的请求后,依次处理300, 210号磁道的请求,所以磁头移动的总距离是$(200-0)+(399-0)+(399-210)=788$。调度过程如下图所示。

**二、综合应用题**

**01.【解答】**
该磁盘有8个盘面,一个柱面大小为$8×8=64$个扇区,即64条逻辑记录。所有磁头是固定在一起的,因此在存放数据时,先存满扇区,后存满磁道,再存满柱面。
编号为3468的逻辑记录对应的柱面号为$3468/64=54$;对应的磁道号为$(3468 \text{ MOD } 64) \text{ DIV } 8 = 1$;对应的扇区号为$(3468 \text{ MOD } 64) \text{ MOD } 8 = 4$。

**02.【解答】**
磁盘转速为27毫秒/转,每个磁道存放9条记录,因此读出1条记录的时间为$27/9 = 3$ms。
1) 读出并处理记录A需要5ms,此时磁头已转到记录B的中间,因此为了读出记录B,必须再转接近一圈(从记录B的中间到记录B)。后续8条记录的读取及处理与此类似,但最后一条记录的读取与处理只需5ms。于是,处理9条记录的总时间为
$$
8×(27+3) + (3 + 2) = 245\text{ms}
$$
【**另解**】注意,从开始读A到最后读完I一共转了9圈,即处理完前8条记录+读第9条记录的时间一共是$27×9=243$ms,加上最后的2ms处理时间,共$243+2=245$ms。
2) 由于每读出一条记录后需要2ms的处理时间,当读出并处理记录A时,不妨设记录A放在第1个盘块中,读/写头已移到第2个盘块的中间,为了能顺序读到记录B,应将它放到第3个盘块中,即应将记录按下表顺序存放:

| 盘块 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 |
| :--- | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: |
| 记录 | A | F | B | G | C | H | D | I | E |

这样,处理一条记录并将磁头移到下一条记录的时间是
$$
3(\text{读出})+2(\text{处理})+1(\text{等待})=6\text{ms}
$$
所以,处理9条记录的总时间为
$$
6×8+(3+2)= 53\text{ms}
$$

**03.【解答】**
1) **FCFS**: 移动磁道的顺序为345, 123, 874, 692, 475, 105, 376。磁头臂必须移过的磁道的数目为$222+751+182+217+370 +271=2013$。
2) **SSTF**: 移动磁道的顺序为345, 376, 475, 692, 874, 123, 105。磁头臂必须移过的磁道的数目为$31+99+217+182+751+18=1298$。
**注意**,磁头臂必须移过的磁道的数目之和的计算没有必要像上面一样对31, 99, 217, 182, 751, 18求和,仔细的读者会发现:从345到874是一路递增的,接着从874到105是一路递减的。所以仅需计算$(874-345) + (874-105)=1298$。这种方法是不是要比上面得出6个数后再计算它们的和要快捷一些?若之前未注意到此法,相信聪明的读者会马上回顾刚做完的1),并会仔细观察以下几问的“规律”,进而总结出自己的思路。
3) **SCAN**: 移动磁道的顺序为345, 123, 105, 0, 376, 475, 692, 874。磁头臂必须移过的磁道的数目为$222+18+105+376 +99+217+182 = 1219$。
4) **LOOK**: 移动磁道的顺序为345, 123, 105, 376, 475, 692, 874。磁头臂必须移过的磁道的数目为$222+18+271+99+217+182=1009$。
5) **C-SCAN**: 移动磁道的顺序为345, 123, 105, 0, 999, 874, 692, 475, 376。磁头臂必须移过的磁道的数目为$222+18+105 +999 +125+182+217+99 = 1967$。
6) **C-LOOK**: 移动磁道的顺序为345, 123, 105, 874, 692, 475, 376。磁头臂必须移过的磁道的数目为$222+18+769+182+217+99=1507$。

**04.【解答】**
磁盘整理前,逻辑上相邻数据块的平均距离为13磁道,读一块数据需要的时间为
$$
13×6+100+25=203\text{ms}
$$
因此,读取一个100块的文件需要的时间为
$$
203×100=20300\text{ms}
$$
磁盘整理后,逻辑上相邻数据块的平均距离为2磁道,读一块数据需要的时间为
$$
2×6+100+25=137\text{ms}
$$
因此,读取一个100块的文件需要的时间为
$$
137×100=13700\text{ms}
$$

**05.【解答】**
磁盘逆时针方向旋转按扇区来看即0, 3, 6,…这个顺序。每个号码连续的扇区正好相隔2个扇区,即数据从控制器传送到内存的时间,所以相当于磁头连续工作。
1) 由题中条件可知,旋转速度为$3000$转/分$=50$转/秒,即$20$ms/转。
读一个扇区需要的时间为$20/8 = 2.5$ms。
读一个扇区并将扇区数据送入内存需要的时间为$2.5×3=7.5$ms。
所以读出一个磁道上的所有扇区需要的时间为$20/2+8×7.5= 70$ms $= 0.07$s。
2) 每个磁道的数据量为$8×512=4$KB。
所以数据传输速率为$4$KB/$0.07$s$=4×1024$kB/($1000×0.07$s) $= 58.5$kB/s。

**注意**
表示存储容量、文件大小时,K等于1024(通常用大写的K);表示传输速率时,k等于1000(通常用小写的k),注意区别。

**06.【解答】**
1) 用位图表示磁盘的空闲状态。每位表示一个磁盘块的空闲状态,共需$16384/32 = 512$个字$=512×4$B$=2$KB,正好可放在系统提供的内存中。
2) 采用C-SCAN调度算法,访问磁道的顺序和移动的磁道数如下表所示:

| 被访问的下一个磁道号 | 移动距离(磁道数) |
| :--- | :--- |
| 120 | 20 |
| 30 | 90 |
| 50 | 20 |
| 90 | 40 |

移动的磁道数为$20+90+20+40=170$,因此总的移动磁道时间为$170$ms。
转速为$6000$转/分,因此平均旋转延迟为$5$ms,总的旋转延迟时间$=20$ms。
转速为$6000$转/分,因此读取一个磁道上的一个扇区的平均读取时间为$0.1$ms,扇区的平均读取时间为$0.1$ms,总的读取扇区的时间为$0.4$ms。
综上,读取上述磁道上所有扇区所花的总时间为$190.4$ms。
3) 采用先来先服务(FCFS)调度策略更高效。因为Flash 半导体存储器的物理结构不需要考虑寻道时间和旋转延迟,可直接按I/O请求的先后顺序服务。

**07.【解答】**
1) 磁盘容量 = 磁盘的柱面数×每个柱面的磁道数×每个磁道的扇区数×每个扇区的大小 = $(300×10×200×512/1024) \text{ KB} = 3×10^5\text{KB}$。
2) 磁头在85号柱面上,对SSTF算法而言,总是访问当前柱面距离最近的地址。注意每个簇包含2个扇区,通过计算得到,85号柱面对应的簇号为$85000 \sim 85999$。通过比较得出,系统最先访问离$85000 \sim 85999$最近的100260,随后访问离100260最近的101660,然后访问 110560,最后访问60005。顺序为100260, 101660, 110560, 60005。
3) 第100530簇在磁盘上的物理地址由其所在的柱面号、磁头号、扇区号构成。
柱面号 = $[$簇号/每个柱面的簇数$] = [100530/(10×200/2)] = 100$。
磁头号 = $[($簇号$\%$每个柱面的簇数$)/$每个磁道的簇数$] = [530/(200/2)] = 5$。
扇区号 = 扇区地址$\%$每个磁道的扇区数 = $(530×2)\%200 = 60$。
将簇号转换成磁盘物理地址的过程由磁盘驱动程序完成。

**08.【解答】**
1) 执行顺序依次是ROM中的引导程序、磁盘引导程序、分区引导程序、操作系统的初始化程序。启动系统时,首先运行 ROM 中的引导代码(bootstrap)。为执行某个分区的操作系统的初始化程序,需要先执行磁盘引导程序以指示引导到哪个分区,然后执行该分区的引导程序,用于引导该分区的操作系统。
2) 4个操作的执行顺序依次是磁盘的物理格式化、对磁盘进行分区、逻辑格式化、操作系统的安装。磁盘只有通过分区和逻辑格式化后才能安装系统和存储信息。物理格式化(也称低级格式化,通常出厂时就已完成)的作用是为每个磁道划分扇区,安排扇区在磁道中的排列顺序,并对已损坏的磁道和扇区做“坏”标记等。随后将磁盘的整体存储空间划分为相互独立的多个分区(如Windows中划分C盘、D盘等),这些分区可以用作多种用途,如安装不同的操作系统和应用程序、存储文件等。然后进行逻辑格式化(也称高级格式化),其作用是对扇区进行逻辑编号,建立逻辑盘的引导记录、文件分配表、文件目录表和数据区等。最后才是操作系统的安装。
3) 由上述分析可知,磁盘扇区的划分是在磁盘的物理格式化操作中完成的,文件系统根目录的建立是在逻辑格式化操作中完成的。

## 5.4 本章疑难点

**1. 为了增加设备分配的灵活性,成功率,可以如何改进?**
可以从以下两方面对基本的设备分配程序加以改进:
1) 增加设备的独立性。进程使用逻辑设备名请求I/O。这样,系统首先从SDT 中找出第一个该类设备的DCT。若该设备忙,则又查找第二个该类设备的DCT。仅当所有该类设备都忙时,才将进程挂到该类设备的等待队列上;只要有一个该类设备可用,系统便进一步计算分配该设备的安全性。
2) 考虑多通路情况。为防止I/O 系统的“瓶颈”现象,通常采用多通路的I/O 系统结构。此时对控制器和通道的分配同样要经过几次反复,即若设备(控制器)所连接的第一个控制器(通道)忙时,则应查看其所连接的第二个控制器(通道),仅当所有控制器(通道)都忙时,此次的控制器(通道)分配才算失败,才将进程挂到控制器(通道)的等待队列上。而只要有一个控制器(通道)可用,系统便可将它分配给进程。
设备分配过程中,先后分别访问的数据结构为 SDT→DCT→COCT→CHCT。要成功分配一个设备,必须要:①设备可用;②控制器可用;③通道可用。所以,“设备分配,要过三关”。

**2. 什么是用户缓冲区、内核缓冲区?**
5.1.4 节中讨论过:“I/O操作完成后,系统将数据从内核复制到用户空间”,这里说的是“内核”其实是指内核缓冲区,“用户空间”是指用户缓冲区。
用户缓冲区是指当用户进程读文件时,通常先申请一块内存数组,称为Buffer,用来存放读取的数据。每次read调用,将读取的数据写入Buffer,之后程序都从 buffer 中获取数据,当buffer使用完后,再进行下一次调用,填充 buffer。可见,用户缓冲区的目的是减少系统调用次数,从而降低系统在用户态与内核态之间切换的开销。
内核也有自己的缓冲区。当用户进程从磁盘读取数据时,不直接读磁盘,而将内核缓冲区中的数据复制到用户缓冲区中。若内核缓冲区中没有数据,则内核请求从磁盘读取,然后将进程挂起,为其他进程服务,等到数据已读取到内核缓冲区中时,将内核缓冲区中的数据复制到用户进程的缓冲区,才通知进程(当然,I/O模型不同,处理的方式也不同)。当用户进程需要写数据时,数据可能不直接写入磁盘,而将数据写入内核缓冲区,时机适当时(如内核缓冲区的数据积累到一定量后),内核才将内核缓冲区的数据写入磁盘。可见,内核缓冲区是为了在操作系统级别提高磁盘I/O 效率,优化磁盘写操作。