| 散列地址 | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 |
|---|---|---|---|---|---|---|---|---|---|---|---|
| 关键字 | 11 | | 14 | 7 | | 20 | 9 | | | 3 | 18 |

装填因子$\alpha=7/11$。
2) $H(14)=9$, 和关键字 3 比较, 不命中; $H_1(14)=(9+1)\%11=10$, 和 18 比较, 不命中; $H_2(14)=(9+4)\%11=2$, 和 14 比较, 命中。因此, 关键字比较序列是 3, 18, 14。
3) $H(8)=2$, 和关键字 14 比较, 不命中: $H_1(8)=(2+1)\%11=3$, 和 7 比较, 不命中; $H_2(8)=(2+4)\%11=6$, 和 9 比较, 不命中; $H_3(8)=(2+9)\%11=0$, 和 11 比较, 不命中; $H_4(8)=(2+16)\%11=7$, 是空位置, 确认查找失败。因此, 确认查找失败时的散列地址是 7。

### 归纳总结

本章的核心考查点是求平均查找长度(ASL), 以度量各种查找算法的性能。查找算法本身依托于查找结构, 查找结构又是由相同数据类型的记录或结点构成的, 所以最终落脚于数据结构类型的区别。不管是何种查找算法, 其平均查找长度的计算公式都是一样的。

查找成功的平均查找长度 $ASL_{成功} = \sum\limits_{i=1}^n p_ic_i$。

查找失败的平均查找长度 $ASL_{不成功} = \sum\limits_{j=0}^n q_jc_j$。

设一个查找集合中已有$n$个数据元素, 每个元素的查找概率为$p_i$, 查找成功的数据比较次数为$c_i(i=1,2,\dots,n)$; 不在此集合中的数据元素分布在由这$n$个元素的间隔构成的$n+1$个子集合内, 每个子集合元素的查找概率为$q_j$, 查找不成功的数据比较次数为$c_j(j=0,1,\dots,n)$。因此, 对某一特定查找算法的查找成功的$ASL_{成功}$和查找失败的$ASL_{不成功}$, 是综合考虑还是分开考虑呢?
若综合考虑, 即$\sum\limits_{i=1}^n p_i + \sum\limits_{j=0}^n q_j=1$, 若所有元素查找概率相等, 则有$p_i=q_j=\frac{1}{2n+1}$；若分开考虑, 即$\sum\limits_{i=1}^n p_i=1$, $\sum\limits_{j=0}^n q_j=1$, 若所有元素查找概率相等, 则有$p_i=\frac{1}{n}$, $q_j=\frac{1}{n+1}$。

虽然综合考虑更为理想, 但在实际应用中多数是分开考虑的, 因为对于查找不成功的情况, 很多场合下没有明确给出, 往往会被忽略掉。不过读者仍要注意的是, 这两种考虑的计算结果是不同的, 考试中一定要仔细阅读题目的要求, 以免失误。

### 思维拓展

本章介绍了几种基本的查找算法, 在实际中又会碰到怎样的查找问题呢?
**题目**：数组中有一个数字出现的次数超过了数组长度的一半, 请找出这个数字。读者也许会想到先进行排序, 位于位置$(n+1)/2$的数即要找的数, 这样最小时间复杂度就为$O(n\log_2 n)$; 若进行散列查找, 数字的范围又未知, 则应如何将时间复杂度控制在$O(n)$内呢?

**提示**
出现的次数超过数组长度的一半, 表明这个数字出现的次数比其他数字出现的次数的总和还多。所以我们可以考虑每次删除两个不同的数, 则在剩下的数中, 待查找数字出现的次数仍然超过总数的一半。通过不断重复这个过程, 不断排除其他的数字, 最终剩下的都为同一个数字, 即要找的数字。