```markdown
向下调整堆;
}
返回出队元素;

08.【解答】
1) 算法思想。
【方法1】定义含10个元素的数组A, 初始时元素值均为该数组类型能表示的最大数 MAX。
```
for M中的每个元素s
    if (s<A[9]) 丢弃A [9]并将s按升序插入A;
```
当数据全部扫描完毕, 数组A[0]~A[9] 保存的就是最小的10个数。
【方法2】定义含10个元素的大根堆H, 元素值均为该堆元素类型能表示的最大数MAX。
```
for M中的每个元素s
    if (s<H的堆顶元素) 删除堆顶元素并将s插入H;
```
当数据全部扫描完毕, 堆H中保存的就是最小的10个数。
2) 算法平均情况下的时间复杂度是$O(n)$, 空间复杂度是$O(1)$。

## 8.5 归并排序、基数排序和计数排序

### 8.5.1 归并排序

**命题追踪** 二路归并操作的功能 (2022)

归并排序与上述基于交换、选择等排序的思想不一样, 归并的含义是将两个或两个以上的有序表合并成一个新的有序表。假定待排序表含有$n$个记录, 则可将其视为$n$个有序的子表, 每个子表的长度为1, 然后两两归并, 得到$\lceil n/2 \rceil$个长度为2或1的有序表; 继续两两归并……如此重复, 直到合并成一个长度为$n$的有序表为止, 这种排序算法称为二路归并排序。
图8.9所示为二路归并排序的一个例子, 经过三趟归并后合并成了有序序列。

初始关键字
[49] [38] [65] [97] [76] [13] [27]
↓
第一趟归并后
[38 49] [65 97] [13 76] [27]
↓
第二趟归并后
[38 49 65 97] [13 27 76]
↓
第三趟归并后
[13 27 38 49 65 76 97]

图8.9 二路归并排序示例

**命题追踪** (算法题)归并排序思想的应用(2011)

Merge()的功能是将前后相邻的两个有序表归并为一个有序表。设两段有序表$A[low...mid]$、$A[mid+1...high]$存放在同一顺序表中的相邻位置, 先将它们复制到辅助数组B中。每次从B的两段中取出一个记录进行关键字的比较, 将较小者放入A中, 当B中有一段的下标超出其对应的表长(该段的所有元素都已复制到A中)时, 将另一段的剩余部分直接复制到A中。算法如下:
```c
ElemType *B=(ElemType *)malloc((n+1)*sizeof(ElemType)); //辅助数组 B
void Merge (ElemType A [], int low, int mid, int high) {
```
//表A的两段A[low...mid] 和A[mid+1...high]各自有序, 将它们合并成一个有序表
```c
    int i,j,k;
    for(k=low;k<=high;k++)
        B[k]=A[k];      //将A中所有元素复制到B中
    for(i=low,j=mid+1,k=i;i<=mid&&j<=high;k++) {
        if (B[i]<=B[j]) //比较B的两个段中的元素
            A[k]=B[i++]; //将较小值复制到A中
        else
            A[k]=B[j++];
    }
    while(i<=mid) A[k++]=B[i++]; //若第一个表未检测完, 复制
    while(j<=high) A[k++]=B[j++]; //若第二个表未检测完, 复制
}
```
**注意**
在上面的代码中, 最后两个 while 循环只有一个会执行。

一趟归并排序的操作是, 调用$\lceil n/2h \rceil$次算法 merge(), 将$L[1...n]$中前后相邻且长度为$h$的有序段进行两两归并, 得到前后相邻、长度为$2h$的有序段, 整个归并排序需要进行$\lceil \log_2 n \rceil$趟。
递归形式的二路归并排序算法是基于分治的, 其过程如下。
分解: 将含有$n$个元素的待排序表分成各含$n/2$个元素的子表, 采用二路归并排序算法对两个子表递归地进行排序。
合并: 合并两个已排序的子表得到排序结果。
```c
void MergeSort (ElemType A[],int low, int high) {
    if (low<high) {
        int mid=(low+high)/2;       //从中间划分两个子序列
        MergeSort (A, low, mid);    //对左侧子序列进行递归排序
        MergeSort (A,mid+1,high);   //对右侧子序列进行递归排序
        Merge (A, low, mid, high);  //归并
    }
}
```
**命题追踪** 归并排序和插入排序的对比(2017)
**命题追踪** 二路归并比较次数的分析(2024)

二路归并排序算法的性能分析如下:
空间效率: Merge()操作中, 辅助空间刚好为$n$个单元, 因此算法的空间复杂度为$O(n)$。
时间效率: 每趟归并的时间复杂度为$O(n)$, 共需进行$\lceil \log_2 n \rceil$趟归并, 因此算法的时间复杂度为$O(n\log_2 n)$。
稳定性: Merge()操作不会改变相同关键字记录的相对次序, 因此二路归并排序算法是一种稳定的排序算法。
适用性: 归并排序适用于顺序存储和链式存储的线性表。

**注意**
一般而言, 对于N个元素进行k路归并排序时, 排序的趟数m满足$K^m=N$, 从而$m=\log_K N$, 又考虑到m为整数, 因此$m=\lceil \log_K N \rceil$。这和前面的二路归并排序算法是一致的。

### 8.5.2 基数排序

基数排序是一种很特别的排序算法, 它不基于比较和移动进行排序, 而基于关键字各位的大小进行排序。基数排序是一种借助多关键字排序的思想对单逻辑关键字进行排序的方法。
假设长度为$n$的线性表中每个结点的关键字由$d$元组$(k^{d-1}, k^{d-2},...,k^1,k^0)$组成, 满足$0 \le k_j^i \le r-1 (0 \le j < n, 0 \le i \le d-1)$。其中$k^{d-1}$为最高位关键字, $k^0$为最低位关键字。
为实现多关键字排序,通常有两种方法:第一种是最高位优先(MSD)法,按关键字位权重递减依次逐层划分成若干更小的子序列,最后将所有子序列依次连接成一个有序序列;第二种是最低位优先(LSD)法,按关键字位权重递增依次进行排序,最后形成一个有序序列。
下面描述以$r$为基数的最低位优先基数排序的过程,在排序过程中,使用$r$个队列$Q_0, Q_1, \dots, Q_{r-1}$。基数排序的过程如下:
对$i=0, 1, \dots, d-1$, 依次做一次分配和收集(其实是一次稳定的排序过程)。
① 分配: 开始时, 把$Q_0, Q_1, \dots, Q_{r-1}$各个队列置成空队列, 然后依次考察线性表中的每个结点$a_j (j=0, 1, \dots, n-1)$, 若$a_j$的关键字$k_j^i=k$, 就把$a_j$放进$Q_k$队列中。
② 收集: 把$Q_0, Q_1, \dots, Q_{r-1}$各个队列中的结点依次首尾相接, 得到新的结点序列, 从而组成新的线性表。

**命题追踪** 基数排序的中间过程的分析(2013、2021)

通常采用链式基数排序, 假设对如下10个记录进行排序:
[278]→[109]→[063]→[930]→[589]→[184]→[505]→[269]→[008]→[083]
每个关键字是1000以下的正整数, 基数$r=10$, 在排序过程中需借助10个链队列, 每个关键字由3位子关键字构成——$K^1K^2K^3$, 分别代表百位、十位和个位, 一共进行三趟“分配”和“收集”操作。第一趟分配用最低位子关键字$K^3$进行, 将所有最低位子关键字(个位)相等的记录分配到同一个队列, 如图8.10(a)所示, 然后进行收集操作。第一趟收集后的结果如图8.10(b)所示。

(a) 第一趟分配后
q[0]: 930
q[1]:
q[2]:
q[3]: 063 → 083
q[4]: 184
q[5]: 505
q[6]:
q[7]:
q[8]: 278 → 008
q[9]: 109 → 589 → 269

(b) 第一趟收集后
[930]→[063]→[083]→[184]→[505]→[278]→[008]→[109]→[589]→[269]

图8.10 第一趟链式基数排序操作

第二趟分配用次低位子关键字$K^2$进行, 将所有次低位子关键字(十位)相等的记录分配到同一个队列, 如图8.11(a)所示。第二趟收集后的结果如图8.11(b)所示。

(a) 第二趟分配后
q[0]: 505 → 008
q[1]: 109
q[2]:
q[3]: 930
q[4]:
q[5]:
q[6]: 063 → 269
q[7]: 278
q[8]: 083 → 184 → 589
q[9]:

(b) 第二趟收集后
[505]→[008]→[109]→[930]→[063]→[269]→[278]→[083]→[184]→[589]

图8.11 第二趟链式基数排序操作

第三趟分配用最高位子关键字$K^1$进行, 将所有最高位子关键字(百位)相等的记录分配到同一个队列, 如图8.12(a)所示, 第三趟收集后的结果如图8.12(b)所示, 至此整个排序结束。

(a) 第三趟分配后
q[0]: 008 → 063 → 083
q[1]: 109 → 184
q[2]: 269 → 278
q[3]:
q[4]:
q[5]: 505 → 589
q[6]:
q[7]:
q[8]:
q[9]: 930

(b) 第三趟收集后
[008]→[063]→[083]→[109]→[184]→[269]→[278]→[505]→[589]→[930]

图8.12 第三趟链式基数排序操作

基数排序算法的性能分析如下。
空间效率: 一趟排序需要的辅助存储空间为$r$($r$个队列: $r$个队头指针和$r$个队尾指针), 但以后的排序中会重复使用这些队列, 所以基数排序的空间复杂度为$O(r)$。

**命题追踪** 元素的移动次数与序列初态无关的排序算法(2015)

时间效率: 基数排序需要进行$d$趟“分配”和“收集”操作。一趟分配需要遍历所有关键字, 时间复杂度为$O(n)$; 一趟收集需要合并$r$个队列, 时间复杂度为$O(r)$。因此基数排序的时间复杂度为$O(d(n+r))$, 它与序列的初始状态无关。
稳定性: 每一趟分配和收集都是从前往后进行的, 不会交换相同关键字的相对位置, 因此基数排序是一种稳定的排序算法。
适用性: 基数排序适用于顺序存储和链式存储的线性表。

### *8.5.3 计数排序

**命题追踪** (算法题)计数排序思想的应用(2013、2015、2018)

计数排序也是一种不基于比较的排序算法。计数排序的思想是: 对每个待排序元素$x$, 统计小于$x$的元素个数, 利用该信息就可确定$x$的最终位置。例如, 若有8个元素小于$x$, 则$x$就排在第9号位置上。当有几个元素相同时, 该排序方案还需做一定的优化。

**注意**
计数排序并不在统考大纲的范围内, 但其排序思想在历年真题中多次涉及。

在计数排序算法的实现中, 假设输入是一个数组$A[n]$, 序列长度为$n$, 我们还需要两个数组: $B[n]$存放输出的排序序列, $C[k]$存储计数值。用输入数组$A$中的元素作为数组$C$的下标(索引), 而该元素出现的次数存储在该元素作为下标的数组$C$中。算法如下:

**命题追踪** 计数排序相关的思想和代码的分析(2021)
```c
void CountSort (ElemType A[], ElemType B[], int n,int k) {
    int i,C[k];
    for(i=0;i<k;i++)
        C[i]=0;            //初始化计数数组
    for(i=0;i<n;i++)
        C[A[i]]++;        //遍历输入数组, 统计每个元素出现的次数
                            //C[A[i]]保存的是等于A[i]的元素个数
    for(i=1;i<k;i++)
        C[i]=C[i]+C[i-1];   //C[x]保存的是小于或等于x的元素个数
    for(i=n-1;i>=0;i--) {
        B[C[A[i]]-1]=A[i]; //从后往前遍历输入数组
        C[A[i]]=C[A[i]]-1; //将元素A[i]放在输出数组B[]的正确位置上
    }
}
```
第一个 for 循环执行完后, 数组$C$的值初始化为0。第二个 for 循环遍历输入数组A, 若一个输入元素的值为$x$, 则将$C[x]$值加1, 该for循环执行完后, $C[x]$中保存的是等于$x$的元素个数。第三个 for 循环通过累加计算后, $C[x]$中保存的是小于或等于$x$的元素个数。第四个 for 循环从后往前遍历数组A, 把每个元素$A[i]$放入它在输出数组B的正确位置上。若数组 A 中不存在相同的元素, 则$C[A[i]]-1$就是$A[i]$在数组B中的最终位置, 这是因为共有$C[A[i]]$个元素小于或等于$A[i]$。若数组A中存在相同的元素, 将每个元素$A[i]$放入数组B[]后, 都要将$C[A[i]]$减1, 这样, 当遇到下一个等于$A[i]$的输入元素(若存在)时, 该元素就可放在数组B中$A[i]$的前一个位置上。
假设输入数组$A[]=\{2,4,3,0,2,3\}$, 第二个for循环执行完后, 辅助数组$C$的情况如图8.13(a)所示; 第三个 for 循环执行完后, 辅助数组$C$的情况如图8.13(b)所示。图8.13(c)至图8.13(h)分别是第四个 for 循环每迭代一次后, 输出数组B和辅助数组$C$的情况。

(a) A: `2 4 3 0 2 3`, C: `1 0 2 2 1` (after 2nd for)
(b) A: `2 4 3 0 2 3`, C: `1 1 3 5 6` (after 3rd for)
(c) i=5, A[5]=3. B[C[3]-1] = B[5-1] = B[4] = 3. C[3]=4.
    A: `2 4 3 0 2 3`, B: `_ _ _ _ 3 _`, C: `1 1 3 4 6`
(d) i=4, A[4]=2. B[C[2]-1] = B[3-1] = B[2] = 2. C[2]=2.
    A: `2 4 3 0 2 3`, B: `_ _ 2 _ 3 _`, C: `1 1 2 4 6`
(e) i=3, A[3]=0. B[C[0]-1] = B[1-1] = B[0] = 0. C[0]=0.
    A: `2 4 3 0 2 3`, B: `0 _ 2 _ 3 _`, C: `0 1 2 4 6`
(f) i=2, A[2]=3. B[C[3]-1] = B[4-1] = B[3] = 3. C[3]=3.
    A: `2 4 3 0 2 3`, B: `0 _ 2 3 3 _`, C: `0 1 2 3 6`
(g) i=1, A[1]=4. B[C[4]-1] = B[6-1] = B[5] = 4. C[4]=5.
    A: `2 4 3 0 2 3`, B: `0 _ 2 3 3 4`, C: `0 1 2 3 5`
(h) i=0, A[0]=2. B[C[2]-1] = B[2-1] = B[1] = 2. C[2]=1.
    A: `2 4 3 0 2 3`, B: `0 2 2 3 3 4`, C: `0 1 1 3 5`

图8.13 计数排序的过程

由上面的过程可知, 计数排序的原理是: 数组的索引(下标)是递增有序的, 通过将序列中的元素作为辅助数组的索引, 其个数作为值放入辅助数组, 遍历辅助数组来排序。
计数排序算法的性能分析如下。
空间效率: 计数排序是一种用空间换时间的做法。输出数组的长度为$n$; 辅助的计数数组的长度为$k$, 空间复杂度为$O(n+k)$。若不把输出数组视为辅助空间, 则空间复杂度为$O(k)$。
时间效率: 上述代码的第1个和第3个for循环所花的时间为$O(k)$, 第2个和第4个for循环所花的时间为$O(n)$, 总时间复杂度为$O(n+k)$。因此, 当$k=O(n)$时, 计数排序的时间复杂度为$O(n)$; 但当$k > O(n\log_2 n)$时, 其效率反而不如一些基于比较的排序(如快速排序、堆排序等)。
稳定性: 上述代码的第4个for 循环从后往前遍历输入数组, 相同元素在输出数组中的相对位置不会改变, 因此计数排序是一种稳定的排序算法。
适用性: 计数排序更适用于顺序存储的线性表。计数排序适用于序列中的元素是整数且元素范围($0 \sim k-1$)不能太大, 否则会造成辅助空间的浪费。

### 8.5.4 本节试题精选

#### 一、单项选择题
01. 以下排序算法中, (C)在一趟结束后不一定能选出一个元素放在其最终位置上。
    A. 简单选择排序 B. 冒泡排序 C. 归并排序 D. 堆排序
02. 以下排序算法中, (C)不需要进行关键字的比较。
    A. 快速排序 B. 归并排序 C. 基数排序 D. 堆排序
03. 在下列排序算法中, 平均情况下空间复杂度为$O(n)$的是(D), 最坏情况下空间复杂度为$O(n)$的是(C)。
    I. 希尔排序 II. 堆排序 III. 冒泡排序
    IV. 归并排序 V. 快速排序 VI. 基数排序
    A. I、IV、VI B. II、V C. IV、V D. IV
04. 下列排序算法中, 排序过程中比较次数的数量级与序列初始状态无关的是(A)。
    A. 归并排序 B. 插入排序 C. 快速排序 D. 冒泡排序
05. 二路归并排序中, 归并趟数的数量级是(B)。
    A. $O(n)$ B. $O(\log n)$ C. $O(n\log n)$ D. $O(n^2)$
06. 若对27个元素只进行三趟多路归并排序, 则选取的归并路数最少为(B)。
    A. 2 B. 3 C. 4 D. 5
07. 将两个各有$N$个元素的有序表合并成一个有序表, 最少的比较次数是(A), 最多的比较次数是(B)。
    A. $N$ B. $2N-1$ C. $2N$ D. $N-1$
08. 用归并排序算法对序列{1,2,6,4,5,3,8,7}进行排序, 共需要进行(C)次比较。
    A. 12 B. 13 C. 14 D. 15
09. 一组经过第一趟二路归并排序后的记录的关键字为{25,50,15,35,80,85,20,40,36,70}, 其中包含5个长度为2的有序表, 用二路归并排序算法对该序列进行第二趟归并后的结果为(B)。
    A. 15, 25, 35, 50, 80, 20, 85, 40, 70, 36 B. 15, 25, 35, 50, 20, 40, 80, 85, 36, 70
    C. 15, 25, 50, 35, 80, 85, 20, 36, 40, 70 D. 15, 25, 35, 50, 80, 20, 36, 40, 70, 85
10. 若将中国人按照生日(不考虑年份, 只考虑月、日)来排序, 则使用下列排序算法时, 最快的是(D)。
    A. 归并排序 B. 希尔排序 C. 快速排序 D. 基数排序
11. 设线性表中每个元素有两个数据项$k_1$和$k_2$, 现对线性表按以下规则进行排序: 先看数据项$k_1$, $k_1$值小的元素在前, 大的元素在后; 在$k_1$值相同的情况下, 再看$k_2$, $k_2$值小的元素在前, 大的元素在后。满足这种要求的排序算法是(D)。
    A. 先按$k_1$进行直接插入排序, 再按$k_2$进行简单选择排序
    B. 先按$k_2$进行直接插入排序, 再按$k_1$进行简单选择排序
    C. 先按$k_1$进行简单选择排序, 再按$k_2$进行直接插入排序
    D. 先按$k_2$进行简单选择排序, 再按$k_1$进行直接插入排序
12. 对{05,46,13,55,94,17,42}进行基数排序, 一趟排序的结果是(C)。
    A. 05, 46, 13, 55, 94, 17, 42 B. 05, 13, 17, 42, 46, 55, 94
    C. 42, 13, 94, 05, 55, 46, 17 D. 05, 13, 46, 55, 17, 42, 94
13. 有$n$个十进制整数进行基数排序, 其中最大的整数为5位, 则基数排序过程中临时建立的队列个数是(D)。
    A. $n$ B. 2 C. 5 D. 10
14. 下列各种排序算法中, (C)需要的附加存储空间最大。
    A. 快速排序 B. 堆排序 C. 归并排序 D. 插入排序
15. **【2013统考真题】** 已知两个长度分别为$m$和$n$的升序链表, 若将它们合并为长度为$m+n$的一个降序链表, 则最坏情况下的时间复杂度是(D)。
    A. $O(n)$ B. $O(mn)$ C. $O(\min(m, n))$ D. $O(\max(m, n))$
16. **【2013统考真题】** 对给定的关键字序列110,119,007,911,114,120,122进行基数排序, 第二趟分配、收集后得到的关键字序列是(C)。
    A. 007, 110, 119, 114, 911, 120, 122 B. 007, 110, 119, 114, 911, 122, 120
    C. 007, 110, 911, 114, 119, 120, 122 D. 110, 120, 911, 122, 114, 007, 119
17. **【2015统考真题】** 下列排序算法中, 元素的移动次数与关键字的初始状态无关的是(C)。
    A. 直接插入排序 B. 冒泡排序 C. 基数排序 D. 快速排序
18. **【2021 统考真题】** 设数组 $S[]=\{93,946,372,9,146,151,301,485,236,327,43,892\}$, 采用最低位优先(LSD)基数排序将S排列成升序序列。第一趟分配、收集后, 元素372之前、之后紧邻的元素分别是(C)。
    Α. 43, 892 Β. 236, 301 C. 301, 892 D. 485, 301
19. **【2022 统考真题】** 使用二路归并排序对含$n$个元素的数组M进行排序时, 二路归并排序的功能是(A)。
    A. 将两个有序表合并为一个新的有序表
    B. 将M划分为两部分, 两部分的元素个数大致相等
    C. 将M划分为n个部分, 每个部分中仅含有一个元素
    D. 将M划分为两部分, 一部分元素的值均小于另一部分元素的值
20. **【2024统考真题】** 现有由关键字组成的3个有序序列(3,5)、(7,9)和(6), 若按从左至右的次序选择有序序列进行二路归并排序, 则关键字之间的总比较次数是(C)。
    A. 3 B. 4 C. 5 D. 6

#### 二、综合应用题
01. 已知序列{503, 87, 512, 61, 908, 170, 897, 275, 653, 462}, 采用非递归的二路归并排序算法对该序列做升序排序时需要几趟排序? 给出每一趟的结果。
02. 设待排序的关键字序列为{12, 2, 16, 30, 28, 10, 16*, 20, 6, 18}, 试写出使用最低位优先(LSD)基数排序算法每趟排序后的结果。
03. **【2021统考真题】** 已知某排序算法如下:
```c
void cmpCountSort (int a[],int b[], int n) {
    int i,j,*count;
    count=(int *)malloc(sizeof(int) *n); 
    //C++语言:count=new int[n];
    for(i=0;i<n;i++)
        count[i]=0;
    for(i=0;i<n-1;i++)
        for(j=i+1;j<n;j++)
            if(a[i]<a[j]) count[j]++;
            else count[i]++;
    for(i=0;i<n;i++)
        b[count[i]]= a[i];
    free (count); //C++语言:delete count;
}
```
请回答下列问题。
1) 若有 int a[]={25,-10,25,10,11,19}, b[6];, 则调用 cmpCountSort(a,b,6)后数组b中的内容是什么?
2) 若a中含有n个元素, 则算法执行过程中, 元素之间的比较次数是多少?
3) 该算法是稳定的吗? 若是, 阐述理由; 否则, 修改为稳定排序算法。

### 8.5.5 答案与解析

#### 一、单项选择题
01. C
我们知道插入排序不能保证在一趟排序结束后一定有元素放在最终位置上。事实上, 归并排序也不能保证。例如, 序列{6,5,7,8,2,1,4,3}进行一趟二路归并排序(从小到大)后为{5,6,7,8,1,2,3,4}, 显然它们都未被放在最终位置上。
02. C
基数排序是基于关键字各位的大小进行排序的, 而不是基于关键字的比较进行的。
03. D、C
归并排序在平均情况和最坏情况下的空间复杂度都是$O(n)$, 快速排序只在最坏情况下才是$O(n)$, 平均情况是$O(\log_2 n)$。因此, 归并排序是本章所有排序算法中占用辅助空间最多的。
04. A
前面已讲过选择排序的比较次数与序列初始状态无关, 归并排序的比较次数的数量级也与序列的初始状态无关。读者应能从算法的原理方面来考虑为什么和初始状态无关。
05. B
$N$个元素进行$k$路归并排序的趟数$m$满足$k^m=N$, 即$m=\lceil \log_k N \rceil$, 本题中为$\lceil \log_2 n \rceil$。
06. B
$N$个元素进行$k$路归并排序的趟数$m$满足$k^m=N$, 这里要求的是$k$, 代入可得$k=3$。
07. A、B
**注意**, 当一个表中的最小元素比另一个表中的最大元素还大时, 比较的次数是最少的, 仅比较$N$次; 而当两个表中的元素依次间隔地比较时, 即$a_1<b_1<a_2<b_2<\dots<a_N<b_N$时, 比较的次数是最多的, 为$2N-1$次。
建议读者对此举一反三: 若将本题中的两个有序表的长度分别设为$M$和$N$, 则最多(或最少)的比较次数是多少? 时间复杂度又是多少?
08. C
第一趟归并后{1,2},{4,6},{3,5},{7,8}, 共比较4次; 第二趟归并后{1,2,4,6},{3,5,7,8}, 共比较4次; 第三趟归并后{1,2,3,4,5,6,7,8}, 共比较6次。三趟归并共需进行14次比较。
09. B
这里采用二路归并排序算法, 且是第二趟排序, 因此每4个元素放在一起归并, 可将序列划分为{25,50,15,35},{80,85,20,40}和{36,70}, 分别对它们进行排序后有{15,25,35,50},{20,40,80,85}和{36,70}。
10. D
按照所有中国人的生日排序, 一方面$N$是非常大的, 另一方面关键字所含的排序码数为2, 且一个排序码的基数为12, 另一个排序码的基数为31, 都是较小的常数值, 因此采用基数排序可以在$O(N)$内完成排序过程。
11. D
本题思路来自基数排序的LSD, 首先应确定$k_1$、$k_2$的排序顺序, 若先排$k_1$再排$k_2$, 则排序结果不符合题意, 排除选项A和C。再考虑排序的稳定性, 当$k_2$排好序后, 再对$k_1$排序, 若对$k_1$排序采用的方法是不稳定的, 则对于$k_1$相同而$k_2$不同的元素可能会改变相对次序, 从而不一定能满足题设要求。直接插入排序是稳定的, 而简单选择排序是不稳定的, 只能选择选项D。
12. C
基数排序有MSD和LSD两种, 基数排序是稳定的。对于选项A, 不符合LSD和MSD; 对于选项B, 符合MSD, 但关键字42、46的相对位置发生了变化; 对于选项D, 不符合LSD和MSD。
13. D
基数排序中建立的队列个数等于进制数。
14. C
快速排序的平均空间复杂度是$O(\log n)$, 归并排序的空间复杂度是$O(n)$, 其他都是$O(1)$。
15. D
考虑两个升序链表合并, 两两比较表中元素, 每比较一次, 确定一个元素的链接位置(取较小元素, 头插法)。当一个链表比较结束后, 将另一个链表的剩余元素插入即可。最坏的情况是两个链表中的元素依次进行比较, 因为$2\max(m, n) \ge m+n$, 所以时间复杂度为$O(\max(m, n))$。此外, 每次比较把两个链表中的较小结点插入新链表的表头, 直到一个链表为空, 因为原链表是升序排列的, 要求合并后为降序排列的, 因此还要把另一个链表剩下的结点一一插入新链表的表头, 不论是最好情况还是最坏情况, 都需要遍历两个链表中的所有结点。
16. C
基数排序的第一趟排序是按照个位数字的大小来进行的, 第二趟排序是按照十位数字的大小来进行的, 排序的过程如下图所示。

[110]→[119]→[007]→[911]→[114]→[120]→[122]
分配:
e[0]: 110 → 120
e[1]: 911
e[2]: 122
e[3]:
e[4]: 114
e[5]:
e[6]:
e[7]: 007
e[8]:
e[9]: 119
收集: [110]→[120]→[911]→[122]→[114]→[007]→[119]

分配:
e[0]: 007
e[1]: 110 → 911 → 114 → 119
e[2]: 120 → 122
e[3]:
...
e[9]:
收集: [007]→[110]→[911]→[114]→[119]→[120]→[122]

17. C
基数排序的元素移动次数与序列初态无关, 而其他三种排序算法都与序列初态有关。
18. C
基数排序是一种稳定的排序算法。因为采用最低位优先(LSD)的基数排序, 即第一趟对个位进行分配和收集操作, 所以第一趟分配和收集后的结果是{151, 301, 372, 892, 93, 43, 485, 946, 146, 236, 327, 9}, 元素372之前、之后紧邻的元素分别是301和892。
19. A.
送分题。本书对归并的定义原话是“归并的含义是将两个或两个以上的有序表合并成一个新的有序表”, 而二路归并是将两个有序表合并为一个新的有序表。
20. C
从左至右对这三个有序序列进行二路归并排序的过程如下:
① 合并(3,5)和(7,9): 比较3和7(1次), 选择3; 比较5和7(1次), 选择5; 剩余元素7和9直接加入。② 合并(3,5,7,9)和(6): 比较3和6(1次), 选择3; 比较5和6(1次), 选择5; 比较7和6(1次), 选择6; 剩余元素7和9直接加入。总共比较5次。

#### 二、综合应用题
01. **【解答】**
$n=10$, 需要排序的趟数=$\lceil \log_2 10 \rceil=4$, 各趟的排序结果如下:
初始序列: 503, 87, 512, 61, 908, 170, 897, 275, 653, 462
第一趟: 87, 503, 61, 512, 170, 908, 275, 897, 462, 653 (长度为2)
第二趟: 61, 87, 503, 512, 170, 275, 897, 908, 462, 653 (长度为4)
第三趟: 61, 87, 170, 275, 503, 512, 897, 908, 462, 653 (长度为8)
第四趟: 61, 87, 170, 275, 462, 503, 512, 653, 897, 908 (长度为10)
02. **【解答】**
使用链式队列的基数排序的排序过程如下图所示。

原始序列: [12] → [2] → [16] → [30] → [28] → [10] → [16*] → [20] → [6] → [18]

**按最低位分配**
- rear[0]: 30 → 10 → 20
- rear[1]:
- rear[2]: 12 → 2
- rear[3]:
- rear[4]:
- rear[5]:
- rear[6]: 16 → 16* → 6
- rear[7]:
- rear[8]: 28 → 18
- rear[9]:
**收集**: [30] → [10] → [20] → [12] → [2] → [16] → [16*] → [6] → [28] → [18]

**按最高位分配**
- rear[0]: 2 → 6
- rear[1]: 10 → 12 → 16 → 16* → 18
- rear[2]: 20 → 28
- rear[3]: 30
- rear[4...9]:
**收集**: [2] → [6] → [10] → [12] → [16] → [16*] → [18] → [20] → [28] → [30]

需要通过2次分配和收集完成排序。
03. **【解答】**
cmpCountSort 算法基于计数排序的思想, 对序列进行排序。cmpCountSort 算法遍历数组中的元素, count 数组记录比对应待排序数组元素下标大的元素个数, 例如, count[1]=3 的意思是数组a中有三个元素比a[1]小, 即a[1]是第四大元素, a[1]的正确位置应是b[3]。
1) 排序结果为b[6]={-10,10,11,19,25,25}。
2) 由代码 for(i=0;i<n-1;i++)和 for(j=i+1;j<n;j++)可知, 在循环过程中, 每个元素都与它后面的所有元素比较一次(所有元素都两两比较一次), 比较次数之和为$(n-1)+(n-2)+\dots+1$, 所以总比较次数是$n(n-1)/2$。
3) 不是。需要将程序中的if语句修改如下:
```c
if (a[i]<=a[j]) count[j]++;
else count[i]++;
```
若不加等号, 两个相等的元素比较时, 前面元素的count值会加1, 则导致原序列中靠前的元素在排序后的序列中处于靠后的位置。

## 8.6 各种内部排序算法的比较及应用

### 8.6.1 内部排序算法的比较

前面讨论的排序算法很多, 对各种排序算法的比较是考研常考的内容。一般基于五个因素进行对比: 时间复杂度、空间复杂度、稳定性、适用性和过程特征。

**命题追踪** ▶ 各种排序算法的特点、比较和适用场景(2017、2020、2022)

从时间复杂度看: 简单选择排序、直接插入排序和冒泡排序平均情况下的时间复杂度都为$O(n^2)$, 且实现过程也较为简单, 但直接插入排序和冒泡排序最好情况下的时间复杂度可以达到$O(n)$, 而简单选择排序则与序列的初始状态无关。希尔排序作为插入排序的拓展, 对较大规模的数据都可以达到很高的效率, 但目前未得出其精确的渐近时间。堆排序利用了一种称为堆的数据结构, 可以在线性时间内完成建堆, 且在$O(n\log_2 n)$内完成排序过程。快速排序基于分治的思想, 虽然最坏情况下的时间复杂度会达到$O(n^2)$, 但快速排序的平均性能可以达到$O(n\log_2 n)$, 在实际应用中常常优于其他排序算法。归并排序同样基于分治的思想, 但其分割子序列与初始序列的排列无关, 因此它的最好、最坏和平均时间复杂度均为$O(n\log_2 n)$。

从空间复杂度看: 简单选择排序、插入排序、冒泡排序、希尔排序和堆排序都仅需借助常数个辅助空间。快速排序需要借助一个递归工作栈, 平均大小为$O(\log_2 n)$, 当然在最坏情况下可能会增长到$O(n)$。二路归并排序在合并操作中需要借助较多的辅助空间用于元素复制, 大小为$O(n)$, 虽然有方法能克服这个缺点, 但其代价是算法会很复杂而且时间复杂度会增加。

**命题追踪** 排序算法的稳定性判断及改进(2021、2023)

从稳定性看: 插入排序、冒泡排序、归并排序和基数排序是稳定的排序算法, 而简单选择排序、快速排序、希尔排序和堆排序都是不稳定的排序算法。平均时间复杂度为$O(n\log_2 n)$的稳定排序算法只有归并排序, 对于不稳定的排序算法, 只需举出一个不稳定的实例即可。对于排序算法的稳定性, 读者应能从算法本身的原理上去理解, 而不应拘泥于死记硬背。

**命题追踪** 更适合采用顺序存储的排序算法(2017)

从适用性看: 折半插入排序、希尔排序、快速排序和堆排序适用于顺序存储。直接插入排序、
```