```c
int w;
ArcNode *p;
d++;                               //路径长度增1
path[d]=u;                         //将当前顶点添加到路径中
visited[u]=1;                      //置已访问标记
if(u==v)
    print(path[]);                 //找到一条路径则输出
                                   //输出路径上的结点
p=G->adjlist[u].firstarc;          //p指向u的第一个相邻点
while(p!=NULL) {
    w=p->adjvex;                   //若顶点w未访问,递归访问它
    if(visited[w]==0)
        FindPath(G,w,v,path,d);
    p=p->nextarc;                  //p指向u的下一个相邻点
}
visited[u]=0;                      //恢复环境,使该顶点可重新使用
```

### 6.4 图的应用
本节是历年考查的重点。图的应用主要包括：最小生成(代价)树、最短路径、拓扑排序和关键路径。一般而言,这部分内容直接以算法设计题形式考查的可能性偏小,而更多的是结合图的实例来考查算法的具体操作过程,读者必须学会手工模拟给定图的各个算法的执行过程。此外,还需掌握对给定模型建立相应的图去解决问题的方法。

### 6.4.1 最小生成树
一个连通图的生成树包含图的所有顶点,并且只含尽可能少的边。对于生成树来说,若砍去一条边,则会使生成树变成非连通图;若增加一条边,则会在图中形成一条回路。
对于一个带权连通无向图$G$,生成树不同,每棵树的权(树中所有边的权值之和)也可能不同。权值之和最小的那棵生成树称为$G$的最小生成树(Minimum-Spanning-Tree, MST)。

**命题追踪** 最小生成树的性质(2012、2017)

不难看出,最小生成树具有如下性质:
1) 若图$G$中存在权值相同的边,则$G$的最小生成树可能不唯一,即最小生成树的树形不唯一。当图$G$中的各边权值互不相等时,$G$的最小生成树是唯一的;若无向连通图$G$的边数比顶点数少1,即$G$本身是一棵树时,则$G$的最小生成树就是它本身。
2) 虽然最小生成树不唯一,但其对应的边的权值之和总是唯一的,而且是最小的。
3) 最小生成树的边数为顶点数减1。

**命题追踪** 最小生成树中某顶点到其他顶点是否具有最短路径的分析(2023)

**注意**
最小生成树中所有边的权值之和最小,但不能保证任意两个顶点之间的路径是最短路径。如下图所示,最小生成树中$A$到$C$的路径长度为5,但图中$A$到$C$的最短路径长度为4。

(图示：左侧为一个带权无向图，顶点为A, B, C, D。边的权值分别为(A,D):1, (A,B):4, (B,C):2, (D,C):3。)

(图示：右侧为左图的最小生成树，顶点为A, B, C, D。边的权值分别为(A,D):1, (A,B):3, (B,C):2。)
<center>最小生成树</center>