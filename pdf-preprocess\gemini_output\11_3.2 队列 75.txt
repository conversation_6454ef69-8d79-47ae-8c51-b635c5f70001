好的，我会将您提供的PDF内容转换为Markdown格式，并严格遵循您提出的所有格式要求。

---

## 第3章 栈、队列和数组

```c
//入栈成功返回1,否则返回0
if(i<0||i>1){
    printf("栈号输入不对");
    exit(0);
}
if(s.top[1]-s.top[0]==1) {
    printf("栈已满\n");
    return 0;
}
switch(i) {
    case 0: s.stack[++s.top[0]]=x; return 1; break;
    case 1: s.stack[--s.top[1]]=x; return 1;
}
```

(2) 出栈操作
代码如下：
```c
elemtp pop(int i) {
//出栈算法。i代表栈号,i=0时为S1栈,i=1时为S2栈
//出栈成功返回出栈元素,否则返回-1
    if(i<0||i>1){
        printf("栈号输入错误\n");
        exit(0);
    }
    switch(i) {
        case 0:
            if(s.top[0]==-1){
                printf("栈空\n");
                return -1;
            }
            else
                return s.stack[s.top[0]--];
            break;
        case 1:
            if(s.top[1]==maxsize) {
                printf("栈空\n");
                return -1;
            }
            else
                return s.stack[s.top[1]++];
            break;
    }//switch
}
```

### 3.2 队列

### 3.2.1 队列的基本概念

**1. 队列的定义**

队列(Queue)简称队，也是一种操作受限的线性表，只允许在表的一端进行插入，而在表的另一端进行删除。向队列中插入元素称为入队或进队；删除元素称为出队或离队。这和我们日常生活中的排队是一致的，最早排队的也是最早离队的，其操作的特性是先进先出(First In First Out, FIFO)，如图3.5所示。

<center>出队列 ← a₁, a₂, a₃, a₄, a₅ ← 入队列</center>
<center>↑ 　　　　　　　　 ↑</center>
<center>队头 　　　　　　　 队尾</center>
<center>图3.5 队列示意图</center>

**队头(Front)**：允许删除的一端，也称队首。
**队尾(Rear)**：允许插入的一端。
**空队列**：不含任何元素的空表。

**2. 队列常见的基本操作**

**InitQueue(&Q)**：初始化队列，构造一个空队列Q。
**QueueEmpty(Q)**：判队列空，若队列Q为空返回true，否则返回false。
**EnQueue(&Q,x)**：入队，若队列Q未满，将x加入，使之成为新的队尾。
**DeQueue(&Q,&x)**：出队，若队列Q非空，删除队首元素，并用x返回。
**GetHead(Q,&x)**：读队首元素，若队列Q非空，则将队首元素赋值给x。
需要注意的是，栈和队列是操作受限的线性表，因此不是任何对线性表的操作都可以作为栈和队列的操作。比如，不可以随便读取栈或队列中间的某个数据。

### 3.2.2 队列的顺序存储结构

**1. 队列的顺序存储**

队列的顺序实现是指分配一块连续的存储单元存放队列中的元素，并附设两个指针：队首指针$front$指向队首元素，队尾指针$rear$指向队尾元素的下一个位置(不同教材对$front$和$rear$的定义可能不同，例如，可以让$rear$指向队尾元素、$front$指向队首元素。对于不同的定义，出入队的操作是不同的，本节后面有一些相关的习题，读者可以结合习题思考)。
队列的顺序存储类型可描述为
```c
#define MaxSize 50              //定义队列中元素的最大个数
typedef struct {
    ElemType data[MaxSize];     //用数组存放队列元素
    int front,rear;             //队首指针和队尾指针
}SqQueue;
```
初始时：$Q.front=Q.rear=0$。
入队操作：队不满时，先送值到队尾元素，再将队尾指针加1。
出队操作：队不空时，先取队首元素值，再将队首指针加1。
图3.6(a)所示为队列的初始状态，有$Q.front==Q.rear==0$成立，该条件可以作为队列判空的条件。

<center></center>
<center>图3.6 队列的操作</center>

但能否用$Q.rear==MaxSize$作为队列满的条件呢？显然不能，图3.6(d)中，队列中仅有一个元素，但仍满足该条件。这时入队出现“上溢出”，但这种溢出并不是真正的溢出，在$data$数组中依然存在可以存放元素的空位置，所以是一种“假溢出”。

**2. 循环队列**

上面指出了顺序队列“假溢出”的问题，这里引出循环队列的概念。将顺序队列臆造为一个环状的空间，即把存储队列元素的表从逻辑上视为一个环，称为循环队列。当队首指针$Q.front=MaxSize-1$后，再前进一个位置就自动到0，这可以利用除法取模运算(%)来实现。

**命题追踪** 特定条件下循环队列队头/队尾指针的初值(2011)

初始时：$Q.front=Q.rear=0$。
队首指针进1：$Q.front=(Q.front+1)\%MaxSize$。
队尾指针进1：$Q.rear=(Q.rear+1)\%MaxSize$。
队列长度：$(Q.rear+MaxSize-Q.front)\%MaxSize$。
出入队时：指针都按顺时针方向进1(如图3.7所示)。

**命题追踪** 特定条件下循环队列队空/队满的判断条件(2014)

那么，循环队列队空和队满的判断条件是什么呢？显然，队空的条件是$Q.front==Q.rear$。若入队元素的速度快于出队元素的速度，则队尾指针很快就会赶上队首指针，如图3.7(d1)所示，此时可以看出队满时也有$Q.front==Q.rear$。循环队列出入队示意图如图3.7所示。

<center></center>
<center>图3.7 循环队列出入队示意图</center>

为了区分是队空还是队满的情况，有三种处理方式：
1) 牺牲一个单元来区分队空和队满，入队时少用一个队列单元，这是一种较为普遍的做法，约定以“队首指针在队尾指针的下一位置作为队满的标志”，如图3.7(d2)所示。
队满条件：$(Q.rear+1)\%MaxSize==Q.front$。
队空条件：$Q.front==Q.rear$。
队列中元素的个数：$(Q.rear-Q.front+MaxSize) \%MaxSize$。
2) 类型中增设$size$数据成员，表示元素个数。若删除成功，则$size$减1，若插入成功，则$size$加1，队空时$Q.size==0$；队满时$Q.size==MaxSize$，两种情况都有$Q.front==Q.rear$。
3) 类型中增设$tag$数据成员，以区分是队满还是队空。删除成功置$tag=0$，若导致$Q.front==Q.rear$，则为队空；插入成功置$tag=1$，若导致$Q.front==Q.rear$，则为队满。

**3. 循环队列的操作**

(1) 初始化
```c
void InitQueue (SqQueue &Q) {
    Q.rear=Q.front=0;       //初始化队首、队尾指针
}
```
(2) 判队空
```c
bool isEmpty (SqQueue Q) {
    if (Q.rear==Q.front)    //队空条件
        return true;
    else
        return false;
}
```
(3) 入队
```c
bool EnQueue (SqQueue &Q, ElemType x) {
    if((Q.rear+1)%MaxSize==Q.front) //队满则报错
        return false;
    Q.data[Q.rear]=x;
    Q.rear=(Q.rear+1)%MaxSize;      //队尾指针加1取模
    return true;
}
```
(4) 出队
```c
bool DeQueue (SqQueue &Q, ElemType &x) {
    if (Q.rear==Q.front)            //队空则报错
        return false;
    x=Q.data[Q.front];
    Q.front=(Q.front+1)%MaxSize;    //队首指针加1取模
    return true;
}
```

### 3.2.3 队列的链式存储结构

**1. 队列的链式存储**

**命题追踪** 根据需求分析队列适合的存储结构(2019)

队列的链式表示称为链式队列，它实际上是一个同时有队首指针和队尾指针的单链表，如图3.8所示。队首指针指向队头结点，队尾指针指向队尾结点，即单链表的最后一个结点。

<center>front → [a₁|•]→[a₂|•]→...→[aₙ|Λ] ← rear</center>
<center>图3.8 不带队头结点的链式队列</center>

队列的链式存储类型可描述为
```c
typedef struct LinkNode {           //链式队列结点
    ElemType data;
    struct LinkNode *next;
} LinkNode;

typedef struct {                    //链式队列
    LinkNode *front, *rear;         //队列的队头和队尾指针
} LinkQueue;
```
不带头结点时，当$Q.front==NULL$且$Q.rear==NULL$时，链式队列为空。

**命题追踪** 链式队列队空的判断(2019)

入队时，建立一个新结点，将新结点插入到链表的尾部，并让$Q.rear$指向这个新插入的结点(若原队列为空队，则令$Q.front$也指向该结点)。出队时，首先判断队是否为空，若不空，则取出队首元素，将其从链表中删除，并让$Q.front$指向下一个结点(若该结点为最后一个结点，则置$Q.front$和$Q.rear$都为$NULL$)。
不难看出，不带头结点的链式队列在操作上往往比较麻烦，因此通常将链式队列设计成一个带头结点的单链表，这样插入和删除操作就统一了，如图3.9所示。

<center>front → [ |•]→[a₁|•]→...→[aₙ|•] ← rear</center>
<center>↑</center>
<center>rear</center>
<center>图3.9 带队头结点的链式队列</center>

用单链表表示的链式队列特别适合于数据元素变动比较大的情形，而且不存在队列满且产生溢出的问题。另外，假如程序中要使用多个队列，与多个栈的情形一样，最好使用链式队列，这样就不会出现存储分配不合理和“溢出”的问题。

**2. 链式队列的基本操作**

**命题追踪** 链式队列出队/入队操作的基本过程(2019)

(1) 初始化
```c
void InitQueue (LinkQueue &Q) { //初始化带头结点的链式队列
    Q.front=Q.rear=(LinkNode*)malloc(sizeof(LinkNode));//建立头结点
    Q.front->next=NULL;         //初始为空
}
```
(2) 判队空
```c
bool IsEmpty (LinkQueue Q) {
    if(Q.front==Q.rear)         //判空条件
        return true;
    else
        return false;
}
```
(3) 入队
```c
void EnQueue (LinkQueue &Q, ElemType x) {
    LinkNode *s=(LinkNode *)malloc(sizeof(LinkNode));//创建新结点
    s->data=x;
    s->next=NULL;
    Q.rear->next=s;             //插入链尾
    Q.rear=s;                   //修改尾指针
}
```
(4) 出队
```c
bool DeQueue (LinkQueue &Q, ElemType &x) {
    if(Q.front==Q.rear)         //空队
        return false;
    LinkNode *p=Q.front->next;
    x=p->data;
    Q.front->next=p->next;
    if (Q.rear==p)
        Q.rear=Q.front;         //若原队列中只有一个结点,删除后变空
    free(p);
    return true;
}
```

### 3.2.4 双端队列

双端队列是指允许两端都可以进行插入和删除操作的线性表，如图3.10所示。双端队列两端的地位是平等的，为了方便理解，将左端也视为前端，右端也视为后端。

<center>前端 　　　　　　　　　　　　　　 后端</center>
<center>插入 ← a₁　 a₂　 a₃ 　…　 aₙ-₁　 aₙ → 插入</center>
<center>删除 → 　　　　　　　　　　　　　　 ← 删除</center>
<center>图3.10 双端队列</center>

在双端队列入队时，前端进的元素排列在队列中后端进的元素的前面，后端进的元素排列在队列中前端进的元素的后面。在双端队列出队时，无论是前端还是后端出队，先出的元素排列在后出的元素的前面。**思考**：如何由入队序列$a,b,c,d$得到出队序列$d,c,a,b$?

**命题追踪** 双端队列出队/入队操作模拟(2010、2021)

输出受限的双端队列：允许在一端进行插入和删除，但在另一端只允许插入的双端队列称为输出受限的双端队列，如图3.11所示。

<center>前端 　　　　　　　　　　　　　　 后端</center>
<center>插入 ← a₁　 a₂　 a₃ 　…　 aₙ-₁　 aₙ → 插入</center>
<center>删除 → 　　　　　　　　　　　　　　 </center>
<center>图3.11 输出受限的双端队列</center>

输入受限的双端队列：允许在一端进行插入和删除，但在另一端只允许删除的双端队列称为输入受限的双端队列，如图3.12所示。若限定双端队列从某个端点插入的元素只能从该端点删除，则该双端队列就蜕变为两个栈底相邻接的栈。

<center>前端 　　　　　　　　　　　　　　 后端</center>
<center>插入 ← a₁　 a₂　 a₃ 　…　 aₙ-₁　 aₙ → 删除</center>
<center>删除 → 　　　　　　　　　　　　　　 </center>
<center>图3.12 输入受限的双端队列</center>

**例** 设有一个双端队列，输入序列为1,2,3,4，试分别求出以下条件的输出序列。
(1) 能由输入受限的双端队列得到，但不能由输出受限的双端队列得到的输出序列。
(2) 能由输出受限的双端队列得到，但不能由输入受限的双端队列得到的输出序列。
(3) 既不能由输入受限的双端队列得到，又不能由输出受限的双端队列得到的输出序列。
**解**：先看输入受限的双端队列，如图3.13所示。假设end1端输入1,2,3,4，则end2端的输出相当于队列的输出，即1,2,3,4；而end1端的输出相当于栈的输出，$n=4$时仅通过end1端有14种输出序列(由Catalan公式得出)，仅通过end1端不能得到的输出序列有$4!-14=10$种：
1, 4, 2, 3
2, 4, 1, 3
3, 4, 1, 2
3, 1, 4, 2
3, 1, 2, 4
4, 3, 1, 2
4, 1, 3, 2
4, 2, 3, 1
4, 2, 1, 3
4, 1, 2, 3
通过end1和end2端混合输出，可以输出这10种中的8种，参看下表。其中，$S_L, X_L$分别代表end1端的入队和出队，$X_R$代表end2端的出队。

| 输出序列 | 入队出队顺序 | 输出序列 | 入队出队顺序 |
| :--- | :--- | :--- | :--- |
| 1,4,2,3 | $S_LX_RS_LS_LS_LX_LX_RX_R$ | 3,1,2,4 | $S_LS_LS_LX_LS_LX_RX_RX_R$ |
| 2,4,1,3 | $S_LS_LX_LS_LS_LX_LX_RX_R$ | 4,1,2,3 | $S_LS_LS_LS_LX_LX_RX_RX_R$ |
| 3,4,1,2 | $S_LS_LS_LX_LS_LX_LX_RX_R$ | 4,1,3,2 | $S_LS_LS_LS_LX_LX_RX_LX_R$ |
| 3,1,4,2 | $S_LS_LS_LX_LX_RS_LX_LX_R$ | 4,3,1,2 | $S_LS_LS_LS_LX_LX_LX_RX_R$ |

剩下两种是不能通过输入受限的双端队列输出的，即4,2,3,1和4,2,1,3。
再看输出受限的双端队列，如图3.14所示。假设end1端和end2端都能输入，仅end2端可以输出。若都从end2端输入，就是一个栈了。当输入序列为1,2,3,4时，输出序列有14种。对于其他10种不能得到的输出序列，交替从end1和end2端输入，还可以输出其中8种。设$S_L$代表end1端的输入，$S_R、X_R$分别代表end2端的输入和输出，则可能的输出序列见下表。

<center>end1 ↔ end2</center>
<center>图3.13 例题中输入受限的双端队列</center>

<center>end1 ↔ end2</center>
<center>图3.14 例题中输出受限的双端队列</center>

| 输出序列 | 入队出队顺序 | 输出序列 | 入队出队顺序 |
| :--- | :--- | :--- | :--- |
| 1,4,2,3 | $S_LX_RS_LS_LS_RX_RX_RX_R$ | 3,1,2,4 | $S_LS_LS_RX_RX_RS_LX_RX_R$ |
| 2,4,1,3 | $S_LS_RX_RS_LS_RX_RX_RX_R$ | 4,1,2,3 | $S_LS_LS_LS_RX_RX_RX_RX_R$ |
| 3,4,1,2 | $S_LS_LS_RX_RS_RX_RX_RX_R$ | 4,2,1,3 | $S_LS_RS_LS_RX_RX_RX_RX_R$ |
| 3,1,4,2 | $S_LS_LS_RX_RX_RS_RX_RX_R$ | 4,3,1,2 | $S_LS_LS_RS_RX_RX_RX_RX_R$ |

通过输出受限的双端队列不能得到的两种输出序列是4,1,3,2和4,2,3,1。
综上所述：
1) 能由输入受限的双端队列得到，但不能由输出受限的双端队列得到的是4,1,3,2。
2) 能由输出受限的双端队列得到，但不能由输入受限的双端队列得到的是4,2,1,3。
3) 既不能由输入受限的双端队列得到，又不能由输出受限的双端队列得到的是4,2,3,1。

**提示**
实际双端队列的考题不会这么复杂，通常仅判断序列是否满足题设条件，代入验证即可。

### 3.2.5 本节试题精选

一、单项选择题

**01.** 栈和队列的主要区别在于( )。
A. 它们的逻辑结构不一样
B. 它们的存储结构不一样
C. 所包含的元素不一样
D. 插入、删除操作的限定不一样

**02.** 队列的“先进先出”特性是指( )。
I. 最后插入队列中的元素总是最后被删除
II. 当同时进行插入、删除操作时，总是插入操作优先
III. 每当有删除操作时，总要先做一次插入操作
IV. 每次从队列中删除的总是最早插入的元素
A. I
B. I和IV
C. II和III
D. IV

**03.** 允许对队列进行的操作有( )。
A. 对队列中的元素排序
B. 取出最近入队的元素
C. 在队列元素之间插入元素
D. 删除队首元素

**04.** 一个队列的入队顺序是1,2,3,4，则出队的输出顺序是( )。
A. 4,3,2,1
B. 1,2,3,4
C. 1,4,3,2
D. 3,2,4,1

**05.** 循环队列存储在数组A[0...n]中，入队时的操作为( )。
A. $rear=rear+1$
B. $rear=(rear+1) \mod (n-1)$
C. $rear=(rear+1) \mod n$
D. $rear=(rear+1) \mod (n+1)$

**06.** 已知循环队列的存储空间为数组A[21]，front指向队首元素的前一个位置，rear指向队尾元素，假设当前front和rear的值分别为8和3，则该队列的长度为( )。
A. 5
B. 6
C. 16
D. 17

**07.** 若用数组A[0...5]实现循环队列，且当前rear和front的值分别为1和5，当从队列中删除一个元素，再加入两个元素后，rear和front的值分别为( )。
A. 3和4
B. 3和0
C. 5和0
D. 5和1

**08.** 假设用数组Q[MaxSize]实现循环队列，队首指针front指向队首元素的前一位置，队尾指针rear指向队尾元素，则判断该队列为空的条件是( )。
A. $Q.rear==(Q.front+1)\%MaxSize$
B. $(Q.rear+1)\%MaxSize==Q.front+1$
C. $(Q.rear+1)\%MaxSize==Q.front$
D. $Q.rear==Q.front$

**09.** 假设循环队列Q[MaxSize]的队首指针为front，队尾指针为rear，队列的最大容量为MaxSize，此外，该队列再没有其他数据成员，则判断该队列已满条件是( )。
A. $Q.front==Q.rear$
B. $Q.front+Q.rear>=MaxSize$
C. $Q.front==(Q.rear+1)\%MaxSize$
D. $Q.rear==(Q.front+1)\%MaxSize$

**10.** 假设用A[0...n]实现循环队列，front、rear分别指向队首元素的前一个位置和队尾元素。若用$(rear+1)\%(n+1)==front$作为队满标志，则( )。
A. 可用$front==rear$作为队空标志
B. 队列中最多可有n+1个元素
C. 可用$front>rear$作为队空标志
D. 可用$(front+1)\%(n+1)==rear$作为队空标志

**11.** 与顺序队列相比，链式队列的( )。
A. 优点是队列的长度不受限制
B. 优点是入队和出队时间效率更高
C. 缺点是不能进行顺序访问
D. 缺点是不能根据队首指针和队尾指针计算队列的长度

**12.** 下列描述的几种链表中，最适合用作队列的是( )。
A. 带队首指针和队尾指针的循环单链表
B. 带队首指针和队尾指针的非循环单链表
C. 只带队首指针的非循环单链表
D. 只带队首指针的循环单链表

**13.** 下列描述的几种链表中，最不适合用作链式队列的是( )。
A. 只带队首指针的非循环双链表
B. 只带队首指针的循环双链表
C. 只带队尾指针的循环双链表
D. 只带队尾指针的循环单链表

**14.** 在用单链表实现队列时，队头设在链表的( )位置。
A. 链头
B. 链尾
C. 链中
D. 以上都可以

**15.** 用链式存储方式的队列进行删除操作时需要( )。
A. 仅修改头指针
B. 仅修改尾指针
C. 头尾指针都要修改
D. 头尾指针可能都要修改

**16.** 在一个链式队列中，假设队首指针为front，队尾指针为rear，x所指向的元素需要入队，则需要执行的操作为( )。
A. $front=x, front=front->next$
B. $x->next=front->next, front=x$
C. $rear->next=x, rear=x$
D. $rear->next=x, x->next=NULL, rear=x$

**17.** 假设循环单链表表示的队列长度为n，队头固定在链表尾，若只设头指针，则入队操作的时间复杂度为( )。
A. $O(n)$
B. $O(1)$
C. $O(n^2)$
D. $O(n\log n)$

**18.** 假设输入序列为1,2,3,4,5，利用两个队列进行出入队操作，不可能输出的序列是( )。
A. 1,2,3,4,5
B. 5,2,3,4,1
C. 1,3,2,4,5
D. 4,1,5,2,3

**19.** 若以1,2,3,4作为双端队列的输入序列，则既不能由输入受限的双端队列得到，又不能由输出受限的双端队列得到的输出序列是( )。
A. 1,2,3,4
B. 4,1,3,2
C. 4,2,3,1
D. 4,2,1,3

**20.**【2010统考真题】某队列允许在其两端进行入队操作，但仅允许在一端进行出队操作。若元素a,b,c,d,e依次入此队列后再进行出队操作，则不可能得到的出队序列是( )。
A. b,a,c,d,e
B. d,b,a,c,e
C. d,b,c,a,e
D. e,c,b,a,d

**21.**【2011统考真题】已知循环队列存储在一维数组A[0...n-1]中，且队列非空时front和rear分别指向队首元素和队尾元素。若初始时队列为空，且要求第一个进入队列的元素存储在A[0]处，则初始时front和rear的值分别是( )。
A. 0, 0
B. 0, n-1
C. n-1, 0
D. n-1, n-1

**22.**【2014统考真题】循环队列放在一维数组A[0...M-1]中，end1指向队首元素，end2指向队尾元素的后一个位置。假设队列两端均可进行入队和出队操作，队列中最多能容纳M-1个元素。初始时为空。下列判断队空和队满的条件中，正确的是( )。
A. 队空：$end1==end2$；队满：$end1==(end2+1) \mod M$
B. 队空：$end1==end2$；队满：$end2==(end1+1) \mod (M-1)$
C. 队空：$end2==(end1+1) \mod M$；队满：$end1==(end2+1) \mod M$
D. 队空：$end1==(end2+1) \mod M$；队满：$end2==(end1+1) \mod (M-1)$

**23.**【2018统考真题】现有队列Q与栈S，初始时Q中的元素依次是1,2,3,4,5,6(1在队头)，S为空。若仅允许下列3种操作：①出队并输出出队元素；②出队并将出队元素入栈；③出栈并输出出栈元素，则不能得到的输出序列是( )。
A. 1,2,5,6,4,3
B. 2,3,4,5,6,1
C. 3,4,5,6,1,2
D. 6,5,4,3,2,1

**24.**【2021统考真题】初始为空的队列Q的一端仅能进行入队操作，另外一端既能进行入队操作又能进行出队操作。若Q的入队序列是1,2,3,4,5，则不能得到的出队序列是( )。
A. 5,4,3,1,2
B. 5,3,1,2,4
C. 4,2,1,3,5
D. 4,1,3,2,5

二、综合应用题

**01.** 若希望循环队列中的元素都能得到利用，则需设置一个标志域tag，并以tag的值为0或1来区分队首指针front和队尾指针rear相同时的队列状态是“空”还是“满”。试编写与此结构相应的入队和出队算法。

**02.** Q是一个队列，S是一个空栈，实现将队列中的元素逆置的算法。

**03.** 利用两个栈S1和S2来模拟一个队列，已知栈的4个运算定义如下：
`Push(S,x);` //元素x入栈S
`Pop(S,x);` //S出栈并将出栈的值赋给x
`StackEmpty(S);` //判断栈是否为空
`StackOverflow(S);` //判断栈是否为满
如何利用栈的运算来实现该队列的3个运算(形参由读者根据要求自己设计)?
`Enqueue;` //将元素x入队
`Dequeue;` //出队，并将出队元素存储在x中
`QueueEmpty;` //判断队列是否为空

**04.**【2019统考真题】请设计一个队列，要求满足：①初始时队列为空；②入队时，允许增加队列占用空间；③出队后，出队元素所占用的空间可重复使用，即整个队列所占用的空间只增不减；④入队操作和出队操作的时间复杂度始终保持为$O(1)$。请回答：
1) 该队列是应选择链式存储结构，还是应选择顺序存储结构?
2) 画出队列的初始状态，并给出判断队空和队满的条件。
3) 画出第一个元素入队后的队列状态。
4) 给出入队操作和出队操作的基本过程。

### 3.2.6 答案与解析

一、单项选择题

**01. D**
栈和队列的逻辑结构都是线性结构，都可以采用顺序存储或链式存储，选项C显然也错误。只有选项D才是栈和队列的本质区别，限定表中插入和删除操作位置的不同。

**02. B**
队列“先进先出”的特性表现在：先入队列的元素先出队列，后入队列的元素后出队列，入队对应的是插入操作，出队对应的是删除操作。选项I和IV均正确。

**03. D**
删除队首元素即出队，是队列的基本操作之一，所以选择选项D。

**04. B**
队列的入队顺序和出队顺序是一致的，这是和栈不同的。

**05. D**
数组下标范围为0~n，因此数组容量为n+1。循环队列中元素入队的操作是$rear=(rear+1) \mod maxsize$，题中$maxsize=n+1$。因此入队操作应为$rear=(rear+1) \mod (n+1)$。

**06. C**
队列的长度为$(rear-front+maxsize)\%maxsize=(rear-front+21)\%21=16$。这种情况和$front$指向当前元素，$rear$指向队尾元素的下一个元素的计算是相同的。

**注意**
数组A[n]的下标范围为0~n-1。若写成A[0...n]，则说明下标范围为0~n。

**07. B**
循环队列中，每删除一个元素，队首指针$front=(front+1)\%6$，每插入一个元素，队尾指针$rear=(rear+1)\%6$。上述操作后，$front=0, rear=3$。

**08. D**
当队列中只有一个元素时，$front$指向该元素的前一个位置，$rear$指向该元素，因此当队列为空时，队首指针等于队尾指针，这样第一个元素入队后，才能符合题目要求。

**09. C**
既然不能附加任何其他数据成员，只能采用牺牲一个存储单元的方法来区分是队空还是队满，约定以“队列头指针在队尾指针的下一位置作为队满的标志”，因此选择选项C。选项A是判断队列是否空的条件，选项B和D都是干扰项。

**注意**
对于这类具体问题，举一些特例判断往往比直接思考问题能更快得到答案。

**10. A**
若用$(rear+1)\%(n+1)==front$作为队满标志，则说明题目采用了牺牲一个存储单元的方法来区分队空和队满，因此可用$front==rear$作为队空标志。

**11. D**
虽然链式队列采用动态分配方式，但其长度也受内存空间的限制，不能无限制增长。顺序队列和链式队列的入队和出队时间复杂度均为$O(1)$。顺序队列和链式队列都可以进行顺序访问。对于顺序队列，可通过队首指针和队尾指针计算队列中的元素个数，而链式队列则不能。

**12. B**
因为队列需在双端进行操作，所以选项C和D的链表显然不太适合链队。对于选项A，链表在完成入队和出队后还要修改为循环的，对于队列来讲这是多余的(画蛇添足)。对于选项B，因为有首指针，所以适合删除首结点；因为有队尾指针，所以适合在其后插入结点。

**13. A**
因为非循环双链表只带队首指针，所以在执行入队操作时需要修改队尾结点的指针域，而查找队尾结点需要$O(n)$的时间。选项B、C和D均可在$O(1)$的时间内找到队首和队尾。

**14. A**
因为在队头做出队操作，为便于删除队首元素，所以总是选择链头作为队头。

**15. D**
队列用链式存储时，删除元素从表头删除，通常仅需修改头指针，但若队列中仅有一个元素，则队尾指针也需要被修改，当仅有一个元素时，删除后队列为空，需修改队尾指针为$rear=front$。

**16. D**
插入操作时，先将结点x插入到链表尾部，再让rear指向这个结点x。选项C的做法不够严密，因为是队尾，所以队尾x->next必须置为空。

**17. A**
依题意，入队操作是在队尾进行，即链表表头。题中已明确说明链表只设头指针，即没有头结点和尾指针，入队后，循环单链表必须保持循环的性质，在只带头指针的循环单链表中寻找表尾结点的时间复杂度为$O(n)$，所以入队的时间复杂度为$O(n)$。

**18. B**
此类题可对各选项进行模拟，假设队列为Q₁和Q₂。对于选项A，元素依次入队Q₁，然后依次出队。对于选项B，5最先出队，只可能是1,2,3,4入队Q₁，5入队Q₂，然后5出队，只能得到5,1,2,3,4。对于选项C，1,2,5入队Q₁，3,4入队Q₂，然后按要求出队。选项D的分析同选项C。

**19. C**
使用排除法。先看可由输入受限的双端队列产生的序列：设右端输入受限，1,2,3,4依次左入，则依次左出可得4,3,2,1，排除选项A；左出、右出、左出、左出可得到4,1,3,2，排除选项B；再看可由输出受限的双端队列产生的序列：设右端输出受限，1,2,3,4依次左入、左入、右入、左入，依次左出可得到4,2,1,3，排除选项D。

**20. C**
本题的队列实际上是一个输出受限的双端队列，如图3.11所示。A操作：a左入(或右入)、b左入、c右入、d右入、e右入。B操作：a左入(或右入)、b左入、c右入、d左入、e右入。D操作：a左入(或右入)、b左入、c左入、d右入、e左入。C操作：a左入(或右入)、b右入、因d未出，此时只能入队，c怎么进都不可能在b和a之间。
**【另解】** 初始时队列为空，第1个元素a左入(或右入)后，第2个元素b无论是左入还是右入都必与a相邻，而选项C中a与b不相邻，不合题意。

**21. B**
根据题意，第一个元素进入队列后存储在A[0]处，此时front和rear值都为0。入队时因为要执行$(rear+1)\%n$操作，所以若入队后指针指向0，则rear初值为n-1，而因为第一个元素在A[0]中，插入操作只改变rear指针，所以front为0不变。

**注意**
①循环队列是指顺序存储的队列，而不是指逻辑上的循环，如循环单链表表示的队列不能称为循环队列。②front和rear的初值并不是固定的。

**22. A**
end1指向队首元素，可知出队操作是先从A[end1]读数，然后end1再加1。end2指向队尾元素的后一个位置，可知入队操作是先存数到A[end2]，然后end2再加1。若用A[0]存储第一个元素，则队列初始时，入队操作先把数据放到A[0]中，然后end2自增，即可知end2初值为0；而end1指向的是队首元素，队首元素在数组A中的下标为0，所以得知end1的初值也为0，可知队空条件为$end1==end2$；然后考虑队列满时，因为队列最多能容纳M-1个元素，假设队列存储在下标为0到M-2的M-1个区域，队头为A[0]，队尾为A[M-2]，此时队列满，考虑在这种情况下end1和end2的状态，end1指向队首元素，可知end1=0，end2指向队尾元素的后一个位置，可知end2=M-2+1=M-1，所以队满条件为$end1==(end2+1) \mod M$。

**23. C**
选项A的操作顺序为①①②②①①③③。选项B的操作顺序为②①①①①①③。选项D的操作顺序为②②②②②①③③③③③。对于选项C：首先输出3，说明1和2必须先依次入栈，而此后2肯定比1先输出，因此无法得到1,2的输出顺序。

**24. D**
假设队列左端允许入队和出队，右端只能入队。对于选项A，依次从右端入队1,2，再从左端入队3,4,5。对于选项B，从右端入队1,2，然后从左端入队3，再从右端入队4，最后从左端入队5。对于选项C，从左端入队1,2，然后从右端入队3，再从左端入队4，最后从右端入队5。无法验证选项D的序列。
`← 54312 →` A
`← 53124 →` B
`← 42135 →` C

二、综合应用题

**01.【解答】**
在循环队列的类型结构中，增设一个整型变量tag，入队时置tag为1，出队时置tag为0(因为只有入队操作可能导致队满，也只有出队操作可能导致队空)。队列Q初始时，置tag=0、front=rear=0。这样队列的4要素如下：
队空条件：$Q.front==Q.rear$ 且 $Q.tag==0$。
队满条件：$Q.front==Q.rear$ 且 $Q.tag==1$。
入队操作：$Q.data[Q.rear]=x; Q.rear=(Q.rear+1)\%MaxSize; Q.tag=1$。
出队操作：$x=Q.data[Q.front]; Q.front=(Q.front+1)\%MaxSize; Q.tag=0$。
1) 设“tag”法的循环队列入队算法：
```c
int EnQueue1(SqQueue &Q, ElemType x) {
    if(Q.front==Q.rear && Q.tag==1) //两个条件都满足时则队满
        return 0;
    Q.data[Q.rear]=x;
    Q.rear=(Q.rear+1)%MaxSize;
    Q.tag=1;                        //可能队满
    return 1;
}
```
2) 设“tag”法的循环队列出队算法：
```c
int DeQueue1(SqQueue &Q, ElemType &x) {
    if(Q.front==Q.rear && Q.tag==0) //两个条件都满足时则队空
        return 0;
    x=Q.data[Q.front];
    Q.front=(Q.front+1)%MaxSize;
    Q.tag=0;                        //可能队空
    return 1;
}
```

**02.【解答】**
本题主要考查大家对队列和栈的特性与操作的理解。因为对队列的一系列操作不可能将其中
的元素逆置，而栈可以将入栈的元素逆序提取出来，所以我们可以让队列中的元素逐个地出队，
入栈；全部入栈后再逐个出栈，入队。
算法的实现如下：
```c
void Inverser(Stack &S, Queue &Q) {
    //本算法实现将队列中的元素逆置
    while(!QueueEmpty(Q)) {
        x=DeQueue(Q);       //队列中全部元素依次出队
        Push(S,x);          //元素依次入栈
    }
    while(!StackEmpty(S)) {
        Pop(S,x);           //栈中全部元素依次出栈
        EnQueue(Q,x);       //再入队
    }
}
```

**03.【解答】**
利用两个栈S1和S2来模拟一个队列，当需要向队列中插入一个元素时，用S1来存放已输入的元素，即S1执行入栈操作。当需要出队时，则对S2执行出栈操作。因为从栈中取出元素的顺序是原顺序的逆序，所以必须先将S1中的所有元素全部出栈并入栈到S2中，再在S2中执行出栈操作，即可实现出队操作，而在执行此操作前必须判断S2是否为空，否则会导致顺序混乱。当栈S1和S2都为空时队列为空。
总结如下：
1) 对S2的出栈操作用作出队，若S2为空，则先将S1中的所有元素送入S2。
2) 对S1的入栈操作用作入队，若S1满，必须先保证S2为空，才能将S1中的元素全部插入S2中。

**入队算法：**
```c
int EnQueue(Stack &S1, Stack &S2, ElemType e) {
    if (!StackOverflow(S1)) {
        Push(S1,e);
        return 1;
    }
    if (StackOverflow(S1) && !StackEmpty(S2)){
        printf("队列为满");
        return 0;
    }
    if (StackOverflow(S1) && StackEmpty(S2)){
        while(!StackEmpty(S1)) {
            Pop(S1,x);
            Push(S2,x);
        }
    }
    Push(S1,e);
    return 1;
}
```
**出队算法：**
```c
void DeQueue(Stack &S1, Stack &S2, ElemType &x) {
    if (!StackEmpty(S2)) {
        Pop(S2,x);
    }
    else if (StackEmpty(S1)) {
        printf("队列为空");
    }
    else {
        while(!StackEmpty(S1)){
            Pop(S1,x);
            Push(S2,x);
        }
        Pop(S2,x);
    }
}
```
**判断队列为空的算法：**
```c
int QueueEmpty(Stack S1, Stack S2) {
    if (StackEmpty(S1) && StackEmpty(S2))
        return 1;
    else
        return 0;
}
```
**04.【解答】**
1) 顺序存储无法满足要求②的队列占用空间随着入队操作而增加。根据要求来分析：要求①容易满足；链式存储方便开辟新空间，要求②容易满足；对于要求③，出队后的结点并不真正释放，用队首指针指向新的队头结点，新元素入队时，有空余结点则无须开辟新空间，赋值到队尾后的第一个空结点即可，然后用队尾指针指向新的队尾结点，这就需要设计成一个首尾相接的循环单链表，类似于循环队列的思想。设置队头、队尾指针后，链式队列的入队操作和出队操作的时间复杂度均为$O(1)$，要求④可以满足。
因此，采用链式存储结构(两段式单向循环链表)，队首指针为$front$，队尾指针为$rear$。
2) 该循环链式队列的实现可以参考循环队列，不同之处在于循环链式队列可以方便地增加空间，出队的结点可以循环利用，入队时空间不够也可以动态增加。同样，循环链式队列也要区分队满和队空的情况，这里参考循环队列牺牲一个单元来判断。初始时，创建只有一个空结点的循环单链表，头指针$front$和尾指针$rear$均指向空结点，如下图所示。

<center>front/rear → [ ]</center>

队空的判定条件：$front==rear$。
队满的判定条件：$front==rear->next$。
3) 插入第一个元素后的状态如下图所示。

<center>front → [1|•] ← rear</center>

4) 操作的基本过程如下：

| 入队操作 | |
| :--- | :--- |
| 若($front==rear->next$) | //队满 |
| 则在rear后面插入一个新的空闲结点； | |
| 入队元素保存到rear所指结点中；$rear=rear->next$；返回。 | |

| 出队操作 | |
| :--- | :--- |
| 若($front==rear$) | //队空 |
| 则出队失败，返回； | |
| 取front所指结点中的元素e；$front=front->next$；返回e。 | |

### 3.3 栈和队列的应用

要熟练掌握栈和队列，必须学习栈和队列的应用，把握其中的规律，然后举一反三。接下来将简单介绍栈和队列的一些常见应用。

### 3.3.1 栈在括号匹配中的应用

假设表达式中允许包含两种括号：圆括号和方括号，其嵌套的顺序任意即([]())或[([][])]等均为正确的格式，[(])或([())或(()]均为不正确的格式。
考虑下列括号序列：