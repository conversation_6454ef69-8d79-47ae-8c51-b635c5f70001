为每次固定c找一个a,使得$L_3=|c-a|$最小。
1) 算法的基本设计思想：
① 使用$D_{min}$记录所有已处理的三元组的最小距离,初值为一个足够大的整数。
② 集合$S_1$、$S_2$和$S_3$分别保存在数组A、B、C中。数组的下标变量$i=j=k=0$,当$i<|S_1|$, $j<|S_2|$且$k<|S_3|$时($|S|$表示集合S中的元素个数),循环执行下面的a) ~ c)。
a) 计算$(A[i], B[j], C[k])$的距离$D$; (计算$D$)
b) 若$D<D_{min}$,则$D_{min}=D$; (更新$D$)
c) 将$A[i]$、B[j]、C[k]中的最小值的下标+1; (对照分析:最小值为a,最大值为c,这里c不变而更新a,试图寻找更小的距离D)
③ 输出$D_{min}$,结束。
2) 算法实现:
```c
#define INT_MAX 0x7fffffff
int abs_(int a) { //计算绝对值
    if (a<0) return -a;
    else return a;
}

bool xls_min(int a, int b, int c) { //a是否是三个数中的最小值
    if(a<=b&&a<=c) return true;
    return false;
}

int findMinofTrip(int A[],int n,int B[],int m,int C[],int p) {
    //D_min用于记录三元组的最小距离,初值赋为INT_MAX
    int i=0,j=0,k=0,D_min=INT_MAX,D;
    while(i<n&&j<m&&k<p&&D_min>0) {
        D=abs_(A[i]-B[j])+abs_(B[j]-C[k])+abs_(C[k]-A[i]);//计算D
        if(D<D_min) D_min=D;                           //更新D
        if(xls_min(A[i],B[j],C[k])) i++;                 //更新a
        else if(xls_min(B[j],C[k],A[i])) j++;
        else k++;
    }
    return D_min;
}
```
3) 设$n=(|S_1|+|S_2|+|S_3|)$,时间复杂度为$O(n)$,空间复杂度为$O(1)$。

## 2.3 线性表的链式表示
顺序表的存储位置可以用一个简单直观的公式表示,它可以随机存取表中任一元素,但插入和删除操作需要移动大量元素。链式存储线性表时,不需要使用地址连续的存储单元,即不要求逻辑上相邻的元素在物理位置上也相邻,它通过“链”建立元素之间的逻辑关系,因此插入和删除操作不需要移动元素,而只需修改指针,但也会失去顺序表可随机存取的优点。

### 2.3.1 单链表的定义
**命题追踪** 单链表的应用(2009、2012、2013、2015、2016、2019)

线性表的链式存储也称单链表,它是指通过一组任意的存储单元来存储线性表中的数据元素。为了建立数据元素之间的线性关系,对每个链表结点,除存放元素自身的信息外,还需要存放一个指向其后继的指针。单链表结点结构如图2.3所示,其中 data 为数据域,存放数据元素;next 为指针域,存放其后继结点的地址。

![图2.3 单链表结点结构](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-3.png)

单链表中结点类型的描述如下:
```c
typedef struct LNode {    //定义单链表结点类型
    ElemType data;        //数据域
    struct LNode *next;   //指针域
}LNode, *LinkList;
```
利用单链表可以解决顺序表需要大量连续存储单元的缺点,但附加的指针域,也存在浪费存储空间的缺点。单链表的元素离散地分布在存储空间中,因此是非随机存取的存储结构,即不能直接找到表中某个特定结点。查找特定结点时,需要从表头开始遍历,依次查找。

通常用头指针L(或 head 等)来标识一个单链表,指出链表的起始地址,头指针为 NULL时表示一个空表。此外,为了操作上的方便,在单链表第一个数据结点之前附加一个结点,称为头结点。头结点的数据域可以不设任何信息,但也可以记录表长等信息。单链表带头结点时,头指针L指向头结点,如图2.4(a)所示。单链表不带头结点时,头指针L指向第一个数据结点,如图2.4(b)所示。表尾结点的指针域为NULL(用“^”表示)。

![图2.4 带头结点和不带头结点的单链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-4.png)

头结点和头指针的关系:不管带不带头结点,头指针都始终指向链表的第一个结点,而头结点是带头结点的链表中的第一个结点,结点内通常不存储信息。

引入头结点后,可以带来两个优点:
① 第一个数据结点的位置被存放在头结点的指针域中,因此在链表的第一个位置上的操作和在表的其他位置上的操作一致,无须进行特殊处理。
② 无论链表是否为空,其头指针都是指向头结点的非空指针(空表中头结点的指针域为空),因此空表和非空表的处理也就得到了统一。

### 2.3.2 单链表上基本操作的实现
带头结点单链表的操作代码书写较为方便,如无特殊说明,本节均默认链表带头结点。

**1. 单链表的初始化**
带头结点和不带头结点的单链表的初始化操作是不同的。带头结点的单链表初始化时,需要创建一个头结点,并让头指针指向头结点,头结点的 next 域初始化为NULL。
```c
bool InitList(LinkList &L) {                 //带头结点的单链表的初始化
    L=(LNode*)malloc(sizeof(LNode));         //创建头结点①
    L->next=NULL;                            //头结点之后暂时还没有元素结点
    return true;
}
```
不带头结点的单链表初始化时,只需将头指针L初始化为NULL。
```c
bool InitList(LinkList &L) {                 //不带头结点的单链表的初始化
    L=NULL;
    return true;
}
```
① 执行`s=(LNode*)malloc(sizeof(LNode))`的作用是由系统生成一个LNode 型的结点,并将该结点的起始位置赋给指针变量s。

**注**
> 设p为指向链表结点的结构体指针,则`*p`表示结点本身,因此可用`p->data`或`(*p).data`访问`*p`这个结点的数据域,二者完全等价。成员运算符(.)左边是一个普通的结构体变量,而指向运算符(->)左边是一个结构体指针。通过`(*p).next`可以得到指向下一个结点的指针,因此`(*(*p).next).data`就是下一个结点中存放的数据,或者直接用`p->next->data`。

**2. 求表长操作**
求表长操作是计算单链表中数据结点的个数,需要从第一个结点开始依次访问表中每个结点,为此需设置一个计数变量,每访问一个结点,其值加1,直到访问到空结点为止。
```c
int Length(LinkList L) {
    int len=0;                               //计数变量,初始为0
    LNode *p=L;
    while(p->next!=NULL) {
        p=p->next;
        len++;                               //每访问一个结点,计数加1
    }
    return len;
}
```
求表长操作的时间复杂度为$O(n)$。另需注意的是,因为单链表的长度是不包括头结点的,因此不带头结点和带头结点的单链表在求表长操作上会略有不同。

**3. 按序号查找结点**
从单链表的第一个结点开始,沿着 next 域从前往后依次搜索,直到找到第i个结点为止,则返回该结点的指针;若i大于单链表的表长,则返回NULL。
```c
LNode *GetElem(LinkList L, int i) {
    LNode *p=L;                              //指针p指向当前扫描到的结点
    int j=0;                                 //记录当前结点的位序,头结点是第0个结点
    while(p!=NULL&&j<i) {                    //循环找到第i个结点
        p=p->next;
        j++;
    }
    return p;                                //返回第i个结点的指针或NULL
}
```
按序号查找操作的时间复杂度为$O(n)$。

**4. 按值查找表结点**
从单链表的第一个结点开始,从前往后依次比较表中各结点的数据域,若某结点的 data 域等于给定值e,则返回该结点的指针;若整个单链表中没有这样的结点,则返回NULL。
```c
LNode *LocateElem(LinkList L, ElemType e) {
    LNode *p=L->next;
    while(p!=NULL&&p->data!=e)               //从第一个结点开始查找数据域为e的结点
        p=p->next;
    return p;                                //找到后返回该结点指针,否则返回NULL
}
```
按值查找操作的时间复杂度为$O(n)$。

**5. 插入结点操作**
插入结点操作将值为x的新结点插入到单链表的第i个位置。先检查插入位置的合法性,然后找到待插入位置的前驱,即第i-1个结点,再在其后插入。其操作过程如图2.5所示。

![图2.5 单链表的插入操作](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-5.png)

**命题追踪** 单链表插入操作的过程(2016、2024)

首先查找第i-1个结点,假设第i-1个结点为`*p`,然后令新结点`*s`的指针域指向`*p`的后继,再令结点`*p`的指针域指向新插入的结点`*s`。
```c
bool ListInsert(LinkList &L, int i, ElemType e) {
    LNode *p=L;                              //指针p指向当前扫描到的结点
    int j=0;                                 //记录当前结点的位序,头结点是第0个结点
    while(p!=NULL&&j<i-1) {                  //循环找到第i-1个结点
        p=p->next;
        j++;
    }
    if(p==NULL)                              //i值不合法
        return false;
    LNode *s=(LNode*)malloc(sizeof(LNode));
    s->data=e;
    s->next=p->next;                         //图2.5 中操作步骤①
    p->next=s;                               //图2.5 中操作步骤②
    return true;
}
```
插入时,①和②的顺序不能颠倒,否则,先执行`p->next=s`后,指向其原后继的指针就不存在了,再执行`s->next=p->next`时,相当于执行了`s->next=s`,显然有误。本算法主要的时间开销在于查找第i-1个元素,时间复杂度为$O(n)$。若在指定结点后插入新结点,则时间复杂度仅为$O(1)$。需注意的是,当链表不带头结点时,需要判断插入位置i是否为1,若是,则要做特殊处理,将头指针L指向新的首结点。当链表带头结点时,插入位置i为1时不用做特殊处理。

**扩展**：对某一结点进行前插操作。
前插操作是指在某结点的前面插入一个新结点,后插操作的定义刚好与之相反。在单链表插入算法中,通常都采用后插操作。以上面的算法为例,先找到第i-1个结点,即插入结点的前驱,再对其执行后插操作。由此可知,对结点的前插操作均可转化为后插操作,前提是从单链表的头结点开始顺序查找到其前驱结点,时间复杂度为$O(n)$。
此外,可采用另一种方式将其转化为后插操作来实现,设待插入结点为`*s`,将`*s`插入到`*p`的前面。我们仍然将`*s`插入到`*p`的后面,然后将`p->data`与`s->data`交换,这样做既满足逻辑关系,又能使得时间复杂度为$O(1)$。该方法的主要代码片段如下:
```c
s->next=p->next;      //修改指针域,不能颠倒
p->next=s;
temp=p->data;         //交换数据域部分
p->data=s->data;
s->data=temp;
```
**6. 删除结点操作**
删除结点操作是将单链表的第i个结点删除。先检查删除位置的合法性,然后查找表中第i-1个结点,即被删结点的前驱,再删除第i个结点。其操作过程如图2.6所示。

![图2.6 单链表结点的删除](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-6.png)

假设结点`*p`为找到的被删结点的前驱,为实现这一操作后的逻辑关系的变化,仅需修改`*p`的指针域,将`*p`的指针域 next 指向`*q`的下一结点,然后释放`*q`的存储空间。
```c
bool ListDelete(LinkList &L, int i, ElemType &e) {
    LNode *p=L;                                //指针p指向当前扫描到的结点
    int j=0;                                   //记录当前结点的位序,头结点是第0个结点
    while(p->next!=NULL&&j<i-1) {              //循环找到第i-1个结点
        p=p->next;
        j++;
    }
    if(p->next==NULL||j>i-1)                   //i值不合法
        return false;
    LNode *q=p->next;                          //令q指向被删除结点
    e=q->data;                                 //用e返回元素的值
    p->next=q->next;                           //将*q结点从链中“断开”
    free(q);                                   //释放结点的存储空间①
    return true;
}
```
同插入算法一样,该算法的主要时间也耗费在查找操作上,时间复杂度为$O(n)$。当链表不带头结点时,需要判断被删结点是否为首结点,若是,则要做特殊处理,将头指针L指向新的首结点。当链表带头结点时,删除首结点和删除其他结点的操作是相同的。

**扩展**：删除结点`*p`。
要删除某个给定结点`*p`,通常的做法是先从链表的头结点开始顺序找到其前驱,然后执行删除操作。其实,删除结点`*p`的操作可用删除`*p`的后继来实现,实质就是将其后继的值赋予其自身,然后再删除后继,也能使得时间复杂度为$O(1)$。该方法的主要代码片段如下:
```c
q=p->next;                   //令q指向*p的后继结点
p->data=p->next->data;       //用后继结点的数据域覆盖
p->next=q->next;             //将*q结点从链中“断开”
free(q);                     //释放后继结点的存储空间
```
**7. 采用头插法建立单链表**
该方法从一个空表开始,生成新结点,并将读取到的数据存放到新结点的数据域中,然后将新结点插入到当前链表的表头,即头结点之后,如图2.7所示。算法实现如下:

![图2.7 采用头插法建立单链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-7.png)

① 执行 free(q) 的作用是由系统回收一个LNode型结点,回收后的空间可供再次生成结点时用。
```c
LinkList List_HeadInsert(LinkList &L) {      //逆向建立单链表
    LNode *s; int x;
    L=(LNode*)malloc(sizeof(LNode));         //创建头结点
    L->next=NULL;                            //初始为空链表
    scanf("%d",&x);                          //输入结点的值
    while(x!=9999) {                         //输入9999表示结束
        s=(LNode*)malloc(sizeof(LNode));     //创建新结点
        s->data=x;
        s->next=L->next;
        L->next=s;                           //将新结点插入表中,L为头指针
        scanf("%d",&x);
    }
    return L;
}
```
采用头插法建立单链表时,读入数据的顺序与生成的链表中元素的顺序是相反的,可用来实现链表的逆置。每个结点插入的时间为$O(1)$,设单链表长为n,则总时间复杂度为$O(n)$。

**思考**
> 若单链表不带头结点,则上述代码中哪些地方需要修改? ①

**8. 采用尾插法建立单链表**
头插法建立单链表的算法虽然简单,但生成的链表中结点的次序和输入数据的顺序不一致。若希望两者次序一致,则可采用尾插法。该方法将新结点插入到当前链表的表尾,为此必须增加一个尾指针r,使其始终指向当前链表的尾结点,如图2.8所示。算法实现如下:

![图2.8 采用尾插法建立单链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-8.png)

```c
LinkList List_TailInsert(LinkList &L) {      //正向建立单链表
    int x;                                   //设元素类型为整型
    L=(LNode*)malloc(sizeof(LNode));         //创建头结点
    LNode *s,*r=L;                           //r为表尾指针
    scanf("%d",&x);                          //输入结点的值
    while(x!=9999) {                         //输入9999表示结束
        s=(LNode *)malloc(sizeof(LNode));
        s->data=x;
        r->next=s;                           //r指向新的表尾结点
        r=s;
        scanf("%d",&x);
    }
    r->next=NULL;                            //尾结点指针置空
    return L;
}
```
因为附设了一个指向表尾结点的指针,所以时间复杂度和头插法的相同。

① 主要修改之处:因为在头部插入新结点,每次插入新结点后,都需要将它的地址赋值给头指针L。

**注意**
> 单链表是整个链表的基础,读者一定要熟练掌握单链表的基本操作算法。在设计算法时,建议先通过画图的方法理清算法的思路,然后进行算法的编写。

### 2.3.3 双链表
单链表结点中只有一个指向其后继的指针,使得单链表只能从前往后依次遍历。要访问某个结点的前驱(插入、删除操作时),只能从头开始遍历,访问前驱的时间复杂度为$O(n)$。为了克服单链表的这个缺点,引入了双链表,双链表结点中有两个指针 prior 和 next,分别指向其直接前驱和直接后继,如图2.9所示。表头结点的prior 域和尾结点的 next 域都是 NULL。

![图2.9 双链表示意图](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-9.png)

双链表中结点类型的描述如下:
```c
typedef struct DNode {        //定义双链表结点类型
    ElemType data;            //数据域
    struct DNode *prior, *next; //前驱和后继指针
} DNode, *DLinklist;
```
双链表在单链表结点中增加了一个指向其前驱的指针 prior,因此双链表的按值查找和按位查找的操作与单链表的相同。但双链表在插入和删除操作的实现上,与单链表有着较大的不同。这是因为“链”变化时也需要对指针 prior 做出修改,其关键是保证在修改的过程中不断链。此外,双链表可以很方便地找到当前结点的前驱,因此,插入、删除操作的时间复杂度仅为$O(1)$。

**1. 双链表的插入操作**
在双链表p所指的结点之后插入结点`*s`,其指针的变化过程如图2.10所示。

![图2.10 双链表插入结点过程](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-10.png)

**命题追踪** 双链表中插入操作的实现(2023)

插入操作的代码片段如下:
```c
//将结点*s插入到结点*p之后
① s->next = p->next;
② p->next->prior = s;
③ s->prior = p;
④ p->next = s;
```
上述代码的语句顺序不是唯一的,但也不是任意的,①步必须在④步之前,否则`*p`的后继结点的指针就会丢掉,导致插入失败。为了加深理解,读者可以在纸上画出示意图。若问题改成要求在结点`*p`之前插入结点`*s`,请读者思考具体的操作步骤。

**2. 双链表的删除操作**
删除双链表中结点`*p`的后继结点`*q`,其指针的变化过程如图2.11所示。

![图2.11 双链表删除结点过程](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-11.png)

**命题追踪** 双链表中删除操作的实现(2016)

删除操作的代码片段如下:
```c
p->next=q->next;        //图2.11 中步骤①
q->next->prior=p;       //图2.11 中步骤②
free(q);                //释放结点空间
```
若问题改成要求删除结点`*q`的前驱结点`*p`,请读者思考具体的操作步骤。

在建立双链表的操作中,也可采用如同单链表的头插法和尾插法,但在操作上需要注意指针的变化和单链表有所不同。

### 2.3.4 循环链表
**1. 循环单链表**
循环单链表和单链表的区别在于,表中最后一个结点的指针不是NULL,而改为指向头结点,从而整个链表形成一个环,如图2.12所示。

在循环单链表中,表尾结点`*r`的next 域指向L,故表中没有指针域为NULL 的结点,因此,循环单链表的判空条件不是头结点的指针是否为空,而是它是否等于头指针 L。

![图2.12 循环单链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-12.png)

**命题追踪** 循环单链表中删除首元素的操作(2021)

循环单链表的插入、删除算法与单链表的几乎一样,所不同的是,若操作是在表尾进行,则执行的操作不同,以让单链表继续保持循环的性质。当然,正是因为循环单链表是一个“环”,所以在任何位置上的插入和删除操作都是等价的,而无须判断是否是表尾。

在单链表中只能从表头结点开始往后顺序遍历整个链表,而循环单链表可以从表中的任意一个结点开始遍历整个链表。有时对循环单链表不设头指针而仅设尾指针,以使得操作效率更高。其原因是,若设的是头指针,对在表尾插入元素需要$O(n)$的时间复杂度,而若设的是尾指针r,`r->next`即头指针,对在表头或表尾插入元素都只需要$O(1)$的时间复杂度。

**2. 循环双链表**
由循环单链表的定义不难推出循环双链表。不同的是,在循环双链表中,头结点的 prior指针还要指向表尾结点,如图2.13所示。当某结点`*p`为尾结点时,`p->next==L`;当循环双链表为空表时,其头结点的 prior 域和 next 域都等于L。

![图2.13 循环双链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-13.png)

### 2.3.5 静态链表
静态链表是用数组来描述线性表的链式存储结构,结点也有数据域 data 和指针域 next,与前面所讲的链表中的指针不同的是,这里的指针是结点在数组中的相对地址(数组下标),也称游标。和顺序表一样,静态链表也要预先分配一块连续的内存空间。

静态链表和单链表的对应关系如图2.14所示。

![图2.14 静态链表存储示意图](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-14.png)

静态链表结构类型的描述如下:
```c
#define MaxSize 50         //静态链表的最大长度
typedef struct {           //静态链表结构类型的定义
    ElemType data;         //存储数据元素
    int next;              //下一个元素的数组下标
} SLinkList[MaxSize];
```
静态链表以`next==-1`作为其结束的标志。静态链表的插入、删除操作与动态链表的相同,只需要修改指针,而不需要移动元素。总体来说,静态链表没有单链表使用起来方便,但在一些不支持指针的高级语言(如Basic)中,这是一种非常巧妙的设计方法。

### 2.3.6 顺序表和链表的比较
**1. 存取(读/写)方式**
顺序表既可以顺序存取,也可以随机存取,链表只能从表头开始依次顺序存取。例如在第i个位置上执行存取的操作,顺序表仅需一次访问,而链表则需从表头开始依次访问i次。

**2. 逻辑结构与物理结构**
采用顺序存储时,逻辑上相邻的元素,对应的物理存储位置也相邻。而采用链式存储时,逻辑上相邻的元素,物理存储位置不一定相邻,对应的逻辑关系是通过指针链接来表示的。

**3. 查找、插入和删除操作**
对于按值查找,顺序表无序时,两者的时间复杂度均为$O(n)$;顺序表有序时,可采用折半查找,此时的时间复杂度为$O(\log n)$。对于按序号查找,顺序表支持随机访问,时间复杂度仅为$O(1)$,而链表的平均时间复杂度为$O(n)$。顺序表的插入、删除操作,平均需要移动半个表长的元素。链表的插入、删除操作,只需修改相关结点的指针域即可。

**4. 空间分配**
顺序存储在静态存储分配情形下,一旦存储空间装满就不能扩充,若再加入新元素,则会出现内存溢出,因此需要预先分配足够大的存储空间。预先分配过大,可能会导致顺序表后部大量闲置;预先分配过小,又会造成溢出。动态存储分配虽然存储空间可以扩充,但需要移动大量元素,导致操作效率降低,而且若内存中没有更大块的连续存储空间,则会导致分配失败。链式存储的结点空间只在需要时申请分配,只要内存有空间就可以分配,操作灵活、高效。此外,链表的每个结点都带有指针域,因此存储密度不够大。

在实际中应该怎样选取存储结构呢?
**1. 基于存储的考虑**
难以估计线性表的长度或存储规模时,不宜采用顺序表;链表不用事先估计存储规模,但链表的存储密度较低,显然链式存储结构的存储密度是小于1的。

**2. 基于运算的考虑**
在顺序表中按序号访问的时间复杂度为$O(1)$,而链表中按序号访问的时间复杂度为$O(n)$,因此若经常做的运算是按序号访问数据元素,则显然顺序表优于链表。
在顺序表中进行插入、删除操作时,平均移动表中一半的元素,当数据元素的信息量较大且表较长时,这一点是不应忽视的;在链表中进行插入、删除操作时,虽然也要找插入位置,但操作主要是比较操作,从这个角度考虑显然后者优于前者。

**3. 基于环境的考虑**
顺序表容易实现,任何高级语言中都有数组类型;链表的操作是基于指针的,相对来讲,前者实现较为简单,这也是用户考虑的一个因素。
总之,两种存储结构各有长短,选择哪一种由实际问题的主要因素决定。通常较稳定的线性表选择顺序存储,而频繁进行插入、删除操作的线性表(动态性较强)宜选择链式存储。

**注意**
> 只有熟练掌握顺序存储和链式存储,才能深刻理解它们的优缺点。

### 2.3.7 本节试题精选
**一、单项选择题**
01. 下列关于线性表的存储结构的描述中,正确的是()。
    I. 线性表的顺序存储结构优于其链式存储结构
    II. 链式存储结构比顺序存储结构能更方便地表示各种逻辑结构
    III. 若频繁使用插入和删除结点操作,则顺序存储结构更优于链式存储结构
    IV. 顺序存储结构和链式存储结构都可以进行顺序存取
    A. I、II、III
    B. II、IV
    C. II、III
    D. III、IV
02. 对于一个线性表,既要求能进行较快速地插入和删除,又要求存储结构能反映数据之间的逻辑关系,则应该用()。
    A. 顺序存储方式 B. 链式存储方式 C. 散列存储方式 D. 以上均可以
03. 链式存储设计时,结点内的存储单元地址()。
    A. 一定连续
    B. 一定不连续
    C. 不一定连续
    D. 部分连续,部分不连续
04. 下列关于线性表的说法中,正确的是()。
    I. 顺序存储方式只能用于存储线性结构
    II. 在一个设有头指针和尾指针的单链表中,删除表尾元素的时间复杂度与表长无关
    III. 带头结点的循环单链表中不存在空指针
    IV. 在一个长度为n的有序单链表中插入一个新结点并仍保持有序的时间复杂度为$O(n)$
    V. 若用单链表来表示队列,则应该选用带尾指针的循环链表
    A. I、II
    B. I、III、IV、V
    C. IV、V
    D. III、IV、V
05. 设线性表中有2n个元素,()在单链表上实现要比在顺序表上实现效率更高。
    A. 删除所有值为x的元素
    B. 在最后一个元素的后面插入一个新元素
    C. 顺序输出前k个元素
    D. 交换第i个元素和第2n-i-1个元素的值(i=0,…,n-1)
06. 在一个单链表中,已知q所指结点是p所指结点的前驱结点,若在q和p之间插入结点s,则执行()。
    A. `s->next=p->next;p->next=s;`
    B. `p->next=s->next;s->next=p;`
    C. `q->next=s;s->next=p;`
    D. `p->next=s;s->next=q;`
07. 给定有n个元素的一维数组,建立一个有序单链表的最低时间复杂度是()。
    Α. $O(1)$
    B. $O(n)$
    C. $O(n^2)$
    D. $O(n\log n)$
08. 将长度为n的单链表链接在长度为m的单链表后面,其算法的时间复杂度采用大O形式表示应该是()。
    Α. $O(1)$
    B. $O(n)$
    C. $O(m)$
    D. $O(n+m)$
09. 单链表中,增加一个头结点的目的是()。
    A. 使单链表至少有一个结点
    B. 标识表结点中首结点的位置
    C. 方便运算的实现
    D. 说明单链表是线性表的链式存储
10. 在一个长度为n的带头结点的单链表h上,设有尾指针r,则执行()操作与链表的表长有关。
    A. 删除单链表中的第一个元素
    B. 删除单链表中的最后一个元素
    C. 在单链表第一个元素前插入一个新元素
    D. 在单链表最后一个元素后插入一个新元素
11. 对于一个头指针为 head 的带头结点的单链表,判定该表为空表的条件是();对于不带头结点的单链表,判定空表的条件为()。
    A. `head==NULL`
    B. `head->next==NULL`
    C. `head->next==head`
    D. `head!=NULL`
12. 在线性表 $a_0, a_1, \dots, a_{100}$中,删除元素 $a_{50}$需要移动()个元素。
    A. 0
    B. 50
    C. 51
    D. 0或50
13. 通过含有$n(n>1)$个元素的数组a,采用头插法建立单链表L,则L中的元素次序()。
    A. 与数组a的元素次序相同
    B. 与数组a的元素次序相反
    C. 与数组a的元素次序无关
    D. 以上都错误
14. 下面关于线性表的一些说法中,正确的是()。
    A. 对一个设有头指针和尾指针的单链表执行删除最后一个元素的操作与链表长度无关
    B. 线性表中每个元素都有一个直接前驱和一个直接后继
    C. 为了方便插入和删除数据,可以使用双链表存放数据
    D. 取线性表第i个元素的时间与i的大小有关
15. 在双链表中向p所指的结点之前插入一个结点q的操作为()。
    A. `p->prior=q; q->next=p; p->prior->next=q; q->prior=p->prior;`
    B. `q->prior=p->prior; p->prior->next=q; q->next=p; p->prior=q->next;`
    C. `q->next=p; p->next=q; q->prior->next=q; q->next=p;`
    D. `p->prior->next=q; q->next=p; q->prior=p->prior; p->prior=q;`
16. 在双链表存储结构中,删除p所指的结点时必须修改指针()。
    A. `p->prior->next=p->next; p->next->prior=p->prior;`
    B. `p->prior=p->prior->prior; p->prior->next=p;`
    C. `p->next->prior=p; p->next=p->next->next;`
    D. `p->next=p->prior->prior; p->prior=p->next->next;`
17. 在如下图所示的双链表中,已知指针p指向结点A,若要在结点A和C之间插入指针q所指的结点B,则依次执行的语句序列可以是()。
    ![双链表插入操作](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-17.png)
    ①`q->next=p->next;` ②`q->prior=p;` ③`p->next=q;` ④`p->next->prior=q;`
    A. ①②④③
    B. ④③②①
    C. ③④①②
    D. ①③④②
18. 在双链表的两个结点之间插入一个新结点,需要修改()个指针域。
    A. 1
    B. 3
    C. 4
    D. 2
19. 在长度为n的有序单链表中插入一个新结点,并仍然保持有序的时间复杂度是()。
    Α. $O(1)$
    B. $O(n)$
    C. $O(n^2)$
    D. $O(n\log_2n)$
20. 与单链表相比,双链表的优点之一是()。
    A. 插入、删除操作更方便
    B. 可以进行随机访问
    C. 可以省略表头指针或表尾指针
    D. 访问前后相邻结点更灵活
21. 对于一个带头结点的循环单链表L,判断该表为空表的条件是()。
    A. 头结点的指针域为空
    B. L的值为NULL
    C. 头结点的指针域与L的值相等
    D. 头结点的指针域与L的地址相等
22. 对于一个带头结点的循环双链表L,判断该表为空表的条件是()。
    A. `L->prior==L&&L->next==NULL`
    B. `L->prior==NULL&&L->next==NULL`
    C. `L->prior==NULL&&L->next==L`
    D. `L->prior==L&&L->next==L`
23. 一个链表最常用的操作是在末尾插入结点和删除结点,则选用()最节省时间。
    A. 带头结点的循环双链表
    B. 循环单链表
    C. 带尾指针的循环单链表
    D. 单链表
24. 设对$n(n>1)$个元素的线性表的运算只有4种:删除第一个元素;删除最后一个元素;在第一个元素之前插入新元素;在最后一个元素之后插入新元素,则最好使用()。
    A. 只有尾结点指针没有头结点指针的循环单链表
    B. 只有尾结点指针没有头结点指针的非循环双链表
    C. 只有头结点指针没有尾结点指针的循环双链表
    D. 既有头结点指针又有尾结点指针的循环单链表
25. 设有两个长度为n的循环单链表,若要求两个循环单链表的头尾相接的时间复杂度为$O(1)$,则对应两个循环单链表各设置一个指针,分别指向()。
    A. 各自的头结点
    B. 各自的尾结点
    C. 各自的首结点
    D. 一个表的头结点,另一个表的尾结点
26. 设有一个长度为n的循环单链表,若从表中删除首元结点的时间复杂度达到$O(n)$,则此时采用的循环单链表的结构可能是()。
    A. 只有表头指针,没有头结点
    B. 只有表尾指针,没有头结点
    C. 只有表尾指针,带头结点
    D. 只有表头指针,带头结点
27. 某线性表用带头结点的循环单链表存储,头指针为head,当`head->next->next==head`成立时,线性表的长度可能是()。
    A. 0
    B. 1
    C. 2
    D. 可能为0或1
28. 有两个长度都为n的双链表,若以$h_1$为头指针的双链表是非循环的,以$h_2$为头指针的双链表是循环的,则下列叙述中正确的是()。
    A. 对于双链表$h_1$,删除首结点的时间复杂度是$O(n)$
    B. 对于双链表$h_2$,删除首结点的时间复杂度是$O(n)$
    C. 对于双链表$h_1$,删除尾结点的时间复杂度是$O(1)$
    D. 对于双链表$h_2$,删除尾结点的时间复杂度是$O(1)$
29. 一个链表最常用的操作是在最后一个元素后插入一个元素和删除第一个元素,则选用()最节省时间。
    A. 不带头结点的循环单链表
    B. 双链表
    C. 单链表
    D. 不带头结点且有尾指针的循环单链表
30. 需要分配较大空间,插入和删除不需要移动元素的线性表,其存储结构为()。
    A. 单链表
    B. 静态链表
    C. 顺序表
    D. 双链表
31. 下列关于静态链表的说法中,正确的是()。
    I. 静态链表兼具顺序表和单链表的优点,因此存取表中第i个元素的时间与i无关
    II. 静态链表能容纳的最大元素个数在表定义时就确定了,以后不能增加
    III. 静态链表与动态链表在元素的插入、删除上类似,不需要移动元素
    IV. 相比动态链表,静态链表可能浪费较多的存储空间
    A. I、II、III
    B. II、III、IV
    C. I、III、IV
    D. I、II、IV
32. **【2016统考真题】** 已知一个带有表头结点的循环双链表L,结点结构为`prev data next`,其中`prev`和`next`分别是指向其直接前驱和直接后继结点的指针。现要删除指针p所指的结点,正确的语句序列是()。
    A. `p->next->prev=p->prev; p->prev->next=p->prev; free(p);`
    B. `p->next->prev=p->next; p->prev->next=p->next; free(p);`
    C. `p->next->prev=p->next; p->prev->next=p->prev; free(p);`
    D. `p->next->prev=p->prev; p->prev->next=p->next; free(p);`
33. **【2016统考真题】** 已知表头元素为c的单链表在内存中的存储状态如下表所示。
| 地址 | 元素 | 链接地址 |
| :--- | :--- | :--- |
| 1000H | a | 1010H |
| 1004H | b | 100CH |
| 1008H | c | 1000H |
| 100CH | d | NULL |
| 1010H | e | 1004H |
| 1014H | | |
    现将f存放于1014H处并插入单链表,若f在逻辑上位于a和e之间,则a、e、f的“链接地址”依次是()。
    A. 1010H、1014H、1004H
    B. 1010H、1004H、1014H
    C. 1014H、1010H、1004H
    D. 1014H、1004H、1010H
34. **【2021统考真题】** 已知头指针h指向一个带头结点的非空循环单链表,结点结构为`data next`,其中`next`是指向直接后继结点的指针,p是尾指针,q是临时指针。现要删除该链表的第一个元素,正确的语句序列是()。
    A. `h->next=h->next->next; q=h->next; free(q);`
    B. `q=h->next; h->next=h->next->next; free(q);`
    C. `q=h->next; h->next=q->next; if(p!=q) p=h; free(q);`
    D. `q=h->next; h->next=q->next; if(p==q) p=h; free(q);`
35. **【2023统考真题】** 现有非空双链表L,其结点结构为`prev data next`, `prev`是指向直接前驱结点的指针,`next`是指向直接后继结点的指针。若要在L中指针p所指向的结点(非尾结点)之后插入指针s指向的新结点,则在执行语句序列“`s->next=p->next; p->next=s;`”后,下列语句序列中还需要执行的是()。
    A. `s->next->prev=p; s->prev=p;`
    B. `p->next->prev=s; s->prev=p;`
    C. `s->prev=s->next->prev; s->next->prev=s;`
    D. `p->next->prev=s->prev; s->next->prev=p;`
36. **【2024统考真题】** 已知带头结点的非空单链表L的头指针为h,结点结构为`data next`,其中`next`是指向直接后继结点的指针。现有指针p和q,若p指向L中非首且非尾的任意一个结点,则执行语句序列“`q=p->next; p->next=q->next; q->next=h->next; h->next=q;`”的结果是()。
    A. 在p所指结点后插入q所指结点
    B. 在q所指结点后插入p所指结点
    C. 将p所指结点移至L的头结点之后
    D. 将q所指结点移动到L的头结点之后

**二、综合应用题**
01. 在带头结点的单链表L中,删除所有值为x的结点,并释放其空间,假设值为x的结点不唯一,试编写算法以实现上述操作。
02. 试编写在带头结点的单链表L中删除一个最小值结点的高效算法(假设该结点唯一)。
03. 试编写算法将带头结点的单链表就地逆置,所谓“就地”是指辅助空间复杂度为$O(1)$。
04. 设在一个带表头结点的单链表中,所有结点的元素值无序,试编写一个函数,删除表中所有处于给定的两个值(作为函数参数给出)之间的元素(若存在)。
05. 给定两个单链表,试分析找出两个链表的公共结点的思想(不用写代码)。
06. 设$C=\{a_1,b_1,a_2,b_2,\dots,a_n,b_n\}$为线性表,采用带头结点的单链表存放,设计一个就地算法,将其拆分为两个线性表,使得$A=\{a_1,a_2,\dots,a_n\}$,$B=\{b_n,b_{n-1},\dots,b_1\}$。
07. 在一个递增有序的单链表中,存在重复的元素。设计算法删除重复的元素,例如(7,10,10,21,30,42,42,42,51,70)将变为(7,10,21,30,42,51,70)。
08. 设A和B是两个单链表(带头结点),其中元素递增有序。设计一个算法从A和B中的公共元素产生单链表C,要求不破坏A、B的结点。
09. 已知两个链表A和B分别表示两个集合,其元素递增排列。编制函数,求A与B的交集,并存放于A链表中。
10. 两个整数序列 $A = a_1,a_2,a_3,\dots,a_m$ 和 $B = b_1,b_2,b_3,\dots,b_n$ 已经存入两个单链表中,设计一个算法,判断序列B是否是序列A的连续子序列。
11. 设计一个算法用于判断带头结点的循环双链表是否对称。
12. 有两个循环单链表,链表头指针分别为h1和h2,编写一个函数将链表h2链接到链表h1之后,要求链接后的链表仍保持循环链表形式。
13. 设有一个带头结点的非循环双链表L,其每个结点中除有`pre`、`data`和`next`域外,还有一个访问频度域`freq`,其值均初始化为零。每当在链表中进行一次`Locate(L,x)`运算时,令值为x的结点中`freq`域的值增1,并使此链表中的结点保持按访问频度递减的顺序排列,且最近访问的结点排在频度相同的结点之前,以便使频繁访问的结点总是靠近表头。试编写符合上述要求的`Locate(L,x)`函数,返回找到结点的地址,类型为指针型。
14. 设将$n(n>1)$个整数存放到不带头结点的单链表L中,设计算法将L中保存的序列循环右移$k(0<k<n)$个位置。例如,若$k=1$,则将链表$\{0,1,2,3\}$变为$\{3,0,1,2\}$。要求:
    1) 给出算法的基本设计思想。
    2) 根据设计思想,采用C或C++语言描述算法,关键之处给出注释。
    3) 说明你所设计算法的时间复杂度和空间复杂度。
15. 单链表有环,是指单链表的最后一个结点的指针指向了链表中的某个结点(通常单链表的最后一个结点的指针域是空的)。试编写算法判断单链表是否存在环。
    1) 给出算法的基本设计思想。
    2) 根据设计思想,采用C或C++语言描述算法,关键之处给出注释。
    3) 说明你所设计算法的时间复杂度和空间复杂度。
16. 设有一个长度n(n为偶数)的不带头结点的单链表,且结点值都大于0,设计算法求这个单链表的最大孪生和。孪生和定义为一个结点值与其孪生结点值之和,对于第i个结点(从0开始),其孪生结点为第n-i-1个结点。要求:
    1) 给出算法的基本设计思想。
    2) 根据设计思想,采用C或C++语言描述算法,关键之处给出注释。
    3) 说明你的算法的时间复杂度和空间复杂度。
17. **【2009统考真题】** 已知一个带有表头结点的单链表,结点结构为
| data | link |
| :--: | :--: |
假设该链表只给出了头指针 list。在不改变链表的前提下,请设计一个尽可能高效的算法,查找链表中倒数第k个位置上的结点(k为正整数)。若查找成功,算法输出该结点的 data 域的值,并返回1;否则,只返回0。要求:
    1) 描述算法的基本设计思想。
    2) 描述算法的详细实现步骤。
    3) 根据设计思想和实现步骤,采用程序设计语言描述算法(使用 C、C++或 Java 语言实现),关键之处请给出简要注释。
18. **【2012统考真题】** 假定采用带头结点的单链表保存单词,当两个单词有相同的后缀时,可共享相同的后缀存储空间,例如,`loading`和`being`的存储映像如下图所示。
    ![共享后缀的单链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-18.png)
    设 str1 和 str2 分别指向两个单词所在单链表的头结点,链表结点结构为`data next`,请设计一个时间上尽可能高效的算法,找出由 str1 和 str2 所指向两个链表共同后缀的起始位置(如图中字符`i`所在结点的位置p)。要求:
    1) 给出算法的基本设计思想。
    2) 根据设计思想,采用C或C++或Java语言描述算法,关键之处给出注释。
    3) 说明你所设计算法的时间复杂度。
19. **【2015统考真题】** 用单链表保存m个整数,结点的结构为`[data][link]`,且$|data| \le n$ (n为正整数)。现要求设计一个时间复杂度尽可能高效的算法,对于链表中 data 绝对值相等的结点,仅保留第一次出现的结点而删除其余绝对值相等的结点。例如,若给定的单链表 head 如下:
    ![带重复绝对值结点的链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-19-1.png)
    则删除结点后的 head 为:
    ![删除后的链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-19-2.png)
    要求:
    1) 给出算法的基本设计思想。
    2) 使用C或C++语言,给出单链表结点的数据类型定义。
    3) 根据设计思想,采用C或C++语言描述算法,关键之处给出注释。
    4) 说明你所设计算法的时间复杂度和空间复杂度。
20. **【2019统考真题】** 设线性表$L=(a_1, a_2, a_3, \dots, a_{n-2}, a_{n-1}, a_n)$采用带头结点的单链表保存,链表中的结点定义如下:
```c
typedef struct node {
    int data;
    struct node* next;
} NODE;
```
请设计一个空间复杂度为$O(1)$且时间上尽可能高效的算法,重新排列L中的各结点,得到线性表$L'=(a_1, a_n, a_2, a_{n-1}, a_3, a_{n-2}, \dots)$。要求:
    1) 给出算法的基本设计思想。
    2) 根据设计思想,采用C或C++语言描述算法,关键之处给出注释。
    3) 说明你所设计的算法的时间复杂度。

### 2.3.8 答案与解析
**一、单项选择题**
**01. B**
两种存储结构适用于不同的场合,不能简单地说谁好谁坏,选项Ⅰ错误。链式存储用指针表示逻辑结构,而指针的设置是任意的,因此比顺序存储结构能更方便地表示各种逻辑结构,选项Ⅱ正确。在顺序存储中,插入和删除结点需要移动大量元素,效率较低,选项Ⅲ的描述刚好相反。顺序存储结构既能随机存取又能顺序存取,而链式结构只能顺序存取,选项Ⅳ正确。

**02. B**
首先直接排除选项A和D。散列存储通过散列函数映射到物理空间,不能反映数据之间的逻辑关系,排除选项C。链式存储能方便地表示各种逻辑关系,且插入和删除操作的时间复杂度为$O(1)$。

**03. A**
链式存储设计时,各个不同结点的存储空间可以不连续,但结点内的存储单元地址必须连续。

**04. D**
顺序存储方式同样适用于存储图和树,选项Ⅰ错误。删除表尾结点时,必须从头开始找到表尾结点的前驱,其时间与表长有关,选项Ⅱ错误。循环单链表中最后一个结点的指针不是NULL,而是指向头结点,整个链表形成一个环,因此不存在空指针,选项Ⅲ正确。有序单链表只能依次查找插入位置,时间复杂度为$O(n)$,选项Ⅳ正确。队列需要在表头删除元素,表尾插入元素,采用带尾指针的循环链表较为方便,插入和删除的时间复杂度都为$O(1)$,选项Ⅴ正确。

**05. A**
对于选项A,在单链表和顺序表上实现的时间复杂度都为$O(n)$,但后者要移动很多元素,因此在单链表上实现效率更高。对于选项B和D,顺序表的效率更高。C无区别。

**06. C**
s插入后,q成为s的前驱,而p成为s的后继,选择选项C。

**注意**
> 可能有读者认为选项C中的两条语句交换后才正确。实际上,因为本题插入位置的前后结点都有指针指示(这与前面介绍的插入操作是不同的),所以选项C中的语句顺序并不会造成断链。在此提醒读者在学习过程中一定要多动脑思考,而不要生搬硬套。

**07. D**
若先建立链表,然后依次插入建立有序表,则每插入一个元素就需遍历链表寻找插入位置,即直接插入排序,时间复杂度为$O(n^2)$。若先将数组排好序,然后建立链表,建立链表的时间复杂度为$O(n)$,数组排序的最好时间复杂度为$O(n\log_2 n)$,总时间复杂度为$O(n\log_2 n)$。故选择选项D。

**08. C**
先遍历长度为m的单链表,找到该单链表的尾结点,然后将其next域指向另一个单链表的首结点,其时间复杂度为$O(m)$。

**09. C**
单链表设置头结点的目的是方便运算的实现,主要好处体现在:第一,有头结点后,插入和删除数据元素的算法就统一了,不再需要判断是否在第一个元素之前插入或删除第一个元素;第二,不论链表是否为空,其头指针是指向头结点的非空指针,链表的头指针不变,因此空表和非空表的处理也就统一了。

**10. B**
删除单链表的最后一个结点需置其前驱结点的指针域为NULL,需要从头开始依次遍历找到该前驱结点,需要$O(n)$的时间,与表长有关。其他操作均与表长无关,读者可自行模拟。

**11. B, A**
在带头结点的单链表中,头指针head指向头结点,头结点的next域指向第一个元素结点,`head->next==NULL`表示该单链表为空。在不带头结点的单链表中,head直接指向第一个元素结点,`head==NULL`表示该单链表为空。

**12. D**
线性表有顺序存储和链式存储两种存储结构。若采用链式存储结构,则删除元素$a_{50}$不需要移动元素;若采用顺序存储结构,则需要依次移动50个元素。

**13. B**
当采用头插法建立单链表时,数组后面的元素插入到单链表L的最前端,所以L中的元素次序与数组a的元素次序相反。

**14. C**
选项A显然错误。选项B中第一个元素和最后一个元素不满足题设要求。双链表能很方便地访问前驱和后继,故删除和插入数据较为方便,选项C正确。选项D未考虑顺序存储的情况。

**15. D**
为了在p之前插入结点q,可以将p的前一个结点的next域指向q,将q的next域指向p,将q的prior域指向p的前一个结点,将p的prior域指向q。仅D满足条件。

**16. A**
与上一题的分析基本类似,只不过这里是删除一个结点,注意将p的前、后两结点链接起来。关键是要保证在结点指针的修改过程中不断链!
**注意**,请读者仔细对比上述两题,弄清双链表的插入和删除方法。

**17. A**
结点A和B分别由指针p和q指示,但结点C仅能由`p->next`间接指示,因此在改变`p->next`之前,必须先将`q->next`指向结点C,即①要在③前面,且④要在③前面(因为若先执行③,则④相当于q->prior指向其自身,显然矛盾)。故只能选择选项A。

**18. C**
当在双链表的两个结点(分别用第一个、第二个结点表示)之间插入一个新结点时,需要修改四个指针域,分别是:新结点的前驱指针域,指向第一个结点;新结点的后继指针域,指向第二个结点;第一个结点的后继指针域,指向新结点;第二个结点的前驱指针域,指向新结点。

**19. B**
设单链表递增有序,首先要在单链表中找到第一个大于x的结点的直接前驱p,在p之后插入该结点。查找的时间复杂度为$O(n)$,插入的时间复杂度为$O(1)$,总时间复杂度为$O(n)$。

**20. D**
在插入和删除操作上,单链表和双链表都不用移动元素,都很方便,但双链表修改指针的操作更为复杂,选项A错误。双链表中可以快速访问任何一个结点的前驱和后继结点,选项D正确。

**21. C**
带头结点的循环单链表L为空表时,满足`L->next==L`,即头结点的指针域与L的值相等,而不是头结点的指针域与L的地址相等。注意,带头结点的循环单链表中不存在空指针。

**22. D**
循环双链表L判空的条件是头结点(头指针)的prior和next域都指向它自身。

**23. A**
在链表的末尾插入和删除一个结点时,需要修改其相邻结点的指针域。而寻找尾结点及尾结点的前驱结点时,只有带头结点的循环双链表所需要的时间最少。

**24. C**
对于选项A,删除尾结点`*p`时,需要找到`*p`的前一个结点,时间复杂度为$O(n)$。对于选项B,删除首结点`*p`时,需要找到`*p`结点,这里没有直接给出头结点指针,而通过尾结点的prior指针找到`*p`结点的时间复杂度为$O(n)$。对于选项D,删除尾结点`*p`时,需要找到`*p`的前一个结点,时间复杂度为$O(n)$。对于C,执行这四种算法的时间复杂度均为$O(1)$。

**25. B**
要求用$O(1)$的时间将两个循环单链表头尾相接,并未指明哪个链表接在另一个链表之后,所以对两个链表都要在$O(1)$的时间找到头结点和尾结点。因此,两个指针应都指向尾结点。

**26. A**
在循环单链表中,删除首元结点后,要保持链表的循环性,因此需要找到首元结点的前驱。当链表带头结点时,其前驱就是头结点,因此不论是表头指针还是表尾指针,删除首元结点的时间都为$O(1)$。当链表不带头结点时,其前驱是尾结点,因此,若有表尾指针,就可在$O(1)$的时间找到尾结点;若只有表头指针,则需要遍历整个链表找到尾结点,时间为$O(n)$。

**27. D**
对一个空循环单链表,有`head->next==head`,推理`head->next->next==head->next==head`。对含有一个元素的循环单链表,头结点(头指针head指示)的next域指向这个唯一的元素结点,该元素结点的next域指向头结点,因此也有`head->next->next=head`。

**28. D**
对于两种双链表,删除首结点的时间复杂度都是$O(1)$。对于非循环双链表,删除尾结点的时间复杂度是$O(n)$;对于循环双链表,删除尾结点的时间复杂度是$O(1)$。

**29. D**
对于选项A,在最后一个元素之后插入元素的情况与普通单链表相同,时间复杂度为$O(n)$;而删除第一个元素时,为保持循环单链表的性质(尾结点指向第一个结点),要先遍历整个链表找到尾结点,再做删除操作,时间复杂度为$O(n)$。对于选项B,双链表的情况与单链表的相同,一个是$O(n)$,一个是$O(1)$。对于选项C,在最后一个元素之后插入一个元素,要遍历整个链表才能找到插入位置,时间复杂度为$O(n)$;删除第一个元素的时间复杂度为$O(1)$。对于选项D,与选项A的分析对比,有尾结点的指针,省去了遍历链表的过程,因此时间复杂度均为$O(1)$。

**30. B**
静态链表采用数组表示,因此需要预先分配较大的连续空间,静态链表同时还具有一般链表的特点,即插入和删除不需要移动元素。

**31. B**
静态链表的存储空间虽然是顺序分配的,但元素的存储不是顺序的,查找时仍然需要按链依次进行,而插入、删除都不需要移动元素。静态链表的存储空间是一次性申请的,能容纳的最大元素个数在定义时就已确定。并非每个空间都存储了元素,因此会造成存储空间的浪费。

**32. D**
选项A的第二句代码,相当于将p前驱结点的后继指针指向其自身,错误;选项B和C的第一句代码,相当于将p后继结点的前驱指针指向其自身,错误。只有选项D正确。

**33. D**
根据存储状态,单链表的结构如下图所示。
![单链表示意图](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-33.png)
其中“链接地址”是指结点next所指的内存地址。当结点f插入后,a指向f,f指向e,e指向b。显然a、e和f的“链接地址”分别是f、b和e的内存地址,即1014H、1004H和1010H。

**34. D**
如图1所示,要删除带头结点的非空循环单链表中的第一个元素,就要先用临时指针q指向待删结点,`q=h->next`;然后将q从链表中断开,`h->next=q->next`(这一步也可写成`h->next=h->next->next`);此时要考虑一种特殊情况,若待删结点是链表的尾结点,即循环单链表中只有一个元素(p和q指向同一个结点),如图2所示,则在删除后要将尾指针指向头结点,即`if(p==q) p=h`;最后释放q结点即可,答案选择选项D。
![删除循环单链表首元素示意图](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-34-1.png)
![只有一个元素的循环单链表](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-34-2.png)

**35. C**
链表的插入操作要保证不会造成断链,画图再依次判断选项。执行完语句“①`s->next=p->next`; ②`p->next=s`;”后的结构如下图所示(虚线表示prev,实线表示next)。
![双链表插入操作步骤1](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-35-1.png)
对于选项A,`s->next->prev=p`,错误。对于选项B,`p->next->prev=s`,让s的prev指向s,错误。对于选项D,两句代码均错误。对于选项C,执行完语句“③`s->prev=s->next->prev`; ④`s->next->prev=s`;”后的结构如下图所示,满足插入的要求。
![双链表插入操作步骤2](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-35-2.png)

**36. D**
假设单链表L的初始状态如图①所示。执行`q=p->next`后的状态如图②所示,q指向p的后继结点。执行`p->next=q->next`后的状态如图③所示,结点p的next指向q的后继结点。执行`q->next=h->next`后的状态如图④所示,结点q的next指向h的后继结点。执行`h->next=q`后的状态如图⑤所示,q所指结点移至L的头结点h之后,选项D正确。
![单链表操作过程](https://raw.githubusercontent.com/RangeFu/DataStructure-408/main/LinearList/2-36.png)