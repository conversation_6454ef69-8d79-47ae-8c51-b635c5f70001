# Jina Embeddings v4 API 文档

## 概述

Jina Embeddings v4 是一个通用嵌入模型，专为通用目的和各种流行任务而设计。支持文本和图像的多模态处理，并通过任务特定的 LoRA 适配器优化性能。

## API 端点

```
https://api.jina.ai/v1/embeddings
```

## 认证

```
Authorization: Bearer JINA_API_KEY
```

## 请求参数详解

### 基础参数

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `model` | string | 模型名称：`jina-embeddings-v4` | 必需 |
| `task` | string | 任务类型 | `text-matching` |
| `input` | array | 输入数据数组 | 必需 |

### 任务类型选择

通过下拉菜单选择任务类型：
- **`text-matching`**（默认）：适用于文本匹配和语义相似性任务

### 高级配置选项

#### Downstream task
**说明**：我们的嵌入模型针对通用目的和各种流行任务进行了优化。选择最适合您的任务的选项，它们会提供针对该任务高度优化的嵌入。

#### Late chunking
- **功能**：应用延迟分块技术来利用模型的长上下文能力生成上下文块嵌入
- **默认**：关闭
- **使用场景**：需要保留长文本上下文信息的检索任务
- [了解更多](相关文档链接)

#### Truncate at maximum length
- **功能**：当启用时，模型将自动删除超过最大上下文长度（8192 tokens）的尾部内容
- **默认**：开启
- **说明**：防止因输入过长导致的错误

#### Output multi-vector embeddings
- **功能**：返回 N x D 多向量嵌入，其中 N 是 token 数量，D 是嵌入维度
- **默认**：关闭
- **使用场景**：适用于需要文档级别和 token 级别表示的高级检索场景

#### Output dimensions
- **默认值**：2048
- **说明**：较小的维度可实现高效存储和检索，对 Matryoshka 表示的影响最小
- **可调范围**：支持从 2048 向下调整

#### Output data type
- **选项**：
  - `Default (as float)`：默认浮点数格式
  - `Binary`：二进制格式（用于更快的向量检索）
- **默认**：`Default (as float)`
- **说明**：二进制格式可加速检索但会牺牲一定精度

#### Compatible mode
- **功能**：遵循与我们的文本嵌入模型相同的请求格式
- **限制**：此模式允许您在不更改请求的情况下切换模型。注意：此模式下不支持图像输入
- **默认**：关闭

### 输入格式

#### 文本输入
```json
{
  "text": "Your text content here"
}
```

#### 图像输入
支持三种格式：

1. **图像 URL**
```json
{
  "image": "https://example.com/image.jpg"
}
```

2. **Base64 编码**
```json
{
  "image": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
}
```

3. **Data URI 格式**
```json
{
  "image": "data:image/jpeg;base64,/9j/4AAQSkZJRg..."
}
```

## 完整请求示例

```bash
curl https://api.jina.ai/v1/embeddings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer JINA_API_KEY" \
  -d '{
    "model": "jina-embeddings-v4",
    "task": "text-matching",
    "input": [
      {
        "text": "A beautiful sunset over the beach"
      },
      {
        "text": "Un beau coucher de soleil sur la plage"
      },
      {
        "text": "海滩上美丽的日落"
      },
      {
        "text": "浜辺に沈む美しい夕日"
      },
      {
        "image": "https://i.ibb.co/nQNGqL0/beach1.jpg"
      },
      {
        "image": "https://i.ibb.co/r5w8hG8/beach2.jpg"
      },
      {
        "image": "iVBORw0KGgoAAAANSUhEUgAAABwAAAA4CAIAAABhUg/jAAAAMklEQVR4nO3MQREAMAgAoLkoFreTiSzhy4MARGe9bX99lEqlUqlUKpVKpVKpVCqVHksHaBwCA60e6kgAAAABJRU5ErkJggg=="
      }
    ]
  }'
```

## 响应格式

```
GET RESPONSE
```
（响应将包含每个输入对应的嵌入向量）

## 示例输入说明

上述示例展示了处理：
- **多语言文本**：英语、法语、中文、日语的相同语义内容
- **图像 URL**：两个海滩图片的 URL
- **Base64 图像**：一个 Base64 编码的图像

## 使用限制

### 输入限制
- 图像和 PDF 限制：最多 512 页
- 每页最大：5MB
- 每个 PDF 文档最大：8MB
- 每个请求最多：512 页

### 速率限制
- 具体限制取决于您的 API 计划
- 新用户可获得 1000 万免费 tokens

## 最佳实践

1. **任务选择**：虽然目前只有 `text-matching` 可用，但选择正确的任务类型对获得最优嵌入质量很重要

2. **维度优化**：根据您的存储和性能需求调整输出维度。较小的维度可以节省存储空间，但可能略微降低精度

3. **多向量使用**：对于需要细粒度匹配的高级检索场景，考虑启用多向量嵌入

4. **批处理**：在单个请求中包含多个输入以提高效率

5. **格式选择**：对于大规模检索系统，考虑使用二进制输出格式以提高检索速度