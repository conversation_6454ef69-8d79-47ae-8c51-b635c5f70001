// ============================================================================
// Claude Desktop MCP Auto-Approve Script (0.10.38)
// ============================================================================
// 
// 修复版本特性:
// ? 修复了连续dialog处理失效的问题
// ? 改进冷却机制：单dialog冷却替代全局冷却
// ? 支持快速连续的MCP工具调用
// ? 基于最新Claude Desktop界面结构
// 
// HOW TO INSTRUCTIONS:
// 1. Open Claude Desktop
// 2. Go to Help -> Enable Developer Mode
// 3. Use keyboard shortcut: Ctrl+Shift+Alt+I (Windows) or Cmd+Shift+Option+I (Mac)
// 4. Go to "Console" tab
// 5. Type "allow pasting" and hit Enter
// 6. Paste this snippet and hit Enter
//
// IMPORTANT: 此版本解决了原始脚本在处理连续dialog时失效的问题
//
// ============================================================================

// Configuration - 可配置选项
const CONFIG = {
  // 单个对话框的冷却时间 (毫秒) - 防止同一dialog被重复处理
  COOLDOWN_MS: 1000, // 降低到1秒，允许更快的连续处理
  
  // 按钮选择策略: "allow_once" (推荐) 或 "allow_always" (风险较高)
  APPROVAL_STRATEGY: "allow_once", // 更安全的选择
  
  // 是否启用详细日志
  VERBOSE_LOGGING: true
};

// 状态跟踪
let processedDialogs = new Map(); // 改为Map存储dialog ID和处理时间的映射

// 工具函数：安全的元素查找
function safeQuerySelector(parent, selector) {
  try {
    return parent.querySelector(selector);
  } catch (error) {
    console.warn("选择器查找失败:", selector, error);
    return null;
  }
}

// 工具函数：安全的文本提取
function safeGetText(element) {
  return element?.textContent?.trim() || "";
}

// 工具函数：生成对话框唯一标识
function getDialogId(dialog) {
  return dialog.id || dialog.getAttribute('aria-labelledby') || 'unknown';
}

// 主要的自动批准逻辑
function processApprovalDialog(dialog) {
  const dialogId = getDialogId(dialog);

  // === 第一步：验证这是MCP工具权限对话框 ===
  const titleElement = dialog.querySelector('h2');
  const titleText = safeGetText(titleElement);
  
  if (!titleText.includes("would like to use this tool")) {
    if (CONFIG.VERBOSE_LOGGING) {
      console.log("?? 跳过：不是工具权限对话框");
    }
    return false;
  }

  // === 第二步：提取工具信息 ===
  const toolNameElement = safeQuerySelector(dialog, '.font-mono');
  const toolName = safeGetText(toolNameElement) || "unknown-tool";
  
  const toolDisplayElement = safeQuerySelector(dialog, '.font-medium.text-\\[0\\.9rem\\]');
  const toolDisplay = safeGetText(toolDisplayElement) || toolName;

  if (CONFIG.VERBOSE_LOGGING) {
    console.log("??? 检测到工具权限请求:");
    console.log("   - 工具名称:", toolName);
    console.log("   - 显示名称:", toolDisplay);
    console.log("   - 对话框ID:", dialogId);
  }

  // === 第三步：查找并点击适当的批准按钮 ===
  const buttons = Array.from(dialog.querySelectorAll("button"));
  
  let targetButton = null;
  const buttonTexts = buttons.map(btn => safeGetText(btn));
  
  if (CONFIG.VERBOSE_LOGGING) {
    console.log("?? 可用按钮:", buttonTexts);
  }

  // 根据配置选择按钮
  if (CONFIG.APPROVAL_STRATEGY === "allow_always") {
    targetButton = buttons.find(btn => 
      safeGetText(btn).toLowerCase().includes("allow always")
    );
  } else {
    targetButton = buttons.find(btn => 
      safeGetText(btn).toLowerCase().includes("allow once")
    );
  }

  if (!targetButton) {
    console.warn("?? 未找到目标批准按钮");
    return false;
  }

  // === 第四步：执行自动点击 ===
  try {
    if (CONFIG.VERBOSE_LOGGING) {
      console.log("?? 自动批准工具:", toolDisplay);
      console.log("   - 点击按钮:", safeGetText(targetButton));
      console.log("   - 策略:", CONFIG.APPROVAL_STRATEGY);
    }
    
    targetButton.click();
    
    return true;
  } catch (error) {
    console.error("? 自动点击失败:", error);
    return false;
  }
}

// 主要的观察器逻辑
const observer = new MutationObserver((mutations) => {
  // 查找所有打开的对话框
  const dialogs = document.querySelectorAll('[role="dialog"][data-state="open"]');
  
  if (dialogs.length === 0) {
    return;
  }

  if (CONFIG.VERBOSE_LOGGING && dialogs.length > 0) {
    console.log("?? 检测到", dialogs.length, "个打开的对话框");
  }

  // 处理每个对话框（移除全局冷却，改为单dialog冷却）
  dialogs.forEach(dialog => {
    const dialogId = getDialogId(dialog);
    const now = Date.now();
    
    // 检查此特定dialog是否在冷却期
    const lastProcessTime = processedDialogs.get(dialogId) || 0;
    if (now - lastProcessTime < CONFIG.COOLDOWN_MS) {
      if (CONFIG.VERBOSE_LOGGING) {
        console.log("?? Dialog", dialogId, "仍在冷却期");
      }
      return;
    }
    
    if (processApprovalDialog(dialog)) {
      // 记录此dialog的处理时间
      processedDialogs.set(dialogId, now);
      if (CONFIG.VERBOSE_LOGGING) {
        console.log("? Dialog", dialogId, "处理完成，设置冷却时间");
      }
    }
  });
});

// 启动观察器
observer.observe(document.body, {
  childList: true,
  subtree: true,
  attributes: true,
  attributeFilter: ['data-state', 'aria-hidden']
});

// 清理函数
function stopAutoApproval() {
  observer.disconnect();
  processedDialogs.clear();
  console.log("?? 自动批准已停止");
}

// 配置更新函数
function updateConfig(newConfig) {
  Object.assign(CONFIG, newConfig);
  console.log("?? 配置已更新:", CONFIG);
}

// 状态查询函数
function getStatus() {
  const now = Date.now();
  const recentActivity = Array.from(processedDialogs.entries())
    .filter(([id, time]) => now - time < 60000) // 最近1分钟的活动
    .map(([id, time]) => ({
      dialogId: id,
      processedAt: new Date(time).toLocaleTimeString()
    }));

  return {
    isActive: true,
    config: CONFIG,
    totalProcessed: processedDialogs.size,
    recentActivity: recentActivity,
    lastActivity: processedDialogs.size > 0 ? 
      new Date(Math.max(...processedDialogs.values())).toLocaleTimeString() : 
      'None'
  };
}

// 导出到全局作用域（用于控制台调试）
window.claudeAutoApproval = {
  stop: stopAutoApproval,
  updateConfig: updateConfig,
  getStatus: getStatus,
  config: CONFIG,
  // 新增调试功能
  clearHistory: () => {
    processedDialogs.clear();
    console.log("?? 处理历史已清除");
  },
  forceProcess: () => {
    const dialogs = document.querySelectorAll('[role="dialog"][data-state="open"]');
    console.log("?? 强制处理", dialogs.length, "个对话框");
    dialogs.forEach(dialog => processApprovalDialog(dialog));
  }
};

// 启动消息
console.log("? Claude Desktop Auto-Approve Script 已激活!");
console.log("?? 当前配置:", CONFIG);
console.log("??? 控制命令:");
console.log("   - 停止: claudeAutoApproval.stop()");
console.log("   - 状态: claudeAutoApproval.getStatus()");
console.log("   - 配置: claudeAutoApproval.updateConfig({APPROVAL_STRATEGY: 'allow_always'})");
console.log("   - 清除历史: claudeAutoApproval.clearHistory()");
console.log("   - 强制处理: claudeAutoApproval.forceProcess()");
console.log("?? 开始监听MCP工具权限对话框...");
console.log("?? 修复说明: 已解决连续dialog失效问题，现在支持快速连续处理");

// ============================================================================
// 重要说明:
// 1. 此脚本基于2025年最新界面结构，支持新的按钮布局
// 2. 默认使用"Allow once"策略，更加安全
// 3. 修复了连续dialog处理失效的问题
// 4. 改进了冷却机制：现在是单dialog冷却而非全局冷却
// 5. 可通过控制台命令动态调整配置和调试
// 6. 脚本仅在当前会话有效，重启Claude Desktop需要重新运行
// 7. 对于生产环境，建议使用MCP服务器解决方案: claude-autoapprove-mcp
// ============================================================================