# 离散事件模拟法的中文教材内容

## 第6章 离散事件模拟法

### 引言

一个概率模型的模拟包括产生此模型的随机机理和观察此模型相应结果两部分。要模拟某一个模型，我们就必须确定某些感兴趣的量。然而，因为模型随时间在变化且其机理的逻辑结构很复杂，故人们一般不能明确地了解其如何变化以及如何确定上述感兴趣的量。基于"离散事件"的思想，一个通用的框架可以帮助人们模拟一个随机时间变化的模型及确定其中某些感兴趣的量。基于这个框架的模拟方法就被称为离散事件模拟法。

### 6.1 离散事件模拟法

变量和事件是离散事件模拟法中最重要的元素。在模拟中，我们始终跟踪某些变量。一般地，有如下三种常用的变量类型：时间变量，计数变量和系统状态变量。

**变量**
1. 时间变量 $t$ — 表示模拟所用的时间总量
2. 计数变量 — 这些变量表示时刻 $t$ 某些事件出现的次数
3. 系统状态变量 (SS) — 此变量描述系统在时刻 $t$ 的状态

只要出现一个事件，上述变量的值就要改变或更新，我们就要收集相应感兴趣的数据作为输出。为确定下一事件何时出现，我们需要一个事件列表 (此列表给出后面最近的事件和这些事件出现的时间表)。只要出现一个事件，我们就要重新设置时间变量、状态变量、计数变量和收集到的相应数据。这样做的目的在于我们可以及时地追踪随时间而变化的系统。

由于上面仅给出了离散事件模拟法的大致思想，故下面几个例子对于了解如何应用此种方法是很有帮助的。在 6.2 节，我们考虑单服务员等候或排队系统的模拟。6.3 节和 6.4 节考虑多服务员排队系统的模拟，其中 6.3 节假设各服务员是串联排列的，而 6.4 节则假设各服务员是并联排列的。6.5 节是一仓储模型，6.6 节是一保险风险模型，6.7 节是一个机器维修模型。在 6.8 节我们考虑一个股票期权模型。

在所有排队模型中，我们假设顾客到达时间服从一个带有有界强度函数 $\lambda(t)$，$t > 0$ 的非齐次泊松过程。在模拟这些模型中，下面子程序将被用来生成随机变量 $T_s$，其中 $T_s$ 表示时间 $s$ 后第一位顾客的到来时间。

对于给定的 $\lambda(t), t > 0$，假设 $\lambda$ 满足 $\lambda(t) \leq \lambda, \forall t$。则生成 $T_s$ 的子程序为：

**生成 $T_s$ 的子程序**
步骤 1：设 $t = s$。
步骤 2：生成随机数 $U$。
步骤 3：记 $t = t - \frac{1}{\lambda} \ln U$。
步骤 4：生成随机数 $U$。
步骤 5：如果 $U < \lambda(t)/\lambda$，记 $T_s = t$ 并停止。
步骤 6：转至步骤 2。

### 6.2 单服务员排队系统

在一个服务站，假设顾客到达时间服从一个强度函数为 $\lambda(t),t \geq 0$ 的非齐次泊松过程，且仅有一个服务员。当服务员空闲时，到达的顾客可以得到即时的服务；而当服务员工作时，新来的顾客要排队等候服务。另外，我们还假设服务员完成一位顾客的服务后，如果有顾客排队等候服务，则他转而服务下一个等候时间最长的顾客（这种服务称之为"先来先得"）；如果没有顾客排队，则他空闲下来等候下一位顾客的到来。假设每一位顾客所需的服务时间是一概率分布为 $G$ 的随机变量，且独立于其他顾客的服务时间和到达时间。另外，假设时间 $T$ 后下班，不再允许顾客进入服务站等候服务，即使服务员已经完成了对所有在时间 $T$ 前进入服务站的顾客的服务，其中 $T$ 是一固定值。

在上面系统中，假设我们感兴趣且想确定的量为：(a) 顾客在系统中所花的平均时间；(b) 时间 $T$ 后，最后一位顾客离开服务站的平均时间，即服务员可以回家时的平均加班时间。

在模拟上述系统时，我们用到的变量如下：

**时间变量**     $t$
**计数变量**     $N_A$：时刻 $t$ 到达的顾客数
                $N_D$：时刻 $t$ 离开的顾客数
**系统状态变量**  $n$：时刻 $t$ 时服务站中的顾客数

因为顾客的到来或离开均可改变上述变量，故我们称它们为"事件"。也就是说，此系统中有两类事件：离开和到达。于是，事件列表包含下一顾客的到达时间和正在接受服务顾客的离开时间，即事件列表为

$$EL = t_A, t_D$$

其中 $t_A$ 是时间 $t$ 后下一顾客的到达时间，$t_D$ 是正在接受服务的顾客的离开时间。如果此时没有顾客接受服务，则取 $t_D$ 为 $\infty$。

我们收集的输出变量为：第 $i$ 个顾客的到达时间 $A(i)$ 和离开时间 $D(i)$；时间 $T$ 后最后一位顾客的离开时间 $T_p$。

开始模拟之前，变量和事件的初始值取为：

**初始化**
取 $t = N_A = N_D = 0$。
取 $SS = 0$。
生成 $T_0$，并取 $t_A = T_0, t_D = \infty$。

为更新此系统，我们就要改变时间直至下一个事件出现。至于出现哪个事件，则要考虑多种情形，且依赖于事件列表中的较小者。下面以 $Y$ 记分布为 $G$ 的服务时间随机变量。

$t =$ 时间变量， $SS = n$， $EL = t_A, t_D$

**情形 1：** $t_A \leq t_D, t_A \leq T$
重新取 $t = t_A$（转到时刻 $t_A$）。
重新取 $N_A = N_A + 1$（因为在时刻 $t_A$ 多了一位顾客）。
重新取 $n = n + 1$（因为此时多了一位顾客）。
生成 $T_l$，并重新取 $t_A = T_l$（这是下一位顾客的到达时间）。
如果 $n = 1$，则生成 $Y$ 并重新取 $t_D = t + Y$（因为此时服务站无顾客，故必须产生下一位顾客所需的服务时间）。
收集输出数据 $A(N_A) = t$（因为顾客 $N_A$ 在时间 $t$ 到达）。

**情形 2：** $t_D < t_A, t_D \leq T$
重新取 $t = t_D$。
重新取 $n = n - 1$。
重新取 $N_D = N_D + 1$（因为一人在时间 $t$ 离开）。
如果 $n = 0$，则重新取 $t_D = \infty$；否则，生成 $Y$ 并重新取 $t_D = t + Y$。
收集输出数据 $D(N_D) = t$（因为顾客 $N_D$ 刚离开）。

**情形 3：** $\min(t_A, t_D) > T, n > 0$
重新取 $t = t_D$。
重新取 $n = n - 1$。
重新取 $N_D = N_D + 1$。
如果 $n > 0$，则生成 $Y$ 并重新取 $t_D = t + Y$。
收集输出数据 $D(N_D) = t$。

**情形 4：** $\min(t_A, t_D) > T, n = 0$
收集输出数据 $T_p = \max(t - T, 0)$。

上述算法的流程图见图 6-1。得到"停止"块时，我们就收集数据 $N_A$（到达顾客的总数，它等于离开顾客的总数 $N_D$）。对每个顾客 $i, i = 1, \cdots, N_A$，我们有其到达和离开时间 $A(i)$ 和 $D(i)$，于是 $D(i) - A(i)$ 就表示顾客 $i$ 在系统中的时间。最后，我们可以得到最后一位顾客离开的时间 $T_p$。每收集一次数据，我们就说完成了一次模拟。每次模拟之后，我们重新对变量初始化并进行另一次模拟，直至我们认为已收集到足够多的数据（何时结束模拟的问题将在第 7 章考虑）。收集到的所有 $T_p$ 的平均即为最后一位顾客离开的平均时间；所有观察到的 $D - A$ 的平均即是一位顾客在系统中所花时间的估计。

![图6-1 模拟单服务员系统的流程图]

**注** 如果我们想存储输出数据以得到即时的顾客人数，则无论是哪个事件出现，都必须输出系统状态和时间变量对 $(n, t)$。例如，如果输出数据为 $(1, 4)$ 和 $(0, 6)$，且以 $n(t)$ 记时刻 $t$ 系统中的顾客人数，则有

$$
n(t) = 0,如果 0 \leq t < 4, \\
n(t) = 1,如果 4 \leq t < 6, \\
n(t) = 0,如果 t = 6
$$

### 6.3 两个服务员的串联排队系统

现在考虑有两个服务员的串联排队系统。假设顾客到达时间服从非齐次泊松过程，且每一位顾客必须先找服务员 1，只有服务员 1 完成对该顾客的服务后，才能找服务员 2。称这样的系统为串联或串行的排队系统。当服务员 1 空闲时，到达的顾客可以即时得到服务员 1 的服务；否则，就要排队等候服务员 1 的服务。类似地，当顾客得到服务员 1 的服务后，如果服务员 2 空闲，则该顾客可以立即得到服务员 2 的服务；否则，他就要排队等候服务员 2 的服务。当完成服务员 2 的服务后，该顾客才离开系统。以 $G_i, i = 1, 2$ 表示服务员 $i$ 的服务时间的分布（见图 6-2）。

![图6-2 两个服务员的串联排队系统]

假设我们感兴趣的是想利用模拟方法研究顾客得到服务员 1 和服务员 2 的总服务时间的分布。我们将利用下列变量解决上述问题。

**时间变量 t**
**系统状态变量SS**
$(n_1, n_2)$：表示有 $n_1, n_2$ 位顾客分别在等候或接受服务员 1 和服务员 2 的服务。

**计数变量**
$N_A$：至时刻 $t$ 到达的顾客数
$N_D$：至时刻 $t$ 离开的顾客数

**输出变量**
$A_1(n)$：顾客 $n$ 的到达时间 $(n \geq 1)$
$A_2(n)$：顾客 $n$ 到达服务员 2 处的时间 $(n \geq 1)$
$D(n)$：顾客 $n$ 的离开时间 $(n \geq 1)$

**事件列表 $t_A, t_1, t_2$：** $t_A$ 表示下一顾客的到达时间，$t_i$ 表示服务员 $i$ 对正在接受服务的顾客的服务时间 $(i = 1, 2)$。如果此时服务员 $i$ 空闲，则记 $t_i = \infty, i = 1, 2$。事件列表始终包含三个变量 $t_A, t_1, t_2$。

为开始进行模拟，我们取变量和事件列表的初始值如下：

**初始化**
取 $t = N_A = N_D = 0$。
取 $SS = (0, 0)$。
生成 $T_0$，且取 $t_A = T_0, t_1 = t_2 = \infty$。

为更新此系统，我们就要改变时间直至下一个事件的出现。至于出现哪个事件，则要考虑多种情形，且依赖于事件列表中的较小者。下面以 $Y_i$ 记分布为 $G_i$ 的服务时间随机变量 $(i = 1, 2)$。

$$SS = (n_1, n_2)， EL = t_A, t_1, t_2$$

**情形 1：** $t_A = \min(t_A, t_1, t_2)$
重新取 $t = t_A$。
重新取 $N_A = N_A + 1$。
重新取 $n_1 = n_1 + 1$。
生成 $T_l$ 并重新取 $t_A = T_l$。
如果 $n_1 = 1$，则生成 $Y_1$ 且重新取 $t_1 = t + Y_1$。
收集输出数据 $A_1(N_A) = t$。

**情形 2：** $t_1 < t_A, t_1 \leq t_2$
重新取 $t = t_1$。
重新取 $n_1 = n_1 - 1, n_2 = n_2 + 1$。
如果 $n_1 = 0$，重新取 $t_1 = \infty$；否则，生成 $Y_1$ 且重新取 $t_1 = t + Y_1$。
如果 $n_2 = 1$，则生成 $Y_2$ 且重新取 $t_2 = t + Y_2$。
收集输出数据 $A_2(N_A - n_1) = t$。

**情形 3：** $t_2 < t_A, t_2 < t_1$
重新取 $t = t_2$。
重新取 $N_D = N_D + 1$。
重新取 $n_2 = n_2 - 1$。
如果 $n_2 = 0$，则重新取 $t_2 = \infty$。
如果 $n_2 > 0$，则生成 $Y_2$ 且重新取 $t_2 = t + Y_2$。
收集输出数据 $D(N_D) = t$。

利用上述更新方法，我们很容易得到模拟此系统和收集相关数据的程序。

### 6.4 两个服务员的并联排队系统

现在考虑模拟有两个服务员的排队系统。如果两个服务员均忙碌，则到达的顾客排队等候。如果服务员 1 空闲，则顾客接受服务员 1 的服务；如果服务员 2 空闲，则接受服务员 2 的服务。当顾客得到服务后（不论是服务员 1 还是服务员 2），则离开系统，且下一个等候时间最久的顾客接受服务。设服务员 $i$ 的服务时间分布为 $G_i, i = 1, 2$（见图 6-3）。

![图6-3 两个服务员的并联排队系统]

假设我们想模拟上述模型，且想知道每位顾客在服务站的时间、每位服务员服务的顾客人数等。由于有两位服务员为顾客服务，故顾客离开的顺序不一定和其到达顺序相同。因此，为掌握哪位顾客结束服务离开系统，就必须知道哪些顾客在系统中。于是，我们按照顾客到达时间给顾客编号：记先到的为顾客 1，第二位为顾客 2 等等。由于顾客是以其到达时间的顺序接受服务的，故知道哪位顾客正在接受服务以及有多少位正在排队等候服务这些信息，就能帮助我们识别等候服务的顾客。假设顾客 $i, j(i < j)$ 正在接受服务，且共有 $n - 2 > 0$ 个顾客在排队。由于编号小于 $j$ 的顾客先于 $j$ 接受服务，且没有编号大于 $j$ 的顾客接受服务（否则他们将先于 $i$ 或 $j$ 接受服务），故 $j + 1, \cdots, j + n - 2$ 位顾客在排队等候服务。

我们将利用下述变量分析此系统：

**时间变量 t**
**系统状态变量 (SS)**
$(n, i_1, i_2)$：$n$ 表示此系统中的顾客数，$i_1, i_2$ 分别表示接受服务员 1, 服务员 2 服务的顾客数。当系统为空时，$SS = (0)$，若唯一的顾客 $j$ 接受服务员 1 或服务员 2 服务时，相应地 $SS = (1, j, 0)$ 或 $SS = (1, 0, j)$。

**计数变量**
$N_A$：至时刻 $t$ 到达的顾客数
$C_j$：至时刻 $t$ 由服务员 $j$ 服务的顾客数 $(j = 1, 2)$

**输出变量**
$A(n)$：顾客 $n$ 的到达时间 $(n \geq 1)$
$D(n)$：顾客 $n$ 的离开时间 $(n \geq 1)$

**事件列表 $t_A, t_1, t_2$：** $t_A$ 表示下一位顾客的到达时间，$t_i$ 表示服务员 $i$ 对正在接受服务顾客的服务时间 $(i = 1, 2)$。如果服务员 $i$ 空闲，则取 $t_i = \infty(i = 1, 2)$。事件列表始终包含三个变量 $t_A, t_1, t_2$。

为开始进行模拟，我们取变量的事件列表的初始值如下：

**初始化**
取 $t = N_A = C_1 = C_2 = 0$。
取 $SS = (0)$。
生成 $T_0$ 并取 $t_A = T_0, t_1 = t_2 = \infty$。

为更新此系统，我们就要改变时间直至下一个事件出现。至于出现哪个事件，则要考虑多种情形，且依赖于事件列表中的较小者。下面以 $Y_i$ 记分布为 $G_i$ 的服务时间随机变量 $(i = 1, 2)$。

**情形 1：** $SS = (n, i_1, i_2), t_A = \min(t_A, t_1, t_2)$
重新取 $t = t_A$。
重新取 $N_A = N_A + 1$。
生成 $T_l$ 并重新取 $t_A = T_l$。
收集输出数据 $A(N_A) = t$。
如果 $SS = (0)$：
    重新取 $SS = (1, N_A, 0)$。
    生成 $Y_1$ 并重新取 $t_1 = t + Y_1$。
如果 $SS = (1, j, 0)$：
    重新取 $SS = (2, j, N_A)$。
    生成 $Y_2$ 并重新取 $t_2 = t + Y_2$。
如果 $SS = (1, 0, j)$：
    重新取 $SS = (2, N_A, j)$。
    生成 $Y_1$ 并重新取 $t_1 = t + Y_1$。
如果 $n > 1$：
    重新取 $SS = (n + 1, i_1, i_2)$。

**情形 2：** $SS = (n, i_1, i_2), t_1 < t_A, t_1 \leq t_2$
重新取 $t = t_1$。
重新取 $C_1 = C_1 + 1$。
收集输出数据 $D(i_1) = t$。
如果 $n = 1$：
    重新取 $SS = (0)$。
    重新取 $t_1 = \infty$。
如果 $n = 2$：
    重新取 $SS = (1, 0, i_2)$。
    重新取 $t_1 = \infty$。
如果 $n > 2$：记 $m = \max(i_1, i_2)$ 且
    重新取 $SS = (n - 1, m + 1, i_2)$。
    生成 $Y_1$ 并重新取 $t_1 = t + Y_1$。

**情形 3：** $SS = (n, i_1, i_2), t_2 < t_A, t_2 < t_1$
情形 3 的更新留作习题。

如果我们利用上述方法模拟此系统，且在某一事先给定的时间点停止模拟，则由输出变量和 $C_1, C_2$ 的最终值可以得到每位顾客的到达和离开时间以及每个服务员的服务人数。

### 6.5 仓储模型

现考虑某商店存储单价为 $r$ 的某类产品。假设需求此产品的顾客量服从参数为 $\lambda$ 的泊松过程，且每位顾客的需求量是一分布为 $G$ 的随机变量。为满足需要，店主手中必须有一定存量的产品，但当手中的库存较少时，店主就要从批发商订购一定数量的产品。店主应用的策略被称为 $(s, S)$ 策略：当手中的存量小于 $s$ 且当前没有待处理的订单，则店主需订购一定数量的产品以使存量达到 $S(s < S)$。也就是说，如果当前没有待处理的订单且存量 $x < s$，则需订购总量为 $S - x$ 的产品。设订购 $y$ 单位产品的费用为一给定函数 $c(y)$，且需 $L$ 个时间单元才能得到这批产品。另外，假设单位产品每单位时间的存储费用为 $h$。当需求量多于商店的当前存量时，则商店卖出所有的产品，但将失去多余的订单。

下面我们将利用模拟方法来估计到某时间 $T$ 时此商店的期望收益。为了模拟，我们先定义如下变量和事件：

**时间变量 t**
**系统状态变量 $(x, y)$ $x$ 表示手头的存量，$y$ 表示已订购但未交货的数量。**
**计数变量**
$C$：至时刻 $t$ 的总订购费用
$H$：至时刻 $t$ 的总仓储费用
$R$：至时刻 $t$ 的总收入。

**事件包含顾客和订单的到达时间，其时间为**
$t_0$：下一顾客的到达时间
$t_1$：订购产品被送达的时间。如果没有待处理订单，则 $t_1 = \infty$。

变量更新的完成依赖于事件出现的时间。如果以 $t, y$ 记当前时间且我们有上述变量值，则考虑如下两种情形：

**情形 1：** $t_0 < t_1$
重新取 $H = H + (t_0 - t)xh$［其原因是我们要支付时间 $t$ 与 $t_0$ 间的每单位产品 $(t_0 - t)h$ 的存储费用］。
重新取 $t = t_0$。
生成 $D$（一个分布为 $G$ 的随机变量，且为时间 $t_0$ 时顾客的需求量）。
记 $w = \min(D, x)$ 为商店卖出的产品总量。则剩余的库存量为 $x - w$。
重新取 $R = R + wr$。
重新取 $x = x - w$。
如果 $x < s$，且 $y = 0$，则重新取 $y = S - x, t_1 = t + L$。
生成 $U$ 并重新取 $t_0 = t - \frac{1}{\lambda} \ln(U)$。

**情形 2：** $t_1 \leq t_0$
重新取 $H = H + (t_1 - t)xh$。
重新取 $t = t_1$。
重新取 $C = C + c(y)$。
重新取 $x = x + y$。
重新取 $y = 0, t_1 = \infty$。

我们容易利用上述更新方法编写分析此模型的模拟程序，并运行此模拟程序直至在某一事先给定时间 $T$ 后第一个事件出现。我们用 $(R - C - H)/T$ 作为商店单位时间内平均收益的估计。改变 $s$ 和 $S$ 的值以确定商店最佳的存储策略。

### 6.6 保险风险模型

假设某保险公司的投保客户独立地向公司索赔且服从参数为 $\lambda$ 的泊松过程。另外，客户的索赔额是一分布 $F$ 的随机变量。再假设来此保险公司投保的新客户数服从参数为 $\nu$ 的泊松过程，而留下来的老客户服从参数为 $\mu$ 的指数分布。最后，再假设每个客户单位时间内付给保险公司的费用 $c$ 是固定的。以 $n_0$ 记最初的客户数，$a_0 > 0$ 表示最初资金，我们感兴趣的是想利用模拟方法估计到时间 $T$ 此公司的资金总是非负的概率。

为模拟此模型，我们定义变量和事件如下：

**时间变量 t**
**系统状态变量 $(n, a)$：$n$ 为客户数，$a$ 是公司当前的资金。**

**事件有三种类型：** 新增一个客户，失去一个客户和索赔。事件列表仅有一个值，即下一个事件出现的时间。

**EL $t_E$**

由 2.9 节所讲的关于指数分布的结果，我们能够说明事件列表为什么仅包含下一事件出现的时间。如果 $(n, a)$ 是时间 $t$ 的系统状态，则由于独立指数随机变量的最小者仍是指数的，故下一事件出现的时间为 $t + X$，其中 $X$ 是一参数为 $\nu + n\mu + n\lambda$ 的指数随机变量。另外，不论下一事件何时出现，它都将是如下情况之一：

以概率 $\frac{\nu}{\nu + n\mu + n\lambda}$ 新增一个客户；

以概率 $\frac{n\mu}{\nu + n\mu + n\lambda}$ 失去一个客户；

以概率 $\frac{n\lambda}{\nu + n\mu + n\lambda}$ 索赔。

当确定下一个事件何时出现后，我们生成一个随机数以确定此事件将导致哪种情况出现，然后利用此信息重新确定系统状态值。

对于给定的状态变量 $(n, a)$，下面以 $X$ 表示参数为 $\nu + n\mu + n\lambda$ 的指数随机变量；以 $J$ 表示以概率 $\frac{\nu}{\nu + n\mu + n\lambda}$ 取 1，以概率 $\frac{n\mu}{\nu + n\mu + n\lambda}$ 取 2，以概率 $\frac{n\lambda}{\nu + n\mu + n\lambda}$ 取 3 的随机变量；以 $Y$ 表示分布函数为 $F$ 的索赔随机变量。

**输出变量 $I$ 为**

$$
I = \begin{cases}
1 & 如果公司资金在 [0, t] 内非负，\\
0 & 其他情况。
\end{cases}
$$

**初始化**
先取
        $t = 0$， $a = a_0$， $n = n_0$
然后生成 $X$ 并取
        $t_E = X$

为更新此系统，我们向前移动至下一事件，并检验它是否超过时间 $T$。

**更新步骤**
**情形 1：** $t_E > T$：
    取 $I = 1$ 且结束此次模拟。
**情形 2：** $t_E \leq T$：
    重新取
        $a = a + nc(t_E - t)$
        $t = t_E$
    生成 $J$：
        $J = 1$: 重新取 $n = n + 1$
        $J = 2$: 重新取 $n = n - 1$
        $J = 3$: 生成 $Y$。如果 $Y > a$，则取 $I = 0$ 且结束此次模拟；否则重新取
        $a = a - Y$
    生成 $X$；重新取 $t_E = t + X$
连续重复上述更新步骤直至此次模拟完成。

### 6.7 维修问题

一个系统的运转需要 $n$ 台机器正常工作，为预防故障机器影响系统运转，现有几台可用机器备用。只要某台机器出现故障，就立即用备用机器替换它，且把它送到修理工厂进行维修，而修理厂仅有一位修理人员且一次仅能维修一台故障机器。一旦故障机器被修好，则立即转入备用状态（见图 6-4）。设每次维修时间均是独立的分布函数为 $G$ 的随机变量，而每台机器投入使用后，其正常工作时间是一独立于过去的随机变量，且其分布函数为 $F$。

![图6-4 维修模型]

当有一台机器出现故障而没有可用的备用机器时，我们称此系统"崩溃"。假设我们最初利用 $n + s$ 台可用机器中的 $n$ 台，而其余的 $s$ 台备用，且感兴趣的是模拟此系统并近似 $E[T]$，其中 $T$ 为系统崩溃时间。

为模拟上述模型，我们应用下述变量。

**时间变量 t**
**系统状态变量 $r$：时间 $t$ 发生故障的机器数**

由于正在工作的机器出现故障或某台故障机器维修完成均改变系统状态变量，故我们称这两种情况为"事件"。为了知道下一事件何时发生，我们必须知道现正常工作的机器发生故障的时间和正在维修的机器所需的维修时间（如果只有一台机器在维修）。因为我们总需要确定 $n$ 个故障时间中的最小者，故用一个由小到大排列的列表来存储这 $n$ 个时间最方便。于是，我们取事件列表如下：

**事件列表：$t_1 \leq t_2 \leq t_3 \leq \cdots \leq t_n, t^*$**

其中 $t_1, \cdots, t_n$ 是 $n$ 台工作机器发生故障的时间，$t^*$ 是正在维修机器被修好的时间（如果没有机器在维修，则取 $t^* = \infty$）。

为开始模拟，我们取各变量的初始值如下：

**初始化**
取 $t = r = 0, t^* = \infty$。
生成来自分布函数 $F$ 的 $n$ 个独立随机数 $X_1, \cdots, X_n$。
对 $\{t_i\}$ 排序，使 $t_1 \leq t_2 \leq \cdots \leq t_i \leq \cdots \leq t_n$。
取事件列表：$t_1, \cdots, t_n, t^*$。

根据如下两种情形更新系统：

**情形 1：** $t_1 < t^*$
    重新取 $t = t_1$。
    重新取 $r = r + 1$（其原因是另一台机器出现故障）。
    如果 $r = s + 1$，停止模拟并收集数据 $T = t$（其原因是此时有 $s + 1$ 个机器发生故障且没有备用的机器可用）。
    如果 $r < s + 1$，则生成来自分布函数 $F$ 的随机数 $X$。此随机数将表示备用机器投入使用后的工作时间。现将值 $t_2, t_3, \cdots, t_n, t + X$ 由小到大重新排序，且以 $t_i$ 记第 $i$ 个最小的 $(i = 1, \cdots, n)$。
    如果 $r = 1$，生成来自分布函数 $G$ 的随机数 $Y$ 且取 $t^* = t + Y$（其原因是此时发生故障的机器仅有一台，故它将得到及时的维修，而 $Y$ 将是它的维修时间，且维修将在 $t + Y$ 时刻完成）。

**情形 2：** $t^* \leq t_1$
    重新取 $t = t^*$。
    重新取 $r = r - 1$。
    如果 $r > 0$，则生成来自分布函数 $G$ 的随机数 $Y$（它表示刚得到维修服务的机器的维修时间）且重新取 $t^* = t + Y$。
    如果 $r = 0$，取 $t^* = \infty$。

上述更新方法的图示说明见图 6-5。

![图6-5 维修问题的图示说明]

每次停止（当 $r = s + 1$ 时）就表明一次模拟的完成，且得到的输出值是崩溃时间 $T$。之后，我们再进行初始值选取和下一次模拟。这样，我们总共进行 $k$ 次模拟，得到输出值为 $T_1, \cdots, T_k$。因为这 $k$ 个随机变量是独立的，且每一个均反映系统崩溃时间，故其平均值 $\sum_{i=1}^k T_i/k$ 即为平均崩溃时间 $E[T]$ 的估计。第 7 章将考虑何时停止模拟，即 $k$ 的选取问题。并且，应用第 7 章中的方法对模拟结果进行统计分析。

### 6.8 股票期权的模拟

以 $S_n, n \geq 0$ 记第 $n$ 天某指定股票的收盘价。一个通用的模型为

$$S_n = S_0 \exp\{X_1 + \cdots + X_n\},  n \geq 0$$

其中 $X_1, X_2, \cdots$ 是独立的来自均值为 $\mu$，方差为 $\sigma^2$ 的正态随机量列。此模型假设每个交易日相对于前一个交易日的增长率具有相同的分布，我们称之为对数正态模型。记 $\alpha = \mu + \sigma^2/2$。现假设你有权在以后 $N$ 天内的任何一天以每股固定价 $K$（称之为敲定价）购买此种股票。如果当股票价格为 $S$ 时你执行此期权，则由于你仅需支付 $K$ 元，故你的获利将为 $S - K$（从理论上讲，由于你执行此期权后，转身就能以价格 $S$ 卖出）。拥有此期权的平均获利依赖于你所应用的期权执行策略（如果在这 $N$ 天之内，股票价格没有超过 $K$，则显然你不会执行此期权）。现已证明，当 $\alpha \geq 0$ 时，最佳策略是：在持有期间，如果股票价格超过 $K$，则执行此期权；否则，不执行此期权。因为 $X_1 + \cdots + X_N$ 是一均值为 $N\mu$，方差为 $N\sigma^2$ 的正态随机变量，故容易清楚地计算此策略的收益。然而，当 $\alpha < 0$ 时，要刻画此种期权的最佳或近似最佳策略是非常困难的，并且我们也很难清楚地计算一个好的合理策略的平均获利。下面，我们将给出一个当 $\alpha < 0$ 时可以利用的策略。尽管此策略不是最佳的，但看来是一个好的合理策略。此策略为：当离期权到期日还有 $m$ 天时，如果对于任一个 $i = 1, \cdots, m$，执行期权得到的期望收益大于等于 $i$ 天后再执行期权的收益（即等到等 $i$ 天后股票的价格高于 $K$，则执行期权；否则，则放弃此权力），则执行此期权。

以 $P_m = S_{N-m}$ 记期权到期前第 $m$ 天的股票价。我们建议的策略为：

**策略** 假设期权到期日还剩 $m$ 天，如果此时 

$$P_m > K$$

且对于每个 $i = 1, \cdots, m$,

$$P_m > K + P_m e^{i\alpha} \Phi(\sigma\sqrt{i} + b_i) - K\Phi(b_i)$$

则执行此期权，其中

$$b_i = \frac{i\mu - \ln(K/P_m)}{\sigma\sqrt{i}}$$

$\Phi(x)$ 为标准正态分布函数，且可由下式较精确地近似计算：当 $x \geq 0$ 时

$$\Phi(x) \approx 1 - \frac{1}{\sqrt{2\pi}}(a_1y + a_2y^2 + a_3y^3)e^{-x^2/2}$$

当 $x < 0$ 时，$\Phi(x) = 1 - \Phi(-x)$，其中

$$y = \frac{1}{1 + 0.33267x},$$

$$a_1 = 0.4361836,  a_2 = -0.1201676,  a_3 = 0.9372980$$

如果执行此期权，则以 $SP$ 记执行此期权时股票的价格；如果此期权没有被执行，则取 $SP$ 等于 $K$。为确定上述策略的期望收益，即 $E[SP] - K$，我们必须借助于统计模拟。对于给定的参数 $\mu, \sigma, N, K, S_0$，我们先生成均值为 $\mu$，标准差为 $\sigma$ 的正态随机数，之后利用关系式

$$P_{m-1} = P_m e^X$$

来产生 $P_m$。如果 $P_m$ 为期权到期前第 $m$ 天的股票收盘价，且此时的策略是不行使此期权，则我们再产生 $X$ 并得到 $P_{m-1}$，且利用计算机检验此时是否执行此期权。

### 6.9 模拟模型的验证

由于离散事件模拟方法是用计算机程序来实现的，人们为确保此程序无缺陷，当然会用各种标准方法以消除种种缺陷。下面，我们将讨论几种可专门用来消除模拟模型缺陷的方法。

对于一个大程序而言，人们将努力去除模块或子程序中的缺陷，其方法通常是把程序分成几个小的易于处理的部分（每部分从逻辑上都自成一体），之后去除每部分的缺陷。例如，在统计模拟中，可以先把随机变量的生成作为一个模块，之后再分别去除每一模块的缺陷。

一般地，统计模拟程序总是要利用很多输入变量，而这些变量的适当选取可以把一个模拟模型简化成一个有解析解的或前面已广泛研究过的模型，于是，我们可以对模拟结果和真实结果进行比较。

在检验阶段，程序应输出它产生的所有随机变量值，并适当选取某些简单的特殊情况，比较模拟结果和手工结果是否有差异。例如，假设我们想模拟有 $k$ 个服务员前 $T$ 个时间单元内的排队系统，而取 $T = 8$（或另一个较小值）和 $k = 2$。此时模拟程序生成如下数据：

顾客数：    1    2    3    4    5    6
到达时刻：  1.5  3.6  3.9  5.2  6.4  7.7
服务时刻：  3.4  2.2  5.1  2.4  3.3  6.2

并假设程序输出的六位顾客在本系统的平均时间为 5.12。

然而，通过手工计算可知：第一位顾客花在系统中的时间为 3.4；第二位顾客为 2.2（注意只有两位服务员）；第三位顾客到达时刻为 3.9，得到服务的时刻为 4.9（当第一位顾客离开），而经过时间 5.1 后离开，故他花在系统中的时间为 6.1；第四位顾客的到达时刻为 5.2，得到服务的时刻为 5.8（当第二位顾客离开），而经过时间 2.4 后离开，故他花在系统中的时间为 3.0。以此类推，手工计算的结果为：

到达时刻：   1.5   3.6   3.9   5.2   6.4   7.7
开始服务时刻： 1.5   3.6   4.9   5.8   8.2   10.0
离开时刻：   4.9   5.8   10.0   8.2   11.5   16.2
在系统中的时间： 3.4   2.2   6.1   3.0   5.1   8.5

于是，时刻 $T = 8$ 之前来到此系统的顾客在系统中的平均时间为

$$\frac{3.4 + 2.2 + 6.1 + 3.0 + 5.1 + 8.5}{6} = 4.71666 \cdots$$

它与程序得到的 5.12 有误差。

利用跟踪器查找计算机程序中的错误是非常有效的。每当一个事件出现后，系统状态变量、事件列表和计数变量均被记录在跟踪器中。由跟踪器内容可以判断模拟系统的表现是否如愿（如果跟踪器显示没有明显错差，则人们可以检验有关输出变量的计算）。

### 习题

1. 编写程序以生成 6.2 节模型的期望输出。应用此程序估计顾客在系统中的平均时间和服务员的平均加班时间，其中顾客到达过程是一参数为 10 的泊松过程，服务时间的密度函数为

   $$g(x) = 20e^{-40x}(40x)^2, x > 0$$

   $T = 9$。先模拟 100 次，之后再模拟 1000 次。

2. 对于 6.2 节的模型，假设我们还希望得到服务员一天中空闲时间的有关信息。请解释如何完成此任务。

3. 假设到达一个单服务员排队系统的顾客服从一个非齐次泊松过程，开始时的参数为每小时 4 个；在后面的 5 小时内，其参数稳定增长至每小时 19 个；而在后面的 5 小时内，其参数又稳定地降至每小时 4 个。如此反复，即 $\lambda(t + 10) = \lambda(t)$。假设服务时间分布是一参数为每小时 25 的指数分布。只要服务员完成一次服务且没有顾客等候，则假设他将休息一次，其休息时间服从 (0,0.3) 上的均匀分布。当服务员休息之后，如果仍没有顾客等候，则他将再休息一次。请基于 500 次模拟估计服务员在前 100 个小时内的平均休息时间。

4. 写出 6.4 节模型之情形 3 的更新方案。

5. 考虑一单服务员的排队模型，其中到达顾客服从一个非齐次泊松过程。如果服务员空闲，则到达的顾客即时得到服务；否则，就要排队等候。现假设每位顾客能排队等候的时间为分布 $F$ 的随机变量，否则就离开系统。以 $G$ 表示服务时间分布。如一位顾客在得到服务之前离开，则称损失一位顾客。现假设我们希望估计时间 $T$ 内损失的平均顾客数。请定义相应变量和事件，并给出更新步骤。

6. 在习题 5 中，假设到达过程是一参数为 5 的泊松过程，$F$ 为 (0,5) 上的均匀分布；$G$ 是一参数为 4 的指数分布。基于 500 次模拟估计时间 100 内损失的平均顾客数（假设先到的顾客先得到服务）。

7. 重复习题 6，但此时假设每当服务员完成一次服务后，不是为排队时间最长的顾客服务，而是为最着急的顾客服务。也就是说，假设现有两位顾客排队等候，且他们最多能等候的时间分别为 $t_1$ 和 $t_2$（即如果 $t_1 < t_2$，则服务员先为第一位服务；否则就先为第二位服务。你认为这种服务方式会增加还是减少的平均损失顾客数？

8. 对于 6.4 节中的模型，假设 $G_1$ 是参数为 4 的指数分布，$G_2$ 是参数为 3 的指数分布，到达顾客服从参数为 6 的泊松过程。编写模拟程序，产生前 1000 位到达顾客的相应数据，并由此估计：
   (a) 这些顾客花在系统中的平均时间;
   (b) 由服务员 1 服务的顾客比例;
   (c) 再做一次模拟，并由此回答 (a) 和 (b)，且比较两次模拟的结果。

9. 假设在 6.4 节的两个服务员并联排队系统中，共有两队在分别等候两个服务员的服务，且新来的顾客排在最短的那支队伍等候。如果两支队伍一样长或两个服务员均空闲，则新来的顾客等候服务员 1 的服务。
   (a) 适当选取分析此模型的变量和事件，并给出更新步骤。
   如假设与习题 8 有相同的分布和参数，则求：
   (b) 前 1000 位顾客花在此系统中的平均时间。
   (c) 得到服务员 1 服务的顾客比例。
   在运行本程序前，你期望 (b) 和 (c) 的答案比习题 8 中的相应答案大还是小？

10. 在习题 9 中假设新来的顾客以概率 $p$ 等候服务员 1 的服务且独立于其他变量。
    (a) 适当选取分析此模型的变量和事件，并给出更新步骤。
    (b) 应用习题 9 中的参数且取 $p$ 为习题 9 中问题 (c) 的答案，模拟此系统并估计在习题 9 中之问题 (b) 中定义的量。你期望本问题的答案比习题 9 得到的答案大还是小？

11. 假设向某保险公司的索赔客户服从参数为每天 10 个的泊松过程，索赔额是一均值为 $\$ 1000$ 的指数随机变量。此保险公司以每天 $\$ 11\ 000$ 的常数连续不断地收得保费。假设此公司的初始资金为 $\$ 25\ 000$ 元，利用统计模拟估计此公司在前 365 天的资金始终为正的概率。

12. 对于 6.6 节中的模型，假定已知此公司的资金在时间 $T$ 前变负。现我们感兴趣的是它在何时资金变负及其资金缺口量。请解释如何利用给定的统计模拟方法得到相关的数据。

13. 对于 6.7 节中的维修模型：
    (a) 编写模拟此模型的程序。
    (b) 利用此程序估计当 $n=4,s=3,F(x)=1-e^{-x},G(x)=1-e^{-2x}$ 时的崩溃时间。

14. 假设在 6.7 节中的模型中，维修工厂有两名服务员，且他们维修机器的时间均为分布 $G$ 的随机变量。请画出此系统的流程图。

15. 在撞击试验系统中，撞击服从参数为每小时 1 个的泊松过程，且每次撞击均给系统带来一定的损害。假设损害是独立的，是密度函数为

    $$f(x) = xe^{-x}, x > 0$$

    的随机变量（也假设它与撞击发生的时间独立），且损害以指数 $\alpha$ 消退，即一个初始损害为 $x$ 的撞击，当其发生 $s$ 时间后，其残余损害为 $xe^{-\alpha s}$。另外，假设损害是可以累加的，即，如果有两次撞击，其初始损害分别为 $x_1,x_2$，发生时间分别为 $t_1$ 和 $t_2$，则时间 $t$ 的损害为 $\sum_{i=1}^2 x_i e^{-\alpha(t-t_i)}$。当总的损害超过某固定值 $C$ 时，此系统瘫痪。
    (a) 假设我们感兴趣的是利用统计模拟来估计系统瘫痪的平均时间。定义此模型的事件和变量，并给出如何模拟此模型的流程图。
    (b) 编写能生成 $k$ 次模拟结果的程序。
    (c) 通过比较程序输出和手工计算结果，验证此程序。
    (d) 取 $\alpha = 0.5, C = 5, k = 1000$，运行此程序并利用其输出估计此系统瘫痪的平均时间。

16. 假设到达某通讯装置的信号服从参数为每小时 2 个的泊松分布，且此装置共有三个频道。当三个频道均设占用时，信号可进入任一频道；但当三个频道均被占用时，此信号将丢失。一个信号联系一个频道的时间是一随机变量，且依赖于信号到达时的天气状况。特别地，如果信号到达时的天气状况"良好"，则此信号的处理时间为一分布函数为

    $$F(x) = x, 0 < x < 1$$

    的随机变量；如果信号到达时的天气"不好"，则其处理时间为一分布函数为

    $$F(x) = x^3, 0 < x < 1$$

    的随机变量。假设开始时天气良好，之后在良好和不好之间周期变化，其中良好和不好天气的长度分别为 2 小时和 1 小时（这就是说，在时间 5，天气由良好转为不好）。
    假设我们感兴趣的是时间 $T = 100$ 时丢失信号的分布。
    (a) 定义事件和变量以便我们能利用离散事件方法。
    (b) 写出上述方法的流程图。
    (c) 编写上述方法的程序。
    (d) 通过比较程序输出和手工计算结果，验证上述程序。
    (e) 运行上述程序并估计前 100 个小时内丢失信号的平均数。

17. 假设某股票的当前价是 100，且某人拥有一张期权，此期权允许他在后面 20 天的任何一天以 100 的价格购买此股票。利用 6.8 节中参数 $\mu = -0.05, \sigma = 0.3$ 的模型及策略，通过模拟方法估计此期权的期望收益。

### 参考文献

[1] Banks, J., and J. Carson, Discrete-Event System Simulation. Prentice-Hall, New Jersey, 1984.

[2] Clymer, J., Systems Analysis Using Simulation and Markov Models. Prentice-Hall, New Jersey, 1990.

[3] Gottfried, B., Elements of Stochastic Process Simulation. Prentice-Hall, New Jersey, 1984.

[4] Law, A. M., and W. D. Kelton, Simulation Modelling and Analysis. 3rd ed. McGraw-Hill, New York, 1997.

[5] Mitrani, I., Simulation Techniques for Discrete Event Systems. Cambridge University Press, Cambridge, U. K., 1982.

[6] Peterson, R., and E. Silver, Decision Systems for Inventory Management and Production Planning. Wiley, New York, 1979.

[7] Pritsker, A., and C. Pedgen, Introduction to Simulation and SLAM. Halsted Press, New York, 1979.

[8] Shannon, R. E., Systems Simulation: The Art and Science. Prentice-Hall, New Jersey, 1975.

[9] Solomon, S. L., Simulation of Waiting Line Systems. Prentice-Hall, New Jersey, 1983.