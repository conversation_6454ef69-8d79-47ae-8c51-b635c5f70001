## 第5章 树与二叉树

**【考纲内容】**
(一)树的基本概念
(二)二叉树
二叉树的定义及其主要特征;二叉树的顺序存储结构和链式存储结构;二叉树的遍历;线索二叉树的基本概念和构造
(三)树、森林
树的存储结构;森林与二叉树的转换;树和森林的遍历
(四)树与二叉树的应用
哈夫曼(Huffman)树和哈夫曼编码;并查集及其应用;堆及其应用

**【知识框架】**
*   树形结构
    *   二叉树
        *   概念：定义、存储结构
        *   操作：三种遍历
        *   应用：
            *   线索二叉树
            *   并查集
            *   哈夫曼树
    *   树和森林
        *   概念：定义、存储结构
        *   操作：
            *   与二叉树的转换
            *   遍历
        *   应用：并查集

**【复习提示】**
本章内容多以选择题或综合题的形式考查,但统考也会出涉及树遍历相关的算法题。树和二叉树的性质、遍历操作、转换、存储结构和操作特性等,满二叉树、完全二叉树、线索二叉树、哈夫曼树的定义和性质,都是选择题必然会涉及的内容。

### 5.1 树的基本概念

#### 5.1.1 树的定义
树是$n(n \ge 0)$个结点的有限集。当$n=0$时,称为空树。在任意一棵非空树中应满足:
1) 有且仅有一个特定的称为根的结点。
2) 当$n>1$时,其余结点可分为$m(m>0)$个互不相交的有限集$T_1, T_2, \dots, T_m$,其中每个集合本身又是一棵树,并且称为根的子树。
显然,树的定义是递归的,即在树的定义中又用到了其自身,树是一种递归的数据结构。树作为一种逻辑结构,同时也是一种分层结构,具有以下两个特点:
1) 树的根结点没有前驱,除根结点外的所有结点有且只有一个前驱。
2) 树中所有结点都可以有零个或多个后继。
树适用于表示具有层次结构的数据。树中的某个结点(除根结点外)最多只和上一层的一个结点(其父结点)有直接关系,根结点没有直接上层结点,因此在$n$个结点的树中有$n-1$条边。而树中每个结点与其下一层的零个或多个结点(其孩子结点)都有直接关系。

#### 5.1.2 基本术语
下面结合图5.1中的树来说明一些基本术语和概念。



1) **祖先、子孙、双亲、孩子、兄弟和堂兄弟。**
考虑结点K,从根A到结点K的唯一路径上的所有其他结点,称为结点K的祖先。如结点B是结点K的祖先,而K是B的子孙,结点B的子孙包括E, F, K, L。路径上最接近结点K的结点E称为K的双亲,而K为E的孩子。根A是树中唯一没有双亲的结点。有相同双亲的结点称为兄弟,如结点K和结点L有相同的双亲E,即K和L为兄弟。双亲在同一层的结点互为堂兄弟,结点G与E, F, H, I, J互为堂兄弟。
2) **结点的层次、深度和高度。**
结点的层次从树根开始定义,根结点为第1层,它的孩子为第2层,以此类推。结点的深度就是结点所在的层次。树的高度(或深度)是树中结点的最大层数。结点的高度是以该结点为根的子树的高度。图5.1中树的高度为4。
3) **结点的度和树的度。**
树中一个结点的孩子个数称为该结点的度,树中结点的最大度数称为树的度。如结点B的度为2,结点D的度为3,树的度为3。
4) **分支结点和叶结点。**
度大于0的结点称为分支结点(也称非终端结点);度为0(没有孩子结点)的结点称为叶结点(也称终端结点)。在分支结点中,每个结点的分支数就是该结点的度。
5) **有序树和无序树。**
树中结点的各子树从左到右是有次序的,不能互换,称该树为有序树,否则称为无序树。假设图5.1为有序树,若将子结点位置互换,则变成一棵不同的树。
6) **路径和路径长度。**
树中两个结点之间的路径是由这两个结点之间所经过的结点序列构成的,而路径长度是路径上所经过的边的个数。
**注意**
因为树中的分支是有向的,即从双亲指向孩子,所以树中的路径是从上向下的,同一双亲的两个孩子之间不存在路径。

7) **森林。**
**命题追踪** 森林中树的数量、边数和结点数的关系(2016)
森林是$m(m \ge 0)$棵互不相交的树的集合。森林的概念与树的概念十分相近,因为只要把树的根结点删去就成了森林。反之,只要给$m$棵独立的树加上一个结点,并把这$m$棵树作为该结点的子树,则森林就变成了树。

**注意**
上述概念无须刻意记忆,根据实例理解即可。考研时不大可能直接考查概念,而都是结合具体的题目考查。做题时,遇到不熟悉的概念可以翻书,练习得多自然就记住了。

#### 5.1.3 树的性质
树具有如下最基本的性质:
**命题追踪** 树中结点数和度数的关系的应用(2010、2016)

1) 树的结点数$n$等于所有结点的度数之和加1。
结点的度是指该结点的孩子数量,每个结点与其每个孩子都由唯一的边相连,因此树中所有结点的度数之和等于树中的边数之和。树中的结点(除根外)都有唯一的双亲,因此结点数$n$等于边数之和加1,即所有结点的度数之和加1。
2) 度为$m$的树中第$i$层上至多有$m^{i-1}$个结点($i \ge 1$)。
第1层至多有1个结点(根结点),第2层至多有$m$个结点,第3层至多有$m^2$个结点,以此类推。使用数学归纳法可推出第$i$层至多有$m^{i-1}$个结点。
3) 高度为$h$的$m$叉树至多有$(m^h-1)/(m-1)$个结点。
当各层结点数达到最大时,树中至多有$1+m+m^2+\dots+m^{h-1} = (m^h-1)/(m-1)$个结点。
**命题追踪** 指定结点数的三叉树的最小高度分析(2022)
4) 度为$m$、具有$n$个结点的树的最小高度$h$为$\lceil \log_m(n(m-1)+1) \rceil$。
为使树的高度最小,在前$h-1$层中,每层的结点数都要达到最大,前$h-1$层最多有$(m^{h-1}-1)/(m-1)$个结点,前$h$层最多有$(m^h-1)/(m-1)$个结点。因此$(m^{h-1}-1)/(m-1)<n \le (m^h-1)/(m-1)$,即$h-1 < \log_m(n(m-1)+1) \le h$,解得$h_{min}=\lceil \log_m(n(m-1)+1) \rceil$。
5) 度为$m$、具有$n$个结点的树的最大高度$h$为$n-m+1$。
树的度为$m$,因此至少有一个结点有$m$个孩子,它们处于同一层。为使树的高度最大,其他层可仅有一个结点,因此最大高度(层数)为$n-m+1$。由此,也可逆推出高度为$h$、度为$m$的树至少有$h+m-1$个结点。

#### 5.1.4 本节试题精选
一、单项选择题
01. 树最适合用来表示( )的数据。
A. 有序
B. 无序
C. 任意元素之间具有多种联系
D. 元素之间具有分支层次关系

02. 一棵有$n$个结点的树的所有结点的度数之和为( )。
A. $n-1$
B. $n$
C. $n+1$
D. $2n$

03. 树的路径长度是从树根到每个结点的路径长度的( )。
A. 总和
B. 最小值
C. 最大值
D. 平均值

04. 对于一棵具有$n$个结点、度为4的树来说,( )。
A. 树的高度至多是$n-3$
B. 树的高度至多是$n-4$
C. 第$i$层上至多有$4(i-1)$个结点
D. 至少在某一层上正好有4个结点

05. 度为4、高度为$h$的树,( )。
A. 至少有$h+3$个结点
B. 至多有$4h-1$个结点
C. 至多有$4h$个结点
D. 至少有$h+4$个结点

06. 假定一棵度为3的树中,结点数为50,则其最小高度为( )。
A. 3
B. 4
C. 5
D. 6

07. 设有一棵度为3的树,其中度为3的结点数$n_3=2$,度为2的结点数$n_2=1$,叶结点数$n_0=6$,则该树的结点总数为( )。
A. 12
B. 9
C. 10
D. $\ge 9$的任意整数

08. 设一棵$m$叉树中有$N_1$个度数为1的结点,$N_2$个度数为2的结点$\dots N_m$个度数为$m$的结点,则该树中共有( )个叶结点。
A. $\sum_{i=1}^{m} (i-1)N_i$
B. $\sum_{i=1}^{m} N_i$
C. $\sum_{i=2}^{m} (i-1)N_i$
D. $\sum_{i=2}^{m} (i-1)N_i+1$

09. 【2010统考真题】在一棵度为4的树T中,若有20个度为4的结点,10个度为3的结点,1个度为2的结点,10个度为1的结点,则树T的叶结点个数是( )。
A. 41
B. 82
C. 113
D. 122

10. 【2016统考真题】若森林F有15条边、25个结点,则F包含树的个数是( )。
A. 8
B. 9
C. 10
D. 11

二、综合应用题
01. 含有$n$个结点的三叉树的最小高度是多少?
02. 已知一棵度为4的树中,度为0,1,2,3的结点数分别为14,4,3,2,求该树的结点总数$n$和度为4的结点个数,并给出推导过程。
03. 已知一棵度为$m$的树中,有$n_1$个度为1的结点,有$n_2$个度为2的结点$\dots$有$n_m$个度为$m$的结点,问该树有多少个叶结点?

#### 5.1.5 答案与解析
一、单项选择题
01. D
树是一种分层结构,它特别适合组织那些具有分支层次关系的数据。

02. A
除根结点外,其他每个结点都是某个结点的孩子,因此树中所有结点的度数加1等于结点数,即所有结点的度数之和等于总结点数减1。这是一个重要的结论,做题时经常用到。

03. A
树的路径长度是指树根到每个结点的路径长的总和,根到每个结点的路径长度的最大值应是树的高度减1。注意与哈夫曼树的带权路径长度相区别。

04. A
要使得具有$n$个结点、度为4的树的高度最大,就要使得每层的结点数尽可能少,类似下图所示的树,除最后一层外,每层的结点数是1,最终该树的高度为$n-3$。树的度为4只能说明存在某结点正好(也最多)有4个孩子结点,选项D错误。

05. A
要使得度为4、高度为$h$的树的总结点数最少,需要满足以下两个条件:
① 至少有一个结点有4个分支。
② 每层的结点数目尽可能少。
情况类似下图所示的树,结点个数为$h+3$。
要使得度为4、高度为$h$的树的总结点数最多,应使每个非叶结点的度均为4,即满树,总结点个数最多为$1+4+4^2+\dots+4^{h-1}$。
对于上面的两题,应画出草图来求解,就能一目了然。

06. C
要求满足条件的树,那么该树是一棵完全三叉树。在度为3的完全三叉树中,第1层有1个结点,第2层有$3^1=3$个结点,第3层有$3^2=9$个结点,第4层有$3^3=27$个结点,因此结点数之和为$1+3+9+27=40$,第5层的结点数$=50-40=10$个,因此最小高度为5。

07. D
总结点数$n = n_0+n_1+n_2+n_3=6+n_1+1+2=n_1+9$,总度数$=n-1=n_1+2n_2+3n_3=n_1+2+6=n_1+8$,根据题目条件无法得出$n$的具体值,只能证明$n$是一个大于或等于9的任意整数。画出满足题目条件的树,可以是如下图所示的一棵树,该树中无法确定$n$的具体数量。

08. D
设叶结点数为$N_0$,总结点数为$N$,则$N=N_1+2N_2+3N_3+\dots+mN_m+1$,又因为$N=N_0+N_1+N_2+N_3+\dots+N_m$,所以$N_0 = N_2+2N_3+\dots+(m-1)N_m+1 = \sum_{i=2}^{m}(i-1)N_i+1$。

09. B
设树中度为$i(i=0,1,2,3,4)$的结点数分别为$n_i$,树中结点总数为$n$,则$n=$分支数+1,而分支数又等于树中各结点的度之和,即$n=1+n_1+2n_2+3n_3+4n_4=n_0+n_1+n_2+n_3+n_4$。依题意,$n_1+2n_2+3n_3+4n_4=10+2+30+80=122$,$n_1+n_2+n_3+n_4=10+1+10+20=41$,可得出$n_0=82$,即树T的叶结点的个数是82。

10. C
**解法1:** 树有一个重要性质,即在$n$个结点的树中有$n-1$条边,“那么对于每棵树,其结点数比边数多1”。本题森林中的结点数比边数多10($25-15=10$),显然共有10棵树。
**解法2:** 仔细分析后发现此题也是考查图的性质:生成树和生成森林。对于图的生成树有一个重要的性质,即图中顶点数若为$n$,则其生成树含有$n-1$条边。对比解法1中树的性质,不难发现两种解法都用到了性质“树中结点数比边数多1”,后面的分析如解法1。

二、综合应用题
01.【解答】
要求含有$n$个结点的三叉树的最小高度,那么满足条件的一定是一棵完全三叉树,设含有$n$个结点的完全三叉树的高度为$h$,第$h$层至少有1个结点,至多有$3^{h-1}$个结点。则有
$1+3^1+3^2+\dots+3^{h-2}<n \le 1+3^1+3^2+\dots+3^{h-2}+3^{h-1}$
即$(3^{h-1}-1)/2<n \le (3^h-1)/2$,得$3^{h-1}<2n+1 \le 3^h$,即$h-1 < \log_3(2n+1) \le h$,$h \ge \log_3(2n+1)$。因为$h$只能为正整数,$h=\lceil \log_3(2n+1) \rceil$,所以这种三叉树的最小高度是$\lceil \log_3(2n+1) \rceil$。

02.【解答】
设树中度为$i(i=0,1,2,3,4)$的结点数为$n_i$,则结点总数$n=n_0+n_1+n_2+n_3+n_4$,即$n=23+n_4$,根据“树中所有结点的度数加1等于结点数”的结论,有$n=0+n_1+2n_2+3n_3+4n_4+1$,即有$n=17+4n_4$。
综合两式得$n_4=2$,$n=25$。所以该树的结点总数为25,度为4的结点个数为2。

03.【解答】
树中的结点数等于所有结点的度数加1,因此有$n=\sum_{i=0}^m in_i+1=n_1+2n_2+3n_3+\dots+mn_m+1$。
又有$n=n_0+n_1+n_2+\dots+n_m$,所以
$n_0=(n_1+2n_2+3n_3+\dots+mn_m+1)-(n_1+n_2+\dots+n_m)$
$=n_2+2n_3+\dots+(m-1)n_m+1=1+\sum_{i=2}^m(i-1)n_i$

**注意**
综合以上几题,常用于求解树结点与度之间关系的有:
① 总结点数$=n_0+n_1+n_2+\dots+n_m$。
② 总分支数$=1n_1+2n_2+\dots+mn_m$(度为$m$的结点引出$m$条分支)。
③ 总结点数=总分支数+1。
这类题目常在选择题中出现,读者对以上关系应当熟练掌握并灵活应用。

### 5.2 二叉树的概念

#### 5.2.1 二叉树的定义及其主要特性
1. **二叉树的定义**
二叉树是一种特殊的树形结构,其特点是每个结点至多只有两棵子树(二叉树中不存在度大于2的结点),并且二叉树的子树有左右之分,其次序不能任意颠倒。
与树相似,二叉树也以递归的形式定义。二叉树是$n(n \ge 0)$个结点的有限集合:
① 或者为空二叉树,即$n=0$。
② 或者由一个根结点和两个互不相交的被称为根的左子树和右子树组成。左子树和右子树又分别是一棵二叉树。
二叉树是有序树,若将其左、右子树颠倒,则成为另一棵不同的二叉树。即使树中结点只有一棵子树,也要区分它是左子树还是右子树。二叉树的5种基本形态如图5.2所示。


(a)空二叉树 (b)只有根结点 (c)只有左子树 (d)左右子树都有 (e)只有右子树

二叉树与度为2的有序树的区别:
① 度为2的树至少有3个结点,而二叉树可以为空。
② 度为2的有序树的孩子的左右次序是相对于另一个孩子而言的,若某个结点只有一个孩子,则这个孩子就无须区分其左右次序,而二叉树无论其孩子数是否为2,均需确定其左右次序,即二叉树的结点次序不是相对于另一结点而言的,而是确定的。
2. **几种特殊的二叉树**
1) **满二叉树。**一棵高度为$h$,且有$2^h-1$个结点的二叉树称为满二叉树,即二叉树中的每层都含有最多的结点,如图5.3(a)所示。满二叉树的叶结点都集中在二叉树的最下一层,并且除叶结点之外的每个结点度数均为2。
可以对满二叉树按层序编号:约定编号从根结点(根结点编号为1)起,自上而下,自左向右。这样,每个结点对应一个编号,对于编号为$i$的结点,若有双亲,则其双亲为$\lfloor i/2 \rfloor$,若有左孩子,则左孩子为$2i$;若有右孩子,则右孩子为$2i+1$。