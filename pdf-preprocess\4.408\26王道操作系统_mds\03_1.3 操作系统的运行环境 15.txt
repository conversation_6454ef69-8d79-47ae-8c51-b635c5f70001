1) CPU在100~150ms时间段内空闲,利用率为250/300=83.3%。
2) 进程A为无等待现象。
3) 进程B为有等待现象,发生在0~50ms和180~200ms时间段。

### 1.3 操作系统的运行环境

### 1.3.1 处理器运行模式Ⓡ

在计算机系统中,通常CPU执行两种不同性质的程序:一种是操作系统内核程序;另一种是用户自编程序(系统外层的应用程序,简称应用程序)。对操作系统而言,这两种程序的作用不同,前者是后者的管理者,因此“管理程序”(内核程序)要执行一些特权指令,而“被管理程序”(用户自编程序)出于安全考虑不能执行这些特权指令。

**命题追踪** 特权指令和非特权指令的特点 (2022)

1) **特权指令**,是指不允许用户直接使用的指令,如I/O指令、关中断指令、内存清零指令,存取用于内存保护的寄存器、修改程序状态字寄存器等的指令。
2) **非特权指令**,是指允许用户直接使用的指令,它不能直接访问系统中的软硬件资源,仅限于访问用户的地址空间,这也是为了防止用户程序对系统造成破坏。

**命题追踪** 内核态执行的指令分析 (2021)

**命题追踪** 用户态发生或执行的事件分析 (2011、2012、2014)

在具体实现上,将CPU的运行模式划分为用户态(目态)和内核态(也称管态、核心态)。可以理解为CPU内部有一个小开关,当小开关为0时,CPU处于内核态,此时CPU可以执行特权指令,切换到用户态的指令也是特权指令。当小开关为1时,CPU处于用户态,此时CPU只能执行非特权指令。应用程序运行在用户态,操作系统内核程序运行在内核态。应用程序向操作系统请求服务时通过使用访管指令,访管指令是在用户态执行的,因此是非特权指令。

在软件工程思想和结构化程序设计方法影响下诞生的现代操作系统,几乎都是分层式的结构。操作系统的各项功能分别被设置在不同的层次上。一些与硬件关联较紧密的模块,如时钟管理、中断处理、设备驱动等处于最低层。其次是运行频率较高的程序,如进程管理、存储器管理和设备管理等。这两部分内容构成了操作系统的内核。这部分内容的指令运行在内核态。

内核是计算机上配置的底层软件,它管理着系统的各种资源,可视为连接应用程序和硬件的一座桥梁,大多数操作系统的内核包括4方面的内容。

**1. 时钟管理**

**命题追踪** 时钟中断服务的内容 (2018)

在计算机的各种部件中,时钟是关键设备。时钟的第一功能是计时,操作系统需要通过时钟管理,向用户提供标准的系统时间。另外,通过时钟中断的管理,可以实现进程的切换。例如,在分时操作系统中采用时间片轮转调度,在实时系统中按截止时间控制运行,在批处

---
① 先弄清楚一个问题,即计算机“指令”和高级语言“代码”是不同的。通常所说的“编写代码”指的是用高级语言(如C、Java等)来编写程序。但CPU看不懂这些高级语言程序的含义,为了让这些程序能顺利执行,就需要将它们“翻译”成CPU能懂的机器语言,即一条条“指令”。所谓执行程序,其实就是CPU根据一条条指令来执行一个个具体的操作。
理系统中通过时钟管理来衡量一个作业的运行程度等。因此,系统管理的方方面面无不依赖于时钟。

**2. 中断机制**

**命题追踪** 中断机制在多道程序设计中的作用 (2016)

引入中断技术的初衷是提高多道程序运行时的CPU利用率,使CPU可以在I/O操作期间执行其他指令。后来逐步得到发展,形成了多种类型,成为操作系统各项操作的基础。例如,键盘或鼠标信息的输入、进程的管理和调度、系统功能的调用、设备驱动、文件访问等,无不依赖于中断机制。可以说,现代操作系统是靠中断驱动的软件。

中断机制中,只有一小部分功能属于内核,它们负责保护和恢复中断现场的信息,转移控制权到相关的处理程序。这样可以减少中断的处理时间,提高系统的并行处理能力。

**3. 原语**

按层次结构设计的操作系统,底层必然是一些可被调用的公用小程序,它们各自完成一个规定的操作,通常将具有这些特点的程序称为原语(Atomic Operation)。它们的特点如下:
1) 处于操作系统的底层,是最接近硬件的部分。
2) 这些程序的运行具有原子性,其操作只能一气呵成(出于系统安全性和便于管理考虑)。
3) 这些程序的运行时间都较短,而且调用频繁。
定义原语的直接方法是关中断,让其所有动作不可分割地完成后再打开中断。系统中的设备驱动、CPU切换、进程通信等功能中的部分操作都可定义为原语,使它们成为内核的组成部分。

**4. 系统控制的数据结构及处理**

系统中用来登记状态信息的数据结构很多,如作业控制块、进程控制块(PCB)、设备控制块、各类链表、消息队列、缓冲区、空闲区登记表、内存分配表等。为了实现有效的管理,系统需要一些基本的操作,常见的操作有以下3种:
1) **进程管理**。进程状态管理、进程调度和分派、创建与撤销进程控制块等。
2) **存储器管理**。存储器的空间分配和回收、内存信息保护程序、代码对换程序等。
3) **设备管理**。缓冲区管理、设备分配和回收等。
可见,内核态指令实际上包括系统调用类指令和一些针对时钟、中断和原语的操作指令。

### 1.3.2 中断和异常的概念Ⓡ

**命题追踪** 用户态切换到内核态的事件分析 (2013、2015)

在操作系统中引入内核态和用户态这两种工作状态后,就需要考虑这两种状态之间如何切换。操作系统内核工作在内核态,而用户程序工作在用户态。系统不允许用户程序实现内核态的功能,而它们又必须使用这些功能。因此,需要在内核态建立一些“门”,以便实现从用户态进入内核态。在实际操作系统中,CPU运行用户程序时唯一能进入这些“门”的途径就是通过中断或异常。发生中断或异常时,运行用户态的CPU会立即进入内核态,这是通过硬件实现的(例如,用一个特殊寄存器的一位来表示CPU所处的工作状态,0表示内核态,1表示用户态。若要进入内核态,则只需将该位置0即可)。中断是操作系统中非常重要的一个概念,对一个运行在计算机上的实用操作系统而言,缺少了中断机制,将是不可想象的。原因是,操作系统的发展过程大

---
① 本节的内容较为精简,建议结合《计算机组成原理考研复习指导》中相应小节进行学习。
体上就是一个想方设法不断提高资源利用率的过程,而提高资源利用率就需要在程序并未使用某种资源时,将它对那种资源的占有权释放,而这一行为就需要通过中断实现。

**1. 中断和异常的定义**

**命题追踪** 可能引发中断或异常的指令分析 (2013、2015)

**中断**(Interruption)也称外中断,是指来自CPU执行指令外部的事件,通常用于信息输入/输出(见第5章),如设备发出的I/O结束中断,表示设备输入/输出处理已经完成。时钟中断,表示一个固定的时间片已到,让处理机处理计时、启动定时运行的任务等。

**异常**(Exception)也称内中断,是指来自CPU执行指令内部的事件,如程序的非法操作码、地址越界、运算溢出、虚存系统的缺页及专门的陷入指令等引起的事件。异常不能被屏蔽,一旦出现,就应立即处理。关于内中断和外中断的联系与区别如图1.2所示。

**图1.2 内中断和外中断的联系与区别**
*   中断
    *   内部异常
        *   故障(Fault) ﹣ 软件中断
        *   自陷(Trap) ┘
        *   终止(Abort) ─ 硬件中断
    *   外部中断(硬件)
        *   可屏蔽中断 INTR
        *   不可屏蔽中断 NMI

**2. 中断和异常的分类**

**命题追踪** 中断和异常的分类 (2016)

外中断可分为可屏蔽中断和不可屏蔽中断。可屏蔽中断是指通过INTR线发出的中断请求,通过改变屏蔽字可以实现多重中断,从而使得中断处理更加灵活。不可屏蔽中断是指通过NMI线发出的中断请求,通常是紧急的硬件故障,如电源掉电等。此外,异常也是不能被屏蔽的。

异常可分为故障、自陷和终止。**故障**(Fault)通常是由指令执行引起的异常,如非法操作码、缺页故障、除数为0、运算溢出等。**自陷**(Trap,也称陷入)是一种事先安排的“异常”事件,用于在用户态下调用操作系统内核程序,如条件陷阱指令、系统调用指令等。**终止**(Abort)是指出现了使得CPU无法继续执行的硬件故障,如控制器出错、存储器校验错等。故障异常和自陷异常属于软件中断(程序性异常),终止异常和外部中断属于硬件中断。

**3. 中断和异常的处理过程**

**命题追踪** 中断和异常的处理过程 (2015、2020、2024)

中断和异常处理过程的大致描述如下:当CPU在执行用户程序的第i条指令时检测到一个异常事件,或在执行第i条指令后发现一个中断请求信号,则CPU打断当前的用户程序,然后转到相应的中断或异常处理程序去执行。若中断或异常处理程序能够解决相应的问题,则在中断或异常处理程序的最后,CPU通过执行中断或异常返回指令,回到被打断的用户程序的第i条指令或第i+1条指令继续执行;若中断或异常处理程序发现是不可恢复的致命错误,则终止用户程序。通常情况下,对中断和异常的具体处理过程由操作系统(和驱动程序)完成。

**命题追踪** 中断处理和子程序调用的比较 (2012)

注意区分中断处理和子程序调用:①中断处理程序与被中断的当前程序是相互独立的,它们之间没有确定的关系;子程序与主程序是同一程序的两部分,它们属于主从关系。②通常中断的产生都是随机的;而子程序调用是通过调用指令(CALL)引起的,是由程序设计者事先安排的。③调用子程序的过程完全属于软件处理过程;而中断处理的过程还需要有专门的硬件电路才能实现。④中断处理程序的入口地址可由硬件向量法产生向量地址,再由向量地址找到入口地址;子程序的入口地址是由CALL指令中的地址码给出的。⑤调用中断处理程序和子程序都需要保护程序计数器(PC)的内容,前者由中断隐指令完成,后者由CALL指令完成(执行CALL指令时,处理器先将当前的PC值压入栈,再将PC设置为被调用子程序的入口地址)。⑥响应中断时,需对同时检测到的多个中断请求进行裁决,而调用子程序时没有这种操作。

### 1.3.3 系统调用

**命题追踪** 系统调用的定义及性质 (2019、2021、2024)

系统调用是操作系统提供给应用程序(程序员)使用的接口,可视为一种供应用程序调用的特殊的公共子程序。系统中的各种共享资源都由操作系统统一掌管,因此凡是与共享资源有关的操作(如存储分配、I/O传输及文件管理等),都必须通过系统调用方式向操作系统提出服务请求,由操作系统代为完成,并将处理结果返回给应用程序。这样,就可以保证系统的稳定性和安全性,防止用户进行非法操作。通常,一个操作系统提供的系统调用命令有几十条乃至上百条之多,每个系统调用都有唯一的系统调用号。这些系统调用按功能大致可分为如下几类。

**命题追踪** 系统调用的功能 (2021)

*   **设备管理**。完成设备的请求或释放,以及设备启动等功能。
*   **文件管理**。完成文件的读、写、创建及删除等功能。
*   **进程控制**。完成进程的创建、撤销、阻塞及唤醒等功能。
*   **进程通信**。完成进程之间的消息传递或信号传递等功能。
*   **内存管理**。完成内存的分配、回收以及获取作业占用内存区大小和起始地址等功能。

显然,系统调用相关功能涉及系统资源管理、进程管理之类的操作,对整个系统的影响非常大,因此系统调用的处理需要由操作系统内核程序负责完成,要运行在内核态。

**命题追踪** 系统调用的处理过程及CPU状态的变化 (2012、2017、2023)

**命题追踪** 系统调用处理过程中操作系统负责的任务 (2022)

下面分析系统调用的处理过程:第一步,用户程序首先将系统调用号和所需的参数压入堆栈;接着,调用实际的调用指令,然后执行一个陷入指令,将CPU状态从用户态转为内核态,再后由硬件和操作系统内核程序保护被中断进程的现场,将程序计数器(PC)、程序状态字(PSW)及通用寄存器内容等压入堆栈。第二步,分析系统调用类型,转入相应的系统调用处理子程序。在系统中配置了一张系统调用入口表,表中的每个表项都对应一个系统调用,根据系统调用号可以找到该系统调用处理子程序的入口地址。第三步,在系统调用处理子程序执行结束后,恢复被中断的或设置新进程的CPU现场,然后返回被中断进程或新进程,继续往下执行。

可以这么理解,用户程序执行“陷入指令”,相当于将CPU的使用权主动交给操作系统内核程序(CPU状态会从用户态进入内核态),之后操作系统内核程序再对系统调用请求做出相应处理。处理完成后,操作系统内核程序又会将CPU的使用权还给用户程序(CPU状态会从内核态回到用户态)。这么设计的目的是:用户程序不能直接执行对系统影响非常大的操作,必须通过系统调用的方式请求操作系统代为执行,以便保证系统的稳定性和安全性。
这样,操作系统的运行环境可以理解为:用户通过操作系统运行上层程序(如系统提供的命令解释程序或用户自编程序),而这个上层程序的运行依赖于操作系统的底层管理程序提供服务支持,当需要管理程序服务时,系统通过硬件中断机制进入内核态,运行管理程序;也可能是程序运行出现异常情况,被动地需要管理程序的服务,此时则通过异常处理进入内核态。管理程序运行结束时,退出中断或异常处理程序,返回到用户程序的断点处继续执行。系统调用的执行过程如图1.3所示。

**图1.3 系统调用的执行过程**
(一个流程图，描述了从用户态到内核态再返回用户态的过程)
*   **用户程序 (用户态, 模式位=1)**: 
    1.  执行用户程序
    2.  调用系统调用
*   **切换到内核**: 陷入 (模式位=0)
*   **内核 (内核态, 模式位=0)**:
    1.  执行系统调用
*   **切换回用户态**: 返回 (模式位=1)
*   **用户程序 (用户态, 模式位=1)**:
    1.  从系统调用返回

在操作系统这一层面上,我们关心的是系统内核态和用户态的软件实现与切换,对于硬件层面的具体理解,可以结合“计算机组成原理”课程中有关中断的内容进行学习。

下面列举一些由用户态转向内核态的例子:
1) 用户程序要求操作系统的服务,即系统调用。
2) 发生一次中断。
3) 用户程序中产生了一个错误状态。
4) 用户程序中企图执行一条特权指令。
从内核态转向用户态由一条指令实现,这条指令也是特权命令,一般是中断返回指令。

### 1.3.4 本节习题精选

**单项选择题**

**01.** 下列关于操作系统的说法中,错误的是( )。
I. 在通用操作系统管理下的计算机上运行程序,需要向操作系统预订运行时间
II. 在通用操作系统管理下的计算机上运行程序,需要确定起始地址,并从这个地址开始执行
III. 操作系统需要提供高级程序设计语言的编译器
IV. 管理计算机系统资源是操作系统关心的主要问题
A. I 
B. I、III
C. II、III
D. I、II、III、IV

**02.** 下列说法中,正确的是( )。
I. 批处理的主要缺点是需要大量内存
II. 当计算机提供了内核态和用户态时,输入/输出指令必须在内核态下执行
III. 操作系统中采用多道程序设计技术的最主要原因是提高CPU和外部设备的可靠性
IV. 操作系统中,通道技术是一种硬件技术
A. I、II
B. I、III
C. II、IV
D. II、III、IV

**03.** 下列关于系统调用的说法中,正确的是( )。
I. 用户程序使用系统调用命令,该命令经过编译后形成若干参数和陷入指令
II. 用户程序使用系统调用命令,该命令经过编译后形成若干参数和屏蔽中断指令
III. 用户程序创建一个新进程,需使用操作系统提供的系统调用接口
IV. 当操作系统完成用户请求的系统调用功能后,应使CPU从内核态转到用户态
A. I、III
B. III、IV
C. I、III、IV
D. II、III、IV

**04.** ( )是操作系统必须提供的功能。
A. 图形用户界面(GUI)
B. 为进程提供系统调用命令
C. 中断处理
D. 编译源程序

**05.** CPU执行的指令被分为两类,其中有一类称为特权指令,它只允许( )使用。
A. 管理员
B. 联机用户
C. 目标程序
D. 操作系统

**06.** 在中断发生后,中断处理的程序属于( )。
A. 用户程序
B. 可能是用户程序,也可能是OS程序
C. 操作系统程序
D. 单独的程序,即不是用户程序也不是OS程序

**07.** CPU的状态分为用户态和内核态,从用户态转换到内核态的唯一途径是( )。
A. 修改程序状态字指令
B. 中断屏蔽
C. 中断
D. 中断处理程序

**08.** 下列指令中,可以在用户态执行的是( )。
I. 置时钟指令 II. 停机指令 III. 存数指令 IV. 寄存器清零指令
A. I、IV
B. III、IV
C. II、III、IV
D. II、III

**09.** 下列指令中,必须在内核态执行的是( )。
I. 陷入指令
II. 系统调用指令
III. 开中断指令
IV. 转移指令
V. 中断屏蔽字设置指令
A. I、II、III、IV
B. II、III
C. II、III、V
D. I、IV

**10.** 下列程序中,必须在内核态执行的是( )。
I. 磁盘调度程序 II. 中断处理程序 III. 设备驱动程序 IV. 操作系统初始化程序
A. I、II、III、IV
B. I、II、III
C. I、II、IV
D. II、III

**11.** 当CPU处于内核态时,它可以执行的指令是( )。
A. 只有特权指令
B. 只有非特权指令
C. 只有访管指令
D. 除访管指令之外的全部指令

**12.** 下列中断事件中,能引起外部中断的事件是( )。
I. 时钟中断
II. 访管中断
III. 缺页中断
A. I
B. III
C. I和II
D. II和III

**13.** 下列关于库函数和系统调用的说法中,不正确的是( )。
A. 库函数运行在用户态,系统调用运行在内核态
B. 使用库函数时开销较小,使用系统调用时开销较大
C. 库函数不方便替换,系统调用通常很方便被替换
D. 库函数可以很方便地调试,而系统调用很麻烦

**14.** 下列关于系统调用和一般过程调用的说法中,正确的是( )。
A. 两者都需要将当前CPU中的PSW和PC的值压栈,以保存现场信息
B. 系统调用的被调用过程一定运行在内核态
C. 一般过程调用的被调用过程一定运行在用户态
D. 两者的调用过程与被调用过程一定都运行在用户态

**15.** 用户在程序中试图读某文件的第100个逻辑块,使用操作系统提供的( )接口。
A. 系统调用
B. 键盘命令
C. 原语
D. 图形用户接口

**16.** 【2011统考真题】下列选项中,在用户态执行的是( )。
A. 命令解释程序
B. 缺页处理程序
C. 进程调度程序
D. 时钟中断处理程序

**17.** 【2012统考真题】下列选项中,不可能在用户态发生的事件是( )。
A. 系统调用
B. 外部中断
C. 进程切换
D. 缺页

**18.** 【2012统考真题】中断处理和子程序调用都需要压栈,以便保护现场,中断处理一定会保存而子程序调用不需要保存其内容的是( )。
A. 程序计数器
B. 程序状态字寄存器
C. 通用数据寄存器
D. 通用地址寄存器

**19.** 【2013统考真题】下列选项中,会导致用户进程从用户态切换到内核态的操作是( )。
Ⅰ. 整数除以零 II. `sin()`函数调用 III. read系统调用
A. 仅I、II
B. 仅I、III
C. 仅II、III
D. I、II和III

**20.** 【2014统考真题】下列指令中,不能在用户态执行的是( )。
A. trap指令
B. 跳转指令
C. 压栈指令
D. 关中断指令

**21.** 【2015统考真题】处理外部中断时,应该由操作系统保存的是( )。
A. 程序计数器(PC)的内容
B. 通用寄存器的内容
C. 快表(TLB)中的内容
D. Cache中的内容

**22.** 【2015统考真题】假定下列指令已装入指令寄存器,则执行时不可能导致CPU从用户态变为内核态(系统态)的是( )。
A. `DIV R0, R1` ; (R0)/(R1)→R0
B. `INT n` ; 产生软中断
C. `NOT R0` ; 寄存器R0的内容取非
D. `MOV R0, addr` ; 把地址addr处的内存数据放入寄存器 R0

**23.** 【2016统考真题】异常是指令执行过程中在处理器内部发生的特殊事件,中断是来自处理器外部的请求事件。下列关于中断或异常情况的叙述中,错误的是( )。
A. “访存时缺页”属于中断
B. “整数除以0”属于异常
C. “DMA传送结束”属于中断
D. “存储保护错”属于异常

**24.** 【2017统考真题】执行系统调用的过程包括如下主要操作:
①返回用户态 ②执行陷入(trap)指令
③传递系统调用参数 ④执行相应的服务程序
正确的执行顺序是( )。
A. ②→③→①→④
B. ②→④→③→①
C. ③→②→④→①
D. ③→④→②→①

**25.** 【2018统考真题】定时器产生时钟中断后,由时钟中断服务程序更新的部分内容是( )。
I. 内核中时钟变量的值
II. 当前进程占用CPU的时间
III. 当前进程在时间片内的剩余执行时间
A. 仅I、II
B. 仅II、III
C. 仅I、III
D. I、II、III
**26.** 【2019统考真题】下列关于系统调用的叙述中,正确的是( )。
I. 在执行系统调用服务程序的过程中,CPU处于内核态
II. 操作系统通过提供系统调用避免用户程序直接访问外设
III. 不同的操作系统为应用程序提供了统一的系统调用接口
IV. 系统调用是操作系统内核为应用程序提供服务的接口
A. 仅I、IV
B. 仅II、III
C. 仅I、II、IV
D. 仅I、III、IV

**27.** 【2020统考真题】下列与中断相关的操作中,由操作系统完成的是( )。
I. 保存被中断程序的中断点
II. 提供中断服务
III. 初始化中断向量表
IV. 保存中断屏蔽字
A. 仅I、II
B. 仅I、II、IV
C. 仅III、IV
D. 仅II、III、IV

**28.** 【2021统考真题】下列指令中,只能在内核态执行的是( )。
A. trap指令
B. I/O指令
C. 数据传送指令
D. 设置断点指令

**29.** 【2021统考真题】下列选项中,通过系统调用完成的操作是( )。
A. 页置换
B. 进程调度
C. 创建新进程
D. 生成随机整数

**30.** 【2022统考真题】下列关于CPU模式的叙述中,正确的是( )。
A. CPU处于用户态时只能执行特权指令
B. CPU处于内核态时只能执行特权指令
C. CPU处于用户态时只能执行非特权指令
D. CPU处于内核态时只能执行非特权指令

**31.** 【2022统考真题】执行系统调用的过程涉及下列操作,其中由操作系统完成的是( )。
I. 保存断点和程序状态字
II. 保存通用寄存器的内容
III. 执行系统调用服务例程
IV. 将CPU模式改为内核态
A. 仅I、III
B. 仅II、III
C. 仅II、IV
D. 仅II、III、IV

**32.** 【2023统考真题】在操作系统内核中,中断向量表适合采用的数据结构是( )。
A. 数组
B. 队列
C. 单向链表
D. 双向链表

**33.** 【2024统考真题】下列关于中断、异常和系统调用的叙述中,错误的是( )。
A. 中断或异常发生时,CPU处于内核态
B. 每个系统调用都有对应的内核服务例程
C. 中断处理程序开始执行时,CPU处于内核态
D. 系统添加新类型设备时,需要注册相应的中断服务例程

### 1.3.5 答案与解析

**单项选择题**

**01. B**
选项I错误:通用操作系统使用时间片轮转调度算法,用户运行程序并不需要预先预订运行时间。选项II正确:操作系统执行程序时,必须从起始地址开始执行。选项III错误:编译器是操作系统的上层软件,不是操作系统需要提供的功能。选项IV正确:操作系统是计算机资源的的管理者,管理计算机系统资源是操作系统关心的主要问题。

**02. C**
选项I错误:批处理的主要缺点是缺少交互性。批处理系统的主要缺点是常考点,读者对此要非常敏感。选项II正确:输入/输出指令属于特权指令,只能由操作系统使用,因此必须在内核态下执行。选项III错误:多道性是为了提高系统利用率和吞吐量而提出的。选项IV正确:I/O通道实际上是一种特殊的处理器,它具有执行I/O指令的能力,并通过执行通道程序来控制I/O操作。

**03. C**
系统调用需要触发陷入指令,如基于x86的Linux系统,该指令为`int 0x80`或`sysenter`,选项I正确。程序设计无法形成屏蔽中断指令,选项II错误。用户程序通过系统调用进行进程控制,选项III正确。执行系统调用时CPU状态要从用户态转到内核态,这是通过中断来实现的,当系统调用返回后,继续执行用户程序,同时CPU状态也从内核态转到用户态,选项IV正确。

**04. C**
中断是操作系统必须提供的功能,因为计算机的各种错误都需要中断处理,内核态与用户态切换也需要中断处理。

**05. D**
内核可以执行CPU能执行的任何指令,用户程序只能执行除特权指令之外的指令。因此特权指令只能由内核即操作系统使用。

**06. C**
中断处理程序是OS专门为处理中断事件而设计的程序。中断发生时,若被中断的是用户程序,则CPU将从用户态转入内核态,在内核态下进行中断的处理;若被中断的是低级中断,则CPU仍保持在内核态。被中断的程序可能是用户程序,但中断的处理程序一定是OS程序。

**07. C**
CPU通过程序状态字寄存器中的某个位来标志当前的状态,但是修改程序状态字的指令本身就属于特权指令,不能在用户态下执行。当CPU执行到一条访管指令或陷阱指令时,会引起访管中断或陷阱中断,CPU会保存断点和其他上下文环境,然后切换到内核态。也就是说,从用户态转换到内核态,不是通过指令来修改CPU的状态标志位的,而是由CPU在中断时自动完成的。中断处理程序一般在内核态执行,因此无法完成“转换成内核态”这一任务。

**08. B**
直接管理系统资源的指令(如设置时钟、启动/关闭硬件设备、切换进程、设置中断等)、系统状态修改指令(如修改中断向量表、切换CPU的运行模式等)、系统控制指令(如停机指令、重启指令等),都属于特权指令,必须在内核态执行。普通的数据处理指令、流程控制指令、读操作指令都不会影响系统的安全和整体状态,可以在用户态执行。

**09. C**
用户程序通过陷入指令让CPU模式从用户态转入内核态,因此在用户态执行。虽然调用的发生可能在用户态(注意区分调用和执行),但是系统调用指令必然在内核态执行。开中断指令和关中断指令都属于特权指令,能够影响到系统的运行,必须在内核态执行。无论是分支跳转指令还是无条件转移指令,都属于普通的程序流程控制指令,在用户态执行。中断屏蔽字寄存器属于中断控制器的一部分,而中断控制器是属于I/O接口的,因此设置中断屏蔽字寄存器的操作就相当于对I/O接口中的I/O端口进行设置,属于I/O指令,必须在内核态执行。

**10. A**
磁盘调度程序控制磁头的运行,和硬件密切相关,运行在内核态。中断处理程序在中断发生后由操作系统内核调用,运行在内核态。设备驱动程序用于和硬件设备进行通信,运行在内核态。操作系统初始化程序负责初始化系统资源、设置环境、加载驱动程序等,运行在内核态。

**11. D**
访管指令(trap指令)是一条在用户态下执行的指令。用户程序在用户态下要求操作系统内核提供服务,就有意识地使用访管指令来引起访管中断,从而将控制权交给内核。在内核态,CPU可以执行除访管指令之外的任何指令。

**12. A**
外部中断是由CPU外部的事件引起的,如I/O设备的请求、时钟信号等。内部中断(也称异常)是由CPU内部的事件引起的,如访管指令(trap指令)、缺页异常等。

**13. C**
库函数是指被封装在库文件中的可复用的代码块,运行在用户态;而系统调用是面向硬件的,运行在内核态,是操作系统为用户提供的接口。库函数可以很方便地调试,而系统调用很麻烦,因为它运行在内核态。库函数可以很方便地替换,而系统调用通常不可替换。库函数属于过程调用,开销较小;系统调用需要在用户空间和内核空间中进行上下文切换,开销较大。

**14. B**
系统调用需要保存PSW和PC的值,一般过程调用只需保存PC的值,选项A错误。系统调用的被调用过程是操作系统中的程序,是系统级程序,必须运行在内核态,选项B正确。一般过程调用的被调用程序与调用程序运行在同一个状态,可能是系统态,也可能是用户态,选项C和D错误。

**15. A**
操作系统通过系统调用向用户程序提供服务,文件I/O需要在内核态运行。

**16. A**
缺页处理和时钟中断都属于中断,在内核态执行;进程调度是操作系统内核进程,无须用户干预,在内核态执行;命令解释程序属于命令接口,是面对用户的,在用户态执行。

**17. C**
本题的关键是对“在用户态发生”(注意与“在用户态执行”区分)的理解。对于选项A,系统调用是操作系统提供给用户程序的接口,系统调用发生在用户态,被调用程序在内核态下执行。对于选项B,外部中断是用户态到内核态的“门”,也发生在用户态,在内核态完成中断处理过程。对于选项C,进程切换属于系统调用执行过程中的事件,只能发生在内核态;对于选项D,缺页产生后,在用户态发生缺页中断,然后进入内核态执行缺页中断服务程序。

**18. B**
子程序调用不改变程序的状态,因为子程序调用是编译器可控的流程,而中断不是。下面以程序`if(a==b)`为例加以说明。该程序通常包含一条测试指令,以及一条根据标志位决定是否需要跳转来调用子程序的指令。编译器不在这两条指令中间插入任何子程序调用代码,因此标志位不变,但中断却随时可能发生,导致标志位改变。具体地说,执行`if(a==b)`时,会进行a-b操作,并生成相应的标志位,进而根据标志位来判断是否发生跳转。假设刚好在生成相应的标志位后发生了中断,若不保存PSW的内容,则后续根据标志位来进行跳转的流程就可能发生错误。但是,若进行了子程序调用,则说明已经根据a-b的标志位进行了跳转,此时PSW的内容已无意义而无须保存。综上所述,中断处理和子程序调用都有可能使PSW的内容发生变化,但中断处理程序执行完返回后,可能需要用到PSW原来的内容,子程序执行完返回后,一定不需要用到PSW原来的内容,因此选择选项B。选项A都会保存,选项C和D不一定会保存。

**19. B**
整数除以零是非法操作,因此CPU会检测到异常,并切换到内核态进行异常处理。`sin()`函数是C语言中的普通运算类库函数,且不会引发系统调用,在用户态执行。在用户态触发read系统调用时,会通过trap指令陷入内核态,由CPU执行相应的系统调用服务例程。

**20. D**
trap指令、跳转指令和压栈指令均可以在用户态执行,其中trap指令负责由用户态转换为内核态。关中断指令为特权指令,必须在内核态才能执行。注意,在操作系统中,关中断指令是权限非常大的指令,因为中断是现代操作系统正常运行的核心保障之一,能把它关掉,说明执行这条指令的一定是权限非常大的机构(内核态)。

**21. B**
外部中断处理过程,PC值由中断隐指令自动保存,而通用寄存器内容由操作系统保存。快表(TLB)和Cache中的内容在外部中断处理过程中通常无须保存,直接置有效位为0即可。

**22. C**
部分指令可能出现异常,从而转到内核态。指令A有除零异常的可能。指令B为软中断指令,用于触发一个中断并跳转到相应的中断处理程序,n表示中断向量号,使用软中断可以在用户态和内核态之间切换,以实现系统调用。指令D有缺页异常的可能。指令C不会发生异常。

**23. A**
中断是指来自CPU执行指令以外事件,如设备发出的I/O结束中断,表示设备输入/输出已完成,希望处理机能够向设备发出下一个输入/输出请求,同时让完成输入/输出后的程序继续运行。异常也称内中断,指源自CPU执行指令内部的事件。选项A错误。

**24. C**
执行系统调用的过程:正在运行的进程先传递系统调用参数,然后由陷入(trap)指令负责将CPU模式从用户态转换为内核态,并将返回地址压入堆栈以备后用,接下来CPU执行相应的内核态服务程序,最后返回用户态。

**25. D**
时钟中断的主要工作是处理和时间有关的信息及决定是否执行调度程序。和时间有关的所有信息包括系统时间、进程的时间片、延时、使用CPU的时间、各种定时器。

**26. C**
用户可以在用户态调用操作系统的服务,但执行具体的系统调用服务程序是处于内核态的,选项I正确;设备管理属于操作系统的职能之一,包括对输入/输出设备的分配、初始化、维护等,用户程序需要通过系统调用使用操作系统的设备管理服务,选项II正确;操作系统不同,底层逻辑、实现方式均不相同,为应用程序提供的系统调用接口也不同,选项III错误;系统调用是用户在程序中调用操作系统提供的子功能,选项IV正确。

**27. D**
当CPU检测到中断信号后,由硬件自动保存被中断程序的断点[程序计数器(PC)和程序状态字寄存器(PSW)],选项I错误。之后,硬件找到该中断信号对应的中断向量,中断向量指明中断服务程序入口地址(各中断向量统一存放在中断向量表中,该表由操作系统初始化,选项III正确)。接下来开始执行中断服务程序,保存中断屏蔽字、保存各通用寄存器的值,并提供与中断信号对应的中断服务,中断服务程序属于操作系统内核,选项II和IV正确。

**28. B**
在内核态下,CPU可执行任何指令,在用户态下CPU只能执行非特权指令,而特权指令只能在内核态下执行。常见的特权指令有:①有关对I/O设备操作的指令;②有关访问程序状态的指令;③存取特殊寄存器的指令;④其他指令。选项A、C和D都是提供给用户使用的指令,可以在用户态执行,只是可能使CPU从用户态切换到内核态。

**29. C**
系统调用是由用户进程发起的,请求操作系统的服务。对于选项A,当内存中的空闲页框不够时,操作系统会将某些页面调出,并将要访问的页面调入,这个过程完全由操作系统完成,不涉及系统调用。对于选项B,进程调度完全由操作系统完成,无法通过系统调用完成。对于选项C,创建新进程可以通过系统调用来完成,如Linux中通过fork系统调用来创建子进程。对于选项D,生成随机数是普通的函数调用,不涉及请求操作系统的服务,如C语言的`random()`函数。

**30. C**
CPU在用户态时只能执行非特权指令,在内核态时可以执行特权指令和非特权指令。

**31. B**
发生系统调用时,CPU通过执行软中断指令将CPU的运行状态从用户态切换到内核态,这个过程与中断和异常的响应过程相同,由硬件负责保存断点和程序状态字,并将CPU模式改为内核态。然后,执行操作系统内核的系统调用入口程序,该内核程序负责保存通用寄存器的内容,再调用执行特定的系统调用服务例程。综上,选项I、IV由硬件完成,选项II、III由操作系统完成。

**32. A**
本题考查了“计算机组成原理”的考点,且综合了“数据结构”的内容。中断向量表用于存放中断处理程序的入口地址,CPU通过查询得到中断类型号,然后据此计算可以得到对应中断服务程序的入口地址在中断向量表的位置,采用数组作为中断向量表的存储结构,可实现时间复杂度为$O(1)$的快速访问,从而提高中断处理的效率。

**33. A**
当中断或异常发生时,CPU既可能处于内核态,又可能处于用户态,具体取决于当时CPU正在处理的任务,选项A错误。不同的系统调用对应不同的内核服务例程,选项B正确。在中断响应阶段,若CPU处于用户态,则需要切换到内核态,因此在中断处理阶段,CPU一定处于内核态,选项C正确。设备种类繁多,计算机不可能事先准备好所有设备对应的中断服务例程(实际上属于设备驱动程序),因此当系统添加新类型的设备时,需要注册相应的中断服务例程。

### 1.4 操作系统结构

随着操作系统功能的不断增多和代码规模的不断扩大,提供合理的结构,对降低操作系统复杂度、提升操作系统安全与可靠性来说变得尤为重要。

**1. 分层法**

分层法是将操作系统分为若干层,底层(层0)为硬件,顶层(层N)为用户接口,每层只能调用紧邻它的低层的功能和服务(单向依赖)。分层的操作系统如图1.4所示。

**图1.4 分层的操作系统**
(一个分层的同心圆图)
*   最外层: 层N 用户接口
*   中间层: ...
*   中间层: 层1
*   最内层: 层0 硬件