### §6 定积分的近似计算

利用牛顿—莱布尼茨公式虽然可以精确地计算定积分的值,但它仅适用于被积函数的原函数能够求得的情形,如果这点办不到或者不容易办到,这就要考虑近似计算的方法,在定积分的很多应用问题中,被积函数甚至没有解析表达式(只是一条实验记录曲线,或者是一组离散的采样值),这时只能采用近似方法去计算相应的定积分。
其实,根据定积分的定义,每一个积分和都可看做是定积分的一个近似值,例如
$$
\int_a^b f(x)dx \approx \sum_{i=1}^n f(\xi_i)\Delta x_i(\text{或}\sum_{i=1}^n f(x_{i-1})\Delta x_i).
\ag{1}
$$
在几何意义上,这是用一系列小矩形面积来近似小曲边梯形面积的结果,所以把这个近似算法称为矩形法,不过,只有当积分区间被分割得很细很细时,矩形法才有一定的精确度。
如果在分割的每个小区间上采用一次或二次多项式来近似替代被积函数,那么可以期望获得比矩形法效果好得多的近似计算公式,下面的梯形法和抛物线法就是这一想法的产物。
一 梯形法
将积分区间$[a,b]$作$n$等分,分点依次为
$$
a = x_0 < x_1 < x_2 < \dots < x_n = b, \Delta x = \frac{b-a}{n}
$$
相应的被积函数值记为
$y_0, y_1, y_2, \dots, y_n (y_i=f(x_i), i=0,1,2,\dots,n)$。
并记曲线$y=f(x)$上相应的点为
$P_0, P_1, P_2, \dots, P_n (P_i(x_i, y_i), i=0,1,2,\dots,n)$。
将曲线上每一段弧$\widehat{P_{i-1}P_i}$用弦$P_{i-1}P_i$来替代,这使得每个小区间$[x_{i-1},x_i]$上的曲边梯形换成了真正的梯形(图 10-26),其面积为
$$
\frac{y_{i-1}+y_i}{2}\Delta x_i, i=1,2,\dots,n.
$$
于是,各个小梯形面积之和就是曲边梯形面积的近似值,即
$$
\int_a^b f(x)dx \approx \sum_{i=1}^n \frac{y_{i-1}+y_i}{2}\Delta x_i,
$$
亦即

---
$$
\int_a^b f(x)dx \approx \frac{b-a}{n}(\frac{y_0+y_n}{2} + y_1 + y_2 + \dots + y_{n-1}).
\ag{2}
$$
称此近似式为定积分的梯形法公式.

y|P_i|P_{i-1}
--|---|---
 |P_0| P_1
y_0|y_i|y_{i-1}
O|a=x_0|x_1|...|x_{i-1}|x_i|...|x_{n-1}|x_n=b|x
图10-26

y|P_2|P_{2i-2}
--|---|---
 |P_0|P_1|P_{2i}|P_{2n}
y_0|y_1|y_2|...|y_{2i-2}|y_{2i-1}|y_{2i}|...|y_{2n}
O|a=x_0|x_1|x_2|...|x_{2i-2}|x_{2i-1}|x_{2i}|...|x_{2n}=b|x
图10-27

二 抛物线法
由梯形法求定积分的近似值,当$y=f(x)$为凸曲线时偏大,为凹曲线时偏小.如果每段曲线改用与它的凸性相接近的抛物线来近似时,就可减少上述缺点,下面介绍抛物线法.
将积分区间$[a,b]$作$2n$等分(图 10-27),分点依次为
$$
a=x_0<x_1<x_2<\dots<x_{2n}=b, \Delta x_i=\frac{b-a}{2n}
$$
对应的被积函数值为
$y_0,y_1,y_2,\dots,y_{2n} (y_i=f(x_i), i=0,1,2,\dots,2n)$.
曲线$y=f(x)$上的相应点为
$P_0,P_1,P_2,\dots,P_{2n} (P_i(x_i,y_i), i=0,1,2,\dots,2n)$.
现把区间$[x_0,x_2]$上的曲线$y=f(x)$用通过三点
$P_0(x_0,y_0),P_1(x_1,y_1),P_2(x_2,y_2)$
的抛物线$p_1(x)=\alpha_1 x^2+\beta_1 x+\gamma_1$来近似替代,便有
$$
\begin{aligned}
\int_{x_0}^{x_2} f(x)dx &\approx \int_{x_0}^{x_2} p_1(x)dx = \int_{x_0}^{x_2}(\alpha_1 x^2 + \beta_1 x + \gamma_1)dx \\
&= \frac{\alpha_1}{3}(x_2^3-x_0^3) + \frac{\beta_1}{2}(x_2^2-x_0^2) + \gamma_1(x_2-x_0) \\
&= \frac{x_2-x_0}{6}[(\alpha_1 x_0^2 + \beta_1 x_0 + \gamma_1) + (\alpha_1 x_2^2 + \beta_1 x_2 + \gamma_1) + \\
&\quad \alpha_1(x_0+x_2)^2 + 2\beta_1(x_0+x_2)+4\gamma_1]
\end{aligned}
$$

---
$$
= \frac{x_2-x_0}{6}(y_0+y_2+4y_1) = \frac{b-a}{6n}(y_0+4y_1+y_2).
$$
最末第二步的得来是利用了$x_0+x_2=2x_1$.
同样地,在$[x_{2i-2},x_{2i}]$上用$p_i(x)=\alpha_i x^2+\beta_i x+\gamma_i$替代曲线$y=f(x)$,将得到
$$
\int_{x_{2i-2}}^{x_{2i}}f(x)dx \approx \int_{x_{2i-2}}^{x_{2i}}p_i(x)dx = \frac{b-a}{6n}(y_{2i-2}+4y_{2i-1}+y_{2i}).
$$
最后,按$i=1,2,\dots,n$把这些近似式相加,得到
$$
\int_a^b f(x)dx = \sum_{i=1}^n \int_{x_{2i-2}}^{x_{2i}}f(x)dx \approx \sum_{i=1}^n \frac{b-a}{6n}(y_{2i-2}+4y_{2i-1}+y_{2i}),
$$
即
$$
\int_a^b f(x)dx \approx \frac{b-a}{6n}[y_0+y_{2n}+4(y_1+y_3+\dots+y_{2n-1})+2(y_2+y_4+\dots+y_{2n-2})].
\ag{3}
$$
这就是抛物线法公式,也称为辛普森(Simpson)公式.
作为例子,我们计算定积分$\int_0^1\frac{dx}{1+x^2}$的近似值.
将区间$[0,1]$十等分,各分点上被积函数的值列表如下(取七位小数):

| $x_i$ | 0 | 0.1 | 0.2 | 0.3 | 0.4 | 0.5 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| $y_i$ | 1 | 0.990 099 0 | 0.961 538 5 | 0.917 431 2 | 0.862 069 0 | 0.800 000 0 |

| $x_i$ | 0.6 | 0.7 | 0.8 | 0.9 | 1 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| $y_i$ | 0.735 294 1 | 0.671 140 9 | 0.609 756 1 | 0.552 486 2 | 0.5 |

1)用矩形法公式(1)去计算:(取四位小数)
$$
\int_0^1 \frac{dx}{1+x^2} \approx \frac{1}{10}(y_0+y_1+\dots+y_9) = 0.809\ 9
$$
$$
(\text{或}\ \frac{1}{10}(y_1+y_2+\dots+y_{10})=0.760\ 0).
$$
2)用梯形法公式(2)去计算:(取四位小数)
$$
\int_0^1 \frac{dx}{1+x^2} \approx \frac{1}{10}(\frac{y_0+y_{10}}{2}+y_1+y_2+\dots+y_9)=0.785\ 0.
$$
3)用抛物线法公式(3)去计算:(取七位小数)
$$
\int_0^1 \frac{dx}{1+x^2} \approx \frac{1}{30}[y_0+y_{10}+4(y_1+y_3+\dots+y_9)+2(y_2+y_4+\dots+y_8)]
$$
$$
=0.785\ 398\ 2.
$$

---
用准确值①
$$
\int_0^1 \frac{dx}{1+x^2} = \text{arctg } 1 = \frac{\pi}{4}=0.785\ 398\ 16\dots
$$
与上述近似值相比较,矩形法的结果只有一位有效数字是准确的,梯形法的结果有三位有效数字是准确的,抛物线法的结果则有六位有效数字是准确的.可见公式(3)明显地优于公式(2),更优于公式(1).
关于定积分近似计算的误差估计,在“数值分析”一类课程中必有详述,这里不再讨论.

### 习题
1. 分别用梯形法和抛物线法近似计算$\int_1^2 \frac{dx}{x}$(将积分区间十等分).
2. 用抛物线法近似计算$\int_0^\pi \frac{\sin x}{x}dx$(分别将积分区间二等分、四等分、六等分).
3. 图10-28所示为河道某一截面图.试由测得数据用抛物线法求截面面积.

(Image of a river cross-section with measurements)
图10-28

4. 下表所列为夏季某一天每隔两小时测得的气温:

| 时间($t_i$) | 0 | 2 | 4 | 6 | 8 | 10 | 12 | 14 | 16 | 18 | 20 | 22 | 24 |
| :--- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- |
| 温度($C_i$) | 25.8 | 23.0 | 24.1 | 25.6 | 27.3 | 30.2 | 33.4 | 35.0 | 33.8 | 31.1 | 28.2 | 27.0 | 25.0 |

(1) 按积分平均$\frac{1}{b-a}\int_a^b f(t)dt$求这一天的平均气温,其中定积分值由三种近似法分别计算;
(2) 若按算术平均$\frac{1}{12}\sum_{i=1}^{12}C_i$或$\frac{1}{13}\sum_{i=0}^{12}C_i$求得平均气温,那么它们与矩形法积分平均和梯形法积分平均各有什么联系? 简述理由.

---
① 这里用一个很容易求得准确值的定积分作为近似计算的例子,主要的理由就是有准确值可以与近似值相比较,实际使用中不会有这样的事.

---
## 第十一章 反常积分

### §1 反常积分概念

一 问题提出
在讨论定积分时有两个最基本的限制:积分区间的有穷性和被积函数的有界性. 但在很多实际问题中往往需要突破这些限制,考虑无穷区间上的“积分”,或是无界函数的“积分”,这便是本章的主题.

**例 1** (第二宇宙速度问题) 在地球表面垂直发射火箭(图 11-1),要使火箭克服地球引力无限远离地球,试问初速度$v_0$至少要多大?
设地球半径为$R$,火箭质量为$m$,地面上的重力加速度为$g$. 按万有引力定律,在距地心$x(\ge R)$处火箭所受的引力为
$$
F = \frac{mgR^2}{x^2}.
$$
于是火箭从地面上升到距离地心为$r(>R)$处需做的功为
$$
\int_R^r \frac{mgR^2}{x^2}dx = mgR^2(\frac{1}{R}-\frac{1}{r}).
$$
当$r \to +\infty$时,其极限$mgR$就是火箭无限远离地球需作的功.我们很自然地会把这极限写作上限为$+\infty$的“积分”:
$$
\int_R^{+\infty} \frac{mgR^2}{x^2}dx = \lim_{r \to +\infty} \int_R^r \frac{mgR^2}{x^2}dx = mgR.
$$
最后,由机械能守恒定律可求得初速度$v_0$至少应使
$$
\frac{1}{2}mv_0^2 = mgR.
$$
用$g=9.81(\text{m/s}^2), R=6.371\times 10^6(\text{m})$代入,便得
$$
v_0 = \sqrt{2gR} \approx 11.2(\text{km/s}).
$$
□