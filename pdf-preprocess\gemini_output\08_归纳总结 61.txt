```c
}
    s=h->next;          //s 指向前半段的第一个数据结点,即插入点
    q=p->next;          //q 指向后半段的第一个数据结点
    p->next=NULL;
    while(q!=NULL) {    //将链表后半段的结点插入到指定位置
        r=q->next;      //r 指向后半段的下一个结点
        q->next=s->next;//将 q 所指结点插入到 s 所指结点之后
        s->next=q;
        s=q->next;      //s 指向前半段的下一个插入点
        q=r;
    }
}
```
3) 第一步找中间结点的时间复杂度为$O(n)$, 第二步逆置的时间复杂度为$O(n)$, 第三步合并链表的时间复杂度为$O(n)$, 所以该算法的时间复杂度为$O(n)$。

### 归纳总结

本章是算法设计题的重点考查章节,因为线性表的算法题的代码量一般都比较少,又具有一定的算法设计技巧,因此适合笔试考查。考研题中常以三段式的结构命题。
在给出题目背景和要求的情况下:
① 给出算法的基本设计思想。
② 采用C或C++语言描述算法,并给出注释。
③ 分析所设计算法的时间复杂度和空间复杂度。
算法具体的设计思想千变万化,难以从一而定。因此读者一定要勤加练习,反复咀嚼本章的练习题,采用多种方法进行设计并比较它们的复杂度,逐渐熟悉各类题型的思考角度和最佳思路。这里,编者列出几种常用的算法设计技巧,仅供参考:对于链表,经常采用的方法有头插法、尾插法、逆置法、归并法、双指针法等,对具体问题需要灵活变通;对于顺序表,因为可以直接存取,所以经常结合排序和查找的几种算法设计思路进行设计,如归并排序、二分查找等。

**注意**
对于算法设计题,若能写出数据结构类型的定义、正确的算法思想,则至少会给一半的分数;若能用伪代码写出,则自然更好;比较复杂的地方可以直接用文字表达。

### 思维拓展

一个长度为$n$的整型数组$A[1...n]$, 给定整数$x$, 设计一个时间复杂度不超过$O(n\log n)$的算法, 查找出这个数组中所有两两之和等于$x$的整数对(每个元素只输出一次)。

**提示**：本题若想到排序,则问题便迎刃而解。先用一种时间复杂度为$O(n\log_2 n)$的排序算法将$A[1...n]$从小到大排序,然后分别从数组的小端($i=1$)和大端($j=n$)开始查找: 若$A[i]+A[j]<x$, $i++$;若$A[i]+A[j]>x$, $j--$;否则输出$A[i]$、$A[j]$,然后$i++,j--$;直到$i>=j$时停止。
请读者思考本题是否有其他求解算法。