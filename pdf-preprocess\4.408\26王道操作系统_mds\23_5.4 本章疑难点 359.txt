### 5.4 本章疑难点

1. 为了增加设备分配的灵活性,成功率,可以如何改进?
可以从以下两方面对基本的设备分配程序加以改进:
1) 增加设备的独立性。进程使用逻辑设备名请求I/O。这样,系统首先从SDT 中找出第一个该类设备的DCT。若该设备忙,则又查找第二个该类设备的DCT。仅当所有该类设备都忙时,才将进程挂到该类设备的等待队列上;只要有一个该类设备可用,系统便进一步计算分配该设备的安全性。
2) 考虑多通路情况。为防止I/O 系统的“瓶颈”现象,通常采用多通路的I/O 系统结构。此时对控制器和通道的分配同样要经过几次反复,即若设备(控制器)所连接的第一个控制器(通道)忙时,则应查看其所连接的第二个控制器(通道),仅当所有控制器(通道)都忙时,此次的控制器(通道)分配才算失败,才将进程挂到控制器(通道)的等待队列上。而只要有一个控制器(通道)可用,系统便可将它分配给进程。
设备分配过程中,先后分别访问的数据结构为 SDT→DCT→COCT→CHCT。要成功分配一个设备,必须要:①设备可用;②控制器可用;③通道可用。所以,“设备分配,要过三关”。

2. 什么是用户缓冲区、内核缓冲区?
5.1.4 节中讨论过:“I/O操作完成后,系统将数据从内核复制到用户空间”,这里说的是“内核”其实是指内核缓冲区,“用户空间”是指用户缓冲区。
用户缓冲区是指当用户进程读文件时,通常先申请一块内存数组,称为Buffer,用来存放读取的数据。每次read调用,将读取的数据写入Buffer,之后程序都从 buffer 中获取数据,当buffer使用完后,再进行下一次调用,填充 buffer。可见,用户缓冲区的目的是减少系统调用次数,从而降低系统在用户态与内核态之间切换的开销。
内核也有自己的缓冲区。当用户进程从磁盘读取数据时,不直接读磁盘,而将内核缓冲区中的数据复制到用户缓冲区中。若内核缓冲区中没有数据,则内核请求从磁盘读取,然后将进程挂起,为其他进程服务,等到数据已读取到内核缓冲区中时,将内核缓冲区中的数据复制到用户进程的缓冲区,才通知进程(当然,I/O模型不同,处理的方式也不同)。当用户进程需要写数据时,数据可能不直接写入磁盘,而将数据写入内核缓冲区,时机适当时(如内核缓冲区的数据积累到一定量后),内核才将内核缓冲区的数据写入磁盘。可见,内核缓冲区是为了在操作系统级别提高磁盘I/O 效率,优化磁盘写操作。

## 参考文献
[1] 汤小丹,梁红兵,哲凤屏,等.计算机操作系统[M].4版.西安:西安电子科技大学出版社,2014.
[2] 李善平,操作系统学习指导和考试指导[M].杭州:浙江大学出版社,2004.
[3] Andrew S. Tanenbaum.现代操作系统[M].4版.北京:机械工业出版社,2017.
[4] Abraham Silberschatz, Peter B. Galvin, Greg Gagne.操作系统概念[M].北京:机械工业出版社,2018.
[5] William Stallings.操作系统:精髓与设计原理[M].第九版.北京:电子工业出版社,2020.
[6] 全国考研计算机大纲配套教材专家委员会,全国硕士研究生入学统一考试计算机学科专业基础综合考试大纲解析(2010版)[M].北京:高等教育出版社,2009.
[7] 李春葆,操作系统联考辅导教程(2011版)[M].北京:清华大学出版社,2010.
[8] 崔巍.2010考研计算机学科专业基础综合辅导讲义[M].北京:原子能出版社,2009.
[9] 翔高教育.计算机学科专业基础综合复习指南[M].上海:复旦大学出版社,2009.
[10] Randal E. Bryant.深入理解计算机系统[M].北京:机械工业出版社,2010.