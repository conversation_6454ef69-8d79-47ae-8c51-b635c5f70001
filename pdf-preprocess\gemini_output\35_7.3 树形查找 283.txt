## 第七章 查找
```c
bool findkey(int A[][], int n, int k) {
    int i=0,j=n-1;
    while(i<n&&j>=0) { //离开边界时查找结束
        if (A[i][j]==k) return true; //查找成功
        else if (A[i][j]>k) j--; //向左移动,在该行内寻找目标值
        else i++; //向下移动,查找下一个更大的元素
    }
    return false; //查找失败
}
```
3) 比较次数不超过$2n$次,时间复杂度为$O(n)$;空间复杂度为$O(1)$。

**07.【解答】**
1) 折半查找要求元素有序顺序存储,字符串默认按字典序排序(字典序是一种比较两个字符串大小的方法,它按字母顺序从左到右逐个比较对应的字符,若某一位可比较出大小,则不再继续比较后面的字符,如 abd<acd、abc<abcd 等),对本题来说 do<for<repeat<while。若各个元素的查找概率不同,折半查找的性能不一定优于顺序查找。
采用顺序查找时,元素按其查找概率的降序排列时查找长度最小。
采用顺序存储结构,数据元素按其查找概率降序排列。采用顺序查找方法。
查找成功时的平均查找长度$=0.35\times1+0.35\times2+0.15\times3+0.15\times4=2.1$。
此时,显然查找长度比折半查找的更短。
2) **答案1：**采用链式存储结构时,只能采用顺序查找,其性能和顺序表一样,类似于上题。数据元素按其查找概率降序排列,构成单链表。采用顺序查找方法。
查找成功时的平均查找长度$=0.35\times1+0.35\times2+0.15\times3+0.15\times4=2.1$。
**答案2：**还可以构造成二叉排序树的形式。采用二叉链表的存储结构,构造二叉排序树,元素的存储方式见下图。采用二叉排序树的查找方法。

[图示：两个标记为“二叉排序树1”和“二叉排序树2”的示意图]
*   二叉排序树1
*   二叉排序树2

查找成功时的平均查找长度$=0.15\times1+0.35\times2+0.35\times2+0.15\times3=2.0$。

### 7.3 树形查找

### 7.3.1 二叉排序树(BST)
构造一棵二叉排序树的目的并不是排序,而是提高查找、插入和删除关键字的速度,二叉排序树这种非线性结构也有利于插入和删除的实现。
1. **二叉排序树的定义**

**命题追踪** 二叉排序树的应用 (2013)

二叉排序树(也称二叉查找树)或者是一棵空树,或者是具有下列特性的二叉树:
1) 若左子树非空,则左子树上所有结点的值均小于根结点的值。
2) 若右子树非空,则右子树上所有结点的值均大于根结点的值。
3) 左、右子树也分别是一棵二叉排序树。

**命题追踪** 二叉排序树中结点值之间的关系 (2015、2018、2024)

根据二叉排序树的定义,左子树结点值<根结点值<右子树结点值,因此对二叉排序树进行中序遍历,可以得到一个递增的有序序列。例如,图 7.4 所示二叉排序树的中序遍历序列为 123468。
```
    6
   / \
  2   8
 / \
1   4
   /
  3
```
**图7.4 一棵二叉排序树**

2. **二叉排序树的查找**
二叉排序树的查找是从根结点开始,沿某个分支逐层向下比较的过程。若二叉排序树非空,先将给定值与根结点的关键字比较,若相等,则查找成功;若不等,若小于根结点的关键字,则在根结点的左子树上查找,否则在根结点的右子树上查找。这显然是一个递归的过程。
二叉排序树的非递归查找算法:
```c
BSTNode *BST_Search(BiTree T, ElemType key) {
    while (T!=NULL&&key!=T->data) { //若树空或等于根结点值,则结束循环
        if (key<T->data) T=T->lchild; //小于,则在左子树上查找
        else T=T->rchild; //大于,则在右子树上查找
    }
    return T;
}
```
**例**如,在图 7.4 中查找值为 4 的结点。首先 4 与根结点 6 比较。因为 4 小于 6,所以在根结点 6 的左子树中继续查找。因为 4 大于 2,所以在结点 2 的右子树中查找,查找成功。
同样,二叉排序树的查找也可用递归算法实现,递归算法比较简单,但执行效率较低。具体的代码实现,留给读者思考。

3. **二叉排序树的插入**
二叉排序树作为一种动态树表,其特点是树的结构通常不是一次生成的,而是在查找过程中,当树中不存在关键字值等于给定值的结点时再进行插入的。
插入结点的过程如下:若原二叉排序树为空,则直接插入;否则,若关键字$k$小于根结点值,则插入到左子树,若关键字$k$大于根结点值,则插入到右子树。新插入的结点一定是一个叶结点,且是查找失败时的查找路径上访问的最后一个结点的左孩子或右孩子。如图 7.5 所示在一棵二叉排序树中依次插入结点 28 和结点 58,虚线表示的边是其查找的路径。

[图示：(a) 插入28 (b) 插入58]
**图7.5 向二叉排序树中插入结点**

二叉排序树插入操作的算法描述如下:
```c
int BST_Insert(BiTree &T, KeyType k) {
    if (T==NULL) { //原树为空,新插入的记录为根结点
        T=(BiTree)malloc(sizeof(BSTNode));
        T->data=k;
        T->lchild=T->rchild=NULL;
        return 1; //返回1,插入成功
    }
    else if (k==T->data) //树中存在相同关键字的结点,插入失败
        return 0;
    else if (k<T->data) //插入T的左子树
        return BST_Insert(T->lchild,k);
    else //插入T的右子树
        return BST_Insert(T->rchild,k);
}
```
4. **二叉排序树的构造**

**命题追踪** 构造二叉排序树的过程 (2020)

从一棵空树出发,依次输入元素,将它们插入二叉排序树中的合适位置。设插入的关键字序列为{45, 24, 53, 45, 12, 24},则生成的二叉排序树如图 7.6 所示。
[图示：(a) 空树 (b) 插入45 (c) 插入24 (d) 插入53 (e) 插入12]
**图7.6 二叉排序树的构造过程**

构造二叉排序树的算法描述如下:
```c
void Creat_BST(BiTree &T, KeyType str[], int n) {
    T=NULL; //初始时T为空树
    int i=0;
    while (i<n) { //依次将每个关键字插入二叉排序树
        BST_Insert(T,str[i]);
        i++;
    }
}
```
5. **二叉排序树的删除**
在二叉排序树中删除一个结点时,不能把以该结点为根的子树上的结点都删除,必须先把被删除结点从存储二叉排序树的链表上摘下,将因删除结点而断开的二叉链表重新链接起来,同时确保二叉排序树的性质不会丢失。删除操作的实现过程按 3 种情况来处理:
① 若被删除结点$p$是叶结点,则直接删除,不会破坏二叉排序树的性质。
② 若结点$p$只有一棵左子树或右子树,则让$p$的子树成为$p$父结点的子树,替代$p$的位置。
③ 若结点$p$有左、右两棵子树,则令$p$的直接后继(或直接前驱)替代$p$,然后从二叉排序树中删去这个直接后继(或直接前驱),这样就转换成了第一或第二种情况。
图 7.7 显示了在 3 种情况下分别删除结点 45, 78, 78 的过程。

[图示：3种情况下的删除过程。(a)删除45 (b)删除78 (c)删除78]
**图7.7 3种情况下的删除过程**

**命题追踪** 二叉排序树中删除并插入某结点的分析 (2013)

**思考：**若在二叉排序树中删除并插入某结点,得到的二叉排序树是否和原来的相同?

6. **二叉排序树的查找效率分析**
二叉排序树的查找效率,主要取决于树的高度。若二叉排序树的左、右子树的高度之差的绝对值不超过 1 (平衡二叉树,下一节),它的平均查找长度和$O(\log_2n)$成正比。在最坏情况下,即构造二叉排序树的输入序列是有序的,则会形成一个只有右孩子的单支树,此时二叉排序树的性能显著变坏,树的高度为$n$,则其平均查找长度为$(n+1)/2$,如图 7.8(b)所示。
```
        (a)             (b)
           45             12
          /  \              \
        24    55             24
       / \    / \             \
      12 37  53 60             28
         / \      \             \
        28 40     70            37
                                  \
                                   40
                                     \
                                      45
                                        \
                                         53
                                           \
                                            55
                                              \
                                               60
                                                 \
                                                  70
```
**图7.8 相同关键字组成的不同二叉排序树**

在等概率情况下,图 7.8(a)查找成功的平均查找长度为
$$
ASL=(1+2\times2+3\times4+4\times3)/10 = 2.9
$$
而图 7.8(b)查找成功的平均查找长度为
$$
ASL = (1+2+3+4+5+6+7+8+9+10)/10 = 5.5
$$
从查找过程看,二叉排序树与二分查找相似。就平均时间性能而言,二叉排序树上的查找和二分查找差不多。但二分查找的判定树唯一,而二叉排序树的查找不唯一,相同的关键字其插入顺序不同可能生成不同的二叉排序树,如图 7.8 所示。
就维护表的有序性而言,二叉排序树无须移动结点,只需修改指针即可完成插入和删除操作,平均执行时间为$O(\log_2n)$。二分查找的对象是有序顺序表,若有插入和删除结点的操作,所花的代价是$O(n)$。当有序表是静态查找表时,宜用顺序表作为其存储结构,而采用二分查找实现其查找操作;若有序表是动态查找表,则应选择二叉排序树作为其逻辑结构。

### 7.3.2 平衡二叉树
1. **平衡二叉树的定义**
为了避免树的高度增长过快,降低二叉排序树的性能,规定在插入和删除结点时,要保证任意结点的左、右子树高度差的绝对值不超过 1,将这样的二叉树称为平衡二叉树(Balanced Binary Tree),也称 AVL 树。定义结点左子树与右子树的高度差为该结点的平衡因子,则平衡二叉树结点的平衡因子的值只可能是-1、0 或 1。

**命题追踪** 平衡二叉树的定义 (2009)

因此,平衡二叉树可定义为或是一棵空树,或是具有下列性质的二叉树:它的左子树和右子树都是平衡二叉树,且左子树和右子树的高度差的绝对值不超过 1。图 7.9(a)所示是平衡二叉树,图 7.9(b)所示是不平衡的二叉树。结点中的数字为该结点的平衡因子。
```
    (a)平衡二叉树        (b)不平衡的二叉树
          1                     2
         / \                   / \
        0   0                 1   0
       / \                   / \
      -1  0                 0   0
```
**图7.9 平衡二叉树和不平衡的二叉树**

2. **平衡二叉树的插入**
二叉排序树保证平衡的基本思想如下:每当在二叉排序树中插入(或删除)一个结点时,首先检查其插入路径上的结点是否因为此次操作而导致了不平衡。若导致了不平衡,则先找到插入路径上离插入结点最近的平衡因子的绝对值大于 1 的结点$A$,再对以$A$为根的子树,在保持二叉排序树特性的前提下,调整各结点的位置关系,使之重新达到平衡。

**命题追踪** 平衡二叉树中插入操作的特点 (2015)

**注**意,每次调整的对象都是最小不平衡子树,即以插入路径上离插入结点最近的平衡因子的绝对值大于 1 的结点作为根的子树。图 7.10 中的虚线框内为最小不平衡子树。

[图示：最小不平衡子树示意图，展示了插入51后，以75为根的子树变得不平衡，然后通过LR旋转恢复平衡的过程]
**图7.10 最小不平衡子树示意**

**命题追踪** 平衡二叉树的插入及调整操作的实例(2010、2019、2021)

平衡二叉树的插入过程的前半部分与二叉排序树相同,但在新结点插入后,若造成查找路径上的某个结点不再平衡,则需要做出相应的调整。可将调整的规律归纳为下列4种情况:
1) LL 平衡旋转(右单旋转)。由于在结点$A$的左孩子(L)的左子树(L)上插入了新结点,A 的平衡因子由 1 增至 2,导致以$A$为根的子树失去平衡,需要一次向右的旋转操作。将$A$的左孩子$B$向右上旋转代替$A$成为根结点,将$A$向右下旋转成为$B$的右孩子,而$B$的原右子树则作为$A$的左子树。如图 7.11 所示,结点旁的数值代表结点的平衡因子,而用方块表示相应结点的子树,下方数值代表该子树的高度。

[图示：(a) 插入结点前 (b) 插入结点导致不平衡 (c) LL旋转(右单旋转)]
**图7.11 LL 平衡旋转**

2) RR 平衡旋转(左单旋转)。由于在结点$A$的右孩子(R)的右子树(R)上插入了新结点,A 的平衡因子由-1 减至-2,导致以$A$为根的子树失去平衡,需要一次向左的旋转操作。将$A$的右孩子$B$向左上旋转代替$A$成为根结点,将$A$向左下旋转成为$B$的左孩子,而$B$的原左子树则作为$A$的右子树,如图 7.12 所示。

[图示：(a) 插入结点前 (b) 插入结点导致不平衡 (c) RR旋转(左单旋转)]
**图7.12 RR 平衡旋转**

3) LR 平衡旋转(先左后右双旋转)。由于在结点$A$的左孩子(L)的右子树(R)上插入新结点,$A$的平衡因子由 1 增至 2,导致以$A$为根的子树失去平衡,需要进行两次旋转操作,先左旋转后右旋转。先将$A$的左孩子$B$的右子树的根结点$C$向左上旋转提升到$B$的位置,然后把$C$向右上旋转提升到$A$的位置,如图 7.13 所示。

[图示：(a) 插入结点前 (b) 插入结点导致不平衡 (c) LR旋转(双旋转)]
**图7.13 LR 平衡旋转**

4) RL 平衡旋转(先右后左双旋转)。由于在结点$A$的右孩子(R)的左子树(L)上插入新结点,$A$的平衡因子由-1 减至-2,导致以$A$为根的子树失去平衡,需要进行两次旋转操作,先右旋转后左旋转。先将$A$的右孩子$B$的左子树的根结点$C$向右上旋转提升到$B$的位置,然后把$C$向左上旋转提升到$A$的位置,如图 7.14 所示。

[图示：(a) 插入结点前 (b) 插入结点导致不平衡 (c) RL旋转(双旋转)]
**图7.14 RL 平衡旋转**

**注意**
LR 和 RL 旋转时,新结点究竟是插入$C$的左子树还是插入$C$的右子树不影响旋转过程,而图 7.13 和图 7.14 中以插入$C$的左子树中为例。

**命题追踪** 构造平衡二叉树的过程 (2013)

以关键字序列{15, 3, 7, 10, 9, 8}构造一棵平衡二叉树的过程为例,图 7.15(d)插入 7 后导致不平衡,最小不平衡子树的根为 15,插入位置为其左孩子的右子树,所以执行 LR 旋转,先左后右双旋转,调整后的结果如图 7.15(e)所示。图 7.15(g)插入 9 后导致不平衡,最小不平衡子树的根为15,插入位置为其左孩子的左子树,所以执行 LL 旋转,右单旋转,调整后的结果如图 7.15(h)所示。图 7.15(i)插入 8 后导致不平衡,最小不平衡子树的根为 7,插入位置为其右孩子的左子树,所以执行 RL 旋转,先右后左双旋转,调整后的结果如图 7.15(j)所示。

3. **平衡二叉树的删除**
与平衡二叉树的插入操作类似,以删除结点$w$为例来说明平衡二叉树删除操作的步骤:
1) 用二叉排序树的方法对结点$w$执行删除操作。
2) 若导致了不平衡,则从结点$w$开始向上回溯,找到第一个不平衡的结点$z$(最小不平衡子树);$y$为结点$z$的高度最高的孩子;$x$是结点$y$的高度最高的孩子。

[图示：(a)空树 (b)插入15 (c)插入3 (d)插入7 (e)LR旋转 (f)插入10 (g)插入9 (h)LL旋转 (i)插入8 (j)RL旋转]
**图7.15 平衡二叉树的生成过程**

3) 然后对以$z$为根的子树进行平衡调整,其中$x$、$y$和$z$可能的位置有 4 种情况:
*   $y$是$z$的左孩子,$x$是$y$的左孩子(LL,右单旋转);
*   $y$是$z$的左孩子,$x$是$y$的右孩子(LR,先左后右双旋转);
*   $y$是$z$的右孩子,$x$是$y$的右孩子(RR,左单旋转);
*   $y$是$z$的右孩子,$x$是$y$的左孩子(RL,先右后左双旋转)。

这四种情况与插入操作的调整方式一样。不同之处在于,插入操作仅需要对以$z$为根的子树进行平衡调整;而删除操作就不一样,先对以$z$为根的子树进行平衡调整,若调整后子树的高度减 1,则可能需要对$z$的祖先结点进行平衡调整,甚至回溯到根结点(导致树高减 1)。
以删除图 7.16(a)的结点 32 为例,由于 32 为叶结点,直接删除即可,向上回溯找到第一个不平衡结点 44 ($z$),$z$的高度最高的孩子结点为 78 ($y$),$y$的高度最高的孩子结点为 50 ($x$),满足RL 情况,先右后左双旋转,调整后的结果如图 7.16(c)所示。

[图示：(a) 删除 32 前 (b) 右旋 (c) 左旋]
**图7.16 平衡二叉树的删除**

4. **平衡二叉树的查找**

**命题追踪** 指定条件下平衡二叉树的结点数的分析 (2012)

在平衡二叉树上进行查找的过程与二叉排序树的相同。因此,在查找过程中,进行关键字的比较次数不超过树的深度。假设以$n_h$表示深度为$h$的平衡二叉树中含有的最少结点数。显然,有$n_0=0, n_1=1, n_2=2$,并且有$n_h=n_{h-1}+n_{h-2}+1$,如图 7.17 所示,依次推出$n_3=4, n_4=7, n_5=12, \dots$。含有$n$个结点的平衡二叉树的最大深度为$O(\log_2n)$,因此平均查找效率为$O(\log_2n)$。

[图示：结点个数n最少的平衡二叉树]
**图7.17 结点个数$n$最少的平衡二叉树**

**注意**
该结论可用于求解给定结点数的平衡二叉树的查找所需的最多比较次数(或树的最大高度)。如在含有 12 个结点的平衡二叉树中查找某个结点的最多比较次数?

深度为$h$的平衡二叉树中含有的最多结点数显然是满二叉树的情况。

### 7.3.3 红黑树
1. **红黑树的定义**
为了保持 AVL 树的平衡性,在插入和删除操作后,会非常频繁地调整全树整体拓扑结构,代价较大。为此在 AVL 树的平衡标准上进一步放宽条件,引入了红黑树的结构。
一棵红黑树是满足如下红黑性质的二叉排序树:
① 每个结点或是红色,或是黑色的。
② 根结点是黑色的。
③ 叶结点(虚构的外部结点、NULL 结点)都是黑色的。
④ 不存在两个相邻的红结点(红结点的父结点和孩子结点均是黑色的)。
⑤ 对每个结点,从该结点到任意一个叶结点的简单路径上,所含黑结点的数量相同。
与折半查找树和 B 树类似,为了便于对红黑树的实现和理解,引入了$n+1$个外部叶结点,以保证红黑树中每个结点(内部结点)的左、右孩子均非空。图 7.18 所示是一棵红黑树。

[图示：一棵红黑树，结点旁标有黑高(bh)值]
**图7.18 一棵红黑树**

从某结点出发(不含该结点)到达一个叶结点的任意一个简单路径上的黑结点总数称为该结点的黑高(记为 bh),黑高的概念是由性质⑤确定的。根结点的黑高称为红黑树的黑高。
**结论1**：从根到叶结点的最长路径不大于最短路径的 2 倍。
由性质⑤,当从根到任意一个叶结点的简单路径最短时,这条路径必然全由黑结点构成。由性质④,当某条路径最长时,这条路径必然是由黑结点和红结点相间构成的,此时红结点和黑结点的数量相同。图 7.18 中的 6-2 和 6-15-18-20 就是这样的两条路径。
**结论2**：有$n$个内部结点的红黑树的高度$h \le 2\log_2(n+1)$。
**证明**：由结论 1 可知,从根到叶结点(不含叶结点)的任何一条简单路径上都至少有一半是黑结点,因此,根的黑高至少为$h/2$,于是有$n \ge 2^{h/2}-1$,即可求得结论。
由结论 2 也可推出,黑高为$h$的红黑树的内部结点数最少是$2^h-1$,最多是$2^{2h}-1$。
可见,红黑树的“适度平衡”,由 AVL 树的“高度平衡”,降低到“任意一个结点左右子树的高度,相差不超过 2 倍”,也降低了动态操作时调整的频率。对于一棵动态查找树,若插入和删除操作比较少,查找操作比较多,则采用 AVL 树比较合适,否则采用红黑树更合适。但由于维护这种高度平衡所付出的代价比获得的效益大得多,红黑树的实际应用更广泛,C++中的 map 和 set(Java 中的 TreeMap 和 TreeSet)就是用红黑树实现的。

2. **红黑树的插入**
红黑树的插入过程和二叉查找树的插入过程基本类似,不同之处在于,在红黑树中插入新结点后需要进行调整(主要通过重新着色或旋转操作进行),以满足红黑树的性质。
**结论3**：新插入红黑树中的结点初始着为红色。
假设新插入的结点初始着为黑色,则这个结点所在的路径比其他路径多出一个黑结点(几乎每次插入都破坏性质⑤),调整起来也比较麻烦。若插入的结点是红色的,则此时所有路径上的黑结点数量不变,仅在出现连续两个红结点时才需要调整,而且这种调整也比较简单。
设结点$z$为新插入的结点。插入过程描述如下:
1) 用二叉查找树插入法插入,并将结点$z$着为红色。若结点$z$的父结点是黑色的,无须做任何调整,此时就是一棵标准的红黑树,结束。
2) 若结点$z$是根结点,则将$z$着为黑色(树的黑高增 1),结束。
3) 若结点$z$不是根结点,且$z$的父结点$p$是红色的,则分为下面三种情况,区别在于$z$的叔结点$y$的颜色不同,因$z.p$是红色的,插入前的树是合法的,根据性质②和④,爷结点$z.p.p$必然存在且为黑色。性质④只在$z$和$z.p$之间被破坏了。
**情况1**：$z$的叔结点$y$是黑色的,且$z$是一个右孩子。
**情况2**：$z$的叔结点$y$是黑色的,且$z$是一个左孩子。
每棵子树$T_1、T_2、T_3$和$T_4$都有一个黑色根结点,且具有相同的黑高。
**情况1** (LR,先左旋,再右旋),即$z$是其爷结点的左孩子的右孩子。先做一次左旋将此情形转变为情况 2 (变为情况 2 后再做一次右旋),左旋后$z$和父结点$z.p$交换位置。因为$z$和$z.p$都是红色的,所以左旋操作对结点的黑高和性质⑤都无影响。
**情况2** (LL,右单旋),即$z$是其爷结点的左孩子的左孩子。做一次右旋,并交换$z$的原父结点和原爷结点的颜色,就可以保持性质⑤,也不会改变树的黑高。这样,红黑树中也不再有连续两个红结点,结束。情况 1 和情况 2 的调整方式如图 7.19 所示。

[图示：情况1和情况2的调整方式，以及图例]
**图7.19 情况1和情况2的调整方式**

若父结点$z.p$是爷结点$z.p.p$的右孩子,则还有两种对称的情况:RL(先右旋,再左旋)和 RR(左单旋),这里不再赘述。红黑树的调整方法和 AVL 树的调整方法有异曲同工之妙。
**情况3**：$z$的叔结点$y$是红色的。
情况 3 ($z$是左孩子或右孩子无影响),$z$的父结点$z.p$和叔结点$y$都是红色的,因为爷结点$z.p.p$是黑色的,将$z.p$和$y$都着为黑色,将$z.p.p$着为红色,以在局部保持性质④和⑤。然后,把$z.p.p$作为新结点$z$来重复循环,指针$z$在树中上移两层。调整方式如图 7.20 所示。

[图示：情况3的调整方式]
**图7.20 情况3的调整方式**

若父结点$z.p$是爷结点$z.p.p$的右孩子,也还有两种对称的情况,不再赘述。
只要满足情况 3 的条件,就会不断循环,每次循环指针$z$都会上移两层,直到满足 2)(表示$z$上移到根结点)或情况 1 或情况 2 的条件。
可能的疑问:虽然插入的初始位置一定是红黑树的某个叶结点,但因为在情况 3 中,结点$z$存在不断上升的可能,所以对于三种情况,结点$z$都有存在子树的可能。
以图 7.21(a)中的红黑树为例(虚线表示插入后的状态),先后插入 5、4 和 12 的过程如图 7.21所示。插入 5,为情况 3,将 5 的父结点 3 和叔结点 10 着为黑色,将 5 的爷结点变为红色,此时因为 7 已是根,所以又重新着为黑色,树的黑高加 1,结束。插入 4,为情况 1 的对称情况(RL),此时特别注意虚构黑色空结点的存在,先对以 5 为根的子树做右旋;转变为情况 2 的对称情况(RR),交换 3 和 4 的颜色,再对以 4 为根的子树做左旋,结束。插入 12,父结点是黑色的,无须任何调整,结束。

[图示：红黑树的插入过程，包含(a)到(e)五个步骤]
**图7.21 红黑树的插入过程**

***3. 红黑树的删除***

**注意**
本节难度较大,考查概率较低,读者可根据自身情况决定是否学习或学习的时机。

红黑树的插入操作容易导致连续的两个红结点,破坏性质④。而删除操作容易造成子树黑高的变化(删除黑结点会导致根结点到叶结点间的黑结点数量减少),破坏性质⑤。
删除过程也是先执行二叉查找树的删除方法。若待删结点有两个孩子,不能直接删除,而要找到该结点的中序后继(或前驱)填补,即右子树中最小的结点,然后转换为删除该后继结点。由于后继结点至多只有一个孩子,这样就转换为待删结点是终端结点或仅有一个孩子的情况。
最终,删除一个结点有以下两种情况:
*   待删结点只有右子树或左子树。
*   待删结点没有孩子。
1) 若待删结点只有右子树或左子树,则只有两种情况,如图 7.22 所示。

[图示：只有右子树或左子树的删除情况]
**图7.22 只有右子树或左子树的删除情况**

只有这两种情况存在。子树只有一个结点,且必然是红色,否则会破坏性质⑤。
2) 待删结点无孩子,且该结点是红色的,这时可直接删除,而不需要做任何调整。
3) 待删结点无孩子,且该结点是黑色的,这时设待删结点为$y$,$x$是用来替换$y$的结点(注意,当$y$是终端结点时,$x$是黑色的 NULL 结点)。删除$y$后将导致先前包含$y$的任何路径上的黑结点数量减 1,因此$y$的任何祖先都不再满足性质⑤,简单的修正办法就是将替换$y$的结点$x$视为还有额外一重黑色,定义为双黑结点。也就是说,若将任何包含结点$x$的路径上的黑结点数量加 1,则在此假设下,性质⑤得到满足,但破坏了性质①。于是,删除操作的任务就转化为将双黑结点恢复为普通结点。
分为以下四种情况,区别在于$x$的兄弟结点$w$及$w$的孩子结点的颜色不同。
**情况1**：$x$的兄弟结点$w$是红色的。
情况 1,$w$必须有黑色左右孩子和父结点。交换$w$和父结点$x.p$的颜色,然后对$x.p$做一次左旋,而不会破坏红黑树的任何规则。现在,$x$的新兄弟结点是旋转之前的$w$的某个孩子结点,其颜色为黑色,这样,就将情况 1 转换为情况 2、3 或 4 处理。调整方式如图 7.23 所示。

[图示：情况1的调整方式]
**图7.23 情况1的调整方式**

**情况2**：$x$的兄弟结点$w$是黑色的,且$w$的右孩子是红色的。
**情况3**：$x$的兄弟结点$w$是黑色的,且$w$的左孩子是红色的,$w$的右孩子是黑色的。
**情况2** (RR,左单旋),即这个红结点是其爷结点的右孩子的右孩子。交换$w$和父结点$x.p$的颜色,把$w$的右孩子着为黑色,并对$x$的父结点$x.p$做一次左旋,将$x$变为单重黑色,此时不再破坏红黑树的任何性质,结束。调整方式如图 7.24 所示。

[图示：情况2的调整方式]
**图7.24 情况2的调整方式**

**情况3** (RL,先右旋,再左旋),即这个红结点是其爷结点的右孩子的左孩子。交换$w$和其左孩子的颜色,然后对$w$做一次右旋,而不破坏红黑树的任何性质。现在,$x$的新兄弟结点的右孩子是红色的,这样就将情况 3 转换为了情况 2。调整方式如图 7.25 所示。

[图示：情况3的调整方式，附注：白色结点表示既可为黑色也可为红色，对操作没有影响]
**图7.25 情况3的调整方式**

**情况4**：$x$的兄弟结点$w$是黑色的,且$w$的两个孩子结点都是黑色的。
在情况 4 中,因为$w$也是黑色的,所以可从$x$和$w$上去掉一重黑色,使得$x$只有一重黑色而$w$变为红色。为了补偿从$x$和$w$中去掉的一重黑色,把$x$的父结点$x.p$额外着一层黑色,以保持局部的黑高不变。通过将$x.p$作为新结点$x$来循环,$x$上升一层。若是通过情况 1 进入情况 4 的,因为原来的$x.p$是红色的,将新结点$x$变为黑色,终止循环,结束。调整方式如图 7.26 所示。

[图示：情况4的调整方式]
**图7.26 情况4的调整方式**

若$x$是父结点$x.p$的右孩子,则还有四种对称的情况,处理方式类似,不再赘述。
归纳总结:在情况 4 中,因$x$的兄弟结点$w$及左右孩子都是黑色,可以从$x$和$w$中各提取一重黑色(以让$x$变为普通黑结点),不会破坏性质④,并把调整任务向上“推”给它们的父结点$x.p$。在情况 1、2 和 3 中,因为$x$的兄弟结点$w$或$w$左右孩子中有红结点,所以只能在$x.p$子树内用调整和重新着色的方式,且不能改变$x$原根结点的颜色(否则向上可能破坏性质④)。情况 1 虽然可能会转换为情况 4,但因为新$x$的父结点$x.p$是红色的,所以执行一次情况 4 就会结束。情况 1、2 和 3 在各执行常数次的颜色改变和至多 3 次旋转后便终止,情况 4 是可能重复执行的唯一情况,每执行一次指针$x$上升一层,至多$O(\log_2n)$次。
以图 7.27(a)中的红黑树为例(虚线表示删除前的状态),依次删除 5 和 15 的过程如图 7.27所示。删除 5,用虚构的黑色 NULL 结点(图中为 N 结点)替换,视为双黑 NULL 结点,为情况 1,交换兄弟结点 12 和父结点 8 的颜色,对 8 做一次左旋;转变为情况 4,从双黑 NULL 结点和 10 中各提取一重黑色(提取后,双黑 NULL 结点变为普通 NULL 结点,图中省略;10 变为红色),因原父结点 8 是红色,所以将 8 变为黑色,结束。删除 15,为情况 3 的对称情况(LR),交换 8 和 10 的颜色,对 8 做左旋;转变为情况 2 的对称情况(LL),交换 10 和 12 的颜色(两者颜色一样,无变化),将 10 的左孩子 8 着为黑色,对 12 做右旋,结束。

[图示：红黑树的删除过程，包含(a)到(f)六个步骤]
**图7.27 红黑树的删除过程**

### 7.3.4 本节试题精选
**一、单项选择题**
**01.** 对于二叉排序树,下面的说法中,()是正确的。
A. 二叉排序树是动态树表,查找失败时插入新结点,会引起树的重新分裂和组合
B. 对二叉排序树进行层序遍历可得到有序序列
C. 用逐点插入法构造二叉排序树,若先后插入的关键字有序,二叉排序树的深度最大
D. 在二叉排序树中进行查找,关键字的比较次数不超过结点数的 1/2

**02.** 按( )遍历二叉排序树得到的序列是一个有序序列。
A. 先序
B. 中序
C. 后序
D. 层次

**03.** 在二叉排序树中进行查找的效率与()有关。
A. 二叉排序树的深度
B. 二叉排序树的结点的个数
C. 被查找结点的度
D. 二叉排序树的存储结构

**04.** 在常用的描述二叉排序树的存储结构中,关键字值最大的结点()。
A. 左指针一定为空
B. 右指针一定为空
C. 左右指针均为空
D. 左右指针均不为空

**05.** 设二叉排序树中关键字由 1 到 1000 的整数构成,现要查找关键字为 363 的结点,下述关键字序列中,不可能是在二叉排序树上查找的序列是()。
A. 2, 252, 401, 398, 330, 344, 397, 363
B. 924, 220, 911, 244, 898, 258, 362, 363
C. 925, 202, 911, 240, 912, 245, 363
D. 2, 399, 387, 219, 266, 382, 381, 278, 363

**06.** 分别以下列序列构造二叉排序树,与用其他 3 个序列所构造的结果不同的是()。
A. (100, 80, 90, 60, 120, 110, 130)
B. (100, 120, 110, 130, 80, 60, 90)
C. (100, 60, 80, 90, 120, 110, 130)
D. (100, 80, 60, 90, 120, 130, 110)

**07.** 从空树开始,依次插入元素 52, 26, 14, 32, 71, 60, 93, 58, 24 和 41 后构成了一棵二叉排序树。在该树查找 60 要进行比较的次数为()。
A. 3
B. 4
C. 5
D. 6

**08.** 在含有$n$个结点的二叉排序树中查找某个关键字的结点时,最多进行()次比较。
A. $n/2$
B. $\log_2n$
C. $\log_2n + 1$
D. $n$

**09.** 五个不同结点构造的二叉查找树的形态共有( )种。
A. 20
B. 30
C. 32
D. 42

**10.** 构造一棵具有$n$个结点的二叉排序树时,最理想情况下的深度为( )。
A. $n/2$
B. $n$
C. $\lfloor\log_2(n + 1)\rfloor$
D. $\lceil\log_2(n+1)\rceil$

**11.** 含有 20 个结点的平衡二叉树的最大深度为( )。
A. 4
B. 5
C. 6
D. 7

**12.** 具有 5 层结点的平衡二叉树至少有( )个结点。
A. 10
B. 12
C. 15
D. 17

**13.** 高度为 3 的平衡二叉排序树的形态共有()种。
A. 13
B. 14
C. 16
D. 15

**14.** 在平衡二叉树的基本操作中,可能发生两次旋转的操作是()。
A. 添加、删除结点
B. 仅删除结点
C. 仅添加结点
D. 都不会

**15.** 将关键字 1, 2, 3, …, 1024 依次插入到初始为空的平衡二叉树中,假设只有一个根结点的二叉树的高度为 0,则插入结束后的平衡二叉树的高度是( )。
A. 8
B. 9
C. 10
D. 11

**16.** 下列关于红黑树和 AVL 树的说法中,不正确的是( )。
I. 一棵含有$n$个结点的红黑树的高度至多为$2\log_2(n + 1)$
II. 若一个结点是红色的,则它的父结点和孩子结点都是黑色的
III. 红黑树的查询效率一般要优于含有相同结点数的 AVL 树
IV. 若 AVL 树的某结点的左右孩子的平衡因子都是零,则该结点的平衡因子也是零
A. I、III
B. III
C. II、IV
D. III、IV

**17.** 下列关于红黑树和 AVL 树的描述中,不正确的是()。
A. 两者都属于自平衡的二叉树
B. 两者查找、插入、删除的时间复杂度都相同
C. 红黑树插入和删除过程至多有 2 次旋转操作
D. 红黑树的任意一个结点的左右子树高度(含叶结点)之比不超过 2

**18.** 下列关于红黑树的说法中,正确的是()。
A. 红黑树的红结点的数目最多和黑结点的数目相同
B. 若红黑树的所有结点都是黑色的,则它一定是一棵满二叉树
C. 红黑树的任何一个分支结点都有两个非空孩子结点
D. 红黑树的子树也一定是红黑树

**19.** 下列四个选项中,满足红黑树定义的是( )。
[图示：四个选项A,B,C,D，分别表示四棵红黑树]