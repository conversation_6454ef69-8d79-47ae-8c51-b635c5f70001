(已访问磁盘2次)。再从目录P中找出文件J的FCB地址读入内存(已访问磁盘3次)。在最坏情况下,该访问页存放在三级索引下,这时候需要一级级地读三级索引块才能得到文件J的地址(已访问磁盘6次)。最后读入文件J中的相应页(共访问磁盘7次)。所以,若要读文件J中的某一页,最多启动磁盘7次。
3) 由图可知,目录文件C和U的目录项较多,可能存放在多个链接在一起的磁盘块中。在最好情况下,所需的目录项都在目录文件的第一个磁盘块中。先从内存的根目录中找到目录文件C的磁盘地址并读入内存(已访问磁盘1次)。在C中找出目录文件I的磁盘地址并读入内存(已访问磁盘2次)。在I中找出目录文件P的磁盘地址并读入内存(已访问磁盘3次)。从P中找到目录文件U的磁盘地址并读入内存(已访问磁盘4次)。从U的第一个磁盘块中找出文件W的FCB地址并读入内存(已访问磁盘5次)。在最好情况下,要访问的页在FCB的前10个直接块中,按照直接块指示的地址读文件W的相应页(已访问磁盘6次)。所以,若要读文件W中的某页,最少启动磁盘6次。
4) 为了减少启动磁盘的次数,可以将需要访问的W文件挂在根目录的最前面的目录项中。此时,只需读内存中的根目录就可找到W的FCB,将FCB读入内存(已访问磁盘1次),最差情况下,需要的W文件的那个页挂在FCB的三级索引下,因此读3个索引块需要访问磁盘3次(已访问磁盘4次)得到该页的物理地址,再去读这个页即可(已访问磁盘5次)。此时,磁盘最多启动5次。

**05.【解答】**
1) 根目录的第一块常驻内存(root 所指的/bin, /dev, /etc, /boot 等可直接获得),根目录找到文件A需要5次读盘。由$255×2+2=512$可知,一个物理块在链式存储结构下可放2条记录及下一个物理块地址,而文件A共有598条记录,因此读取A的所有记录所需的读盘次数为$598/2=299$,所以将文件A读到内存至少需读盘$299+5=304$次。
2) 当文件为连续文件时,找到文件A同样需要5次读盘,且知道文件A的地址后通过计算只需一次读盘即可读出第487条记录,所以至少需要$5+1=6$次读盘。

## 4.3 文件系统

在学习本节时,请读者思考以下问题:
1) 什么是文件系统?
2) 文件系统要完成哪些功能?
本节除了“外存空闲空间管理”,其他都是2022年统考大纲的新增考点,这些内容都是比较抽象的、看不见也摸不着的原理,在常用的国内教材(如汤小丹的教材)中都鲜有涉及。若觉得不太好理解这些内容,则建议读者结合王道的最新课程进行学习。

### 4.3.1 文件系统结构

文件系统(File system)提供高效和便捷的磁盘访问,以便允许存储、定位、提取数据。文件系统有两个不同的设计问题:第一个问题是,定义文件系统的用户接口,它涉及定义文件及其属性、所允许的文件操作、如何组织文件的目录结构。第二个问题是,创建算法和数据结构,以便映射逻辑文件系统到物理外存设备。现代操作系统有多种文件系统类型,因此文件系统的层次结构也不尽相同。图4.19是一个合理的文件系统层次结构。

应用程序
↓
逻辑文件系统
↓
文件组织模块
↓
基本文件系统
↓
I/O控制
↓
设备

图4.19 一个合理的文件系统层次结构

(1) **I/O控制层**
包括设备驱动程序和中断处理程序,在内存和磁盘系统之间传输信息。设备驱动程序将输入的命令翻译成底层硬件的特定指令,硬件控制器利用这些指令使I/O设备与系统交互。设备驱动程序告诉I/O控制器对设备的什么位置采取什么动作。
(2) **基本文件系统**
向对应的设备驱动程序发送通用命令,以读取和写入磁盘的物理块。每个物理块由磁盘地址标识。该层也管理内存缓冲区,并保存各种文件系统、目录和数据块的缓存。在进行磁盘块传输前,分配合适的缓冲区,并对缓冲区进行管理。管理它们对于系统性能的优化至关重要。
(3) **文件组织模块**
组织文件及其逻辑块和物理块。文件组织模块可以将文件的逻辑块地址转换为物理块地址,每个文件的逻辑块从0到N编号,它与数据的物理块不匹配,因此需要通过转换来定位。文件组织模块还包括空闲空间管理器,以跟踪未分配的块,根据需求提供给文件组织模块。
(4) **逻辑文件系统**
用于管理文件系统中的元数据信息。元数据包括文件系统的所有结构,而不包括实际数据(或文件内容)。逻辑文件系统管理目录结构,以便根据给定文件名为文件组织模块提供所需要的信息。它通过文件控制块来维护文件结构。逻辑文件系统还负责文件保护。

### 4.3.2 文件系统布局

1. **文件系统在磁盘中的结构**
文件系统存放在磁盘上,多数磁盘划分为一个或多个分区,每个分区中有一个独立的文件系统。文件系统可能包括如下信息:启动存储在那里的操作系统的方式、总的块数、空闲块的数量和位置、目录结构以及各个具体文件等。图4.20所示为一个可能的文件系统布局。
简单描述如下:
1) **主引导记录(Master Boot Record, MBR)**,位于磁盘的0号扇区,用来引导计算机,MBR的后面是分区表,该表给出每个分区的起始和结束地址。表中的一个分区被标记为活动分区。当计算机启动时,BIOS读入并执行MBR。MBR做的第一件事是确定活动分区,读入它的第一块,即引导块。
2) **引导块(boot block)**, MBR执行引导块中的程序后,该程序负责启动该分区中的操作系统。每个分区都是统一从一个引导块开始,即使它不含有一个可启动的操作系统,也不排除以后会在该分区安装一个操作系统。Windows 系统称之为分区引导扇区。
除了从引导块开始,磁盘分区的布局是随着文件系统的不同而变化的。文件系统经常包含有如图4.20所列的一些项目。
3) **超级块(super block)**,包含文件系统的所有关键信息,在计算机启动时,或者在该文件系统首次使用时,超级块会被读入内存。超级块中的典型信息包括分区的块的数量、块的大小、空闲块的数量和指针、空闲的FCB数量和FCB指针等。
4) 文件系统中空闲块的信息,可以用位示图或指针链接的形式给出。后面也许跟的是一组i节点,每个文件对应一个节点,i节点说明了文件的方方面面。接着可能是根目录,它存放文件系统目录树的根部。最后,磁盘的其他部分存放了其他所有的目录和文件。

整个磁盘
↓
MBR | 分区表 | 磁盘分区
↓
引导块 | 超级块 | 空闲空间管理 | i节点 | 根目录 | 文件和目录

图4.20 一个可能的文件系统布局

2. **文件系统在内存中的结构**
内存中的信息用于管理文件系统并通过缓存来提高性能。这些数据在安装文件系统时被加载,在文件系统操作期间被更新,在卸载时被丢弃。这些结构的类型可能包括:
1) 内存中的安装表(mount table),包含每个已安装文件系统分区的有关信息。
2) 内存中的目录结构的缓存,包含最近访问目录的信息。
3) 整个系统的打开文件表,包含每个打开文件的FCB副本、打开计数及其他信息。
4) 每个进程的打开文件表,包含进程打开文件的文件描述符(Windows称之为文件句柄)和指向整个系统的打开文件表中对应表项的指针。

### 4.3.3 文件存储空间管理

一个存储设备可以按整体用于文件系统,也可以细分。例如,一个磁盘可以划分为2个分区,每个分区都可以有单独的文件系统。包含文件系统的分区通常称为卷(volume)。卷可以是磁盘的一部分,也可以是整个磁盘,还可以是多个磁盘组成RAID集,如图4.21所示。
在一个卷中,存放文件数据的空间(文件区)和FCB的空间(目录区)是分离的。因为存在很多种类的文件表示和存放格式,所以现代操作系统中一般都有很多不同的文件管理模块,通过它们可以访问不同格式的卷中的文件。卷在提供文件服务前,必须由对应的文件程序进行初始化,划分好目录区和文件区,建立空闲空间管理表格及存放卷信息的超级块。
文件存储设备分成许多大小相同的物理块,并以块为单位交换信息,因此,文件存储设备的管理实质上是对空闲块的组织和管理,它包括空闲块的组织、分配与回收等问题。

(图4.21 逻辑卷与物理盘的关系：逻辑卷a和逻辑卷b的数据区和目录区分布在物理盘1、2、3上)

**命题追踪** 磁盘空闲块管理的方法及特点(2019、2024)

1. **空闲表法**
空闲表法属于连续分配方式,它与内存的动态分区分配类似,为每个文件分配一块连续的存储空间。系统为外存上的所有空闲区建立一张空闲表,每个空闲区对应一个空闲表项,其中包括表项序号、该空闲区的第一个空闲盘块号、该空闲区的空闲盘块数等信息。再将所有空闲区按其起始盘块号递增的次序排列,如表4.2所示。

表4.2 空闲盘块表
| 序号 | 第一个空闲盘块号 | 空闲盘块数 |
| :--: | :------------: | :--------: |
| 1 | 2 | 4 |
| 2 | 9 | 3 |
| 3 | 15 | 5 |
| 4 | … | … |

**盘块的分配：**
空闲盘区的分配与内存的动态分配类似,也是采用首次适应算法、最佳适应算法等。例如,在系统为某新创建的文件分配空闲盘块时,先顺序地检索空闲盘块表的各表项,直至找到第一个其大小能满足要求的空闲区,再将该盘区分配给用户,同时修改空闲盘块表。
**盘块的回收：**
在对用户所释放的存储空间进行回收时,也采用类似于内存回收的方法,即要考虑回收区是否与空闲盘块表中插入点的前区和后区相邻接,对相邻接者应予以合并。
空闲表法的优点是具有较高的分配速度,可减少访问磁盘的I/O频率。对于较小的文件(1~5个盘块),可以采用连续分配方式为文件分配几个相邻的盘块。

2. **空闲链表法**
空闲链表法是指将所有空闲盘区拉成一条空闲链,可分为以下两种。
(1) **空闲盘块链**
空闲盘块链是指将磁盘上的所有空闲空间以盘块为单位拉成一条链。每个盘块都有指向下一个空闲盘块的指针。当用户请求分配存储空间时,从链首开始,依次摘下适当数量的空闲盘块分配给用户。当用户释放存储空间时,将回收的盘块依次插入空闲盘块链的末尾。
空闲盘块链的优点是分配和回收一个盘块的过程非常简单。缺点是在为一个文件分配盘块时可能要重复操作多次,效率较低;又因它是以盘块为单位的,空闲盘块链会很长。
(2) **空闲盘区链**
空闲盘区链是指将磁盘上的所有空闲盘区拉成一条链,每个盘区包含若干相邻的盘块。每个盘区含有指向下一个空闲盘区的指针和本盘区的盘块数。分配盘区的方法与内存的动态分区分配类似,通常采用首次适应算法。回收盘区时,同样也要将回收区与相邻接的空闲盘区合并。
空闲盘区链的优缺点正好与空闲盘块链的相反,优点是分配与回收的效率较高,且空闲盘区链较短。缺点是分配与回收的过程比较复杂。

3. **位示图法**

**命题追踪** 位示图的应用及相关计算(2010、2014、2015、2023)

位示图是利用二进制的一位来表示磁盘中一个盘块的使用情况,磁盘上的所有盘块都有一个二进制位与之对应。当其值为“0”时,表示对应的盘块空闲;为“1”时,表示已分配。这样,一个$m×n$位组成的位示图就可用来表示$m×n$个盘块的使用情况,如图4.22所示。
**盘块的分配：**
1) 顺序扫描位示图,从中找出一个或一组其值为“0”的二进制位。
2) 将找到的一个或一组二进制位转换成与之对应的盘块号。假设找到值为“0”的二进制位处在位示图的第$i$行、第$j$列,则其对应的盘块号$b$应按下式计算($n$为每行位数):
$$
b = n(i-1)+j
$$
3) 修改位示图,令$map[i,j]=1$。

| | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16 |
| :---: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :--: | :--: | :--: | :--: | :--: | :--: | :--: |
| **1** | 1 | 1 | 0 | 0 | 0 | 1 | 1 | 1 | 0 | 0 | 1 | 0 | 0 | 1 | 1 | 0 |
| **2** | 0 | 0 | 0 | 1 | 1 | 1 | 1 | 1 | 1 | 0 | 0 | 0 | 0 | 1 | 1 | 1 |
| **3** | 1 | 1 | 1 | 0 | 0 | 0 | 1 | 1 | 1 | 1 | 1 | 1 | 0 | 0 | 0 | 0 |
| **4** | : | | | | | | | | | | | | | | | |
| **16** | | | | | | | | | | | | | | | | |

图4.22 位示图法示意图

**盘块的回收：**
1) 将回收盘块的盘块号转换成位示图中的行号和列号。转换公式为:
$$
i = (b-1) \text{ DIV } n + 1 \\
j=(b-1) \text{ MOD } n + 1
$$
2) 修改位示图,令$map[i,j]=0$。

**注意**
如无特别提示,本书所用位示图的行和列都从1开始编号。特别注意,若题中指明从0开始编号,则上述计算方法要进行相应的调整。

位示图法的优点是很容易在位示图中找到一个或一组相邻接的空闲盘块。位示图很小,占用空间少,因此可将它保存在内存中,从而节省许多磁盘启动的开销。
位示图法的问题是位示图大小会随着磁盘容量的增加而增大,因此常用于小型计算机。

4. **成组链接法**
空闲表法和空闲链表法都不适用于大型文件系统,因为这会使空闲表或空闲链表太大。UNIX系统中采用的是成组链接法,它结合了上述两种方法的思想而克服“表太长”的缺点。
成组链接法的思想:将空闲盘块分成若干组,如每100个盘块作为一组,每组的第一个盘块记录下一组的空闲盘块总数和空闲盘块号。这样,由各组的第一个盘块可以链接成一条链。第一组的空闲盘块总数和空闲盘块号保存在内在的专用栈中,称为空闲盘块号栈。假设系统空闲区为第201~7999号盘块,则第一组的盘块号为201~300…次末组的盘块号为7801~7900,最末一组的盘块号为7901~7999。最末一组只有99个盘块,它们的块号记录在前一组的7900号盘块中,该块中存放的第一个盘块号是“0”,以作为空闲盘块链的结束标志,如图4.23所示。
简而言之,每组(除了最后一组)的第一块作为索引块,然后将这些索引块链接起来。
**盘块的分配：**
根据空闲盘块号栈的指针,将与之对应的盘块分配给用户,同时移动指针,并将栈中的空闲盘块数减1。若该指针指向的是栈底的盘块号,则在该盘块号对应的盘块中保存的是下一组空闲盘块号,因此要将该盘块的内容读入栈,作为新的空闲盘块号栈的内容,并将原栈底盘块号对应的盘块分配出去(其中有用的数据已读入栈)。
**例如**，在图4.23中,分配盘块时,先依次分配201~299号盘块,当需要分配300号盘块时,首先将300号盘块的内容读入空闲盘块号栈,然后再分配300号盘块。

(图4.23 成组链接法示意图: 一个名为"空闲盘块号栈"的栈，栈顶指向201，栈底是299，共100个。栈底的299号盘块指向下一个组的起始盘块300。300号盘块记录了301-399号盘块。这个链式结构一直延续到7901号盘块。)

**盘块的回收：**
将回收的盘块号存入空闲盘块号栈的顶部,同时移动指针,并将栈中的空闲盘块数加1。当栈中的空闲盘块数已达100时,表示栈已满,将现有栈中的100个空闲盘块号存入新回收的盘块,并将该新回收的盘块号作为新栈底,再将栈中的空闲盘块数置为1。
表示空闲空间的位向量表或空闲盘块号栈,以及卷中的目录区、文件区划分信息都要存放在磁盘中,一般放在卷头位置,在UNIX系统中称为超级块。在对卷中的文件进行操作前,超级块要预先读入内存,并且经常保持主存超级块与磁盘卷中超级块的一致性。

### 4.3.4 虚拟文件系统

虚拟文件系统(VFS)屏蔽了不同文件系统的差异和操作细节,向上为用户提供了文件操作的统一调用接口,如图4.24所示。当用户程序访问文件时,通过VFS提供的统一调用函数(如`open()`等)来操作不同文件系统的文件,而无须考虑具体的文件系统和实际的存储介质。
虚拟文件系统采用了面向对象的思想,它抽象出一个通用的文件系统模型,定义了通用文件系统都支持的接口。新的文件系统只要支持并实现这些接口,即可安装和使用。为了实现虚拟文件系统,系统抽象了四种对象类型。每个对象都包含数据和函数指针,这些函数指针指向操作这些数据的文件系统的实现函数。这四种对象类型如下。
(1) **超级块对象**
表示一个已安装(或称挂载)的特定文件系统。超级块对象对应于磁盘上特定扇区的文件系统超级块,用于存储已安装文件系统的元信息。其操作方法包含一系列可在超级块对象上调用的操作函数,主要有分配inode、销毁inode、读inode、写inode等。

(图4.24 虚拟文件系统的示意图: 用户进程(用户空间) -> POSIX -> 虚拟文件系统 (VFS接口) -> [Ext2/3 -> 硬盘1], [NTFS -> 硬盘2], [FAT -> U盘] (文件系统/内核空间))

(2) **索引节点对象**
表示一个特定的文件。索引节点和文件是一对一的关系。只有当文件被访问时,才在内存中创建索引节点对象,每个索引节点对象都会复制磁盘索引节点包含的一些数据。索引节点对象还提供许多操作函数,如创建新索引节点、创建硬链接、创建新目录等。
(3) **目录项对象**
表示一个特定的目录项。目录项对象是一个路径的组成部分,它包含指向关联索引节点的指针,还包含指向父目录和指向子目录的指针。不同于前面两个对象,目录项对象在磁盘上没有对应的数据结构,而是VFS在遍历路径的过程中,将它们逐个解析成目录项对象的。
(4) **文件对象**
表示一个与进程相关的已打开文件。可以通过调用`open()`打开一个文件,通过调用`close()`关闭一个文件。文件对象和物理文件的关系类似于进程和程序的关系。文件对象仅是进程视角上代表已打开的文件,它反过来指向其索引节点。文件对象包含与该文件相关联的目录项对象,包含该文件的文件系统、文件指针等,还包含在该文件对象上的一系列操作函数。
当进程发起一个面向文件的系统调用时,内核调用VFS中的一个函数,该函数调用目标文件系统中的相应函数,将文件系统请求转换到面向设备的指令。以在用户空间调用`write()`为例,它在VFS中通过`sys_write()`函数处理,`sys_write()`找到具体文件系统提供的写方法,将控制权交给该文件系统,最后由该文件系统与物理介质交互并写入数据,如图4.25所示。

`write()` (用户空间) -> `sys_write()` (VFS(虚拟文件系统)) -> `文件系统的写方法` (文件系统) -> `物理介质`
图4.25 write()系统调用操作示意图

对用户来说,不需要关心不同文件系统的具体实现细节,只需要对一个虚拟的文件操作界面进行操作。VFS对每个文件系统的所有细节进行抽象,使得不同的文件系统在系统中运行的进程看来都是相同的。严格来说,VFS并不是一种实际的文件系统,它只存在于内存中,不存在于任何外存空间中。VFS在系统启动时建立,在系统关闭时消亡。

### 4.3.5 文件系统挂载

如文件在使用前要打开那样,文件系统在进程使用之前必须先安装,也称挂载(Mounting)。将设备中的文件系统挂载到某个目录后,就可通过这个目录来访问该设备上的文件。注意,这里的设备指的是逻辑上的设备,如一个磁盘上的不同分区都可视为不同的设备。
Windows系统维护一个扩展的两级目录结构,用驱动器字母表示设备和卷。卷具有常规树结构的目录,与驱动器号相关联,还含有指向已安装文件系统的指针。特定文件的路径形式为`driver-letter:\path\to\file`,访问时,操作系统找到相应文件系统的指针,并遍历该设备的目录结构,以查找指定的文件。新版的Windows允许文件系统安装在目录树下的任意位置,就像UNIX一样。在启动时,Windows操作系统自动发现所有设备,并且安装所有找到的文件系统。
UNIX使用系统的根文件系统,它是在系统启动时直接安装的,也是内核映像所在的文件系统。除了根文件系统,所有其他文件系统都要先挂载到根文件系统中的某个目录后才能访问。其他文件系统要么在系统初始化时自动安装,要么由用户挂载在已安装文件系统的目录下。安装文件系统的这个目录称为安装点,同一个设备可以有多个安装点,同一个安装点同时只能挂载一个设备。将设备挂载到安装点之后,通过该目录就可以读取该设备中的数据。
假定将存放在磁盘/dev/fd0上的ext2文件系统通过mount命令安装到/flp:
```
mount -t ext2 /dev/fd0 /flp
```
如需卸载该文件系统,可以使用umount命令。
贯穿本章内容有两条主线:第一条主线是介绍一种新的抽象数据类型——文件,从逻辑结构和物理结构两个方面进行;第二条主线是操作系统是如何管理“文件”的,介绍了多文件的逻辑结构的组织,即目录,还介绍了如何处理用户对文件的服务请求,即磁盘管理。只从宏观上认识是远不够的,从宏观上把握知识的目的是从微观上更加准确地掌控细微知识点,在考试中取得好成绩。读者要通过反复做题和思考,不断加深自己对知识点的掌握程度。

### 4.3.6 本节小结

本节开头提出的问题的参考答案如下。
1) 什么是文件系统?
操作系统中负责管理和存储文件信息的软件机构称为文件管理系统,简称文件系统。文件系统由三部分组成:与文件管理有关的软件、被管理文件及实施文件管理所需的数据结构。
2) 文件系统要完成哪些功能?
对于用户而言,文件系统最主要的功能是实现对文件的基本操作,让用户可以按名存储和查找文件,组织成合适的结构,并应当具有基本的文件共享和文件保护功能。对于操作系统本身而言,文件系统还需要管理与磁盘的信息交换,完成文件逻辑结构和物理结构上的变换,组织文件在磁盘上的存放,采取好的文件排放顺序和磁盘调度方法以提升整个系统的性能。

### 4.3.7 本节习题精选

**一、单项选择题**
**01.** 从用户的观点看,操作系统中引入文件系统的目的是( )。
A. 保护用户数据
B. 实现对文件的按名存取
C. 实现虚拟存储
D. 保存用户和系统文档及数据

**02.** 逻辑文件系统的功能有( )。
I. 文件按名存取
II. 文件目录组织管理
III. 把文件名转换为文件描述符或文件句柄
IV. 存储保护
A. I、II和III
B. II、III和IV
C. I、II和IV
D. I、II、III和IV

**03.** 下列关于文件系统的说法中,正确的是( )。
A. 一个文件系统可以管理的文件数量受限于文件控制块的数量
B. 一个文件系统可使用的容量一定等于其所在磁盘的容量
C. 一个文件系统中单个文件的大小只受磁盘剩余容量大小的限制
D. 一个文件系统不能将数据存放在多个磁盘上

**04.** UNIX操作系统中,文件的索引结构放在( )。
A. 超级块
B. 索引节点
C. 目录项
D. 空闲块

**05.** 文件的存储空间管理实质上是对( )的组织和管理。
A. 文件目录
B. 外存已占用区域
C. 外存空闲区
D. 文件控制块

**06.** 对外存文件区的管理应以( )为主要目标。
A. 提高系统吞吐量
B. 提高换入换出速度
C. 降低存储费用
D. 提高存储空间的利用率

**07.** 位示图可用于( )。
A. 文件目录的查找
B. 磁盘空间的管理
C. 主存空间的管理
D. 文件的保密

**08.** 下列各种文件存储空间的管理方法中,( )需要使用空闲盘块号栈。
A. 空闲表法
B. 空闲链表法
C. 位示图法
D. 成组链接法

**09.** 硬盘的主引导扇区( )。
A. 包含引导记录
B. 包含分区表和主引导程序
C. 只包含主引导程序
D. 只包含分区表

**10.** 若用8个字(字长32位)组成的位示图管理内存,即位示图有8行、32列,行号和列号均从1开始,则块号为100的内存块所对应位示图的位置是( )。
A. 字号为3,位号为5
B. 字号为4,位号为4
C. 字号为3,位号为4
D. 字号为4,位号为5

**11.** 比较难得到连续空间的空闲空间管理方式是( )。
A. 空闲链表
B. 空闲表
C. 位示图
D. 成组链接

**12.** 下列选项中,( )不是Linux实现虚拟文件系统VFS所定义的对象类型。
A. 超级块(superblock)对象
B. 目录项(inode)对象
C. 文件(file)对象
D. 数据(data)对象

**13.【2014统考真题】** 现有一个容量为10GB的磁盘分区,磁盘空间以簇为单位进行分配,簇的大小为4KB,若采用位图法管理该分区的空闲空间,即用一位来标识一个簇是否被分配,则存放该位图所需的簇数为( )。
A. 80
B. 320
C. 80K
D. 320K

**14.【2015统考真题】** 文件系统用位图法表示磁盘空间的分配情况,位图存于磁盘的32~127号块中,每个盘块占1024B,盘块和块内字节均从0开始编号。假设要释放的盘块号为409612,则位图中要修改的位所在的盘块号和块内字节序号分别是( )。
A. 81, 1
B. 81, 2
C. 82, 1
D. 82, 2

**15.【2019统考真题】** 下列选项中,可用于文件系统管理空闲磁盘块的数据结构是( )。
I. 位图 II. 索引节点 III. 空闲磁盘块链 IV. 文件分配表(FAT)
A. 仅I、II
B. 仅I、III、IV
C. 仅I、III
D. 仅II、III、IV

**16.【2023统考真题】** 某系统采用页式存储管理,用位图管理空闲页框。若页大小为4KB,物理内存大小为16GB,则位图所占空间的大小是( )。
A. 128B
B. 128KB
C. 512KB
D. 4MB

**17.【2024统考真题】** 文件系统需要占用部分外存空间记录空闲块位置。在下列方法中,占用外存空间的大小与当前空闲块数量无关的是( )。
A. 位图法
B. 空闲表法
C. 成组链接法
D. 空闲链表法

**二、综合应用题**

**01.** 一计算机系统利用位示图来管理磁盘文件空间。假定该磁盘组共有100个柱面,每个柱面有20个磁道,每个磁道分成8个盘块(扇区),每个盘块1KB,位示图如下图所示。

| i\j | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 |
| :--: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: |
| **0** | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 |
| **1** | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 | 1 |
| **2** | 1 | 1 | 0 | 1 | 1 | 1 | 0 | 1 | 1 | 1 | 1 | 1 | 1 | 0 | 0 | 0 |
| **3** | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 |

1) 试给出位示图中位置(i,j)与对应盘块所在的物理位置(柱面号,磁头号,扇区号)之间的计算公式。假定柱面号、磁头号、扇区号都从0开始编号。
2) 试说明分配和回收一个盘块的过程。

**02.** 假定一个盘组共有100个柱面,每个柱面上有16个磁道,每个磁道分成4个扇区。
1) 整个磁盘空间共有多少个存储块?(每个扇区对应一个存储块)
2) 若用字长32位的单元来构造位示图,共需要多少个字?
3) 位示图中第18个字的第16位对应的块号是多少?(字号和位号都从1开始)

### 4.3.8 答案与解析

**一、单项选择题**
**01. B**
从系统角度看,文件系统负责对文件的存储空间进行组织、分配,负责文件的存储并对文件进行保护、检索。从用户角度看,文件系统根据一定的格式将文件存放到存储器中适当的地方,当用户需要使用文件时,系统根据用户所给的文件名能够从存储器中找到所需要的文件。
**02. D**
逻辑文件系统的功能包括对文件按名存取,进行文件目录组织管理,将文件名转换为文件描述符或文件句柄,进行存储保护,因此4个说法均正确。
**03. A**
一个文件系统的容量不一定等于承载该文件系统的磁盘容量。一个磁盘可分为多个分区,每个分区可以有不同的文件系统,单个文件的大小不仅受磁盘剩余容量大小的限制,还受FCB和FAT表等结构的限制。利用磁盘阵列技术,一个文件系统可将数据存放到多个磁盘上。
**04. B**
UNIX采用树形目录结构,文件信息存放在索引节点中。超级块是用来描述文件系统的。
**05. C**
文件存储空间管理即文件空闲空间管理。文件管理要解决的重要问题是,如何为创建文件分配存储空间,即如何找到空闲盘块,并对其管理。
**06. D**
文件区占磁盘空间的大部分,因为通常的文件都较长时间地驻留在外存上,对它们的访问频率是较低的,所以对文件区管理的主要目标是提高存储空间的利用率。
**07. B**
位示图方法是空闲块管理方法,用于管理磁盘空间。
**08. D**
成组链接法将所有空闲盘块分成若干组,每组的第一个盘块记录下一组的空闲盘块总数和空闲盘块号。第一组的空闲盘块总数和空闲盘块号存放在内存的专用栈中,称为空闲盘块号栈。
**09. B**
硬盘的主引导扇区由三部分组成:主引导程序,也称主引导记录(MBR),用于系统启动时将控制转给用户指定的并在分区表中登记了的某个活动分区;分区表,给出每个分区的起始和结束地址;结束标志,其值通常为AA55。
**10. B**
首先求出块号100所在的行号,1~32在行号1中,33~64在行号2中,65~96在行号3中,97~128在行号4中,所以块号100在行号4中;然后求出块号100在行号4中的哪列,行号4的第1列是块号97,以此类推,块号100在行号4中的第4列。另解,行号row和列号col分别为
$$
\text{row} = (100-1) \text{ DIV } 32 + 1 = 4 \\
\text{col} = (100-1) \text{ MOD } 32 + 1 = 4
$$
即字号为4,位号也为4。

**注意**
若注明行号和列号从0开始,则答案是不同的。

**11. A**
空闲链表法适用于离散分配,比较难得到连续空间。空闲表法适用于连续分配,容易得到连续空间。位示图法适用于连续分配和离散分配,容易找到连续的空闲块。成组链接法是将连续分配和离散分配相结合的方法,也能方便找到连续的空闲块。
**12. D**
为了实现虚拟文件系统(VFS), Linux主要抽象了四种对象类型:超级块对象、索引节点对象、目录项对象和文件对象。选项D错误。
**13. A**
簇的总数为$10\text{GB}/4\text{KB} = 2.5\text{M}$。用一位标识一簇是否被分配,整个磁盘共需要2.5Mbit,即需要$2.5\text{M}/8 = 320\text{KB}$,因此共需要$320\text{KB}/4\text{KB} = 80$簇。
**14. C**
盘块号 = 起始块号+$\lfloor \text{盘块号}/(1024×8) \rfloor = 32 + \lfloor 409612/(1024×8) \rfloor = 32+50 = 82$。这里问的是块内字节号而不是位号,因此还需除以8,块内字节号 = $\lfloor (\text{盘块号}\%(1024×8))/8 \rfloor = 1$。
**15. B**
传统文件系统管理空闲磁盘的方法包括空闲表法、空闲链表法、位示图法和成组链接法,选项I、III正确。FAT的表项与物理磁盘块一一对应,并且可以用一个特殊的数字-1表示文件的最后一块,用-2表示这个磁盘块是空闲的(当然也可用-3,-4来表示),因此FAT不仅记录了文件中各个块的先后链接关系,还标记了空闲的磁盘块,操作系统可以通过FAT对文件存储空间进行管理,选项IV正确。索引节点是操作系统为了实现文件名与文件信息分开而设计的数据结构,存储了文件描述信息,索引节点属于文件目录管理部分的内容,选项II错误。
**16. C**
物理内存大小为16GB,页大小为4KB,则物理内存的总页框数为$16\text{GB}/4\text{KB} = 2^{34}/2^{12} = 2^{22}$。位图用1位来表示一个页框是否空闲,所以占用的空间大小为$2^{22}\text{b}=2^{19}\text{B}=512\text{KB}$。
**17. A**
位图法利用一个二进制位来表示磁盘中一个盘块的使用情况,磁盘上的所有盘块都有一个二进制位与之对应。当其值为“0”时,表示对应的盘块空闲;当其值为“1”时,表示已分配。位图所占空间的大小只取决于外存空间的总大小(盘块数量),而与当前空闲块数量无关。

**二、综合应用题**
**01.【解答】**
1) 根据位示图的位置$(i,j)$,得出盘块的序号$b=i×16+j$;用C表示柱面号,H表示磁头号,S表示扇区号,则有
$$
C = b/(20×8), H = (b\%(20×8))/8, S=b\%8
$$
2) 分配:顺序扫描位示图,找出1个其值为“0”的二进制位(“0”表示空闲),利用上述公式将其转换成相应的序号$b$,并修改位示图,置$(i,j)=1$。
回收:将回收盘块的盘块号换算成位示图中的$i$和$j$,转换公式为
$$
b = C×20×8+ H×8+S, i=b/16, j=b\%16
$$
最后将计算出的$(i,j)$在位示图中置“0”。

**02.【解答】**
1) 整个磁盘空间的存储块(扇区)数量为$4×16×100=6400$个。
2) 位示图应为6400个位,若用字长为32位($n=32$)的单元来构造位示图,则需要$6400/32=200$个字。
3) 位示图中第18个字的第16位($i=18,j=16$)对应的块号为$32×(18-1)+16=560$。

## 4.4 本章疑难点

1. **文件的物理分配方式的比较**
文件的三种物理分配方式的比较如表4.3所示。

表4.3 文件三种分配方式的比较
| | 访问第n条记录 | 优点 | 缺点 |
| :---: | :---: | :--- | :--- |
| **连续分配** | 需访问磁盘1次 | 顺序存取时速度快,文件定长时可根据文件起始地址及记录长度进行随机访问 | 文件存储要求连续的存储空间,会产生碎片,不利于文件的动态扩充 |
| **链接分配** | 需访问磁盘n次 | 可解决外存的碎片问题,提高外存空间的利用率,动态增长较方便 | 只能按照文件的指针链顺序访问,查找效率低,指针信息存放消耗外存空间 |
| **索引分配** | m级需访问磁盘m+1次 | 可以随机访问,文件易于增删 | 索引表增加存储空间的开销,索引表的查找策略对文件系统效率影响较大 |

2. **文件打开的过程描述**
1. 检索目录,要求打开的文件应该是已经创建的文件,它应登记在文件目录中,否则会出错。在检索到指定文件后,就将其磁盘iNode复制到活动iNode表中。
2. 将参数mode所给出的打开方式与活动iNode中在创建文件时所记录的文件访问权限相比较,若合法,则此次打开操作成功。
3. 当打开合法时,为文件分配用户打开文件表表项和系统打开文件表表项,并为后者设置初值,通过指针建立表项与活动iNode之间的联系,再将文件描述符fd返回给调用者。