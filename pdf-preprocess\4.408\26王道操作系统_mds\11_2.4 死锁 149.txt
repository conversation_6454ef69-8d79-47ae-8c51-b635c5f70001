线程B并发执行，初始时 lock值为FALSE，当线程A执行完`*a=*b`后发生了进程调度，切换到线程B执行，线程B执行完 newSwap 后发生线程切换，此时线程A和B都能进入临界区，不能实现互斥访问。
28.【解答】
1) 代码$C_1$执行对B的写操作，且$P_1$和$P_2$需要互斥执行$C_1$，因此$C_1$的代码是临界区。
2) 有一组互斥关系和一组同步关系。互斥关系为：$P_1$和$P_2$需要互斥访问缓冲区B；同步关系为：$P_1$执行$C_1$后，$P_2$才能执行$C_2$。缓冲区B初始为空，且最大容量为1，因此可不用定义互斥信号量 mutex。因为在缓冲区大小为1的条件下，同步信号量S就可同时保证$P_1$和$P_2$互斥访问B，符合题意“尽可能定义少的信号量”的要求。进程$P_1$和$P_2$的同步伪代码如下。

| Semaphore S = 0; //实现进程PI与P2的同步 | |
| :--- | :--- |
| **P1** | **P2** |
| C1; | ... |
| signal(S); | wait(S); |
| ... | C2; |
| | ... |

3) 缓冲区B初始非空，因此仅需一个互斥信号量 mutex 来保证$P_1$和$P_2$互斥访问B即可。进程$P_1$和$P_2$的互斥伪代码如下。

| Semaphore mutex=1; //实现P1与P2互斥执行C3 | |
| :--- | :--- |
| **P1** | **P2** |
| ... | ... |
| wait(mutex); | wait(mutex); |
| C3; | C3; |
| signal(mutex); | signal(mutex); |
| ... | ... |

## 2.4 死锁
在学习本节时，请读者思考以下问题：
1) 为什么会产生死锁？产生死锁有什么条件？
2) 有什么办法可以解决死锁问题？
学完本节，读者应了解死锁的由来、产生条件及基本解决方法，区分避免死锁和预防死锁。

### 2.4.1 死锁的概念
**1. 死锁的定义**
在多道程序系统中，由于进程的并发执行，极大提升了系统效率。然而，多个进程的并发执行也带来了新的问题——死锁。所谓死锁，是指多个进程因竞争资源而造成的一种僵局（互相等待对方手里的资源），使得各个进程都被阻塞，若无外力干涉，这些进程都无法向前推进。
下面通过一些实例来说明死锁现象。
先看生活中的一个实例。在一条河上有一座桥，桥面很窄，只能容纳一辆汽车通行。若有两辆汽车分别从桥的左右两端驶上该桥，则会出现下述冲突情况：此时，左边的汽车占有桥面左边的一段，右边的汽车占有桥面右边的一段，要想过桥则需等待左边或右边的汽车向后行驶以退出桥面。但若左右两边的汽车都只想向前行驶，则两辆汽车都无法过桥。
在计算机系统中也存在类似的情况。例如，某计算机系统中只有一台打印机和一台输入设备，进程$P_1$正占用输入设备，同时又提出使用打印机的请求，但此时打印机正被进程$P_2$所占用，而$P_2$在未释放打印机之前，又提出请求使用正被$P_1$占用的输入设备。这样，两个进程相互无休止地等待下去，均无法继续向前推进，此时两个进程陷入死锁状态。

**2. 死锁与饥饿**
一组进程处于死锁状态是指组内的每个进程都在等待一个事件，而该事件只可能由组内的另一个进程产生。与死锁相关的另一个问题是饥饿，即进程在信号量内无穷等待的情况。
产生饥饿的主要原因是：当系统中有多个进程同时申请某类资源时，由分配策略确定资源分配给进程的次序，有的分配策略可能是不公平的，即不能保证等待时间上界的存在。在这种情况下，即使系统未发生死锁，某些进程也可能长时间等待。当等待时间给进程的推进带来明显影响时，称发生了饥饿。例如，当有多个进程需要打印文件时，若系统分配打印机的策略是最短文件优先，则长文件的打印任务将因短文件的源源不断到来而被无限期推迟，最终导致饥饿，甚至“饿死”。饥饿并不表示系统一定会死锁，但至少有一个进程的执行被无限期推迟。
死锁和饥饿的共同点都是进程无法顺利向前推进的现象。
死锁和饥饿的主要差别：①发生饥饿的进程可以只有一个；而死锁是因循环等待对方手里的资源而导致的，因此，若有死锁现象，则发生死锁的进程必然大于或等于两个。②发生饥饿的进程可能处于就绪态（长期得不到CPU，如SJF算法的问题），也可能处于阻塞态（如长期得不到所需的I/O设备，如上述举例）；而发生死锁的进程必定处于阻塞态。

**3. 死锁产生的原因**
**命题追踪** 单类资源竞争时发生死锁的临界条件的分析 (2009、2014)
(1) 系统资源的竞争
通常系统中拥有的不可剥夺资源（如磁带机、打印机等），其数量不足以满足多个进程运行的需要，使得进程在运行过程中，会因争夺资源而陷入僵局。只有对不可剥夺资源的竞争才可能产生死锁，对可剥夺资源（如CPU和主存）的竞争是不会引起死锁的。
(2) 进程推进顺序非法
请求和释放资源的顺序不当，也同样会导致死锁。例如，进程$P_1$, $P_2$分别保持了资源$R_1$, $R_2$，而$P_1$申请资源$R_2$、$P_2$申请资源$R_1$时，两者都会因为所需资源被占用而阻塞，于是导致死锁。
信号量使用不当也会造成死锁。进程间彼此相互等待对方发来的消息，也会使得这些进程无法继续向前推进。例如，进程A等待进程B发的消息，进程B又在等待进程A发的消息，可以看出进程A和B不是因为竞争同一资源，而是在等待对方的资源导致死锁。

**4. 死锁产生的必要条件**
产生死锁必须同时满足以下4个条件，只要其中任一条件不成立，死锁就不会发生。
1) **互斥条件**。进程要求对所分配的资源（如打印机）进行排他性使用，即在一段时间内某资源仅为一个进程所占有。此时若有其他进程请求该资源，则请求进程只能等待。
2) **不可剥夺条件**。进程所获得的资源在未使用完之前，不能被其他进程强行夺走，即只能由获得该资源的进程自己来释放（只能是主动释放）。
3) **请求并保持条件**。进程已经保持了至少一个资源，但又提出了新的资源请求，而该资源已被其他进程占有，此时请求进程被阻塞，但对自己已获得的资源保持不放。
4) **循环等待条件**。存在一种进程资源的循环等待链，链中每个进程已获得的资源同时被链中下一个进程所请求。即存在一个处于等待态的进程集合{$P_0$, $P_1$,…, $P_n$}，其中$P_i$等待的资源被$P_{i+1}$ ($i$=0,1,…, n-1)占有，$P_n$等待的资源被$P_0$占有，如图2.13所示。
直观上看，循环等待条件似乎和死锁的定义一样，其实不然。按死锁定义构成等待环所要求的条件更严，它要求$P_i$等待的资源必须由$P_{i+1}$来满足，而循环等待条件则无此限制。例如，系统中有两台输出设备，$P_0$和$P_1$各占有一台，且$P_k$不属于集合{0, 1,..., n}。$P_i$等待一台输出设备，它可从$P_0$或$P_k$获得。因此，虽然$P_0$, $P_1$和其他一些进程形成了等待环，但$P_k$不在圈内，若$P_k$释放了输出设备，则可打破循环等待，如图2.14所示。因此循环等待只是死锁的必要条件。
图2.13 循环等待
图2.14 满足条件但无死锁
资源分配图含圈而系统又不一定有死锁的原因是，同类资源数大于1。但若系统中每类资源都只有一个资源，则资源分配图含圈就变成了系统出现死锁的充分必要条件。

**注**意区分不可剥夺条件与请求并保持条件。下面用一个简单的例子进行说明：若你手上拿着一个苹果（即便你不打算吃），别人不能将你手上的苹果拿走，则这就是不可剥夺条件；若你左手拿着一个苹果，允许你右手再去拿一个苹果，则这就是请求并保持条件。

**5. 死锁的处理策略**
为使系统不发生死锁，必须设法破坏产生死锁的4个必要条件之一，或允许死锁产生，但当死锁发生时能检测出死锁，并有能力实现恢复。
1) **死锁预防**。设置某些限制条件，破坏产生死锁的4个必要条件中的一个或几个。
2) **避免死锁**。在资源的动态分配过程中，用某种方法防止系统进入不安全状态。
3) **死锁的检测及解除**。无须采取任何限制性措施，允许进程在运行过程中发生死锁。通过系统的检测机构及时地检测出死锁的发生，然后采取某种措施解除死锁。
预防死锁和避免死锁都属于事先预防策略，预防死锁的限制条件比较严格，实现起来较为简单，但往往导致系统的效率低，资源利用率低；避免死锁的限制条件相对宽松，资源分配后需要通过算法来判断是否进入不安全状态，实现起来较为复杂。
死锁的几种处理策略的比较见表2.5。

**表2.5 死锁处理策略的比较**
| | 资源分配策略 | 各种可能模式 | 主要优点 | 主要缺点 |
| :--- | :--- | :--- | :--- | :--- |
| **死锁<br>预防** | 保守，宁可资源闲置 | 一次请求所有资源，资源剥夺，资源按序分配 | 适用于突发式处理的进程，不必进行剥夺 | 效率低，初始化时间延长；剥夺次数过多；不便灵活申请新资源 |
| **死锁<br>避免** | 是预防和检测的折中(在运行时判断是否可能死锁) | 寻找可能的安全允许顺序 | 不必进行剥夺 | 必须知道将来的资源需求；进程不能被长时间阻塞 |
| **死锁<br>检测** | 宽松，只要允许就分配资源 | 定期检查死锁是否已经发生 | 不延长进程初始化时间，允许对死锁进行现场处理 | 通过剥夺解除死锁，造成损失 |

### 2.4.2 死锁预防
**命题追踪** 死锁预防的特点(2019)
预防死锁的发生只需破坏死锁产生的4个必要条件之一即可。
**1. 破坏互斥条件**
若将只能互斥使用的资源改造为允许共享使用，则系统不会进入死锁状态。但有些资源根本不能同时访问，如打印机等临界资源只能互斥使用。所以，破坏互斥条件而预防死锁的方法不太可行，而且为了系统安全，很多时候还必须保护这种互斥性。
**2. 破坏不可剥夺条件**
当一个已经保持了某些不可剥夺资源的进程，请求新的资源而得不到满足时，它必须释放已经保持的所有资源，待以后需要时再重新申请。这意味着，进程已占有的资源会被暂时释放，或者说是被剥夺了，从而破坏了不可剥夺条件。
该策略实现起来比较复杂。释放已获得的资源可能造成前一阶段工作的失效，因此这种方法常用于状态易于保存和恢复的资源，如CPU的寄存器及内存资源，一般不能用于打印机之类的资源。反复地申请和释放资源既影响进程推进速度，又增加系统开销，进而降低了系统吞吐量。
**3. 破坏请求并保持条件**
要求进程在请求资源时不能持有不可剥夺资源，可以通过两种方法实现：
1) 采用预先静态分配方法，即进程在运行前一次申请完它所需要的全部资源。在它的资源未满足前，不让它投入运行。在进程的运行期间，不会再提出资源请求，从而破坏了“请求”条件。在等待期间，进程不占有任何资源，从而破坏了“保持”条件。
2) 允许进程只获得运行初期所需的资源后，便可开始运行。进程在运行过程中再逐步释放已分配给自己且已使用完毕的全部资源后，才能请求新的资源。
方法一的实现简单，但缺点也显而易见，系统资源被严重浪费，其中有些资源可能仅在运行初期或快结束时才使用，而且会导致“饥饿”现象，由于个别资源长期被其他进程占用，将导致等待该资源的进程迟迟不能开始运行。方法二则改进了这些缺点。
**4. 破坏循环等待条件**
为了破坏循环等待条件，可以采用顺序资源分配法。首先给系统中的各类资源编号，规定每个进程必须按编号递增的顺序请求资源，同类资源（编号相同的资源）一次申请完。也就是说，一个进程只在已经占有小编号的资源时，才有资格申请更大编号的资源。按此规则，已持有大编号资源的进程不可能再逆向申请小编号的资源，因此不会产生循环等待的现象。
这种方法的缺点：编号必须相对稳定，因此不便于增加新类型设备；尽管在编号时已考虑到大多数进程使用这些资源的顺序，但是进程实际使用资源的顺序还是可能和编号的次序不一致，这就会造成资源的浪费；此外，必须按规定次序申请资源，也会给用户编程带来麻烦。

### 2.4.3 死锁避免
避免死锁同样属于事先预防策略，但并不是事先采取某种限制措施破坏死锁的必要条件，而是在每次分配资源的过程中，都要分析此次分配是否会带来死锁风险，只有在不产生死锁的情况下，系统才会为其分配资源。这种方法所施加的限制条件较弱，可以获得较好的系统性能。
**1. 系统安全状态**
避免死锁的方法中，允许进程动态地申请资源，但系统在进行资源分配之前，应先计算此次分配的安全性。若此次分配不会导致系统进入不安全状态，则允许分配；否则让进程等待。
所谓**安全状态**，是指系统能按某种进程推进顺序($P_1$, $P_2$,…, $P_n$)为每个进程$P_i$分配其所需的资源，直至满足每个进程对资源的最大需求，使每个进程都可顺利完成。此时称$P_1, P_2, …, P_n$为**安全序列**（可能有多个）。若系统无法找到一个安全序列，则称系统处于**不安全状态**。

**命题追踪** 系统安全状态的分析(2018)
**例** 假设系统有三个进程$P_1, P_2$和$P_3$，共有12台磁带机。$P_1$需要10台，$P_2$和$P_3$分别需要4台和9台。假设在$T_0$时刻，$P_1, P_2$和$P_3$已分别获得5台、2台和2台，尚有3台未分配，见表2.6。

**表2.6 资源分配**
| 进程名 | 最大需求 | 已分配 | 可用 |
| :--- | :--- | :--- | :--- |
| $P_1$ | 10 | 5 | 3 |
| $P_2$ | 4 | 2 | |
| $P_3$ | 9 | 2 | |

在$T_0$时刻是安全的，因为存在一个安全序列<$P_2, P_1, P_3$>，只要系统按此进程序列分配资源，那么每个进程都能顺利完成。也就是说，当前可用资源数为3，先将2台分配给$P_2$以满足其最大需求，$P_2$结束并归还资源后，系统有5台可用；接下来给$P_1$分配5台以满足其最大需求，$P_1$结束并归还资源后，剩余10台可用；最后分配7台给$P_3$，这样$P_3$也能顺利完成。
若在$T_0$时刻后，系统分配1台给$P_3$，剩余可用资源数为2，此时系统进入不安全状态，因为此时已无法再找到一个安全序列。当系统进入不安全状态后，便可能导致死锁。例如，将剩下的2台分配给$P_2$，这样，$P_2$完成后只能释放4台，既不能满足$P_1$又不能满足$P_3$，致使它们都无法推进到完成，彼此都在等待对方释放资源，陷入僵局，即导致死锁。
若系统处于安全状态，则一定不会发生死锁；若系统进入不安全状态，则有可能发生死锁（处于不安全状态未必就会发生死锁，但发生死锁时一定是处于不安全状态）。

**2. 银行家算法**
**命题追踪** 银行家算法的特点(2013、2019)
**银行家算法**是最著名的死锁避免算法，其思想是：将操作系统视为银行家，操作系统管理的资源视为银行家管理的资金，进程请求资源相当于用户向银行家贷款。进程运行之前先声明对各种资源的最大需求量，其数量不应超过系统的资源总量。当进程在运行中申请资源时，系统必须先确定是否有足够的资源分配给该进程。若有，再进一步试探在将这些资源分配给进程后，是否会使系统处于不安全状态。若不会，则将资源分配给它，否则让进程等待。
(1) 数据结构描述
假设系统中有$n$个进程，$m$类资源，在银行家算法中需要定义下面4个数据结构。
1) **可利用资源向量Available**: 含有$m$个元素的数组，其中每个元素代表一类可用的资源数量。$Available[j]=K$表示此时系统中有$K$个$R_j$类资源可用。
2) **最大需求矩阵Max**: $n×m$矩阵，定义系统中$n$个进程中的每个进程对$m$类资源的最大需求。$Max[i,j]=K$表示进程$P_i$需要$R_j$类资源的最大数量为$K$。
3) **分配矩阵Allocation**: $n×m$矩阵，定义系统中每类资源当前已分配给每个进程的资源数。$Allocation[i,j]=K$表示进程$P_i$当前已分得$R_j$类资源的数量为$K$。
4) **需求矩阵Need**: $n×m$矩阵，表示每个进程接下来最多还需要多少资源。$Need[i,j]=K$表示进程$P_i$还需要$R_j$类资源的数量为$K$。
上述三个矩阵间存在下述关系：
$$
Need = Max - Allocation
$$
通常，Max矩阵和Allocation矩阵是题中的已知条件，而求出Need矩阵是解题的第一步。
(2) 银行家算法描述
设$Request_i$是进程$P_i$的请求向量，$Request_i[j]=K$表示进程$P_i$需要$j$类资源$K$个。当$P_i$发出资源请求后，系统按下述步骤进行检查：
① 若$Request_i[j] \le Need[i,j]$，则转向步骤②；否则认为出错，因为它所需要的资源数已超过它所宣布的最大值。
② 若$Request_i[j] \le Available[j]$，则转向步骤③；否则，表示尚无足够资源，$P_i$必须等待。
③ 系统试探着将资源分配给进程$P_i$，并修改下面数据结构中的数值：
$$
\begin{align*}
Available &= Available - Request_i; \\
Allocation[i, j] &= Allocation[i, j] + Request_i[j]; \\
Need[i, j] &= Need[i, j] - Request_i[j];
\end{align*}
$$
④ 系统执行安全性算法，检查此次资源分配后，系统是否处于安全状态。若安全，才正式将资源分配给进程$P_i$，以完成本次分配；否则，将本次的试探分配作废，恢复原来的资源分配状态，让进程$P_i$等待。
(3) 安全性算法
设置工作向量Work，表示系统中的剩余可用资源数量，它有$m$个元素，在执行安全性算法前，令$Work=Available$。
① 初始时安全序列为空。
② 从Need矩阵中找出符合下面条件的行：该行对应的进程不在安全序列中，而且该行小于或等于Work向量，找到后，将对应的进程加入安全序列；若找不到，则执行步骤④。
③ 进程$P_i$进入安全序列后，可顺利执行，直至完成，并释放分配给它的资源，所以应执行$Work = Work + Allocation[i]$，其中$Allocation[i]$是Allocation矩阵中对应的行，返回步骤②。
④ 若此时安全序列中已有所有进程，则系统处于安全状态，否则系统处于不安全状态。
看完上面对银行家算法的过程描述后，可能有眼花缭乱的感觉，现在通过举例来加深理解。

**3. 安全性算法举例**
**命题追踪** 银行家算法的安全序列分析(2011、2012、2018、2020、2022)
**例** 假定系统中有5个进程{$P_0, P_1, P_2, P_3, P_4$}和三类资源{A, B, C}，各种资源的数量分别为10, 5, 7，在$T_0$时刻的资源分配情况见表2.7。
$T_0$时刻的安全性。利用安全性算法对$T_0$时刻的资源分配进行分析。

**表2.7 $T_0$时刻的资源分配表**
| | **资源情况** | | |
| :--- | :--- | :--- | :--- |
| **进程名** | **Max** | **Allocation** | **Available** |
| | **A B C** | **A B C** | **A B C** |
| $P_0$ | 7 5 3 | 0 1 0 | |
| $P_1$ | 3 2 2 | 2 0 0 (3 0 2) | 3 3 2 (2 3 0) |
| $P_2$ | 9 0 2 | 3 0 2 | |
| $P_3$ | 2 2 2 | 2 1 1 | |
| $P_4$ | 4 3 3 | 0 0 2 | |

① 从题目中我们可以提取 Max 矩阵和 Allocation 矩阵，这两个矩阵相减可得到 Need 矩阵：
$$
\begin{bmatrix} 7 & 5 & 3 \\ 3 & 2 & 2 \\ 9 & 0 & 2 \\ 2 & 2 & 2 \\ 4 & 3 & 3 \end{bmatrix} - \begin{bmatrix} 0 & 1 & 0 \\ 2 & 0 & 0 \\ 3 & 0 & 2 \\ 2 & 1 & 1 \\ 0 & 0 & 2 \end{bmatrix} = \begin{bmatrix} 7 & 4 & 3 \\ 1 & 2 & 2 \\ 6 & 0 & 0 \\ 0 & 1 & 1 \\ 4 & 3 & 1 \end{bmatrix} \\
\quad Max \qquad\qquad Allocation \qquad\quad Need
$$
② 然后，将Work 向量与 Need 矩阵的各行进行比较，找出比 Work 矩阵小的行。例如，在初始时，
$$
\begin{align*}
(3,3,2) &> (1,2,2) \\
(3,3,2) &> (0,1,1)
\end{align*}
$$
对应的两个进程分别为$P_1$和$P_3$，这里我们选择$P_1$（也可选择$P_3$）暂时加入安全序列。
③ 释放$P_1$所占的资源，即将$P_1$进程对应的Allocation 矩阵中的一行与Work 向量相加：
$$
(3\ 3\ 2) + (2\ 0\ 0) = (5\ 3\ 2) = Work
$$
此时需求矩阵更新为（去掉了$P_1$对应的一行）：
```
P₀ [7 4 3]
P₂ [6 0 0]
P₃ [0 1 1]
P₄ [4 3 1]
```
再用更新的 Work 向量和 Need 矩阵重复步骤②。利用安全性算法分析$T_0$时刻的资源分配情况如表2.8所示，最后得到一个安全序列{$P_1, P_3, P_4, P_2, P_0$}。

**表2.8 $T_0$时刻的安全序列的分析**
| | **资源情况** | | | |
| :--- | :--- | :--- | :--- | :--- |
| **进程名** | **Work** | **Need** | **Allocation** | **Work+Allocation** |
| | **A B C** | **A B C** | **A B C** | **A B C** |
| $P_1$ | 3 3 2 | 1 2 2 | 2 0 0 | 5 3 2 |
| $P_3$ | 5 3 2 | 0 1 1 | 2 1 1 | 7 4 3 |
| $P_4$ | 7 4 3 | 4 3 1 | 0 0 2 | 7 4 5 |
| $P_2$ | 7 4 5 | 6 0 0 | 3 0 2 | 10 4 7 |
| $P_0$ | 10 4 7 | 7 4 3 | 0 1 0 | 10 5 7 |

**4. 银行家算法举例**
安全性算法是银行家算法的核心，在银行家算法的题目中，一般会有某个进程的一个资源请求向量，读者只要执行上面所介绍的银行家算法的前三步，马上就会得到更新的 Allocation矩阵和 Need 矩阵，再按照上例的安全性算法判断，就能知道系统能否满足进程提出的资源请求。
**例** 假设当前系统中资源的分配和剩余情况如表2.7所示。
(1) $P_1$请求资源：$P_1$发出请求向量$Request_1(1,0,2)$，系统按银行家算法进行检查：
$Request_1(1,0,2) \le Need_1(1,2,2)$
$Request_1(1,0,2) \le Available(3,3,2)$
系统先假定可为$P_1$分配资源，并修改
Available = Available - $Request_1$ = (2, 3, 0)
Allocation₁ = Allocation₁ + $Request_1$ = (3, 0, 2)
Need₁ = Need₁ - $Request_1$ = (0, 2, 0)
由此形成的资源变化情况如表2.7中的圆括号所示。
* $Work = Available = (2,3,0)$，再利用安全性算法检查此时系统是否安全，如表2.9所示。

**表2.9 $P_1$申请资源时的安全性检查**
| | **资源情况** | | | |
| :--- | :--- | :--- | :--- | :--- |
| **进程名** | **Work** | **Need** | **Allocation** | **Work+Allocation** |
| | **A B C** | **A B C** | **A B C** | **A B C** |
| $P_3$ | 2 3 0 | 0 2 0 | 3 0 2 | 5 3 2 |
| $P_3$ | 5 3 2 | 0 1 1 | 2 1 1 | 7 4 3 |
| $P_4$ | 7 4 3 | 4 3 1 | 0 0 2 | 7 4 5 |
| $P_0$ | 7 4 5 | 7 4 3 | 0 1 0 | 7 5 5 |
| $P_2$ | 7 5 5 | 6 0 0 | 3 0 2 | 10 5 7 |

由所进行的安全性检查得知，可找到一个安全序列{$P_3, P_4, P_0, P_2$}。因此，系统是安全的，可以立即将$P_1$所申请的资源分配给它。分配后系统中的资源情况如表2.10所示。

**表2.10 为$P_1$分配资源后的有关资源数据**
| | **资源情况** | | |
| :--- | :--- | :--- | :--- |
| **进程名** | **Allocation** | **Need** | **Available** |
| | **A B C** | **A B C** | **A B C** |
| $P_0$ | 0 1 0 | 7 4 3 | 2 3 0 |
| $P_1$ | 3 0 2 | 0 2 0 | |
| $P_2$ | 3 0 2 | 6 0 0 | |
| $P_3$ | 2 1 1 | 0 1 1 | |
| $P_4$ | 0 0 2 | 4 3 1 | |

(2) $P_4$请求资源: $P_4$发出请求向量 $Request_4(3,3,0)$, 系统按银行家算法进行检查:
$Request_4(3,3,0) \le Need_4(4,3,1)$;
$Request_4(3,3,0) > Available(2,3,0)$, 让$P_4$等待。
(3) $P_0$请求资源: $P_0$发出请求向量 $Request_0(0,2,0)$, 系统按银行家算法进行检查:
$Request_0(0,2,0) \le Need_0(7,4,3)$;
$Request_0(0,2,0) \le Available(2,3,0)$
系统暂时先假定可为$P_0$分配资源, 并修改有关数据:
Available = Available - $Request_0$ = (2, 1, 0)
Allocation₀ = Allocation₀ + $Request_0$ = (0, 3, 0)
Need₀ = Need₀ - $Request_0$ = (7, 2, 3), 结果如表2.11 所示。

**表2.11 为$P_0$分配资源后的有关资源数据**
| | **资源情况** | | |
| :--- | :--- | :--- | :--- |
| **进程名** | **Allocation** | **Need** | **Available** |
| | **A B C** | **A B C** | **A B C** |
| $P_0$ | 0 3 0 | 7 2 3 | 2 1 0 |
| $P_1$ | 3 0 2 | 0 2 0 | |
| $P_2$ | 3 0 2 | 6 0 0 | |
| $P_3$ | 2 1 1 | 0 1 1 | |
| $P_4$ | 0 0 2 | 4 3 1 | |
进行安全性检查：可用资源 $Available(2,1,0)$ 已不能满足任何进程的需要，系统进入不安全状态，因此拒绝$P_0$的请求，让$P_0$等待，并将 Available, Allocation₀, Need₀恢复为之前的值。

### 2.4.4 死锁检测和解除
**命题追踪** 死锁避免和死锁检测的区分(2015)
前面介绍的死锁预防和避免算法，都是在为进程分配资源时施加限制条件或进行检测，若系统为进程分配资源时不采取任何预防或避免措施，则应该提供死锁检测和解除的手段。

**1. 死锁检测**
**命题追踪** 死锁避免和死锁检测对比(2015)
死锁避免和死锁检测的对比。死锁避免需要在进程的运行过程中一直保证之后不可能出现死锁，因此需要知道进程从开始到结束的所有资源请求。而死锁检测是检测某个时刻是否发生死锁，不需要知道进程在整个生命周期中的资源请求，只需知道对应时刻的资源请求。

**命题追踪** 多在资源竞争时发生死锁的临界条件分析(2016、2021)
可用资源分配图来检测系统所处的状态是否为死锁状态。如图2.15(a)所示，用圆圈表示一个进程，用框表示一类资源。一种类型的资源可能有多个，因此用框中的一个圆表示一类资源中的一个资源。从进程到资源的有向边称为请求边，表示该进程申请一个单位的该类资源；从资源到进程的有向边称为分配边，表示该类资源已有一个资源分配给了该进程。
在图2.15(a)所示的资源分配图中，进程$P_1$已分得了两个$R_1$资源，并又请求一个$R_2$资源；进程$P_2$分得了一个$R_1$资源和一个$R_2$资源，并又请求一个$R_1$资源。
图2.15 资源分配图及其化简过程
(a)
(b)
(c)
简化资源分配图可检测系统状态S是否为死锁状态。简化方法如下：
1) 在资源分配图中，找出既不阻塞又不孤立的进程$P_i$（找出一条有向边与它相连，且该有向边对应资源的申请数量小于或等于系统中已有的空闲资源数量，如在图2.15(a)中，$R_1$没有空闲资源，$R_2$有一个空闲资源。若所有连接该进程的边均满足上述条件，则这个进程能继续运行直至完成，然后释放它所占有的所有资源）。消去它所有的请求边和分配边，使之成为孤立的节点。在图2.15(a)中，$P_2$是满足这一条件的进程节点，将$P_2$的所有边消去，便得到图2.15(b)所示的情况。
这里要注意一个问题，判断某种资源是否有空闲，应该用它的资源数量减去它在资源分配图中的出度，例如在图2.15(a)中，$R_1$的资源数为3，而出度也为3，所以$R_1$没有空闲资源，$R_2$的资源数为2，出度为1，所以$R_2$有一个空闲资源。
2) 进程$P_i$所释放的资源，可以唤醒某些因等待这些资源而阻塞的进程，原来的阻塞进程可能变为非阻塞进程。在图2.15(a)中，$P_2$就满足这样的条件。根据1）中的方法进行一系列简化后，若能消去图中所有的边，则称该图是可完全简化的，如图2.15(c)所示。
S为死锁的条件是当且仅当S状态的资源分配图是不可完全简化的，该条件为**死锁定理**。

**2. 死锁解除**
**命题追踪** 解除死锁的方式(2019)
一旦检测出死锁，就应立即采取相应的措施来解除死锁。死锁解除的主要方法有：
1) **资源剥夺法**。挂起某些死锁进程，并抢占它的资源，将这些资源分配给其他的死锁进程。但应防止被挂起的进程长时间得不到资源而处于资源匮乏的状态。

**注意**
> 在资源分配图中，用死锁定理化简后，还有边相连的那些进程就是死锁进程。

2) **撤销进程法**。强制撤销部分、甚至全部死锁进程，并剥夺这些进程的资源。撤销的原则可以按进程优先级和撤销进程代价的高低进行。这种方式实现简单，但付出的代价可能很大，因为有些进程可能已经接近结束，一旦被终止，以后还得从头再来。
3) **进程回退法**。让一个或多个死锁进程回退到足以回避死锁的地步，进程回退时自愿释放资源而非被剥夺。要求系统保持进程的历史信息，设置还原点。

### 2.4.5 本节小结
本节开头提出的问题的参考答案如下。
**1) 为什么会产生死锁？产生死锁有什么条件？**
由于系统中存在一些不可剥夺资源，当两个或两个以上的进程占有自身的资源并请求对方的资源时，会导致每个进程都无法向前推进，这就是死锁。死锁产生的必要条件有4个，分别是互斥条件、不剥夺条件、请求并保持条件和循环等待条件。
**互斥条件**是指进程要求分配的资源是排他性的，即最多只能同时供一个进程使用。
**不剥夺条件**是指进程在使用完资源之前，资源不能被强制夺走。
**请求并保持条件**是指进程占有自身本来拥有的资源并要求其他资源。
**循环等待条件**是指存在一种进程资源的循环等待链。
**2) 有什么办法可以解决死锁问题？**
死锁的处理策略可以分为预防死锁、避免死锁及死锁的检测与解除。
**死锁预防**是指通过设立一些限制条件，破坏死锁的一些必要条件，让死锁无法发生。
**死锁避免**指在动态分配资源的过程中，用一些算法防止系统进入不安全状态，从而避免死锁。
**死锁的检测和解除**是指在死锁产生前不采取任何措施，只检测当前系统有没有发生死锁，若有，则采取一些措施解除死锁。

### 2.4.6 本节习题精选
**一、单项选择题**
**01. 下列情况中，可能导致死锁的是( )。**
A. 进程释放资源
B. 一个进程进入死循环
C. 多个进程竞争资源出现了循环等待
D. 多个进程竞争使用共享型设备

**02. 在哲学家进餐问题中，若所有哲学家同时拿起左筷子，则发生死锁，因为他们都需要右筷子才能用餐。为了让尽可能多的哲学家可以同时用餐，并且不发生死锁，可以利用信号量PV操作实现同步互斥，下列说法中正确的是( )。**
A. 使用信号量进行控制的方法一定可以避免死锁
B. 同时检查两支筷子是否可用的方法可以预防死锁，但是会导致饥饿问题
C. 限制允许拿起筷子的哲学家数量可以预防死锁，它破坏了“循环等待”条件
D. 对哲学家顺序编号，奇数号哲学家先拿左筷子，然后拿右筷子，而偶数号哲学家刚好相反，可以预防死锁，它破坏了“互斥”条件

**03. 下列关于进程死锁的描述中，错误的是( )。**
A. 若每个进程只能同时申请或拥有一个资源，则不会发生死锁
B. 若多个进程可以无冲突共享访问所有资源，则不会发生死锁
C. 若所有进程的执行严格区分优先级，则不会发生死锁
D. 若进程资源请求之间不存在循环等待，则不会发生死锁

**04. 一次分配所有资源的方法可以预防死锁的发生，它破坏死锁4个必要条件中的( )。**
A. 互斥
B. 占有并请求
C. 非剥夺
D. 循环等待

**05. 系统产生死锁的可能原因是( )。**
A. 独占资源分配不当
B. 系统资源不足
C. 进程运行太快
D. CPU内核太多

**06. 死锁的避免是根据( )采取措施实现的。**
A. 配置足够的系统资源
B. 使进程的推进顺序合理
C. 破坏死锁的四个必要条件之一
D. 防止系统进入不安全状态

**07. 死锁预防是保证系统不进入死锁状态的静态策略，其解决办法是破坏产生死锁的四个必要条件之一。下列方法中破坏了“循环等待”条件的是( )。**
A. 银行家算法
B. 一次性分配策略
C. 剥夺资源法
D. 资源有序分配策略

**08. 可以防止系统出现死锁的手段是( )。**
A. 用PV操作管理共享资源
B. 使进程互斥地使用共享资源
C. 采用资源静态分配策略
D. 定时运行死锁检测程序

**09. 某系统中有三个并发进程都需要四个同类资源，则该系统必然不会发生死锁的最少资源是( )。**
A. 9
B. 10
C. 11
D. 12

**10. 某系统中共有11台磁带机，X个进程共享此磁带机设备，每个进程最多请求使用3台，则系统必然不会死锁的最大X值是( )。**
A. 4
B. 5
C. 6
D. 7

**11. 若系统中有5个某类资源供若干进程共享，则不会引起死锁的情况是( )。**
A. 有6个进程，每个进程需1个资源
B. 有5个进程，每个进程需2个资源
C. 有4个进程，每个进程需3个资源
D. 有3个进程，每个进程需4个资源

**12. 解除死锁通常不采用的方法是( )。**
A. 终止一个死锁进程
B. 终止所有死锁进程
C. 从死锁进程处抢夺资源
D. 从非死锁进程处抢夺资源

**13. 采用资源剥夺法可以解除死锁，还可以采用( )方法解除死锁。**
A. 执行并行操作
B. 撤销进程
C. 拒绝分配新资源
D. 修改信号量

**14. 在下列死锁的解决方法中，属于死锁预防策略的是( )。**
A. 银行家算法
B. 资源有序分配算法
C. 死锁检测算法
D. 资源分配图化简法

**15. 三个进程共享四个同类资源，这些资源的分配与释放只能一次一个。已知每个进程最多需要两个该类资源，则该系统( )。**
A. 有些进程可能永远得不到该类资源
B. 必然有死锁
C. 进程请求该类资源必然能得到
D. 必然是死锁

**16. 以下有关资源分配图的描述中，正确的是( )。**
A. 有向边包括进程指向资源类的分配边和资源类指向进程申请边两类
B. 矩形框表示进程，其中圆点表示申请同一类资源的各个进程
C. 圆圈节点表示资源类
D. 资源分配图是一个有向图，用于表示某时刻系统资源与进程之间的状态

**17. 死锁的四个必要条件中，无法破坏的是( )。**
A. 环路等待资源
B. 互斥使用资源
C. 占有且等待资源
D. 非抢夺式分配

**18. 死锁与安全状态的关系是( )。**
A. 死锁状态有可能是安全状态
B. 安全状态有可能成为死锁状态
C. 不安全状态就是死锁状态
D. 死锁状态一定是不安全状态

**19. 死锁检测时检查的是( )。**
A. 资源有向图
B. 前驱图
C. 搜索树
D. 安全图

**20. 某个系统采用下列资源分配策略。若一个进程提出资源请求得不到满足，而此时没有由于等待资源而被阻塞的进程，则自己就被阻塞。而当此时已有等待资源而被阻塞的进程，则检查所有由于等待资源而被阻塞的进程。若它们有申请进程所需要的资源，则将这些资源取出并分配给申请进程。这种分配策略会导致( )。**
A. 死锁
B. 颠簸
C. 回退
D. 饥饿

**21. 系统的资源分配图在下列情况下，无法判断是否处于死锁状态的有( )。**
I. 出现了环路
II. 没有环路
III. 每种资源只有一个，并出现环路
IV. 每个进程节点至少有一条请求边
A. I、II、III、IV
B. I、III、IV
C. I、IV
D. 以上答案都不正确

**22. 下列关于死锁的说法中，正确的有( )。**
I. 死锁状态一定是不安全状态
II. 产生死锁的根本原因是系统资源分配不足和进程推进顺序非法
III. 资源的有序分配策略可以破坏死锁的循环等待条件
IV. 采用资源剥夺法可以解除死锁，还可以采用撤销进程方法解除死锁
A. I、III
B. II
C. IV
D. 四个说法都对

**23. 下面是并发进程的程序代码，正确的是( )。**
```c
Semaphore x1=x2=y=1;
int c1=c2=0;

P1()
{
    while (1) {
        P(x1);
        if (++c1==1) P(y);
        V(x1);
        computer(A);
        P(x1);
        if (--c1==0) V(y);
        V(x1);
    }
}

P2()
{
    while (1) {
        P(x2);
        if (++c2==1) P(y);
        V(x2);
        computer(B);
        P(x2);
        if (--c2==0) V(y);
        V(x2);
    }
}
```
A. 进程不会死锁，也不会“饥饿”
B. 进程不会死锁，但是会“饥饿”
C. 进程会死锁，但是不会“饥饿”
D. 进程会死锁，也会“饥饿”

**24. 有两个并发进程，对于如下这段程序的运行，正确的说法是( )。**
```c
int x,y,z,t,u;

P1()
{
    while(1) {
        x=1;
        y=0;
        if x>=1 then y=y+1;
        z=y;
    }
}

P2()
{
    while(1) {
        x=0;
        t=0;
        if x<=1 then t=t+2;
        u=t;
    }
}
```
A. 程序能正确运行，结果唯一
B. 程序不能正确运行，可能有两种结果
C. 程序不能正确运行，结果不确定
D. 程序不能正确运行，可能死锁

**25. 一个进程在获得资源后，只能在使用完资源后由自己释放，这属于死锁必要条件的( )。**
A. 互斥条件
B. 请求和释放条件
C. 不剥夺条件
D. 防止系统进入不安全状态

**26. 假设具有5个进程的进程集合P = {$P_0, P_1, P_2, P_3, P_4$}，系统中有三类资源A, B, C, 假设在某时刻有如下状态，见下表。**
| 进程名 | Allocation A B C | Max A B C | Available A B C |
| :--- | :--- | :--- | :--- |
| $P_0$ | 0 0 3 | 0 0 4 | x y z |
| $P_1$ | 1 0 0 | 1 7 5 | |
| $P_2$ | 1 3 5 | 2 3 5 | |
| $P_3$ | 0 0 2 | 0 6 4 | |
| $P_4$ | 0 0 1 | 0 6 5 | |
系统是处于安全状态的，则x,y,z的取值可能是( )。
I. 1,4,0 II. 0,6,2 III. 1,1,1 IV. 0,4,7
A. I、II、IV
B. I、II
C. 仅I
D. I、III

**27. 死锁定理是用于处理死锁的( )方法。**
A. 预防死锁
B. 避免死锁
C. 检测死锁
D. 解除死锁

**28. 某系统有m个同类资源供n个进程共享，若每个进程最多申请k个资源(k>1)，采用银行家算法分配资源，为保证系统不发生死锁，则各进程的最大需求量之和应( )。**
A. 等于m
B. 等于m+n
C. 小于m+n
D. 大于m+n
**29. 采用银行家算法可以避免死锁的发生，这是因为该算法( )。**
A. 可以抢夺已分配的资源
B. 能及时为各进程分配资源
C. 任何时刻都能保证每个进程能得到所需的资源
D. 任何时刻都能保证至少有一个进程可以得到所需的全部资源

**30. 用银行家算法避免死锁时，检测到( )时才分配资源。**
A. 进程首次申请资源时对资源的最大需求量超过系统现存的资源量
B. 进程已占有的资源数与本次申请的资源数之和超过对资源的最大需求量
C. 进程已占有的资源数与本次申请的资源数之和不超过对资源的最大需求量，且现存资源量能满足尚需的最大资源量
D. 进程已占有的资源数与本次申请的资源数之和不超过对资源的最大需求量，且现存资源量能满足本次申请量，但不能满足尚需的最大资源量

**31. 下列各种方法中，可用于解除已发生死锁的是( )。**
A. 撤销部分或全部死锁进程
B. 剥夺部分或全部死锁进程的资源
C. 降低部分或全部死锁进程的优先级
D. A和B都可以

**32. 假定某计算机系统有$R_1$和$R_2$两类可使用资源（其中$R_1$有两个单位，$R_2$有一个单位），它们被进程$P_1$和$P_2$所共享，且已知两个进程均按下列顺序使用两类资源：申请$R_1$→申请$R_2$→申请$R_1$→释放$R_1$→释放$R_2$→释放$R_1$，则在系统运行过程中，( )。**
A. 不可能产生死锁
B. 有可能产生死锁，因为$R_1$资源不足
C. 有可能产生死锁，因为$R_2$资源不足
D. 只有一种进程执行序列可能导致死锁

**33. 【2009统考真题】某计算机系统中有8台打印机，由K个进程竞争使用，每个进程最多需要3台打印机。该系统可能发生死锁的K的最小值是( )。**
A. 2
B. 3
C. 4
D. 5

**34. 【2011统考真题】某时刻进程的资源使用情况见下表，此时的安全序列是( )。**
| 进程名 | 已分配资源 $R_1 R_2 R_3$ | 尚需分配 $R_1 R_2 R_3$ | 可用资源 $R_1 R_2 R_3$ |
| :--- | :--- | :--- | :--- |
| $P_1$ | 2 0 0 | 0 0 1 | 0 2 1 |
| $P_2$ | 1 2 0 | 1 3 2 | |
| $P_3$ | 0 1 1 | 1 3 1 | |
| $P_4$ | 0 0 1 | 2 0 0 | |
A. $P_1, P_2, P_3, P_4$
B. $P_1, P_3, P_2, P_4$
C. $P_1, P_4, P_3, P_2$
D. 不存在

**35. 【2012统考真题】假设5个进程$P_0, P_1, P_2, P_3, P_4$共享三类资源$R_1, R_2, R_3$，这些资源总数分别为18, 6, 22. $T_0$时刻的资源分配情况如下表所示，此时存在的一个安全序列是( )。**
| 进程名 | 已分配资源 $R_1 R_2 R_3$ | 资源最大需求 $R_1 R_2 R_3$ |
| :--- | :--- | :--- |
| $P_0$ | 3 2 3 | 5 5 10 |
| $P_1$ | 4 0 3 | 5 3 6 |
| $P_2$ | 4 0 5 | 4 0 11 |
| $P_3$ | 2 0 4 | 4 2 5 |
| $P_4$ | 3 1 4 | 4 2 4 |
A. $P_0, P_2, P_4, P_1, P_3$
B. $P_1, P_0, P_3, P_4, P_2$
C. $P_2, P_1, P_0, P_3, P_4$
D. $P_3, P_4, P_2, P_1, P_0$

**36. 【2013统考真题】下列关于银行家算法的叙述中，正确的是( )。**
A. 银行家算法可以预防死锁
B. 当系统处于安全状态时，系统中一定无死锁进程
C. 当系统处于不安全状态时，系统中一定会出现死锁进程
D. 银行家算法破坏了死锁必要条件中的“请求和保持”条件

**37. 【2014统考真题】某系统有n台互斥使用的同类设备，三个并发进程分别需要3, 4, 5台设备，可确保系统不发生死锁的设备数n最小为( )。**
A. 9
B. 10
C. 11
D. 12

**38. 【2015统考真题】若系统$S_1$采用死锁避免方法，$S_2$采用死锁检测方法。下列叙述中，正确的是( )。**
I. $S_1$会限制用户申请资源的顺序，而$S_2$不会
II. $S_1$需要进程运行所需的资源总量信息，而$S_2$不需要
III. $S_1$不会给可能导致死锁的进程分配资源，而$S_2$会
A. 仅I、II
B. 仅II、III
C. 仅I、III
D. I、II、III

**39. 【2016统考真题】系统中有3个不同的临界资源$R_1, R_2$和$R_3$，被4个进程$P_1, P_2, P_3, P_4$共享。各进程对资源的需求为：$P_1$申请$R_1$和$R_2$，$P_2$申请$R_2$和$R_3$，$P_3$申请$R_1$和$R_3$，$P_4$申请$R_2$。若系统出现死锁，则处于死锁状态的进程数至少是( )。**
A. 1
B. 2
C. 3
D. 4

**40. 【2018统考真题】假设系统中有4个同类资源，进程$P_1, P_2$和$P_3$需要的资源数分别为4, 3和1，$P_1, P_2$和$P_3$已申请到的资源数分别为2, 1和0，则执行安全性检测算法的结果是( )。**
A. 不存在安全序列，系统处于不安全状态
B. 存在多个安全序列，系统处于安全状态
C. 存在唯一安全序列$P_3, P_1, P_2$，系统处于安全状态
D. 存在唯一安全序列$P_3, P_2, P_1$，系统处于安全状态

**41. 【2019统考真题】下列关于死锁的叙述中，正确的是( )。**
I. 可以通过剥夺进程资源解除死锁
II. 死锁的预防方法能确保系统不发生死锁
III. 银行家算法可以判断系统是否处于死锁状态
IV. 当系统出现死锁时，必然有两个或两个以上的进程处于阻塞态
A. 仅II、III
B. 仅I、II、IV
C. 仅I、II、III
D. 仅I、III、IV

**42. 【2020统考真题】某系统中有A、B两类资源各6个，$t$时刻的资源分配及需求情况如下表所示。**
| 进程名 | A已分配数量 | B已分配数量 | A需求总量 | B需求总量 |
| :--- | :--- | :--- | :--- | :--- |
| $P_1$ | 2 | 3 | 4 | 4 |
| $P_2$ | 2 | 1 | 3 | 1 |
| $P_3$ | 1 | 2 | 3 | 4 |
$t$时刻安全性检测结果是( )。
A. 存在安全序列$P_1、P_2、P_3$
B. 存在安全序列$P_2、P_1、P_3$
C. 存在安全序列$P_2、P_3、P_1$
D. 不存在安全序列

**43. 【2021统考真题】若系统中有$n(n \ge 2)$个进程，每个进程均需要使用某类临界资源2个，则系统不会发生死锁所需的该类资源总数至少是( )。**
A. 2
B. n
C. n+1
D. 2n

**44. 【2022统考真题】系统中有三个进程$P_0、P_1、P_2$及三类资源A、B、C。若某时刻系统分配资源的情况如下表所示，则此时系统中存在的安全序列的个数为( )。**
| 进程名 | 已分配资源数 A B C | 尚需资源数 A B C | 可用资源数 A B C |
| :--- | :--- | :--- | :--- |
| $P_0$ | 2 0 1 | 0 2 1 | 1 3 2 |
| $P_1$ | 0 2 0 | 1 2 3 | |
| $P_2$ | 1 0 1 | 0 1 3 | |
A. 1
B. 2
C. 3
D. 4

**二、综合应用题**
**01. 设系统中有下述解决死锁的方法：**
1) 银行家算法。
2) 检测死锁，终止处于死锁状态的进程，释放该进程占有的资源。
3) 资源预分配。
简述哪种办法允许最大的并发性，即哪种办法允许更多的进程无等待地向前推进。请按“并发性”从大到小对上述三种办法排序。

**02. 某银行计算机系统要实现一个电子转账系统，基本业务流程是：首先对转出方和转入方的账户进行加锁，然后进行转账业务，最后对转出方和转入方的账户进行解锁。若不采取任何措施，系统会不会发生死锁？为什么？请设计一个能够避免死锁的办法。**

**03. 设有进程$P_1$和进程$P_2$并发执行，都需要使用资源$R_1$和$R_2$，使用资源的情况见下表。**
| 进程$P_1$ | 进程$P_2$ |
| :--- | :--- |
| 申请资源$R_1$ | 申请资源$R_2$ |
| 申请资源$R_2$ | 申请资源$R_1$ |
| 释放资源$R_1$ | 释放资源$R_2$ |
| 释放资源$R_2$ | 释放资源$R_1$ |
试判断是否会发生死锁，并解释和说明产生死锁的原因与必要条件。

**04. 系统有同类资源m个，供n个进程共享，若每个进程对资源的最大需求量为k，试问：当m, n, k的值分别为下列情况时（见下表），是否会发生死锁？**
| 序号 | m | n | k | 是否会死锁 | 说明 |
| :--- | :- | :- | :- | :--- | :--- |
| 1 | 6 | 3 | 3 | | |
| 2 | 9 | 3 | 3 | | |
| 3 | 13| 6 | 3 | | |

**05. 有三个进程$P_1, P_2$和$P_3$并发工作。进程$P_1$需要资源$S_3$和资源$S_1$；进程$P_2$需要资源$S_2$和资源$S_1$；进程$P_3$需要资源$S_3$和资源$S_2$。问：**
1) 若对资源分配不加限制，会发生什么情况？为什么？
2) 为保证进程正确运行，应采用怎样的分配策略？列出所有可能的方法。

**06. 某系统有$R_1, R_2$和$R_3$共三种资源，在$T_0$时刻$P_1, P_2, P_3$和$P_4$这四个进程对资源的占用和需求情况见下表，此时系统的可用资源向量为(2, 1, 2)。试问：**
1) 系统是否处于安全状态？若安全，则请给出一个安全序列。
**资源情况**
| 进程名 | 最大资源需求量 $R_1 R_2 R_3$ | 已分配资源数量 $R_1 R_2 R_3$ |
| :--- | :--- | :--- |
| $P_1$ | 3 2 2 | 1 0 0 |
| $P_2$ | 6 1 3 | 4 1 1 |
| $P_3$ | 3 1 4 | 2 1 1 |
| $P_4$ | 4 2 2 | 0 0 2 |
2) 若此时进程$P_1$和进程$P_2$均发出资源请求向量Request(1, 0, 1)，为了保证系统的安全性，应如何分配资源给这两个进程？说明所采用策略的原因。
3) 若2)中两个请求立即得到满足后，系统此刻是否处于死锁状态？

**07. 考虑某个系统在下表时刻的状态。**
| 进程名 | Allocation A B C D | Max A B C D | Available A B C D |
| :--- | :--- | :--- | :--- |
| $P_0$ | 0 0 1 2 | 0 0 1 2 | 1 5 2 0 |
| $P_1$ | 1 0 0 0 | 1 7 5 0 | |
| $P_2$ | 1 3 5 4 | 2 3 5 6 | |
| $P_3$ | 0 0 1 4 | 0 6 5 6 | |
使用银行家算法回答下面的问题：
1) Need矩阵是怎样的？
2) 系统是否处于安全状态？如安全，请给出一个安全序列。
3) 若从进程$P_1$发来一个请求(0, 4, 2, 0)，这个请求能否立刻被满足？如安全，请给出一个安全序列。

**08. 假设具有5个进程的进程集合P = {$P_0, P_1, P_2, P_3, P_4$}，系统中有三类资源A, B, C，假设在某时刻有如下状态：**
| 进程名 | Allocation A B C | Max A B C | Available A B C |
| :--- | :--- | :--- | :--- |
| $P_0$ | 0 0 3 | 0 0 4 | 1 4 0 |
| $P_1$ | 1 0 0 | 1 7 5 | |
| $P_2$ | 1 3 5 | 2 3 5 | |
| $P_3$ | 0 0 2 | 0 6 4 | |
| $P_4$ | 0 0 1 | 0 6 5 | |
当前系统是否处于安全状态？若系统中的可利用资源Available为(0, 6, 2)，系统是否安全？若系统处在安全状态，请给出安全序列；若系统处在非安全状态，简要说明原因。

### 2.4.7 答案与解析
**一、单项选择题**
**01. C**
引起死锁的4个必要条件是：互斥、占有并等待、非剥夺和循环等待。本题中，出现了循环等待的现象，意味着可能导致死锁。进程释放资源不会导致死锁，进程自己进入死循环只能产生“饥饿”，不涉及其他进程。共享型设备允许多个进程申请使用，因此不会造成死锁。再次提醒，死锁一定要有两个或两个以上的进程才会导致，而饥饿可能由一个进程导致。
**02. C**
... (The provided text cuts off here, but a full conversion would continue with all answers.)