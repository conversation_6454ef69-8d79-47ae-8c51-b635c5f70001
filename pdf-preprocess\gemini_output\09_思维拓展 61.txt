```c
}
    s=h->next;          //s 指向前半段的第一个数据结点,即插入点
    q=p->next;          //q 指向后半段的第一个数据结点
    p->next=NULL;
    while(q!=NULL) {
        r=q->next;      //r 指向后半段的下一个结点
        q->next=s->next;//将 q 所指结点插入到 s 所指结点之后
        s->next=q;
        s=q->next;      //s 指向前半段的下一个插入点
        q=r;
    }
}
```
3) 第一步找中间结点的时间复杂度为$O(n)$,第二步逆置的时间复杂度为$O(n)$,第三步合并链表的时间复杂度为$O(n)$,所以该算法的时间复杂度为$O(n)$。

### 归纳总结

本章是算法设计题的重点考查章节,因为线性表的算法题的代码量一般都比较少,又具有一定的算法设计技巧,因此适合笔试考查。考研题中常以三段式的结构命题。
在给出题目背景和要求的情况下:
① 给出算法的基本设计思想。
② 采用C或C++语言描述算法,并给出注释。
③ 分析所设计算法的时间复杂度和空间复杂度。
算法具体的设计思想千变万化,难以从一而定。因此读者一定要勤加练习,反复咀嚼本章的练习题,采用多种方法进行设计并比较它们的复杂度,逐渐熟悉各类题型的思考角度和最佳思路。这里,编者列出几种常用的算法设计技巧,仅供参考:对于链表,经常采用的方法有头插法、尾插法、逆置法、归并法、双指针法等,对具体问题需要灵活变通;对于顺序表,因为可以直接存取,所以经常结合排序和查找的几种算法设计思路进行设计,如归并排序、二分查找等。

**注意**
对于算法设计题,若能写出数据结构类型的定义、正确的算法思想,则至少会给一半的分数;若能用伪代码写出,则自然更好;比较复杂的地方可以直接用文字表达。

### 思维拓展

一个长度为$n$的整型数组$A[1...n]$,给定整数$x$,设计一个时间复杂度不超过$O(n\log n)$的算法,查找出这个数组中所有两两之和等于$x$的整数对(每个元素只输出一次)。
提示:本题若想到排序,则问题便迎刃而解。先用一种时间复杂度为$O(n\log_2 n)$的排序算法将$A[1...n]$从小到大排序,然后分别从数组的小端($i=1$)和大端($j=n$)开始查找: 若$A[i]+A[j]<x$, $i++$;若$A[i]+A[j]>x$, $j--$;否则输出$A[i]$、$A[j]$,然后$i++$,$j--$;直到$i>=j$时停止。
请读者思考本题是否有其他求解算法。

---

## 第3章 栈、队列和数组

**【考纲内容】**
(一) 栈和队列的基本概念
(二) 栈和队列的顺序存储结构
(三) 栈和队列的链式存储结构
(四) 多维数组的存储
(五) 特殊矩阵的压缩存储
(六) 栈、队列和数组的应用

**【知识框架】**
*   线性表
    *   操作受限
        *   栈
            *   顺序栈
            *   链栈
            *   共享栈
    *   推广
        *   队列
            *   循环队列
            *   链式队列
            *   双端队列
        *   数组
            *   一维数组
            *   多维数组:压缩存储、稀疏矩阵

**【复习提示】**
本章通常以选择题的形式考查,题目不算难,但命题的形式比较灵活,其中栈(出入栈的过程、出栈序列的合法性)和队列的操作及其特征是重点。因为它们均是线性表的应用和推广,所以也容易出现在算法设计题中。此外,栈和队列的顺序存储、链式存储及其特点,双端队列的特点,栈和队列的常见应用,以及数组和特殊矩阵的压缩存储都是读者必须掌握的内容。

### 3.1 栈

#### 3.1.1 栈的基本概念

**1. 栈的定义**
**命题追踪** ≫ 栈的特点(2017)
栈(Stack)是只允许在一端进行插入或删除操作的线性表。首先栈是一种线性表,但限定这种线性表只能在某一端进行插入和删除操作,如图3.1所示。