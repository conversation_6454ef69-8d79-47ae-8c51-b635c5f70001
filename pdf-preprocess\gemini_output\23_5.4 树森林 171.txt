```markdown
pmin[k]=pmax[k]=bt.SqBiTNode[k];
for(k=bt.ElemNum-1;k>0;k--){    //从最后一个叶结点向根遍历
    if(bt.SqBiTNode[k]!=-1){
        m=(k-1)/2;                //双亲
        if(k%2==1&&bt.SqBiTNode[m]>pmax[k]) //其为左孩子
            pmin[m]=pmin[k];
        else if(k%2==0&&bt.SqBiTNode[m]<pmin[k]) //其为右孩子
            pmax[m]=pmax[k];
        else return false;
    }
}
return true;
}
```

### 5.4 树、森林

#### 5.4.1 树的存储结构
树的存储方式有多种,既可采用顺序存储结构,又可采用链式存储结构,但无论采用何种存储方式,都要求能唯一地反映树中各结点之间的逻辑关系,这里介绍3种常用的存储结构。

**1. 双亲表示法**

这种存储结构采用一组连续空间来存储每个结点,同时在每个结点中增设一个伪指针,指示其双亲结点在数组中的位置。如图5.20所示,根结点下标为0,其伪指针域为-1。
双亲表示法的存储结构描述如下:
```c
#define MAX_TREE_SIZE 100            //树中最多结点数
typedef struct {                     //树的结点定义
    ElemType data;                   //数据元素
    int parent;                      //双亲位置域
} PTNode;
typedef struct {                     //树的类型定义
    PTNode nodes[MAX_TREE_SIZE];     //双亲表示
    int n;                           //结点数
} PTree;
```



双亲表示法利用了每个结点(根结点除外)只有唯一双亲的性质,可以很快地得到每个结点的双亲结点,但求结点的孩子时则需要遍历整个结构。

---

**注意**
区别树的顺序存储结构与二叉树的顺序存储结构。在树的顺序存储结构中,数组下标代表结点的编号,下标中所存的内容指示了结点之间的关系。而在二叉树的顺序存储结构中,数组下标既代表了结点的编号,又指示了二叉树中各结点之间的关系。当然,二叉树属于树,因此二叉树也可用树的存储结构来存储,但树却不都能用二叉树的存储结构来存储。

**2. 孩子表示法**

孩子表示法是将每个结点的孩子结点视为一个线性表,且以单链表作为存储结构,则$n$个结点就有$n$个孩子链表(叶结点的孩子链表为空表)。而$n$个头指针又组成一个线性表,为便于查找,可采用顺序存储结构。图5.21(a)是图5.20(a)中的树的孩子表示法。



与双亲表示法相反,孩子表示法寻找孩子的操作非常方便,而寻找双亲的操作则需要遍历$n$个结点中孩子链表指针域所指向的$n$个孩子链表。

**3. 孩子兄弟表示法**

孩子兄弟表示法也称二叉树表示法,即以二叉链表作为树的存储结构。孩子兄弟表示法使每个结点包括三部分内容:结点值、指向结点第一个孩子结点的指针,以及指向结点下一个兄弟结点的指针(沿此域可以找到结点的所有兄弟结点),如图5.21(b)所示。
孩子兄弟表示法的存储结构描述如下:
```c
typedef struct CSNode {
    ElemType data;                            //数据域
    struct CSNode *firstchild, *nextsibling;  //第一个孩子和右兄弟指针
} CSNode, *CSTree;
```
孩子兄弟表示法比较灵活,其最大的优点是可以方便地实现树转换为二叉树的操作,易于查找结点的孩子等,但缺点是从当前结点查找其双亲结点比较麻烦。若为每个结点增设一个parent域指向其父结点,则查找结点的父结点也很方便。

#### 5.4.2 树、森林与二叉树的转换
二叉树和树都可以用二叉链表作为存储结构。从物理结构上看,树的孩子兄弟表示法与二叉树的二叉链表表示法是相同的,因此可以用同一存储结构的不同解释将一棵树转换为二叉树。

**1. 树转换为二叉树**

**命题追踪**
▶ 树和二叉树的转换及相关性质的推理(2009、2011)

树转换为二叉树的规则:每个结点的左指针指向它的第一个孩子,右指针指向它在树中的相邻右兄弟,这个规则也称左孩子右兄弟。根结点没有兄弟,因此树转换得到的二叉树没有右子树,如图5.22所示。



树转换为二叉树的画法:
1) 在兄弟结点之间加一连线;
2) 对每个结点,只保留它与第一个孩子的连线,而与其他孩子的连线全部抹掉;
3) 以树根为轴心,顺时针旋转45°。

**2. 森林转换为二叉树**

**命题追踪**
▶ 森林和二叉树的转换及相关性质的推理(2014)

将森林转换为二叉树的规则与树类似。先将森林中的每棵树转换为二叉树,由于任意一棵树对应的二叉树的右子树必空,森林中各棵树的根也可视为兄弟关系,将第二棵树对应的二叉树当作第一棵二叉树根的右子树……以此类推,就可以将森林转换为二叉树。

森林转换为二叉树的画法:
1) 将森林中的每棵树转换成相应的二叉树;
2) 每棵树的根也可视为兄弟关系,在每棵树的根之间加一根连线;
3) 以第一棵树的根为轴心顺时针旋转45°。
或者先在森林中每棵树的根之间加一根连线,然后再采用树转换为二叉树的方法。

**3. 二叉树转换为森林**

**命题追踪**
▶ 由遍历序列构造一棵二叉树并转换为对应的森林(2020、2021)

二叉树转换为森林的规则:若二叉树非空,则二叉树的根及其左子树为第一棵树的二叉树形式,所以将根的右链断开。二叉树根的右子树又可视为一个由除第一棵树外的森林转换后的二叉树,应用同样的方法,直到最后只剩一棵没有右子树的二叉树为止,最后将每棵二叉树依次转换成树,就得到了原森林,如图5.23所示。二叉树转换为树或森林是唯一的。



#### 5.4.3 树和森林的遍历
**1. 树的遍历**

**命题追踪**
▶ 树与二叉树遍历方法的对应关系(2019)

树的遍历是指用某种方式访问树中的每个结点,且仅访问一次。主要有两种方式:
1) 先根遍历。若树非空,则按如下规则遍历:
*   先访问根结点。
*   再依次遍历根结点的每棵子树,遍历子树时仍遵循先根后子树的规则。
其遍历序列与这棵树相应二叉树的先序序列相同。
2) 后根遍历。若树非空,则按如下规则遍历:
*   先依次遍历根结点的每棵子树,遍历子树时仍遵循先子树后根的规则。
*   再访问根结点。
其遍历序列与这棵树相应二叉树的中序序列相同。
图5.22的树的先根遍历序列为ABEFCDG,后根遍历序列为EFBCGDA。
另外,树也有层次遍历,与二叉树的层次遍历思想基本相同,即按层序依次访问各结点。

**2. 森林的遍历**

按照森林和树相互递归的定义,可得到森林的两种遍历方法。
1) 先序遍历森林。若森林为非空,则按如下规则遍历:
*   访问森林中第一棵树的根结点。
*   先序遍历第一棵树中根结点的子树森林。
*   先序遍历除去第一棵树之后剩余的树构成的森林。
2) 中序遍历森林。森林为非空时,按如下规则遍历:
*   中序遍历森林中第一棵树的根结点的子树森林。
*   访问第一棵树的根结点。
*   中序遍历除去第一棵树之后剩余的树构成的森林。
图5.23的森林的先序遍历序列为ABCDEFGHI,中序遍历序列为BCDAFEHIG。

**命题追踪**
▶ 森林与二叉树遍历方法的对应关系(2020)

当森林转换成二叉树时,其第一棵树的子树森林转换成左子树,剩余树的森林转换成右子树,可知森林的先序和中序遍历即其对应二叉树的先序和中序遍历。
树和森林的遍历与二叉树的遍历关系见表5.1。

**表5.1 树和森林的遍历与二叉树遍历的对应关系**

| 树 | 森林 | 二叉树 |
|:---:|:---:|:---:|
| 先根遍历 | 先序遍历 | 先序遍历 |
| 后根遍历 | 中序遍历 | 中序遍历 |

**注意**
森林的遍历方法的命名按照严蔚敏老师的经典教材。有些教材也将森林的中序遍历称为后序遍历,称中序遍历是相对其二叉树而言的,称后序遍历是因为根确实是最后才访问。

#### 5.4.4 本节试题精选
**一、单项选择题**

**01.** 下列关于树的说法中,正确的是( )。
I. 对于有$n$个结点的二叉树,其高度为$log_2n$
II. 完全二叉树中,若一个结点没有左孩子,则它必是叶结点
III. 高度为$h(h>0)$的完全二叉树对应的森林所含的树的个数一定是$h$
IV. 一棵树中的叶子数一定等于与其对应的二叉树的叶子数
A. I和III
B. IV
C. I和II
D. II

**02.** 利用二叉链表存储森林时,根结点的右指针是( )。
A. 指向最左兄弟 B. 指向最右兄弟 C. 一定为空 D. 不一定为空

**03.** 设森林F中有3棵树,第1、2、3棵树的结点个数分别为$M_1$,$M_2$和$M_3$,与森林F对应的二叉树根结点的右子树上的结点个数是( )。
A. $M_1$
B. $M_1+M_2$
C. $M_3$
D. $M_2+M_3$

**04.** 设森林F中有4棵树,第1、2、3、4棵树的结点数分别为a、b、c和d,与森林F对应的二叉树的根结点的左子树上的结点数是( )。
A. a
B. b+c+d
C. a-1
D. a+b+c

**05.** 设森林F对应的二叉树为B,它有m个结点,B的根为p,p的右子树结点数为n,森林F中第一棵树的结点数是( )。
A. m-n
B. m-n-1
C. n+1
D. 条件不足,无法确定

**06.** 设森林F对应的二叉树是一棵具有16个结点的完全二叉树,则森林F中树的数目和结点最多的树的结点数分别是( )。
A. 2和8
B. 2和9
C. 4和8
D. 4和9

**07.** 森林$T=(T_1, T_2, \dots, T_m)$转化为二叉树BT的过程为:若$m=0$,则BT为空,若$m \neq 0$,则( )。
A. 将中间子树$T_{mid}$ ($mid=(1+m)/2$)的根作为BT的根;将$(T_1, T_2, \dots, T_{mid-1})$转换为BT的左子树;将$(T_{mid+1}, \dots, T_m)$转换为BT的右子树
B. 将子树$T_1$的根作为BT的根;将$T_1$的子树森林转换成BT的左子树;将$(T_2, T_3, \dots, T_m)$转换成BT的右子树
C. 将子树$T_1$的根作为BT的根;将$T_1$的左子树森林转换成BT的左子树;将$T_1$的右子树森林转换为BT的右子树;其他以此类推
D. 将森林T的根作为BT的根;将$(T_1, T_2, \dots, T_m)$转化为该根下的结点,得到一棵树,然后将这棵树再转化为二叉树BT

**08.** 设F是一个森林,B是由F变换来的二叉树。若F中有n个非终端结点,则B中右指针域为空的结点有( )个。
A. n-1
B. n
C. n+1
D. n+2

**09.** 设某树的孩子兄弟链表示中共有6个空的左指针域、7个空的右指针域,包括5个结点的左、右指针域都为空,则该树中叶结点的个数是( )。
A. 7
B. 6
C. 5
D. 不能确定

**10.** 若$T_1$是由有序树T转换而来的二叉树,则T中结点的后根序列就是$T_1$中结点的( )序列。
A. 先序
B. 中序
C. 后序
D. 层序

**11.** 某二叉树结点的中序序列为BDAECF,后序序列为DBEFCA,则该二叉树对应的森林包括( )棵树。
A. 1
B. 2
C. 3
D. 4

**12.** 设X是树T中的一个非根结点,B是T所对应的二叉树。在B中,X是其双亲结点的右孩子,下列结论中正确的是( )。
A. 在树T中,X是其双亲结点的第一个孩子
B. 在树T中,X一定无右边兄弟
C. 在树T中,X一定是叶结点
D. 在树T中,X一定有左边兄弟

**13.** 下图是一棵逻辑上的树T,则在关于该树的存储结构的叙述中,错误的是( )。



A. 若T采用双亲表示法,则有9个指向双亲的指针
B. 若T采用孩子表示法,则在T中查找某个结点的孩子比双亲表示法更方便
C. 若T采用孩子兄弟表示法,则在T中查找某个结点的双亲的时间复杂度为O(1)
D. 双亲表示法是顺序存储结构,孩子表示法和孩子兄弟表示法是链式存储结构

**14.** 在森林的二叉树表示中,结点M和结点N是同一父结点的左儿子和右儿子,则在该森林中( )。
A. M和N有同一双亲
B. M和N可能无公共祖先
C. M是N的儿子
D. M是N的左兄弟

**15.** 【2009统考真题】将森林转换为对应的二叉树,若在二叉树中,结点u是结点v的父结点的父结点,则在原来的森林中,u和v可能具有的关系是( )。
Ⅰ. 父子关系 Ⅱ. 兄弟关系 Ⅲ. u的父结点与v的父结点是兄弟关系
A. 只有Ⅱ
B. Ⅰ和Ⅱ
C. Ⅰ和Ⅲ
D. Ⅰ、Ⅱ和Ⅲ

**16.** 【2011统考真题】已知一棵有2011个结点的树,其叶结点个数为116,该树对应的二叉树中无右孩子的结点个数是( )。
A. 115
B. 116
C. 1895
D. 1896

**17.** 【2014统考真题】将森林F转换为对应的二叉树T,F中叶结点的个数等于( )。
A. T中叶结点的个数
B. T中度为1的结点个数
C. T中左孩子指针为空的结点个数
D. T中右孩子指针为空的结点个数

**18.** 【2019统考真题】若将一棵树T转化为对应的二叉树BT,则下列对BT的遍历中,其遍历序列与T的后根遍历序列相同的是( )。
A. 先序遍历
B. 中序遍历
C. 后序遍历
D. 按层遍历

**19.** 【2020统考真题】已知森林F及与之对应的二叉树T,若F的先根遍历序列是a, b, c, d, e, f,后根遍历序列是b, a, d, f, e, c,则T的后序遍历序列是( )。
A. b, a, d, f, e, c
B. b, d, f, e, c, a
C. b, f, e, d, c, a
D. f, e, d, c, b, a

**20.** 【2021统考真题】某森林F对应的二叉树为T,若T的先序遍历序列是a, b, d, c, e, g, f,中序遍历序列是b, d, a, e, g, c, f,则F中树的棵数是( )。
A. 1
B. 2
C. 3
D. 4

**二、综合应用题**

**01.** 给定一棵树的先根遍历序列和后根遍历序列,能否唯一确定一棵树?若能,请举例说明;若不能,请给出反例。

**02.** 将下面一个由3棵树组成的森林转换为二叉树。



**03.** 已知某二叉树的先序序列和中序序列分别为ABDEHCFIMGJKL和DBHEAIMFCGKLJ,请画出这棵二叉树,并画出二叉树对应的森林。

**04.** 编程求以孩子兄弟表示法存储的森林的叶结点数。

**05.** 以孩子兄弟链表为存储结构,请设计递归算法求树的深度。

#### 5.4.5 答案与解析
**一、单项选择题**

**01.** D
若n个结点的二叉树是一棵单支树,则其高度为n。完全二叉树中最多存在一个度为1的结点且只有左孩子,若不存在左孩子,则一定也不存在右孩子,因此必是叶结点,选项Ⅱ正确。只有满二叉树才具有性质III,如下图所示。



在树转换为二叉树时,若有几个叶结点具有共同的双亲,则转换成二叉树后只有一个叶结点(最右边的叶结点),如下图所示,选项IV错误。注意,若树中的任意两个叶结点都不存在相同的双亲,则树中的叶子数才有可能与其对应的二叉树中的叶子数相等。



**02.** D
森林与二叉树具有对应关系,因此,我们存储森林时应先将森林转换成二叉树,转换的方法就是“左孩子右兄弟”,与树不同的是,若存在第二棵树,则二叉链表的根结点的右指针指向的是森林中的第二棵树的根结点。若此森林只有一棵树,则根结点的右指针为空。因此,右指针可能为空也可能不为空。

**03.** D
与树转换为二叉树不同,森林中的每棵树是独立的,因此先要将每棵树的根结点全部视为兄弟结点的关系。森林转换为二叉树后,树2作为树1的根结点的右子树,树3作为树2的根结点的右子树,因此森林F对应的二叉树根结点的右子树上的结点个数是$M_2+M_3$。

**04.** C
森林转换为二叉树后,二叉树的根结点为第1棵树的根结点,二叉树的根结点的左子树包含第1棵树的所有孩子,因此森林F对应的二叉树的根结点的左子树上的结点数是$a-1$。

**05.** A
森林转换成二叉树时采用孩子兄弟表示法,根结点及其左子树为森林中的第一棵树。右子树为其他剩余的树。所以,第一棵树的结点个数为$m-n$。

**06.** D
森林转换为二叉树后,二叉树的根结点及其左子树由第1棵树转换得到,二叉树的根结点的右子树由剩余的森林转换得到,以此类推,可以划分出第2,3,...棵树的结点。具有16个结点的完全二叉树的形态如下图所示,沿二叉树的根结点往右下遍历,共有4个结点,可知森林中有4棵树,其中第1棵树的结点数最多,有9个。



**07.** B
将森林中每棵树的根结点视为兄弟结点的关系,再按照“左孩子右兄弟”的规则来进行转化。

**08.** C
根据森林与二叉树转换规则“左孩子右兄弟”。二叉树B中右指针域为空代表该结点没有兄弟结点。森林中每棵树的根结点从第二个开始依次连接到前一棵树的根的右孩子,因此最后一棵树的根结点的右指针为空。另外,每个非终端结点,其所有孩子结点在转换之后,最后一个孩子的右指针也为空,所以树B中右指针域为空的结点有$n+1$个。

**09.** B
在树的孩子兄弟表示法中,若一个结点没有孩子(叶结点),则表现为该结点的左指针域为空,因此本题答案为“6”。至于“5个结点的左、右指针域都为空”,表示树中有5个结点既没有孩子又没有右兄弟,约束条件比题中的“求叶结点的个数”要求更严格。

**10.** B
有序树T转换成二叉树$T_1$时,T的后根序列是对应$T_1$的中序序列还是后序序列呢(显然树的后根序列不可能对应二叉树的先序序列和层序序列)?看下图所示的例子,在树T中,叶结点B应最先访问,在$T_1$中,B的右兄弟C转换为它的右孩子,若对应$T_1$的后序序列,则C应在B的前面访问,所以T的后根序列不可能对应的后序序列。



**11.** C
根据二叉树的前序序列和中序序列可以确定一棵二叉树。根据后序序列,A是二叉树的根结点。根据中序序列,二叉树的形态如下图(a)所示。对于A的左子树,根据后序序列,B比D后被访问,因此B必为D的父结点,又根据中序序列,D是B的右儿子。对于A的右子树,同理可确定结点E、C、F的关系。此二叉树的形态如下图(b)所示。



再根据二叉树与森林的对应关系,森林中树的棵数即其对应二叉树(向右上旋转45°后)的根结点A及其右兄弟数,或解释为:对应二叉树从根结点A开始不断往右孩子访问,所访问到的结点数。可知此森林中有3棵树,根结点分别为A, C和F。

**12.** D
在二叉树B中,X是其双亲的右孩子,因此在树T中,X必是其双亲结点的右兄弟,换句话说,X在树中必有左兄弟。

**13.** C
若T采用双亲表示法存储,则除根结点外,其余每个结点都有指向其双亲的指针,T共有10个结点,于是有9个指向双亲的指针,选项A正确。若T采用孩子表示法存储,则每个结点的孩子被视为一个线性表,且以单链表作为存储结构,只要遍历该单链表,就能找到某个结点的所有孩子,而双亲表示法要寻找某个结点的孩子,就必须遍历整棵树,选项B正确。若T采用孩子兄弟表示法,则在T中查找某个结点的双亲也必须遍历整棵树,时间复杂度为$O(n)$,选项C错误。选项D显然正确。

**14.** B
在森林的二叉树表示中,当M和N的父结点是二叉树根结点时,M和N在不同的树上。因此M和N可能无公共祖先。

**15.** B
森林与二叉树的转换规则为“左孩子右兄弟”。在最后生成的二叉树中,父子关系在对应森林关系中可能是兄弟关系或者原本就是父子关系。
情形I:若结点v是结点u的第二个孩子结点,转换时,结点v就变成结点u第一个孩子的右孩子,符合要求。情形II:结点u和v是兄弟结点的关系,但二者之中还有一个兄弟结点k,则转换后结点v就变为结点k的右孩子,而结点k则是结点u的右孩子,符合要求。



情形III:若结点u的父结点与v的父结点是兄弟关系,则转换后,结点u和v分别在两者最左父结点的两棵子树中,不可能出现在同一条路径中。



**【另解】**由题意可知u是v的父结点的父结点,如下图所示,有四种情况:



根据树与二叉树的转换规则,将这四种情况转换成树中结点的关系。(1)在原来的树中u是v的父结点的父结点;(2)在树中u是v的父结点;(3)在树中u是v的父结点的兄弟;(4)在树中u与v是兄弟关系。由此可知选项Ⅰ和Ⅱ正确。

**16.** D
树转换为二叉树时,树的每个分支结点的所有子结点中的最右子结点无右孩子,根结点转换后也没有右孩子,因此,对应二叉树中无右孩子的结点个数=分支结点数+1=2011-116+1=1896。
通常本题应采用特殊法求解,设题意中的树是如下图所示的结构,则对应的二叉树中仅有前115个叶结点有右孩子,所以无右孩子的结点个数=2011-115=1896。



**17.** C
将森林转化为二叉树相当于用孩子兄弟表示法来表示森林。在变化过程中,原森林某结点的第一个孩子结点作为它的左子树,它的兄弟作为它的右子树。森林中的叶结点没有孩子结点,转化为二叉树时,该结点就没有左结点,因此F中叶结点的个数等于T中左孩子指针为空的结点个数。此题还可通过一些特例来排除选项A、B和D。

**18.** B
后根遍历树可分为两步:①从左到右访问双亲结点的每个孩子(转化为二叉树后,先访问根结点,再访问右子树);②访问完所有孩子后再访问它们的双亲结点(转化为二叉树后,先访问左子树,再访问根结点),因此树的后根遍历序列与其相应二叉树的中序遍历序列相同。对于此类题,采用特殊值法求解通常会更便捷,左下图树T转换为二叉树BT的过程如下图所示,树的后序遍历序列显然和其相应二叉树的中序遍历序列相同,均为5,6,7,2,3,4,1。



**19.** C
森林F的先根遍历序列对应于其二叉树T的先序遍历序列,森林F的后根遍历序列对应于其二叉树T的中序遍历序列。即T的先序遍历序列为a, b, c, d, e, f,中序遍历序列为b, a, d, f, e, c。根据二叉树T的先序序列和中序序列可以唯一确定它的结构,构造过程如下:



可以得到二叉树T的后序序列为b, f, e, d, c, a。

**20.** C
由二叉树T的先序序列和中序序列可以构造出T,如下图所示。由森林转化成二叉树的规则可知,森林中每棵树的根结点以右子树的方式相连,所以T中的结点a、c、f为F中树的根结点,森林F中有3棵树。



**二、综合应用题**

**01.【解答】**
一棵树的先根遍历结果与其对应二叉树的先序遍历结果相同,树的后根遍历结果与其对应二叉树表示的中序遍历结果相同。二叉树的先序序列和中序序列能够唯一地确定这棵二叉树,因此,根据题目给出的条件,利用树的先根遍历序列和后根遍历序列能够唯一地确定这棵树。例如,对于下图所示的树,对应二叉树的先序序列为1,2,3,4,5,6,8,7,中序序列为3,4,8,6,7,5,2,1。原树的先根遍历序列为1,2,3,4,5,6,8,7,后根遍历序列为3,4,8,6,7,5,2,1。



**注意**
树的先根遍历、后根遍历与对应二叉树的前序遍历、中序遍历对应。

**02.【解答】**
根据树与二叉树“左孩子右兄弟”的转换规则,将森林转换为二叉树的过程如下:①将每棵树的根结点也视为兄弟关系,在兄弟结点之间加一连线。②对每个结点,只保留它与第一个子结点的连线,与其他子结点的连线全部抹掉。③以树根为轴心,顺时针旋转45°。结果如下图所示。



**03.【解答】**
知道二叉树的先序和中序遍历后,可以唯一确定这棵树的结构。然后把二叉树转换到树和森林的方式是,若结点x是双亲y的左孩子,则把x的右孩子、右孩子的右孩子……都与y用连线连起来,最后去掉所有双亲到右孩子的连线。
最后得到的二叉树及对应的森林如下图所示。



**04.【解答】**
当森林(树)以孩子兄弟表示法存储时,若结点没有孩子(fch=NULL),则它必是叶子,总的叶结点个数是孩子子树(fch)上的叶子数和兄弟子树(nsib)上的叶结点个数之和。
算法代码如下:
```c
typedef struct node
{
    ElemType        data;               //数据域
    struct node     *fch, *nsib;        //孩子与兄弟域
}*Tree;
int Leaves(Tree t) {                    //计算以孩子兄弟表示法存储的森林的叶子数
    if (t==NULL)
        return 0;                       //树空返回0
    if (t->fch==NULL)                   //若结点无孩子,则该结点必是叶子
        return 1+Leaves(t->nsib);       //返回叶结点和其兄弟子树中的叶结点数
    else                                //孩子子树和兄弟子树中叶子数之和
        return Leaves(t->fch)+Leaves(t->nsib);
}
```

**05.【解答】**
由孩子兄弟链表表示的树,求高度的算法思想:采用递归算法,若树为空,高度为零;否则,高度为第一子女树高度加1和兄弟子树高度的大者。其非递归算法使用队列,逐层遍历树,取得树的高度。算法代码如下:
```c
int Height(CSTree bt) {                 //递归求以孩子兄弟链表表示的树的深度
    int hc, hs;
    if (bt==NULL)
        return 0;
    else {                              //否则,高度取子女高度+1和兄弟子树高度的大者
        hc=Height(bt->firstchild);      //第一子女树高
        hs=Height(bt->nextsibling);     //兄弟树高
        if(hc+1>hs)
            return hc+1;
        else
            return hs;
    }
}
```

### 5.5 树与二叉树的应用

#### 5.5.1 哈夫曼树和哈夫曼编码
**1. 哈夫曼树的定义**

在介绍哈夫曼树之前,先介绍几个相关的概念:
在许多应用中,树中结点常常被赋予一个表示某种意义的数值,称为该结点的权。
从树的根到一个结点的路径长度与该结点上权值的乘积,称为该结点的带权路径长度。
树中所有叶结点的带权路径长度之和称为该树的带权路径长度,记为
$$
\text{WPL} = \sum_{i=1}^{n}w_il_i
$$
式中,$w_i$是第$i$个叶结点所带的权值,$l_i$是该叶结点到根结点的路径长度。
在含有$n$个带权叶结点的二叉树中,其中带权路径长度(WPL)最小的二叉树称为哈夫曼树,也称最优二叉树。例如,图5.24中的3棵二叉树都有4个叶结点a,b,c,d,分别带权7,5,2,4,它们的带权路径长度分别为
```