## 第2章 进程与线程

**【考纲内容】**
(一) 进程与线程
进程与线程的基本概念；进程/线程的状态与转换
线程的实现：内核支持的线程，线程库支持的线程
进程与线程的组织与控制
进程间通信：共享内存，消息传递，管道，信号
(二) CPU调度与上下文切换
调度的基本概念；调度的目标；
调度的实现：调度器/调度程序(scheduler)，调度的时机与调度方式（抢占式/非抢占式），闲逛进程，内核级线程与用户级线程调度
CPU 调度算法
多处理机调度
上下文及其切换机制
(三) 同步与互斥
同步与互斥的基本概念
基本的实现方法：软件方法；硬件方法
锁；信号量；条件变量
经典同步问题：生产者-消费者问题，读者-写者问题；哲学家进餐问题
(四) 死锁
死锁的基本概念；死锁预防
死锁避免；死锁检测和解除

**【复习提示】**
进程管理是操作系统的核心，也是每年必考的重点。其中，进程的概念、进程调度、信号量机制实现同步和互斥、进程死锁等更是重中之重，必须深入掌握。需要注意的是，除选择题外，本章还容易出综合题，其中信号量机制实现同步和互斥、进程调度算法和死锁等都可能命制综合题，如利用信号量进行进程同步就在往年的统考中频繁出现。

### 2.1 进程与线程
在学习本节时，请读者思考以下问题：
1) 为什么要引入进程？
2) 什么是进程？进程由什么组成？
3) 进程是如何解决问题的？

希望读者带着上述问题去学习本节内容，并在学习的过程中多思考，从而更深入地理解本节内容。进程本身是一个比较抽象的概念，它不是实物，看不见、摸不着，初学者在理解进程概念时存在一定困难，在介绍完进程的相关知识后，我们会用比较直观的例子帮助大家理解。

### 2.1.1 进程的概念和特征
**1. 进程的概念**
在多道程序环境下，允许多个程序并发执行，此时它们将失去封闭性，并具有间断性及不可再现性的特征。为此引入了进程(Process)的概念，以便更好地描述和控制程序的并发执行，实现操作系统的并发性和共享性（最基本的两个特性）。
为了使参与并发执行的每个程序（含数据）都能独立地运行，必须为之配置一个专门的数据结构，称为**进程控制块**(Process Control Block, PCB)。系统利用PCB来描述进程的基本情况和运行状态，进而控制和管理进程。相应地，由程序段、相关数据段和PCB三部分构成了进程实体（也称进程映像）。所谓创建进程，就是创建进程的PCB；而撤销进程，就是撤销进程的PCB。
从不同的角度，进程可以有不同的定义，比较典型的定义有：
1) 进程是一个正在执行程序的实例。
2) 进程是一个程序及其数据从磁盘加载到内存后，在CPU上的执行过程。
3) 进程是一个具有独立功能的程序在一个数据集合上运行的过程。
引入进程实体的概念后，我们可将传统操作系统中的进程定义为：“进程是进程实体的运行过程，是系统进行资源分配和调度的一个独立单位。”
读者要准确理解这里说的系统资源。它指CPU、存储器和其他设备服务于某个进程的“时间”，例如将CPU资源理解为CPU的时间片才是准确的。因为进程是这些资源分配和调度的独立单位，即“时间片”分配的独立单位，这就决定了进程一定是一个动态的、过程性的概念。

**2. 进程的特征**
进程是由多道程序的并发执行而引出的，它和程序是两个截然不同的概念。程序是静态的，进程是动态的，进程的基本特征是对比单个程序的顺序执行提出的。
1) **动态性**。进程是程序的一次执行，它有着创建、活动、暂停、终止等过程，具有一定的生命周期，是动态地产生、变化和消亡的。动态性是进程最基本的特征。
2) **并发性**。指多个进程同存于内存中，能在一段时间内同时运行。引入进程的目的就是使进程能和其他进程并发执行。并发性是进程的重要特征，也是操作系统的重要特征。
3) **独立性**。指进程是一个能独立运行、独立获得资源和独立接受调度的基本单位。凡未建立PCB的程序，都不能作为一个独立的单位参与运行。
4) **异步性**。由于进程的相互制约，使得进程按各自独立的、不可预知的速度向前推进。异步性会导致执行结果的不可再现性，为此在操作系统中必须配置相应的进程同步机制。
通常不会直接考查进程有什么特性，所以读者对上面的4个特性不必记忆，只求理解。

### 2.1.2 进程的组成
进程是一个独立的运行单位，也是操作系统进行资源分配和调度的基本单位。它由以下三部分组成，其中最核心的是进程控制块（PCB）。

**1. 进程控制块**
进程创建时，操作系统为它新建一个PCB，该结构之后常驻内存，任意时刻都可以存取，并在进程结束时删除。PCB是进程实体的一部分，是进程存在的唯一标志。
进程执行时，系统通过其PCB了解进程的现行状态信息，以便操作系统对其进行控制和管理；进程结束时，系统收回其PCB，该进程随之消亡。
当操作系统希望调度某个进程运行时，要从该进程的PCB中查出其现行状态及优先级；在调度到某个进程后，要根据其PCB中所保存的CPU状态信息，设置该进程恢复运行的现场，并根据其PCB中的程序和数据的内存始址，找到其程序和数据；进程在运行过程中，当需要和与之合作的进程实现同步、通信或访问文件时，也需要访问PCB；当进程由于某种原因此暂停运行时，又需将其断点的CPU环境保存在PCB中。可见，在进程的整个生命期中，系统总是通过PCB对进程进行控制的，亦即系统唯有通过进程的PCB才能感知到该进程的存在。
表2.1是一个PCB的实例。PCB主要包括进程描述信息、进程控制和管理信息、资源分配清单和CPU相关信息等。各部分的主要说明如下：

| 进程描述信息 | 进程控制和管理信息 | 资源分配清单 | 处理机相关信息 |
| :--- | :--- | :--- | :--- |
| 进程标识符(PID) | 进程当前状态 | 代码段指针 | 通用寄存器值 |
| 用户标识符(UID) | 进程优先级 | 数据段指针 | 地址寄存器值 |
| | 代码运行入口地址 | 堆栈段指针 | 控制寄存器值 |
| | 程序的外存地址 | 文件描述符 | 标志寄存器值 |
| | 进入内存时间 | 键盘 | 状态字 |
| | CPU占用时间 | 鼠标 | |
| | 信号量使用 | | |

1) **进程描述信息**。进程标识符：标志各个进程，每个进程都有一个唯一的标识号。用户标识符：进程所归属的用户，用户标识符主要为共享和保护服务。
2) **进程控制和管理信息**。进程当前状态：描述进程的状态信息，作为CPU分配调度的依据。进程优先级：描述进程抢占CPU的优先级，优先级高的进程可优先获得CPU。
3) **资源分配清单**，用于说明有关内存地址空间或虚拟地址空间的状况，所打开文件的列表和所使用的输入/输出设备信息。
4) **处理机相关信息**，也称CPU上下文，主要指CPU中各寄存器的值。当进程处于执行态时，CPU的许多信息都在寄存器中。当进程被切换时，CPU状态信息都必须保存在相应的PCB中，以便在该进程重新执行时，能从断点继续执行。
在一个系统中，通常存在着许多进程的PCB，有的处于就绪态，有的处于阻塞态，而且阻塞的原因各不相同。为了方便进程的调度和管理，需要将各个进程的PCB用适当的方法组织起来。目前，常用的组织方式有链接方式和索引方式两种。链接方式将同一状态的PCB链接成一个队列，不同状态对应不同的队列，也可将处于阻塞态的进程的PCB，根据其阻塞原因的不同，排成多个阻塞队列。索引方式将同一状态的进程组织在一个索引表中，索引表的表项指向相应的PCB，不同状态对应不同的索引表，如就绪索引表和阻塞索引表等。

**2. 程序段**
程序段就是能被进程调度程序调度到CPU执行的程序代码段。注意，程序可被多个进程共享，即多个进程可以运行同一个程序。

**3. 数据段**
一个进程的数据段，可以是进程对应的程序加工处理的原始数据，也可以是程序执行时产生的中间或最终结果。

### 2.1.3 进程的状态与转换
进程在其生命周期内，由于系统中各个进程之间的相互制约及系统的运行环境的变化，使得进程的状态也在不断地发生变化。通常进程有以下5种状态，前3种是进程的基本状态。
1) **运行态**。进程正在CPU上运行。在单CPU中，每个时刻只有一个进程处于运行态。
2) **就绪态**。进程获得了除CPU外的一切所需资源，一旦得到CPU，便可立即运行。系统中处于就绪态的进程可能有多个，通常将它们排成一个队列，称为就绪队列。
**命题追踪** 执行中断处理程序时进程的状态(2023)
3) **阻塞态**，也称**等待态**。进程正在等待某一事件而暂停运行，如等待某个资源可用（不包括CPU）或等待I/O完成。即使CPU空闲，该进程也不能运行。系统通常将处于阻塞态的进程也排成一个队列，甚至根据阻塞原因的不同，设置多个阻塞队列。
4) **创建态**。进程正在被创建，尚未转到就绪态。创建进程需要多个步骤：首先申请一个空白PCB，并向PCB中填写用于控制和管理进程的信息；然后为该进程分配运行时所必须的资源；最后将该进程转入就绪态并插入就绪队列。但是，若进程所需的资源尚不能得到满足，如内存不足，则创建工作尚未完成，进程此时所处的状态称为创建态。
5) **终止态**。进程正从系统中消失，可能是进程正常结束或其他原因退出运行。进程需要结束运行时，系统首先将该进程置为终止态，然后进一步处理资源释放和回收等工作。
区分就绪态和阻塞态：就绪态是指进程仅缺少CPU，只要获得CPU就立即运行；而阻塞态是指进程需要其他资源（除了CPU）或等待某一事件。之所以将CPU和其他资源分开，是因为在分时系统的时间片轮转机制中，每个进程分到的时间片是若干毫秒。也就是说，进程得到CPU的时间很短且非常频繁，进程在运行过程中实际上是频繁地转换到就绪态的；而其他资源（如外设）的使用和分配或某一事件的发生（如I/O完成）对应的时间相对来说很长，进程转换到阻塞态的次数也相对较少。这样来看，就绪态和阻塞态是进程生命周期中两个完全不同的状态。
**命题追踪** 引起进程状态转换的事件(2014、2015、2018、2023)
图2.1说明了5种进程状态的转换，而3种基本状态之间的转换如下：
* **就绪态→运行态**：处于就绪态的进程被调度后，获得CPU资源（分派CPU的时间片），于是进程由就绪态转换为运行态。
* **运行态→就绪态**：处于运行态的进程在时间片用完后，不得不让出CPU，从而进程由运行态转换为就绪态。此外，在可剥夺的操作系统中，当有更高优先级的进程就绪时，调度程序将正在执行的进程转换为就绪态，让更高优先级的进程执行。
* **运行态→阻塞态**：进程请求某一资源（如外设）的使用和分配或等待某一事件的发生（如I/O操作的完成）时，它就从运行态转换为阻塞态。进程以系统调用的形式请求操作系统提供服务，这是一种特殊的、由运行用户态程序调用操作系统内核过程的形式。

[图]
创建 --(创建)--> 就绪 --(调度)--> 运行 --(时间到)--> 就绪
运行 --(事件等待)--> 阻塞 --(事件发生)--> 就绪
运行 --(退出)--> 终止
[图 caption]
图2.1 5种进程状态的转换

* **阻塞态→就绪态**：进程等待的事件到来时，如I/O操作完成或中断结束时，中断处理程序必须将相应进程的状态由阻塞态转换为就绪态。
需要注意的是，一个进程从运行态变为阻塞态是主动的行为，而从阻塞态变为就绪态是被动的行为，需要其他相关进程的协助。

### 2.1.4 进程控制
进程控制的主要功能是对系统中的所有进程实施有效的管理，它具有创建新进程、撤销已有进程、实现进程状态转换等功能。在操作系统中，一般将进程控制用的程序段称为**原语**，原语的特点是执行期间不允许中断，它是一个不可分割的基本单位。

**1. 进程的创建**
**命题追踪** 父进程与子进程的关系和特点(2020、2024)
允许一个进程创建另一个进程，此时创建者称为**父进程**，被创建的进程称为**子进程**。子进程可以继承父进程所拥有的资源。当子进程终止时，应将其从父进程那里获得的资源还给父进程。
**命题追踪** 导致创建进程的操作(2010)
在操作系统中，终端用户登录系统、作业调度、系统提供服务、用户程序的应用请求等都会引起进程的创建。操作系统创建一个新进程的过程如下（创建原语）：
**命题追踪** 创建新进程时的操作(2021)
1) 为新进程分配一个唯一的进程标识号，并申请一个空白PCB（PCB是有限的）。若PCB申请失败，则创建失败。
2) 为进程分配其运行所需的资源，如内存、文件、I/O设备和CPU时间等（在PCB中体现）。这些资源或从操作系统获得，或仅从其父进程获得。若资源不足（如内存），则并不是创建失败，而是处于创建态，等待内存资源。
3) 初始化PCB，主要包括初始化标志信息、初始化CPU状态信息和初始化CPU控制信息，以及设置进程的优先级等。
4) 若进程就绪队列能够接纳新进程，则将新进程插入就绪队列，等待被调度运行。

**2. 进程的终止**
引起进程终止的事件主要有：①**正常结束**，表示进程的任务已完成并准备退出运行。②**异常结束**，表示进程在运行时，发生了某种异常事件，使程序无法继续运行，如存储区越界、保护错、非法指令、特权指令错、运行超时、算术运算错、I/O故障等。③**外界干预**，指进程应外界的请求而终止运行，如操作员或操作系统干预、父进程请求和父进程终止。
**命题追踪** 终止进程时的操作(2024)
操作系统终止进程的过程如下（终止原语）：
1) 根据被终止进程的标识符，检索出该进程的PCB，从中读出该进程的状态。
2) 若被终止进程处于运行状态，立即终止该进程的执行，将CPU资源分配给其他进程。
3) 若该进程还有子孙进程，则通常需将其所有子孙进程终止（有些系统无此要求）。
4) 将该进程所拥有的全部资源，或归还给其父进程，或归还给操作系统。
5) 将该PCB从所在队列（链表）中删除。
有些系统不允许子进程在父进程终止的情况下存在，对于这类系统，若一个进程终止，则它的所有子进程也终止，这种现象称为**级联终止**。然而，不是所有操作系统都是这么设计的。

### 2.1.5 进程的通信
**进程通信**是指进程之间的信息交换。PV操作（见2.3节）是低级通信方式，高级通信方式是指以较高的效率传输大量数据的通信方式。高级通信方法主要有以下三类。

**1. 共享存储**
在通信的进程之间存在一块可直接访问的共享空间，通过对这片共享空间进行读/写操作实现进程之间的信息交换，如图2.2所示。在对共享空间进行读/写操作时，需要使用同步互斥工具（如P操作、V操作）对共享空间的读/写进行控制。共享存储又分为两种：低级方式的共享是基于数据结构的共享；高级方式的共享则是基于存储区的共享。操作系统只负责为通信进程提供可共享使用的存储空间和同步互斥工具，而数据交换则由用户自己安排读/写指令完成。
**注意**，进程空间一般都是独立的，进程运行期间一般不能访问其他进程的空间，想让两个进程共享空间，必须通过特殊的系统调用实现，而进程内的线程是自然共享进程空间的。
简单理解就是，甲和乙中间有一个大布袋，甲和乙交换物品是通过大布袋进行的，甲将物品放在大布袋里，乙拿走。但乙不能直接到甲的手中拿东西，甲也不能直接到乙的手中拿东西。

**2. 消息传递**
若通信的进程之间不存在可直接访问的共享空间，则必须利用操作系统提供的消息传递方法实现进程通信。在消息传递系统中，进程间的数据交换以格式化的**消息**(Message)为单位。进程通过操作系统提供的发送消息和接收消息两个原语进行数据交换。这种方式隐藏了通信实现细节，使通信过程对用户透明，简化了通信程序的设计，是当前应用最广泛的进程间通信机制。在微内核操作系统中，微内核与服务器之间的通信就采用了消息传递机制。该机制能很好地支持多CPU系统、分布式系统和计算机网络，因此也成为这些领域最主要的通信工具。
1) **直接通信方式**。发送进程直接将消息发送给接收进程，并将它挂在接收进程的消息缓冲队列上，接收进程从消息缓冲队列中取得消息，如图2.3所示。
2) **间接通信方式**。发送进程将消息发送到某个中间实体，接收进程从中间实体取得消息。这种中间实体一般称为**信箱**。该通信方式广泛应用于计算机网络中。
简单理解就是，甲要将某些事情告诉乙，就要写信，然后通过邮差送给乙。直接通信就是邮差将信直接送到乙的手上；间接通信就是乙家门口有一个邮箱，邮差将信放到邮箱里。

**3. 管道通信**
**命题追踪** 管道通信的特点(2014)
**管道**是一个特殊的共享文件，也称pipe文件，数据在管道中是先进先出的。管道通信允许两个进程按生产者-消费者方式进行通信（见图2.4），只要管道不满，写进程就能向管道的一端写入数据；只要管道非空，读进程就能从管道的一端读出数据。为了协调双方的通信，管道机制必须提供三方面的协调能力：①**互斥**，指当一个进程对管道进行读/写操作时，其他进程必须等待。②**同步**，指写进程向管道写入一定数量的数据后，写进程阻塞，直到读进程取走数据后再将它唤醒；读进程将管道中的数据取空后，读进程阻塞，直到写进程将数据写入管道后才将其唤醒。③**确定对方的存在**。

在Linux中，管道是一种使用非常频繁的通信机制。从本质上说，管道也是一种文件，但它又和一般的文件有所不同，管道可以克服使用文件进行通信的两个问题，具体表现如下：
1) **限制管道的大小**。管道文件是一个固定大小的缓冲区，在Linux中该缓冲区的大小为4KB，这使得它的大小不像普通文件那样不加检验地增长。使用单个固定缓冲区也会带来问题，比如在写管道时可能变满，这种情况发生时，随后对管道的write()调用将默认地被阻塞，等待某些数据被读取，以便腾出足够的空间供write()调用。
2) 读进程也可能工作得比写进程快。当管道内的数据已被读取时，管道变空。当这种情况发生时，一个随后的read()调用将被阻塞，等待某些数据的写入。
管道只能由创建进程所访问，当父进程创建一个管道后，管道是一种特殊文件，子进程会继承父进程的打开文件，因此子进程也继承父进程的管道，并可用它来与父进程进行通信。

**注意**
从管道读数据是一次性操作，数据一旦被读取，就释放空间以便写更多数据。普通管道只允许单向通信，若要实现两个进程双向通信，则需要定义两个管道。

**4. 信号**
**信号**(Signal)是一种用于通知进程发生了某个事件的机制。不同的系统事件对应不同的信号类型，每类信号对应一个序号。例如，Linux定义了30种信号，分别用序号1~30表示。
在进程的PCB中，用至少n位向量记录该进程的待处理信号，如Linux使用一个32位的int型变量表示。若给某个进程发送一个信号，则把该类信号对应的位修改为1。一旦该信号被处理，就把对应的位修改为0。此外，还用另一个n位向量记录被阻塞（被屏蔽）的信号。当某个位为1时，表示该位对应的信号类型将被进程忽略，无须响应。
接下来探讨信号是如何发送的，主要有两种方式：
1) 内核给某个进程发送信号。当内核检测到某个特定的系统事件时，就给进程发送信号。例如，若进程使用非法指令，则内核给该进程发送SIGILL信号（序号为4）。
2) 一个进程给另一个进程发送信号。进程可以调用kill函数，要求内核发送一个信号给目的进程（需要指明接收进程的PID和信号的序号）。当然，进程也可给自己发送信号。
当操作系统把一个进程从内核态切换到用户态时（如系统调用返回时），会检查该进程是否有未被阻塞的待处理信号，若有，则强制进程接收信号，并立即处理信号（若有多个待处理信号，则通常先处理序号更小的信号）。信号的处理方式有两种：
1) **执行默认的信号处理程序**。操作系统为每类信号预设了默认的信号处理程序。例如，收到SIGILL信号的默认操作就是终止进程。
2) **执行进程定义的信号处理程序**。进程可为某类信号自定义信号处理程序。例如，进程可以定义收到SIGILL信号时输出“hello world”，而不是终止进程。
信号处理程序运行结束后，通常会返回进程的下一条指令继续执行。

### 2.1.6 线程和多线程模型
**1. 线程的基本概念**
引入进程的目的是更好地使多道程序并发执行，提高资源利用率和系统吞吐量；而引入**线程**(Threads)的目的则是减小程序在并发执行时所付出的时空开销，提高操作系统的并发性能。
线程最直接的理解就是**轻量级进程**，它是一个基本的CPU执行单元，也是程序执行流的最小单元，由线程ID、程序计数器、寄存器集合和堆栈组成。线程是进程中的一个实体，是被系统独立调度和分派的基本单位，线程自己不拥有系统资源，只拥有一点儿在运行中必不可少的资源，但它可与同属一个进程的其他线程共享进程所拥有的全部资源。一个线程可以创建和撤销另一个线程，同一进程中的多个线程之间可以并发执行。由于线程之间相互制约，致使线程在运行中呈现出间断性。线程也有就绪、阻塞和运行三种基本状态。
引入线程后，进程的内涵发生了改变，进程只作为除CPU外的系统资源的分配单元，而线程则作为CPU的分配单元。由于一个进程内部有多个线程，若线程的切换发生在同一个进程内部，则只需要很少的时空开销。下面从几个方面对线程和进程进行比较。

**2. 线程与进程的比较**
**命题追踪** 进程和线程的比较(2012)
1) **调度**。在传统的操作系统中，拥有资源和独立调度的基本单位都是进程，每次调度都要进行上下文切换，开销较大。在引入线程的操作系统中，线程是独立调度的基本单位，而线程切换的代价远低于进程。在同一进程中，线程的切换不会引起进程切换。但从一个进程中的线程切换到另一个进程中的线程时，会引起进程切换。
2) **并发性**。在引入线程的操作系统中，不仅进程之间可以并发执行，一个进程中的多个线程之间也可并发执行，甚至不同进程中的线程也能并发执行，从而使操作系统具有更好的并发性，提高了系统资源的利用率和系统的吞吐量。
3) **拥有资源**。进程是系统中拥有资源的基本单位，而线程不拥有系统资源（仅有一点必不可少、能保证独立运行的资源），但线程可以访问其隶属进程的系统资源，这主要表现在属于同一进程的所有线程都具有相同的地址空间。要知道，若线程也是拥有资源的单位，则切换线程就需要较大的时空开销，线程这个概念的提出就没有意义。
4) **独立性**。每个进程都拥有独立的地址空间和资源，除了共享全局变量，不允许其他进程访问。某个进程中的线程对其他进程不可见。同一进程中的不同线程是为了提高并发性及进行相互之间的合作而创建的，它们共享进程的地址空间和资源。
5) **系统开销**。在创建或撤销进程时，系统都要为之分配或回收进程控制块（PCB）及其他资源，如内存空间、I/O设备等。操作系统为此所付出的开销，明显大于创建或撤销线程时的开销。类似地，在进程切换时涉及进程上下文的切换，而线程切换时只需保存和设置少量寄存器内容，开销很小。此外，同一进程内的多个线程共享进程的地址空间，因此这些线程之间的同步与通信非常容易实现，甚至无须操作系统的干预。
6) **支持多处理器系统**。对于传统单线程进程，不管有多少个CPU，进程只能运行在一个CPU上。对于多线程进程，可将进程中的多个线程分配到多个CPU上执行。

**3. 线程的属性**
多线程操作系统中的进程已不再是一个基本的执行实体，但它仍具有与执行相关的状态。所谓进程处于“执行”状态，实际上是指该进程中的线程正在执行。线程的主要属性如下：
**命题追踪** 线程所拥有资源的特点(2011、2024)
1) 线程是一个轻型实体，它不拥有系统资源，但每个线程都应有一个唯一的标识符和一个线程控制块，线程控制块记录线程执行的寄存器和栈等现场状态。
2) 不同的线程可以执行相同的程序，即同一个服务程序被不同的用户调用时，操作系统将它们创建成不同的线程。
3) 同一进程中的各个线程共享该进程所拥有的资源。
4) 线程是CPU的独立调度单位，多个线程是可以并发执行的。在单CPU的计算机系统中，各线程可交替地占用CPU；在多CPU的计算机系统中，各线程可同时占用不同的CPU，若各个CPU同时为一个进程内的各线程服务，则可缩短进程的处理时间。
5) 一个线程被创建后，便开始了它的生命周期，直至终止。线程在生命周期内会经历阻塞态、就绪态和运行态等各种状态变化。
为什么线程的提出有利于提高系统并发性？可以这样来理解：由于有了线程，线程切换时，有可能发生进程切换，也有可能不发生进程切换，平均而言每次切换所需的开销就变小了，因此能够让更多的线程参与并发，而不会影响到响应时间等问题。

**4. 线程的状态与转换**
与进程一样，各线程之间也存在共享资源和相互合作的制约关系，致使线程在运行时也具有间断性。相应地，线程在运行时也具有下面三种基本状态。
**执行态**：线程已获得CPU而正在运行。
**就绪态**：线程已具备各种执行条件，只需再获得CPU便可立即执行。
**阻塞态**：线程在执行中因某事件受阻而处于暂停状态。
线程这三种基本状态之间的转换和进程基本状态之间的转换是一样的。

**5. 线程的组织与控制**
**(1) 线程控制块**
**命题追踪** 线程的特点(2019、2024)
与进程类似，系统也为每个线程配置一个线程控制块TCB，用于记录控制和管理线程的信息。线程控制块通常包括：①线程标识符；②一组寄存器，包括程序计数器、状态寄存器和通用寄存器；③线程运行状态，用于描述线程正处于何种状态；④优先级；⑤线程专有存储区，线程切换时用于保存现场等；⑥堆栈指针，用于过程调用时保存局部变量及返回地址等。
同一进程中的所有线程都能访问进程的地址空间和全局变量。但是，每个线程都拥有自己的堆栈，且互不共享（可以这么理解：线程的堆栈被包含在进程的地址空间内，因此同一进程中的各个线程事实上可以访问彼此的堆栈，但编程规范通常不推荐这么做）。

**(2) 线程的创建**
线程也是具有生命期的，它由创建而产生，由调度而执行，由终止而消亡。相应地，在操作系统中就有用于创建线程和终止线程的函数（或系统调用）。
用户程序启动时，通常仅有一个称为初始化线程的线程正在执行，其主要功能是用于创建新线程。在创建新线程时，需要利用一个线程创建函数，并提供相应的参数，如指向线程主程序的入口指针、堆栈的大小、线程优先级等。线程创建函数执行完后，将返回一个线程标识符。

**(3) 线程的终止**
当一个线程完成自己的任务后，或线程在运行中出现异常而要被强制终止时，由终止线程调用相应的函数执行终止操作。但是有些线程（主要是系统线程）一旦被建立，便一直运行而不会被终止。通常，线程被终止后并不立即释放它所占有的资源，只有当进程中的其他线程执行了分离函数后，被终止线程才与资源分离，此时的资源才能被其他线程利用。
被终止但尚未释放资源的线程仍可被其他线程调用，以使被终止线程重新恢复运行。

**6. 线程的实现方式**
**命题追踪** 两种线程的特点与比较(2019)
线程的实现可以分为两类：**用户级线程**(User-Level Thread, ULT)和**内核级线程**(Kernel-Level Thread, KLT)。内核级线程也称内核支持的线程。

**(1) 用户级线程(ULT)**
通俗地说，用户级线程就是“从用户视角能看到的线程”。在用户级线程中，有关线程管理（创建、撤销和切换等）的所有工作都由应用程序在用户空间内（用户态）完成，无须操作系统干预，内核意识不到线程的存在。应用程序可以通过使用线程库设计成多线程程序。通常，应用程序从单线程开始，在该线程中开始运行，在其运行的任何时刻，可以通过调用线程库中的派生例程创建一个在相同进程中运行的新线程。图2.5(a)说明了用户级线程的实现方式。
对于设置了用户级线程的系统，其调度仍然以进程为单位进行，各个进程轮流执行一个时间片。假设进程A包含1个用户级线程，进程B包含100个用户级线程，这样，进程A中线程的运行时间将是进程B中各线程运行时间的100倍，因此对线程来说实质上是不公平的。
这种实现方式的优点如下：①线程切换不需要转换到内核空间，节省了模式切换的开销。②调度算法可以是进程专用的，不同的进程可根据自身的需要，对自己的线程选择不同的调度算法。③用户级线程的实现与操作系统平台无关，对线程管理的代码是属于用户程序的一部分。

这种实现方式的缺点如下：①系统调用的阻塞问题，当线程执行一个系统调用时，不仅该线程被阻塞，进程内的所有线程也都被阻塞。②不能发挥多CPU的优势，内核每次分配给一个进程的仅有一个CPU，因此进程中仅有一个线程能执行。

**(2) 内核级线程(KLT)**
在操作系统中，无论是系统进程还是用户进程，都是在操作系统内核的支持下运行的，与内核紧密相关。内核级线程同样也是在内核的支持下运行的，线程管理的所有工作也是在内核空间内（内核态）实现的。操作系统也为每个内核级线程设置一个线程控制块TCB，内核根据该控制块感知某线程的存在，并对其加以控制。图2.5(b)说明了内核级线程的实现方式。
这种实现方式的优点如下：①能发挥多CPU的优势，内核能同时调度同一进程中的多个线程并行执行。②若进程中的一个线程被阻塞，则内核可以调度该进程中的其他线程占用CPU，也可运行其他进程中的线程。③内核支持线程具有很小的数据结构和堆栈，线程切换比较快、开销小。④内核本身也可采用多线程技术，可以提高系统的执行速度和效率。
这种实现方式的缺点如下：同一进程中的线程切换，需要从用户态转到内核态进行，系统开销较大。这是因为用户进程的线程在用户态运行，而线程调度和管理是在内核实现的。

**(3) 组合方式**
有些系统使用组合方式的多线程实现。在组合实现方式中，内核支持多个内核级线程的建立、调度和管理，同时允许用户程序建立、调度和管理用户级线程。一些内核级线程对应多个用户级线程，这是用户级线程通过时分多路复用内核级线程实现的。同一进程中的多个线程可以同时在多CPU上并行执行，且在阻塞一个线程时不需要将整个进程阻塞，所以组合方式能结合KLT和ULT的优点，并且克服各自的不足。图2.5(c)展示了这种组合实现方式。

在线程实现方式的介绍中，提到了通过线程库来创建和管理线程。**线程库**(thread library)是为程序员提供创建和管理线程的API。实现线程库主要的方法有如下两种：
①在用户空间中提供一个没有内核支持的库。这种库的所有代码和数据结构都位于用户空间中。这意味着，调用库内的一个函数只导致用户空间中的一个本地函数的调用。
②实现由操作系统直接支持的内核级的一个库。对于这种情况，库内的代码和数据结构位于内核空间。调用库中的一个API函数通常会导致对内核的系统调用。
目前使用的三种主要线程库是：POSIX Pthreads、Windows API、Java。Pthreads 作为POSIX标准的扩展，可以提供用户级或内核级的库。Windows API是用于Windows系统的内核级线程库。Java线程API允许线程在Java程序中直接创建和管理。JVM实例通常运行在宿主操作系统之上，Java线程API通常采用宿主系统的线程库来实现，因此在Windows系统中Java线程通常采用Windows API来实现，在类UNIX系统中采用Pthreads来实现。

**7. 多线程模型**
在同时支持用户级线程和内核级线程的系统中，用户级线程和内核级线程连接方式的不同，形成了下面三种不同的多线程模型。
1) **多对一模型**。将多个用户级线程映射到一个内核级线程，如图2.6(a)所示。每个进程只被分配一个内核级线程，线程的调度和管理在用户空间完成。仅当用户线程需要访问内核时，才将其映射到一个内核级线程上，但每次只允许一个线程进行映射。
**优点**：线程管理是在用户空间进行的，无须切换到内核态，因此效率比较高。
**缺点**：若一个线程在访问内核时发生阻塞，则整个进程都会被阻塞；在任何时刻，只有一个线程能够访问内核，多个线程不能同时在多个CPU上运行。
2) **一对一模型**。将每个用户级线程映射到一个内核级线程，如图2.6(b)所示。每个进程有与用户级线程数量相同的内核级线程，线程切换由内核完成，需要切换到内核态。
**优点**：当一个线程被阻塞后，允许调度另一个线程运行，所以并发能力较强。
**缺点**：每创建一个用户线程，相应地就需要创建一个内核线程，开销较大。
3) **多对多模型**。将$n$个用户级线程映射到$m$个内核级线程上，要求$n≥m$，如图2.6(c)所示。
**特点**：既克服了多对一模型并发度不高的缺点，又克服了一对一模型的一个用户进程占用太多内核级线程而开销太大的缺点。此外，还拥有上述两种模型各自的优点。

[多线程模型图]
(a) 多对一模型：多个用户线程映射到一个核心线程。
(b) 一对一模型：每个用户线程映射到一个核心线程。
(c) 多对多模型：多个用户线程映射到少数的几个核心线程。
[图 caption]
图2.6 多线程模型

### 2.1.7 本节小结
本节开头提出的问题的参考答案如下。
1) **为什么要引入进程？**
在多道程序设计的背景下，进程之间需要共享系统资源，因此会导致各程序在执行过程中出现相互制约的关系，程序的执行会表现出间断性等特征。这些特征都是在程序的执行过程中发生的，是动态的过程，而传统的程序本身是一组指令的集合，是静态的概念，无法描述程序在内存中的执行情况，即无法从程序的字面上看出它何时执行、何时停顿，也无法看出它与其他执行程序的关系，因此，程序这个静态概念已不能如实反映程序并发执行过程的特征。为了深刻描述程序动态执行过程的性质乃至更好地支持和管理多道程序的并发执行，便引入了进程的概念。
2) **什么是进程？进程由什么组成？**
进程是一个具有独立功能的程序关于某个数据集合的一次运行活动。它可以申请和拥有系统资源，是一个动态的概念，是一个活动的实体。它不只是程序的代码本身，还包括当前的活动，通过程序计数器的值和处理寄存器的内容来表示。
一个进程实体由程序段、相关数据段和PCB三部分构成，其中PCB是标志一个进程存在的唯一标识，程序段是进程运行的程序的代码，数据段则存储程序运行过程中相关的一些数据。
3) **进程是如何解决问题的？**
进程将能够识别程序运行状态的一些变量存放在PCB中，通过这些变量系统能够更好地了解进程的状况，并在适当时机进行进程的切换，以避免一些资源的浪费，甚至划分为更小的调度单位——线程来提高系统的并发度。

本节主要介绍什么是进程，并围绕这个问题进行一些阐述和讨论，为下一节讨论的内容做铺垫，但之前未学过相关课程的读者可能比较费解，到现在为止对进程这个概念还未形成比较清晰的认识。接下来，我们再用一个比较熟悉的概念来类比进程，以便大家能彻底理解本节的内容到底在讲什么，到底解决了什么问题。
我们用“人的生命历程”来类比进程。首先，人的生命历程一定是一个动态的、过程性的概念，要研究人的生命历程，先要介绍经历这个历程的主体是什么。主体当然是人，相当于经历进程的主体是进程映像，人有自己的身份，相当于进程映像里有PCB；人生历程会经历好几种状态：出生的时候、弥留的时候、充满斗志的时候、发奋图强的时候及失落的时候，相当于进程有创建、撤销、就绪、运行、阻塞等状态，这几种状态会发生改变，人会充满斗志而转向发奋图强，发奋图强获得进步之后又会充满斗志预备下一次发奋图强，或者发奋图强后遇到阻碍会进入失落状态，然后在别人的开导之下又重新充满斗志。类比进程，会由就绪态转向运行态，运行态转向就绪态，或者运行态转向阻塞态，然后在别的进程帮助下返回就绪态。若我们用“人生历程”这个过程的概念去类比进程，则对进程的理解就更深一层。前面生活化的例子可以帮我们理解进程的实质，但它毕竟有不严谨的地方。一种较好的方式是，在类比进程和人生历程后，再看一遍前面较为严谨的书面阐述和讨论，这样对知识的掌握会更加准确而全面。
这里再给出一些学习计算机科学知识的建议。学习时，很多同学会陷入一个误区，即只注重对定理、公式的应用，而忽视对基础概念的理解。这是我们从小到大应付考试而培养出的一个毛病，因为熟练应用公式和定理对考试有立竿见影的效果。公式、定理的应用固然重要，但基础概念的理解能让我们透彻地理解一门学科，更利于我们产生兴趣，培养创造性思维。

### 2.1.8 本节习题精选
**一、单项选择题**
**01.** 一个进程映像是()。
A. 由协处理器执行的一个程序
B. 一个独立的程序+数据集
C. PCB结构与程序和数据的组合
D. 一个独立的程序

**02.** 进程之间交换数据不能通过()途径进行。
A. 共享文件
B. 消息传递
C. 访问进程地址空间
D. 访问共享存储区

**03.** 进程与程序的根本区别是()。
A. 静态和动态特点
B. 是不是被调入内存
C. 是不是具有就绪、运行和等待三种状态
D. 是不是占有处理器

**04.** 下列关于进程的描述中，最不符合操作系统对进程的理解的是()。
A. 进程是在多程序环境中的完整程序
B. 进程可以由程序、数据和PCB描述
C. 线程(Thread)是一种特殊的进程
D. 进程是程序在一个数据集合上的运行过程，它是系统进行资源分配和调度的一个独立单元

**05.** 下列关于并发进程特性的叙述中，正确的是()。
A. 进程是一个动态过程，其生命周期是连续的
B. 并发进程执行完毕后，一定能够得到相同的结果
C. 并发进程对共享变量的操作结果与执行速度无关
D. 并发进程的运行结果具有不可再现性

**06.** 下列关于进程的叙述中，正确的是()。
A. 进程获得处理器运行是通过调度得到的
B. 优先级是进程调度的重要依据，一旦确定就不能改动
C. 在单处理器系统中，任何时刻都只有一个进程处于运行态
D. 进程申请处理器而得不到满足时，其状态变为阻塞态

**07.** 并发进程执行的相对速度是()。
A. 由进程的程序结构决定的
B. 由进程自己来控制的
C. 与进程调度策略有关
D. 在进程被创建时确定的

**08.** 下列任务中，()不是由进程创建原语完成的。
A. 申请PCB并初始化
B. 为进程分配内存空间
C. 为进程分配CPU
D. 将进程插入就绪队列

**09.** 下列关于进程和程序的叙述中，错误的是()。
A. 一个进程在其生命周期中可执行多个程序
B. 一个进程在同一时刻可执行多个程序
C. 一个程序的多次运行可形成多个不同的进程
D. 一个程序的一次执行可产生多个进程

**10.** 下列选项中，导致创建新进程的操作是()。
I. 用户登录
II. 高级调度发生时
III. 操作系统响应用户提出的请求
IV. 用户打开了一个浏览器程序
A. 仅I和IV
B. 仅II和IV
C. I、II和IV
D. 全部

**11.** 操作系统是根据()来对并发执行的进程进行控制和管理的。
A. 进程的基本状态
B. 进程控制块
C. 多道程序设计
D. 进程的优先权

**12.** 在任何时刻，一个进程的状态变化()引起另一个进程的状态变化。
A. 必定
B. 一定不
C. 不一定
D. 不可能

**13.** 在单处理器系统中，若同时存在10个进程，则处于就绪队列中的进程最多有()个。
A. 1
B. 8
C. 9
D. 10

**14.** 一个进程释放了一台打印机，它可能改变()的状态。
A. 自身进程
B. 输入/输出进程
C. 另一个等待打印机的进程
D. 所有等待打印机的进程

**15.** 系统进程所请求的一次I/O操作完成后，将使进程状态从()。
A. 运行态变为就绪态
B. 运行态变为阻塞态
C. 就绪态变为运行态
D. 阻塞态变为就绪态

**16.** 一个进程的基本状态可以从其他两种基本状态转变过去，这个基本的状态一定是()。
A. 运行态
B. 阻塞态
C. 就绪态
D. 终止态

**17.** 在分时系统中，通常处于()的进程最多。
A. 运行态
B. 就绪态
C. 阻塞态
D. 终止态

**18.** 并发进程失去封闭性，是指()。
A. 多个相对独立的进程以各自的速度向前推进
B. 并发进程的执行结果与速度无关
C. 并发进程执行时，在不同时刻发生的错误
D. 并发进程共享变量，其执行结果与速度有关

**19.** 通常用户进程被建立后，()。
A. 便一直存在于系统中，直到被操作人员撤销
B. 随着进程运行的正常或不正常结束而撤销
C. 随着时间片轮转而撤销与建立
D. 随着进程的阻塞或者唤醒而撤销与建立

**20.** 进程在处理器上执行时，()。
A. 进程之间是无关的，具有封闭特性
B. 进程之间都有交互性，相互依赖、相互制约，具有并发性
C. 具有并发性，即同时执行的特性
D. 进程之间可能是无关的，但也可能是有交互性的

**21.** 下列关于父进程和子进程的叙述中，正确的是()。
A. 为了标志父子关系，可让子进程和父进程拥有相同的PID
B. 父进程和子进程是相互独立的，可以并发执行
C. 撤销子进程时，一定会同时撤销父进程
D. 父进程创建了子进程，要等父进程执行完后，子进程才能执行

**22.** 若一个进程实体由PCB、共享正文段、数据堆段和数据栈段组成，请指出下列C语言程序中的内容及相关数据结构各位于哪一段中。
I. 全局赋值变量()
II. 未赋值的局部变量()
III. 函数调用实参传递值()
IV. 用malloc()要求动态分配的存储区()
V. 常量值(如1995、"string")()
VI. 进程的优先级()
A. PCB
B. 正文段
C. 堆段
D. 栈段

**23.** 同一程序经过多次创建，运行在不同的数据集上，形成了()的进程。
A. 不同
B. 相同
C. 同步
D. 互斥

**24.** PCB是进程存在的唯一标志，下列()不属于PCB。
A. 进程ID
B. CPU状态
C. 堆栈指针
D. 全局变量

**25.** 一个计算机系统中，进程的最大数量主要受到()限制。
A. 内存大小
B. 用户数目
C. 打开的文件数
D. 外部设备数量

**26.** 进程创建完成后会进入一个序列，这个序列称为()。
A. 阻塞队列
B. 挂起序列
C. 就绪队列
D. 运行队列

**27.** 进程自身决定()。
A. 从运行态到阻塞态
B. 从运行态到就绪态
C. 从就绪态到运行态
D. 从阻塞态到就绪态

**28.** 下列关于原语操作的叙述中，错误的是()。
A. 操作系统使用原语对进程进行管理和控制
B. 原语在执行过程中不允许被中断
C. 原语在内核态下执行，常驻内存
D. 原语被定义为“原子操作”，意思是其执行速度非常快

**29.** 用信箱实现进程间互通信息的通信机制要有两个通信原语，它们是()。
A. 发送原语和执行原语
B. 就绪原语和执行原语
C. 发送原语和接收原语
D. 就绪原语和接收原语

**30.** 速度最快的进程通信方式是()。
A. 消息传递
B. Socket
C. 共享内存
D. 管道

**31.** 信箱通信是一种()通信方式。
A. 直接通信
B. 间接通信
C. 低级通信
D. 信号量

**32.** 下列关于信号发送和处理的描述中，错误的是()。
A. 一个进程可以给自己发送信号
B. 操作系统的内核可以给进程发送信号
C. 操作系统的内核对每种信号都有默认处理程序
D. 用户可以对每种信号自定义处理函数

**33.** 下列关于信号的处理的描述中，错误的是()。
A. 当进程从内核态转为用户态时，会检查是否有待处理的信号
B. 当进程从用户态转为内核态时，也会检查是否有待处理的信号
C. 操作系统对某些信号的处理是可以忽略的
D. 操作系统允许进程通过系统调用，自定义某些信号的处理程序

**34.** 下面的叙述中，正确的是()。
A. 引入线程后，处理器只能在线程间切换
B. 引入线程后，处理器仍在进程间切换
C. 线程的切换，不会引起进程的切换
D. 线程的切换，可能引起进程的切换

**35.** 下列关于线程的叙述中，正确的是()。
A. 线程包含CPU现场，可以独立执行程序
B. 每个线程都有自己独立的地址空间
C. 每个进程只能包含一个线程
D. 同一进程中的线程间通信也必须使用系统调用函数

**36.** 下面的叙述中，正确的是()。
A. 线程是比进程更小的能独立运行的基本单位，可以脱离进程独立运行
B. 引入线程可提高程序并发执行的程度，可进一步提高系统效率
C. 线程的引入增加了程序执行时的时空开销
D. 一个进程一定包含多个线程

**37.** 下面的叙述中，正确的是()。
A. 同一进程内的线程可并发执行，不同进程的线程只能串行执行
B. 同一进程内的线程只能串行执行，不同进程的线程可并发执行
C. 同一进程或不同进程内的线程都只能串行执行
D. 同一进程或不同进程内的线程都可以并发执行

**38.** 下列选项中，()不是线程的优点。
A. 提高系统并发性
B. 节约系统资源
C. 便于进程通信
D. 增强进程安全性

**39.** 下列关于进程和线程的说法中，正确的是()。
A. 一个进程可以包含一个或多个线程，一个线程可以属于一个或多个进程
B. 多线程技术具有明显的优越性，如速度快、通信简便、设备并行性高等
C. 由于线程不作为资源分配单位，线程之间可以无约束地并行执行
D. 线程也称轻量级进程，因为线程都比进程小

**40.** 在下列描述中，()并不是多线程系统的特长。
A. 利用线程并行地执行矩阵乘法运算
B. Web服务器利用线程响应HTTP请求
C. 键盘驱动程序为每个正在运行的应用配备一个线程，用以响应该应用的键盘输入
D. 基于GUI的调试程序用不同的线程分别处理用户输入、计算和跟踪等操作

**41.** 在进程转换时，下列()转换是不可能发生的。
A. 就绪态→运行态
B. 运行态→就绪态
C. 运行态→阻塞态
D. 阻塞态→运行态

**42.** 当()时，进程从执行状态转变为就绪态。
A. 进程被调度程序选中
B. 时间片到
C. 等待某一事件
D. 等待的事件发生

**43.** 两个合作进程(Cooperating Processes)无法利用()交换数据。
A. 文件系统
B. 共享内存
C. 高级语言程序设计中的全局变量
D. 消息传递系统

**44.** 以下可能导致一个进程从运行态变为就绪态的事件是()。
A. 一次I/O操作结束
B. 运行进程需做I/O操作
C. 运行进程结束
D. 出现了比现在进程优先级更高的进程

**45.** ()必会引起进程切换。
A. 一个进程创建后，进入就绪态
B. 一个进程从运行态变为就绪态
C. 一个进程从阻塞态变为就绪态
D. 以上答案都不对

**46.** 进程处于()时，它处于非阻塞态。
A. 等待从键盘输入数据
B. 等待协作进程的一个信号
C. 等待操作系统分配CPU时间
D. 等待网络数据进入内存

**47.** 一个进程被唤醒，意味着()。
A. 该进程可以重新竞争CPU
B. 优先级变大
C. PCB移动到就绪队列之首
D. 进程变为运行态

**48.** 进程创建时，不需要做的是()。
A. 填写一个该进程的进程表项
B. 分配该进程适当的内存
C. 将该进程插入就绪队列
D. 为该进程分配CPU

**49.** 计算机系统中两个协作进程之间不能用来进行进程间通信的是()。
A. 数据库
B. 共享内存
C. 消息传递机制
D. 管道

**50.** 下面关于用户级线程和内核级线程的描述中，错误的是()。
A. 采用轮转调度算法，进程中设置内核级线程和用户级线程的效果完全不同
B. 跨进程的用户级线程调度也不需要内核参与，控制简单
C. 用户级线程可以在任何操作系统中运行
D. 若系统中只有用户级线程，则CPU的调度对象是进程

**51.** 在内核级线程相对于用户级线程的优点的如下描述中，错误的是()
A. 同一进程内的线程切换，系统开销小
B. 当内核线程阻塞时，CPU将调度同一进程中的其他内核线程执行
C. 内核级线程的程序实体可以在内核态运行
D. 对多处理器系统，核心可以同时调度同一进程的多个线程并行运行

**52.** 下列关于用户级线程相对于内核级线程的优点的描述中，错误的是()
A. 一个线程阻塞不影响另一个线程的运行
B. 线程的调度不需要内核直接参与，控制简单
C. 线程切换代价小
D. 允许每个进程定制自己的调度算法，线程管理比较灵活

**53.** 下列关于用户级线程的优点的描述中，不正确的是()。
A. 线程切换不需要切换到内核态
B. 支持不同的应用程序采用不同的调度算法
C. 在不同操作系统上不经修改就可直接运行
D. 同一个进程内的多个线程可以同时调度到多个处理器上执行

**54.** 下列选项中，可能导致用户级线程切换的事件是()。
A. 系统调用
B. I/O请求
C. 异常处理
D. 线程同步

**55.** 下列关于用户级线程的描述中，错误的是()。
A. 用户级线程由线程库进行管理
B. 用户级线程只有在创建和调度时需要内核的干预
C. 操作系统无法直接调度用户级线程
D. 线程库中线程的切换不会导致进程切换

**56.** 下面的说法中，正确的是()。
A. 不论是系统支持的线程还是用户级线程，其切换都需要内核的支持
B. 线程是资源分配的单位，进程是调度和分派的单位
C. 不管系统中是否有线程，进程都是拥有资源的独立单位
D. 在引入线程的系统中，进程仍是资源调度和分派的基本单位

**57.** 在多对一的线程模型中，当一个多线程进程中的某个线程被阻塞后，()。
A. 该进程的其他线程仍可继续运行
B. 整个进程都将阻塞
C. 该阻塞线程将被撤销
D. 该阻塞线程将永远不可能再执行

**58.** 并发性较好的多线程模型有()。
Ⅰ. 一对一模型
Ⅱ. 多对一模型
Ⅲ. 多对多模型
A. 仅Ⅰ
B. Ⅰ和Ⅱ
C. Ⅰ和Ⅲ
D. Ⅰ、Ⅱ和Ⅲ

**59.** 下列关于多对一模型的叙述中，错误的是()。
A. 一个进程的多个线程不能并行运行在多个处理器上
B. 进程中的用户级线程由进程自己管理
C. 线程切换会导致进程切换
D. 一个线程的系统调用会导致整个进程阻塞

**60.** 【2010统考真题】下列选项中，导致创建新进程的操作是()。
I. 用户登录成功
II. 设备分配
III. 启动程序执行
A. 仅I和II
B. 仅II和III
C. 仅I和III
D. I、II、III

**61.** 【2011统考真题】在支持多线程的系统中，进程P创建的若干线程不能共享的是()。
A. 进程P的代码段
B. 进程P中打开的文件
C. 进程P的全局变量
D. 进程P中某线程的栈指针

**62.** 【2012统考真题】下列关于进程和线程的叙述中，正确的是()。
A. 不管系统是否支持线程，进程都是资源分配的基本单位
B. 线程是资源分配的基本单位，进程是调度的基本单位
C. 系统级线程和用户级线程的切换都需要内核的支持
D. 同一进程中的各个线程拥有各自不同的地址空间

**63.** 【2014统考真题】一个进程的读磁盘操作完成后，操作系统针对该进程必做的是()。
A. 修改进程状态为就绪态
B. 降低进程优先级
C. 给进程分配用户内存空间
D. 增加进程时间片大小

**64.** 【2014统考真题】下列关于管道(Pipe)通信的叙述中，正确的是()。
A. 一个管道可实现双向数据传输
B. 管道的容量仅受磁盘容量大小限制
C. 进程对管道进行读操作和写操作都可能被阻塞
D. 一个管道只能有一个读进程或一个写进程对其操作

**65.** 【2015统考真题】下列选项中，会导致进程从执行态变为就绪态的事件是()。
A. 执行P(wait)操作
B. 申请内存失败
C. 启动I/O设备
D. 被高优先级进程抢占

**66.** 【2018统考真题】下列选项中，可能导致当前进程P阻塞的事件是()。
I. 进程P申请临界资源
II. 进程P从磁盘读数据
III. 系统将CPU分配给高优先权的进程
A. 仅Ⅰ
B. 仅Ⅱ
C. 仅Ⅰ、Ⅱ
D. Ⅰ、Ⅱ、Ⅲ

**67.** 【2019统考真题】下列选项中，可能将进程唤醒的事件是()。
I. I/O结束
II. 某进程退出临界区
III. 当前进程的时间片用完
A. 仅Ⅰ
B. 仅Ⅲ
C. 仅Ⅰ、Ⅱ
D. Ⅰ、Ⅱ、Ⅲ

**68.** 【2019统考真题】下列关于线程的描述中，错误的是()。
A. 内核级线程的调度由操作系统完成
B. 操作系统为每个用户级线程建立一个线程控制块
C. 用户级线程间的切换比内核级线程间的切换效率高
D. 用户级线程可以在不支持内核级线程的操作系统上实现

**69.** 【2020统考真题】下列关于父进程与子进程的叙述中，错误的是()。
A. 父进程与子进程可以并发执行
B. 父进程与子进程共享虚拟地址空间
C. 父进程与子进程有不同的进程控制块
D. 父进程与子进程不能同时使用同一临界资源

**70.** 【2021统考真题】下列操作中，操作系统在创建新进程时，必须完成的是()。
I. 申请空白的进程控制块
II. 初始化进程控制块
III. 设置进程状态为执行态
A. 仅Ⅰ
B. 仅Ⅰ、Ⅱ
C. 仅Ⅰ、Ⅲ
D. 仅Ⅱ、Ⅲ

**71.** 【2022统考真题】下列事件或操作中，可能导致进程P由执行态变为阻塞态的是()。
I. 进程P读文件
II. 进程P的时间片用完
III. 进程P申请外设
IV. 进程P执行信号量的wait()操作
A. 仅Ⅰ、Ⅳ
B. 仅Ⅱ、Ⅲ
C. 仅Ⅲ、Ⅳ
D. 仅Ⅰ、Ⅲ、Ⅳ

**72.** 【2023统考真题】下列操作完成时，导致CPU从内核态转为用户态的是()。
A. 阻塞进程
B. 执行CPU调度
C. 唤醒进程
D. 执行系统调用

**73.** 【2023统考真题】下列由当前线程引起的事件或执行的操作中，可能导致该线程由执行态变为就绪态的是()。
A. 键盘输入
B. 缺页异常
C. 主动出让CPU
D. 执行信号量的wait()操作

**74.** 【2024统考真题】下列选项中，操作系统在终止进程时不一定执行的是()。
A. 终止子进程
B. 回收进程占用的设备
C. 释放进程控制块
D. 回收为进程分配的内存

**75.** 【2024统考真题】若进程P中的线程T先打开文件，得到文件描述符fd，再创建两个线程Ta和Tb，则在下列资源中，Ta与Tb可共享的是()。
I. 进程P的地址空间
II. 线程T的栈
III. 文件描述符fd
A. 仅Ⅰ
B. 仅Ⅰ、Ⅲ
C. 仅Ⅱ、Ⅲ
D. Ⅰ、Ⅱ、Ⅲ

**二、综合应用题**
**01.** 为何进程之间的通信必须借助于操作系统内核功能？简单说明进程通信的几种主要方式。
**02.** 什么是多线程？多线程与多任务有什么区别？
**03.** 回答下列问题：
1) 若系统中没有运行进程，是否一定没有就绪进程？为什么？
2) 若系统中既没有运行进程，又没有就绪进程，系统中是否就没有进程？为什么？
3) 在采用优先级进程调度时，运行进程是否一定是系统中优先级最高的进程？
**04.** 某分时系统中的进程可能出现如下图所示的状态变化，请回答下列问题：
1) 根据图示，该系统应采用什么进程调度策略？
2) 将图中每个状态变化的可能原因填写在下表中。

[状态转换图]
- `运行` 状态，有箭头 `6` 指向 `就绪` 状态（标记为 N）。
- `就绪` 状态（标记为 N），有箭头 `1` 指向 `运行` 状态。
- `运行` 状态有箭头 `2` 指向 `等待磁盘读文件` 状态。
- `等待磁盘读文件` 状态有箭头 `5` 指向 `就绪` 状态。
- `运行` 状态有箭头 `3` 指向 `等待打印机输出` 状态。
- `等待打印机输出` 状态有箭头 `4` 指向 `就绪` 状态。

| 变化 | 原 因 |
| :--- | :--- |
| 1 | |
| 2 | |
| 3 | |
| 4 | |
| 5 | |
| 6 | |

### 2.1.9 答案与解析
**一、单项选择题**
**01. C**
进程映像是PCB、程序段和数据的组合，其中PCB是进程存在的唯一标志。
**02. C**
每个进程包含独立的地址空间，进程各自的地址空间是私有的，只能执行自己地址空间中的程序，且只能访问自己地址空间中的数据，相互访问会导致指针的越界错误（学完内存管理将有更好的认识）。因此，进程之间不能直接交换数据，但可利用操作系统提供的共享文件、消息传递、共享存储区等进行通信。
**03. A**
动态性是进程最重要的特性，以此来区分文件形式的静态程序。操作系统引入进程的概念，是为了从变化的角度动态地分析和研究程序的执行。
**04. A**
进程是一个独立的运行单位，也是操作系统进行资源分配和调度的基本单位，它包括PCB、程序和数据以及执行栈区，仅仅说进程是在多程序环境下的完整程序是不合适的，因为程序是静态的，它以文件形式存放在计算机的硬盘内，而进程是动态的。
**05. D**
并发进程可能因等待资源或因被抢占CPU而暂停运行，其生命周期是不连续的。执行速度会影响进程之间的执行顺序和内存冲突问题，从而导致不同的操作结果。并发进程之间存在相互竞争和制约，导致每次运行可能得到不同的结果，选项D正确。
**06. A**
选项B错在优先级分静态和动态两种，动态优先级是根据运行情况而随时调整的。选项C错在系统发生死锁时有可能进程全部都处于阻塞态，CPU空闲。选项D错在进程申请处理器得不到满足时就处于就绪态，等待处理器的调度。
**07. C**
并发进程执行的相对速度与进程调度策略有关，因为进程调度策略决定了哪些进程可以获得处理机，以及获得处理机的时间长短，从而影响进程执行的速度和效率。
**08. C**
进程创建原语的执行过程：申请空白PCB，并为新进程申请一个唯一的数字标识符。为新进程分配资源，包括内存、I/O设备等。初始化PCB，将新进程插入就绪队列。从上述过程可以看出，为进程分配CPU不是由进程创建原语完成的，而是由进程调度实现的。
**09. B**
一个进程可以顺序地执行一个或多个程序，只要在执行过程中改变其CPU状态和内存空间即可，但不能同时执行多个程序，选项B错误，选项A正确。一个程序可以对应多个进程，即多个进程可以执行同一个程序。例如，同一个文本编辑器可以被多个用户或多个窗口同时运行，每次运行都形成一个新进程。一个程序在执行过程中也可产生多个进程。例如，一个程序可以通过系统调用fork()或create()来创建子进程，从而实现并发处理或分布式计算。选项C和D正确。
**10. D**
用户登录时，操作系统会为用户创建一个登录进程，用于验证用户身份和提供用户界面。高级调度即作业调度，会从后备队列上选择一个作业调入内存，并为之创建相应的进程。操作系统响应用户提出的请求时，通常会为用户创建一个子进程，用于执行用户指定的任务或程序。用户打开一个浏览器程序时，也是一种操作系统响应用户请求的情况，同样会创建一个新进程。
**11. B**
在进程的整个生命周期中，系统总是通过其PCB对进程进行控制。也就是说，系统是根据进程的PCB而非任何其他因素来感知到进程存在的，PCB是进程存在的唯一标志。同时PCB常驻内存。A和D选项的内容都包含在进程PCB中。
**12. C**
一个进程的状态变化可能引起另一个进程的状态变化。例如，一个进程时间片用完，可能引起另一个就绪进程的运行。同时，一个进程的状态变化也可能不会引起另一个进程的状态变化。例如，一个进程由阻塞态转变为就绪态就不会引起其他进程的状态变化。
**13. C**
在单处理器系统中，不可能出现10个进程都处于就绪态的情况。但9个进程处于就绪态、1个进程处于运行态是可能的。此外还要想到，可能10个进程都处于阻塞态。
**14. C**
由于打印机是独占资源，当一个进程释放打印机后，另一个等待打印机的进程就可能从阻塞态转到就绪态。当然，也存在一个进程执行完毕后由运行态转为终止态时释放打印机的情况，但这并不是由于释放打印机引起的，相反是因为运行完成才释放了打印机。
**15. D**
I/O操作完成之前进程在等待结果，状态为阻塞态；完成后进程等待事件就绪，变为就绪态。
**16. C**
只有就绪态可以既由运行态转变过去又能由阻塞态转变过去。时间片到，运行态变为就绪态；当所需要资源到达时，进程由阻塞态转变为就绪态。
**17. B**
分时系统中处于就绪态的进程最多，这些进程都在争夺CPU的使用权，而CPU的数量是有限的。处于运行态的进程只能有一个或少数几个。处于阻塞态的进程也不会太多，阻塞事件的发生频率不会太高。处于终止态的进程也不多，这些进程已释放资源，不再占用内存空间。
**18. D**
程序封闭性是指进程执行的结果只取决于进程本身，不受外界影响。也就是说，进程在执行过程中不管是不停顿地执行，还是走走停停，进程的执行速度都不会改变它的执行结果。失去封闭性后，不同速度下的执行结果不同。
**19. B**
进程有它的生命周期，不会一直存在于系统中，也不一定需要用户显式地撤销。进程在时间片结束时只是就绪，而不是撤销。阻塞和唤醒是进程生存期的中间状态。进程可在完成时撤销，或在出现内存错误等时撤销。
**20. D**
选项A和B都说得太绝对，进程之间可能具有相关性，也可能是相互独立的。选项C错在“同时”。
**21. B**
虽然父进程创建了子进程，它们有一定的关系，但仍然是两个不同的进程，拥有各自的PID，选项A错误。父进程和子进程是相互独立的，两个进程能并发执行，且互不影响，选项B正确，选项D错误。撤销一个进程并不一定会导致另一个进程也被撤销，父进程撤销后，子进程可能有两种状态：①子进程一并被终止；②子进程成为孤儿进程，被init进程领养。子进程撤销不会导致父进程撤销，C错误。
**22. B、D、D、C、B、A**
C语言编写的程序在使用内存时一般分为三个段：正文段（代码和赋值数据段）、数据堆段和数据栈段。二进制代码和常量存放在正文段，动态分配的存储区存放在数据堆段，临时使用的变量存放在数据栈段。由此，我们可以确定全局赋值变量在正文段赋值数据段，未赋值的局部变量和实参传递在栈段，动态内存分配在堆段，常量在正文段，进程的优先级只能在PCB内。
**23. A**
进程是程序的一次执行过程，它不仅包括程序的代码，还包括程序的数据和状态。同一个程序经过多次创建，运行在不同的数据集上，会形成不同的进程，它们之间没有必然的联系。
**24. D**
进程实体主要是代码、数据和PCB。因此，要清楚了解PCB内所含的数据结构内容，主要有四大类：进程标志信息、进程控制信息、进程资源信息、CPU现场信息。由此可知，全局变量与PCB无关，而只与用户代码有关。
**25. A**
进程创建需要占用系统内存来存放PCB的数据结构，因此一个系统能够创建的进程总数是有限的，进程的最大数量取决于系统内存的大小，它在系统安装时就已确定（若后期内存增加，则系统能够创建的进程总数也会增加）。而用户数量、外设数量和文件等均与此无关。
**26. C**
我们先要考虑创建进程的过程，当该进程所需的资源分配完成而只等CPU时，进程的状态为就绪态，因此所有的就绪PCB一般以链表方式链成一个序列，称为就绪队列。
**27. A**
只有从运行态到阻塞态的转换是由进程自身决定的。从运行态到就绪态的转换是由于进程的时间片用完，“主动”调用程序转向就绪态。虽然从就绪态到运行态的转换同样是由调度程序决定的，但进程是“被动的”。从阻塞态到就绪态的转换是由协作进程决定的。
**28. D**
原语是由若干条指令组成的、用于实现某个特定功能的程序段。它与一般的程序的区别如下：它是“原子操作”，即一个操作中的所有动作要么全做，要么全不做，在执行过程中不允许被中断，因此“原子操作”并不是指执行速度快，选项D错误。对进程的管理和控制功能是通过各种原语实现的，如创建原语等。原语是操作系统内核的组成部分，它常驻内存，且在内核态下执行。
**29. C**
用信箱实现进程间互通信息的通信机制要有两个通信原语，它们是发送原语和接收原语。
**30. C**
消息传递需要在内核和用户空间中进行数据的拷贝，而且需要对消息进行格式化和排队，这些都会增加通信的开销。套接字(Socket)通常用于不同机器之间的进程通信，需要经过传输层以下的协议栈，而且可能涉及数据的加密和压缩，这些都会降低通信的速度。共享内存允许多个进程直接访问同一块物理内存，不需要任何数据的拷贝和中介，是最快的进程通信方式。管道需要在内核和用户空间进行数据的拷贝，而且一般是单向传输，降低了通信的效率。
**31. B**
信箱通信属于消息传递中的间接通信方式，因为信箱通信借助于收发双方进程之外的共享数据结构作为通信中转，发送方和接收方不直接建立联系，没有处理时间上的限制，发送方可以在任何时间发送信息，接收方也可以在任何时间接收信息。
**32. D**
有些信号是不能被用户自定义处理函数的，只能执行操作系统默认的处理程序，选项D错误。
**33. B**
信号的处理时机只会在进程从内核态转为用户态时。当进程从用户态转为内核态时，不会检查是否有待处理的信号，选项B错误。操作系统对某些信号的默认处理可能就是忽略。
**34. D**
在同一进程中，线程的切换不会引起进程的切换。当从一个进程中的线程切换到另一个进程中的线程时，才会引起进程的切换，因此选项A、B、C错误。
**35. A**
线程的CPU现场是指线程运行时所需的一组寄存器的值，包括程序计数器、状态寄存器、通用寄存器和栈指针等。当线程切换时，操作系统会保存当前线程的CPU现场，并恢复下一个线程的CPU现场。线程是CPU调度的基本单位，当然可以独立执行程序，选项A正确。线程没有自己独立的地址空间，它共享其所属进程的空间，选项B错误。进程可以创建多个线程，选项C错误。同一个进程中的线程间通信可以直接通过它们共享的存储空间，选项D错误。
**36. B**
线程是进程内一个相对独立的执行单元，但不能脱离进程单独运行，只能在进程中运行。引入线程是为了减少程序执行时的时空开销。一个进程可包含一个或多个线程。
**37. D**
同一个进程或不同进程内的线程可以并发执行，并发是指多个线程在一段时间内交替执行，而不一定是同时执行的。在多核CPU中，同一个进程或不同进程内的线程可以并行执行，并行是指多个线程在同一时刻同时执行。若实现了并行，则一定也实现了并发。
**38. D**
线程的优点有提高系统并发性、节约系统资源、便于进程通信等，但线程并不能增强进程安全性，因为线程共享进程的地址空间和资源，若一个线程出错，则可能影响整个进程的运行。
**39. B**
一个进程可以包含一个或多个线程，但一个线程只能属于一个进程，选项A错误。线程共享进程的资源，但线程之间不能无约束地并行执行，因为线程之间还需要进行同步和互斥，以免造成数据的不一致和冲突，选项C错误。线程也称轻量级进程，但并不能说所有线程都比进程小，选项D错误。
**40. C**
整个系统只有一个键盘，而且键盘输入是人的操作，速度比较慢，完全可以使用一个线程来处理整个系统的键盘输入。
**41. D**
阻塞的进程在获得所需资源时只能由阻塞态转变为就绪态，并插入就绪队列，而不能直接转变为运行态。
**42. B**
当进程的时间片到时，进程由运行态转变为就绪态，等待下一个时间片的到来。
**43. C**
不同的进程拥有不同的代码段和数据段，全局变量是对同一进程而言的，在不同的进程中是不同的变量，没有任何联系，所以不能用于交换数据。此题也可用排除法做，选项A、B、D均是课本上所讲的。管道是一种文件。
**44. D**
进程处于运行态时，它必须已获得所需的资源，在运行结束后就撤销。只有在时间片到或出现了比现在进程优先级更高的进程时才转变成就绪态。选项A使进程从阻塞态到就绪态，选项B使进程从运行态到阻塞态，选项C使进程撤销。
**45. B**
进程切换是指CPU调度不同的进程执行，当一个进程从运行态变为就绪态时，CPU调度另一个进程执行，引起进程切换。
**46. C**
进程有三种基本状态，处于阻塞态的进程由于某个事件不满足而等待。这样的事件一般是I/O操作，如键盘等，或是因互斥或同步数据引起的等待，如等待信号或等待进入互斥临界区代码段等，等待网络数据进入内存是为了进程同步。而等待CPU调度的进程处于就绪态，只有它是非阻塞态。
**47. A**
当一个进程被唤醒时，这个进程就进入了就绪态，等待进程调度而占有CPU运行。进程被唤醒在某种情形下优先级可以增大，但一般不会变为最大，而由固定的算法来计算。也不会在唤醒后位于就绪队列的队首，就绪队列是按照一定的规则赋予其位置的，如先来先服务，或者高优先级优先，或者短进程优先等，更不能直接占有处理器运行。
**48. D**
进程创建原语完成的工作是：向系统申请一个空闲PCB，为被创建进程分配必要的资源，然后将其PCB初始化，并将此PCB插入就绪队列，最后返回一个进程标志号。当调度程序为进程分配CPU后，进程开始运行。所以进程创建的过程中不会包含分配CPU的过程，这不是进程创建者的工作，而是调度程序的工作。
**49. A**
进程之间的通信方式主要有管道、消息传递、共享内存、文件映射和套接字等。数据库不能直接作为进程之间的通信方式。
**50. B**
用户级线程的调度仍以进程为单位，各个进程轮流执行一个时间片，假设进程A包含1个用户级线程，而进程B包含100个用户级线程，此时进程A中单个线程的运行时间将是进程B中各个线程平均运行时间的100倍；内核级线程的调度是以线程为单位的，各个线程轮流执行一个时间片，同样假设进程A包含1个内核级线程，而进程B包含100个内核级线程，此时进程B的运行时间将是进程A的100倍，选项A正确。用户级线程的调度单位是进程，跨进程的线程调度需要内核支持，选项B错误。用户级线程是由用户程序或函数库实现的，不依赖于操作系统的支持，选项C正确。用户级线程对操作系统是透明的，CPU调度的对象仍然是进程，选项D正确。
**51. A**
在内核级线程中，同一进程中的线程切换，需要从用户态转到内核态进行，系统开销较大，选项A错误。CPU调度是在内核中进行的，在内核级线程中，调度是在线程一级进行的，因此内核可以同时调度同一进程的多个线程在多CPU上并行运行（用户级线程则不行），选项B正确、选项D正确。内核级线程可以在内核态执行系统调用子程序，直接利用系统调用为它服务，因此选项C正确。注意，用户级线程是在用户空间中实现的，不能直接利用系统调用获得内核的服务，当用户级线程要获得内核服务时，必须借助于操作系统的帮助，因此用户级线程只能在用户态运行。
**52. A**
进程中的某个用户级线程被阻塞，则整个进程也被阻塞，即进程中的其他用户级线程也被阻塞，选项A错误。用户级线程的调度是在用户空间进行的，节省了模式切换的开销，不同进程可以根据自身的需要，对自己的线程选择不同的调度算法，因此选项B、C和D都正确。
**53. D**
用户级线程是不需要内核支持而在用户程序中实现的线程，不能利用多处理器的并行性，因为操作系统只能看到进程。其余说法均正确。
**54. D**
本题可用排除法。用户级线程的切换是由应用程序自己控制的，不需要操作系统的干预，操作系统感受不到用户级线程的存在。因此，系统调用、I/O请求和异常处理这些涉及内核态的事件都不会导致用户级线程切换，但会导致内核级线程切换。线程同步是指多个线程之间协调执行顺序的机制，如互斥锁、信号量、条件变量等。当一个线程在等待同步条件时，应用程序可以选择切换到另一个就绪的用户级线程，以提高CPU的利用率。
**55. B**
用户级线程不依赖于操作系统内核，而是由用户程序自己实现的，选项A正确。用户级线程的创建和调度都是在用户态下实现的，不需要切换到内核态，选项B错误。操作系统只能看到一个单线程进程，而不知道进程内部有多个用户级线程，选项C正确。线程库中线程的切换只涉及用户栈和寄存器等上下文的保存和恢复，不涉及内核栈和页表等内核上下文的切换，选项D正确。
**56. C**
引入线程后，进程仍然是资源分配的单位。内核级线程是处理器调度和分派的单位，线程本身不具有资源，它可以共享所属进程的全部资源，选项C正确，选项B、D明显错误。至于选项A，可以这样来理解：假如有一个内核进程，它映射到用户级后有多个线程，那么这些线程之间的切换不需要在内核级切换进程，也就不需要内核的支持。
**57. B**
在多对一的线程模型中，只有一个内核级线程，用户级线程的“多”对操作系统透明，因此操作系统内核只能感知到一个调度单位的存在。因此，该进程的一个线程被阻塞后，该进程就被阻塞，进程的其他线程当然也被阻塞。注意，作为对比，在一对一模型中将每个用户级线程都映射到一个内核级线程，因此当某个线程被阻塞时，不会导致整个进程被阻塞。
**58. C**
一对一模型和多对多模型能充分利用内核级线程，发挥多处理机的优势，能同时调度同一个进程中的多个线程并发执行，具有较好的并发性。
**59. C**
多对一模型中的线程切换不会导致进程切换，而是在用户空间进行的。其余说法均正确。
**60. C**
创建进程的原因主要有：①用户登录；②高级调度；③系统处理用户程序的请求；④用户程序的应用请求。对于选项I，用户登录成功后，系统要为此创建一个用户管理的进程，包括用户桌面、环境等，所有用户进程都会在该进程下创建和管理。对于选项II，设备分配是通过在系统中设置相应的数据结构实现的，不需要创建进程，这是操作系统中I/O核心子系统的内容。对于选项III，启动程序执行是引起创建进程的典型事件，启动程序执行属于③或④。
**61. D**
进程是资源分配的基本单位，线程是CPU调度的基本单位。进程的代码段、进程打开的文件、进程的全局变量等都是进程的资源，唯有进程中某线程的栈指针（包含在线程TCB中）是属于线程的，属于进程的资源可以共享，属于线程的栈指针是独享的，对其他线程透明。
**62. A**
在引入线程后，进程依然是资源分配的基本单位，线程是调度的基本单位，同一进程中的各个线程共享进程的地址空间。在用户级线程中，有关线程管理的所有工作都由应用程序完成，无须内核的干预，内核意识不到线程的存在。
**63. A**
进程申请读磁盘操作时，因为要等待I/O操作完成，会把自身阻塞，此时进程变为阻塞态；I/O操作完成后，进程得到了想要的资源，会从阻塞态转换到就绪态（这是操作系统的行为）。而降低进程优先级、分配用户内存空间和增加进程的时间片大小都不一定会发生，选择选项A。
**64. C**
普通管道只允许单向通信，数据只能往一个方向流动，要实现双向数据传输，就需要定义两个方向相反的管道，选项A错误。管道是一种存储在内存中的、固定大小的缓冲区，管道的大小通常为内存的一页，其大小并不是受磁盘容量大小的限制，选项B错误。由于管道的读/写操作都可能遇到缓冲区满或空的情况，当管道满时，写操作会被阻塞，直到有数据读出；而当管道空时，读操作会被阻塞，直到有数据写入，因此选项C正确。一个管道可以有多个读进程或多个写进程对其进行操作，但是这会增加数据竞争和混乱的风险，为了避免这种情况，应使用互斥锁或信号量等同步机制来保证每次只有一个进程对管道进行读或写操作，选项D错误。
**65. D**
P(wait)操作表示进程请求某一资源，选项A、B和C都因为请求某一资源会进入阻塞态，而选项D只是被剥夺了CPU资源，进入就绪态，一旦得到CPU即可运行。
**66. C**
进程等待某资源为可用（不包括CPU）或等待输入/输出完成均会进入阻塞态，因此选项Ⅰ、Ⅱ正确；选项III中的情况发生时，进程进入就绪态，因此选项III错误。
**67. C**
当被阻塞进程等待的某资源（不包括CPU）可用时，进程将被唤醒。I/O结束后，等待该I/O结束而被阻塞的有关进程会被唤醒，选项I正确；某进程退出临界区后，之前因需要进入该临界区而被阻塞的有关进程会被唤醒，选项II正确；当前进程的时间片用完后进入就绪队列等待重新调度，优先级最高的进程获得CPU资源从就绪态变成执行态，选项III错误。
**68. B**
应用程序没有进行内核级线程管理的代码，只有一个到内核级线程的编程接口，内核为进程及其内部的每个线程维护上下文信息，调度也是在内核中由操作系统完成的，选项A正确。用户级线程的控制块是由用户空间的库函数维护的，操作系统并不知道用户级线程的存在，用户级线程的控制块一般存放在用户空间的数据结构中，如链表或数组，由用户空间的线程库来管理。操作系统只负责为每个进程建立一个进程控制块，操作系统只能看到进程，而看不到用户级线程，所以不会为每个用户级线程建立一个线程控制块。但是，内核级线程的线程控制块是由操作系统创建的，当一个进程创建一个内核级线程时，操作系统会为该线程分配一个线程控制块，并将其加入内核的线程管理数据结构，选项B错误。用户级线程的切换可以在用户空间完成，内核级线程的切换需要操作系统帮助进行调度，因此用户级线程的切换效率更高，选项C正确。用户级线程的管理工作可以只在用户空间中进行，因此可以在不支持内核级线程的操作系统上实现，选项D正确。
**69. B**
父进程与子进程当然可以并发执行，选项A正确。父进程可与子进程共享一部分资源，但不能共享虚拟地址空间，在创建子进程时，会为子进程分配资源，如虚拟地址空间等，选项B错误。临界资源一次只能为一个进程所用，选项D正确。进程控制块（PCB）是进程存在的唯一标志，每个进程都有自己的PCB，选项C正确。
**70. B**
操作系统感知进程的唯一方式是通过进程控制块（PCB），所以创建一个新进程就是为其申请一个空白的进程控制块，并且初始化一些必要的进程信息，如初始化进程标志信息、初始化CPU状态信息、设置进程优先级等。选项Ⅰ、Ⅱ正确。创建一个进程时，一般会为其分配除CPU外的大多数资源，所以一般将其设置为就绪态，让它等待调度程序的调度。
**71. D**
进程P读文件时，进程从执行态进入阻塞态，等待磁盘I/O完成，选项Ⅰ正确。进程P的时间片用完，导致进程从执行态进入就绪态，转入就绪队列等待下次被调度，选项Ⅱ错误。进程P申请外设，若外设是独占设备且正在被其他进程使用，则进程P从执行态进入阻塞态，等待系统分配外设，选项Ⅲ正确。进程P执行信号量的wait()操作，若信号量的值小于或等于0，则进程进入阻塞态，等待其他进程用signal()操作唤醒，选项Ⅳ正确。
**72. D**
操作系统通过执行软中断指令陷入内核态执行系统调用，系统调用执行完成后，恢复被中断的进程或设置新进程的CPU现场，然后返回被中断进程或新进程。只有系统调用是用户进程调用内核功能，CPU从用户态切换到内核态，执行完后再返回到用户态。选项A、B、C的操作都是在内核态进行的，执行前后都可能处在内核态，只有中断返回时才切换为用户态。
**73. C**
在等待键盘输入的操作中，当前线程处于阻塞态，键盘输入完成后，再调出相应的中断服务程序进行处理，由中断服务程序负责唤醒当前线程，选项A错误。当线程检测到缺页异常时，会调用缺页异常处理程序从外存调入缺失的页面，线程状态从执行态转为阻塞态，选项B错误。当线程的时间片用完后，主动放弃CPU，此时若线程还未执行完，就进入就绪队列等待下次调度，此时线程状态从执行态转为就绪态，选项C正确。线程执行wait()后，若成功获取资源，则线程状态不变，若未能获取资源，则线程进入阻塞态，选项D错误。
**74. A**
当操作系统终止进程时，所有的进程资源（如内存空间、进程控制块、设备、打开文件、I/O缓冲区等）都会被释放。有些系统不允许子进程在父进程已终止的情况下存在，对于这类系统，若一个进程终止，则它的所有子进程也终止，这种现象称为级联终止。但是，不是所有操作系统都是这么设计的，因此终止子进程不一定在终止进程时执行。
**75. B**
线程可理解为轻量级进程，仅拥有一点必不可少、能保证独立运行的资源。例如，在每个线程中都有一个用于控制线程运行的线程控制块（TCB）、用于指示被执行指令序列的程序计数器、保留局部变量、少数状态参数和返回地址等的一组寄存器和堆栈。因此，线程T的栈空间是线程T所独有的，不会被线程Ta和Tb共享。多个线程共享所属进程所拥有的资源，进程P的线程可以访问进程P的全部地址空间和资源（如已打开的文件、定时器、信号量机构等）以及它申请到的I/O设备等。综上所述，Ta和Tb可共享的是进程P的地址空间和文件描述符fd。

**二、综合应用题**
**01.** 【解答】
在操作系统中，进程是竞争和分配计算机系统资源的基本单位。每个进程都有自己的独立地址空间。为了保证多个进程能够彼此互不干扰地共享物理内存，操作系统利用硬件地址机制对进程的地址空间进行了严格的保护，限制每个进程只能访问自己的地址空间。
具体解答如下。
每个进程有自己独立的地址空间。在操作系统和硬件的地址保护机制下，进程无法访问其他进程的地址空间，必须借助于系统调用函数实现进程之间的通信。进程通信的主要方式有：
1) **共享内存区**。通过系统调用创建共享内存区。多个进程可以（通过系统调用）连接同一个共享内存区，通过访问共享内存区实现进程之间的数据交换。使用共享内存区时需要利用信号量解决同步互斥问题。
2) **消息传递**。通过发送/接收消息，系统调用实现进程之间的通信。当进程发送消息时，系统将消息从用户缓冲区复制到内核中的消息缓冲区，然后将消息缓冲区挂入消息队列。进程发送的消息保持在消息队列中，直到被另一进程接收。当进程接收消息时，系统从消息队列中解挂消息缓冲区，将消息从内核的消息缓冲区中复制到用户缓冲区，然后释放消息缓冲区。
3) **管道系统**。管道允许两个进程按标准的生产者-消费者方式进行通信：生产者向管道的一端（写入端）写，消费者从管道的另一端（读出端）读。管道只允许单向通信。在读/写过程中，操作系统保证数据的写入顺序和读出顺序是一致的。
4) **共享文件**。利用操作系统提供的文件共享功能实现进程之间的通信。这时，也需要信号量来解决文件共享操作中的同步和互斥问题。
**02.** 【解答】
多线程是指在一个程序中可以定义多个线程并同时运行它们，每个线程可以执行不同的任务。
多线程与多任务的区别：多任务是针对操作系统而言的，代表操作系统可以同时执行的程序个数；多线程是针对一个程序而言的，代表一个程序可以同时执行的线程个数，而每个线程可以完成不同的任务。
**03.** 【解答】
1) 是。若系统中未运行进程，则系统很快会选择一个就绪进程运行。只有就绪队列中无进程时，CPU才可能处于空闲状态。
2) 不一定。因为系统中的所有进程可能都处于等待态，可能处于死锁状态，也有可能因为等待的事件未发生而进入循环等待态。
3) 不一定。因为高优先级的进程有可能正处在等待队列中，进程调度会从就绪队列中选择一个进程占用CPU，这个被选中的进程可能优先级较低。
**04.** 【解答】
根据题意，首先由图进行分析，进程由运行态可以直接回到就绪队列的末尾，而且就绪队列中是先来先服务。那么，什么情况才能发生这样的变化呢？只有采用单一时间片轮转的调度系统，分配的时间片用完后，才会发生上述情况。因此，该系统一定采用时间片轮转调度算法，采用时间片轮转算法的操作系统一般均为交互式操作系统。由图可知，进程被阻塞时，可以进入不同的阻塞队列，等待打印机输出结果和等待磁盘读取文件。所以，它是一个多阻塞队列的时间片轮转法的调度系统。
具体解答如下。
1) 根据题意，该系统采用的是时间片轮转法调度进程策略。
2) 可能的变化见下表。

| 变化 | 原因 |
| :--- | :--- |
| 1 | 进程被调度，获得CPU，进入运行态 |
| 2 | 进程需要读文件，因I/O操作进入阻塞态 |
| 3 | 进程打印输出结果，因打印机未结束而阻塞 |
| 4 | 打印机打印结束，进程重新回归就绪态，并排在尾部 |
| 5 | 进程所需数据已从磁盘进入内存，进程回到就绪态 |
| 6 | 运行的进程因为时间片用完而让出CPU，排到就绪队列尾部 |