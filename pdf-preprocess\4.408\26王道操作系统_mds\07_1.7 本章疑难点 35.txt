BIOS 将控制权交给排在首位的启动设备后,CPU 将该设备主引导扇区的内容[主引导记录(MBR)]加载到内存中,然后由 MBR 检查分区表,查找活动分区,并将该分区的引导扇区的内容[分区引导记录(PBR)]加载到内存加以执行。
16. A
软件能实现的功能也能由硬件实现,因为虚拟机软件能实现的功能也能由硬件实现,软件和硬件的分界面是系统结构设计者的任务,选项 I 和 II 正确。实现真正并行的是多核处理机,多台虚拟机同时运行在同一物理机器上,类似于多个程序运行在同一个系统中。
17. B
VMware Workstation 虚拟机属于第二类虚拟机管理程序,若真实硬件直接执行虚拟机中的敏感指令,则该指令非法时可能导致宿主操作系统崩溃,而这是不可能的,实际上是由第二类虚拟机管理程序模拟真实硬件环境。虚拟机看起来和真实物理计算机没什么两样,因此当然可以安装多个操作系统。VMware Workstation 就是一个安装在计算机上的程序,在创建虚拟机时,会为该虚拟机创建一组文件,这些虚拟机文件都存储在主机的磁盘上。
18. B
第一类 VMM 直接运行在硬件上;第二类 VMM 运行在宿主操作系统上,不能直接和硬件打交道,因此第一类 VMM 的效率通常更高。VMM 的功能没有操作系统的功能复杂,其代码量少于一个完整的操作系统。选项 III 是基本概念。第一类 VMM 运行在最高特权级(内核态),而第二类 VMM 和普通应用程序的地位相同,通常运行在较低特权级(用户态)。
19. D
系统开机后,操作系统的程序会被自动加载到内存中的系统区,这段区域是 RAM。部分未复习计组的读者对该内容可能不太熟悉,但熟悉了各类存储介质后,解答本题并不难。
20. A
在操作系统初始化的过程中需要创建中断向量表,以实现通电自检(POST),CPU 检测到中断信号后,根据中断号查询中断向量表,跳转到相应的中断处理程序,选项 A 正确。在硬盘逻辑格式化之前,需要先对硬盘进行分区,即创建硬盘分区表。分区完成后,对物理分区进行逻辑格式化(创建文件系统),为每个分区初始化一个特定的文件系统,并创建文件系统的根目录。若某个分区采用 UNIX 文件系统,则还要在该分区中建立文件系统的索引节点表。
21. D
微内核构架将内核中最基本的功能保留在内核,只有微内核运行在内核态,其余模块都运行在用户态,一个模块中的错误只会使这个模块崩溃,而不会使整个系统崩溃,因此具有较高的可靠性和安全性。微内核的非核心功能运行在用户空间,可通过插件或模块的方式进行扩展,无须改动内核代码,因此具有较强的可扩展性。微内核需要频繁地在用户态和内核态之间进行切换,操作系统的执行开销偏大,从而影响系统性能。

### 1.7 本章疑难点
1. 并行性与并发性的区别和联系
并行性和并发性是既相似又有区别的两个概念。并行性是指两个或多个事件在同一时刻发生,并发性是指两个或多个事件在同一时间间隔内发生。
在多道程序环境下,并发性是指在一段时间内,宏观上有多个程序同时运行,但在单处理器系统中每个时刻却仅能有一道程序执行,因此微观上这些程序只能分时地交替执行。若在计算机系统中有多个处理器,则这些可以并发执行的程序便被分配到多个处理器上,实现并行执行,即利用每个处理器来处理一个可并发执行的程序。

2. 特权指令与非特权指令
特权指令是指有特殊权限的指令,由于这类指令的权限最大,使用不当将导致整个系统崩溃,如清内存、置时钟、分配系统资源、修改虚存的段表或页表、修改用户的访问权限等。若所有程序都能使用这些指令,则系统一天死机n次就不足为奇。为保证系统安全,这类指令不能直接提供给用户使用,因此特权指令必须在内核态执行。实际上,CPU在内核态下可以执行指令系统的全集。形象地说,特权指令是那些儿童不宜的东西,而非特权指令是老少皆宜的东西。
为了防止用户程序中使用特权指令,用户态下只能使用非特权指令,内核态下可以使用全部指令。在用户态下使用特权指令时,将产生中断以阻止用户使用特权指令。所以将用户程序放在用户态下运行,而操作系统中必须使用特权指令的那部分程序在内核态下运行,从而保证了系统的安全性和可靠性。从用户态转换为内核态的唯一途径是中断或异常。

3. 访管指令与访管中断
访管指令(trap 指令)是一条在用户态下执行的指令。在用户程序中,因要求操作系统提供服务而有意识地使用访管指令,从而产生一个中断事件(自愿中断),将操作系统转换为内核态,称为访管中断。访管中断由访管指令产生,程序员使用访管指令向操作系统请求服务。
为什么要在程序中引入访管指令呢?这是因为用户程序只能在用户态下运行。若用户程序想要完成在用户态下无法完成的工作,该怎么办?解决这个问题要靠访管指令。访管指令本身不是特权指令,其基本功能是让程序拥有“自愿进管”的手段,从而引起访管中断。

4. 定义微内核结构OS的四个方面
1) 足够小的内核。
2) 基于客户/服务器模式。
3) 应用“机制与策略分离”原理。机制是指实现某一功能的具体执行机构。策略则是在机制的基础上借助于某些参数和算法来实现该功能的优化,或达到不同的功能目标。在传统的 OS 中,将机制放在 OS 内核的较低层中,将策略放在内核的较高层中。而在微内核 OS 中,通常将机制放在 OS 的微内核中。正因如此,才可以将内核做得很小。
4) 采用面向对象技术。基于面向对象技术中的“抽象”和“隐蔽”原则能控制系统的复杂性,进一步利用“对象”“封装”和“继承”等概念还能确保操作系统的正确性、可靠性、易扩展性等。正因如此,面向对象技术被广泛应用于现代操作系统的设计之中。

## 第二章 进程与线程
【考纲内容】
(一) 进程与线程
进程与线程的基本概念; 进程/线程的状态与转换
线程的实现: 内核支持的线程, 线程库支持的线程
进程与线程的组织与控制
进程间通信: 共享内存, 消息传递, 管道, 信号
(二) CPU 调度与上下文切换
调度的基本概念; 调度的目标;
调度的实现: 调度器/调度程序 (scheduler), 调度的时机与调度方式 (抢占式/非抢占式), 闲逛进程, 内核级线程与用户级线程调度
CPU 调度算法
多处理机调度
上下文及其切换机制
(三) 同步与互斥
同步与互斥的基本概念
基本的实现方法: 软件方法; 硬件方法
锁; 信号量; 条件变量
经典同步问题: 生产者-消费者问题, 读者-写者问题; 哲学家进餐问题
(四) 死锁
死锁的基本概念; 死锁预防
死锁避免; 死锁检测和解除

扫一扫
视频讲解

【复习提示】
进程管理是操作系统的核心,也是每年必考的重点。其中,进程的概念、进程调度、信号量机制实现同步和互斥、进程死锁等更是重中之重,必须深入掌握。需要注意的是,除选择题外,本章还容易出综合题,其中信号量机制实现同步和互斥、进程调度算法和死锁等都可能命制综合题,如利用信号量进行进程同步就在往年的统考中频繁出现。

### 2.1 进程与线程
在学习本节时,请读者思考以下问题:
1) 为什么要引入进程?
2) 什么是进程? 进程由什么组成?