```markdown
## 6.3 图的遍历

图的遍历是指从图中的某一顶点出发,按照某种搜索方法沿着图中的边对图中的所有顶点访问一次,且仅访问一次。注意到树是一种特殊的图,所以树的遍历实际上也可视为一种特殊的图的遍历。图的遍历算法是求解图的连通性问题、拓扑排序和求关键路径等算法的基础。

图的遍历比树的遍历要复杂得多,因为图的任意一个顶点都可能和其余的顶点相邻接,所以在访问某个顶点后,可能沿着某条路径搜索又回到该顶点。为避免同一顶点被访问多次,在遍历图的过程中,必须记下每个已访问过的顶点,为此可以设一个辅助数组$visited[]$来标记顶点是否被访问过。图的遍历算法主要有两种:广度优先搜索和深度优先搜索。

### 6.3.1 广度优先搜索

广度优先搜索(Breadth-First-Search, BFS)类似于树的层序遍历。基本思想是:首先访问起始顶点$v$,接着由$v$出发,依次访问$v$的各个未访问过的邻接顶点$w_1, w_2, \dots, w_i$,然后依次访问$w_1, w_2, \dots, w_i$的所有未被访问过的邻接顶点;再从这些访问过的顶点出发,访问它们所有未被访问过的邻接顶点,直至图中所有顶点都被访问过为止。若此时图中尚有顶点未被访问,则另选图中一个未曾被访问的顶点作为始点,重复上述过程,直至图中所有顶点都被访问到为止。Dijkstra 单源最短路径算法和 Prim 最小生成树算法也应用了类似的思想。

换句话说,广度优先搜索遍历图的过程是以$v$为起始点,由近至远依次访问和$v$有路径相通且路径长度为$1,2,\dots$的顶点。广度优先搜索是一种分层的查找过程,每向前走一步可能访问一批顶点,不像深度优先搜索那样有往回退的情况,因此它不是一个递归的算法。为了实现逐层的访问,算法必须借助一个辅助队列,以记忆正在访问的顶点的下一层顶点。

广度优先搜索算法的伪代码如下:

```c
bool visited[MAX_VERTEX_NUM];        //访问标记数组
void BFSTraverse(Graph G) {          //对图G进行广度优先遍历
    for(i=0;i<G.vexnum;++i)
        visited[i]=FALSE;            //访问标记数组初始化
    InitQueue(Q);                    //初始化辅助队列Q
    for(i=0;i<G.vexnum;++i)          //从0号顶点开始遍历
        if(!visited[i])              //对每个连通分量调用一次BFS()
            BFS(G,i);                //若vi未访问过,从vi开始调用 BFS()
}
```

用邻接表实现广度优先搜索的算法如下:

```c
void BFS(ALGraph G, int i) {
    visit(i);                        //访问初始顶点i
    visited[i]=TRUE;                 //对i做已访问标记
    EnQueue(Q,i);                    //顶点i入队
    while(!IsEmpty(Q)){
        DeQueue(Q,v);                //队首顶点v出队
        for(p=G.vertices[v].firstarc;p;p=p->nextarc){ //检测v的所有邻接点
            w=p->adjvex;
            if (visited[w]==FALSE) {
                visit(w);            //w为v的尚未访问的邻接点,访问w
                visited[w]=TRUE;     //对w做已访问标记
                EnQueue(Q,w);        //顶点w入队
            }
        }
    }
}
```

用邻接矩阵实现广度优先搜索的算法如下:

```c
void BFS(MGraph G,int i) {
    visit(i);                        //访问初始顶点i
    visited[i]=TRUE;                 //对i做已访问标记
    EnQueue(Q,i);                    //顶点i入队
    while(!IsEmpty(Q)){
        DeQueue(Q,v);                //队首顶点v出队
        for(w=0;w<G.vexnum;w++)      //检测v的所有邻接点
            if(visited[w]==FALSE&&G.edge[v][w]==1){
                visit(w);            //w为v的尚未访问的邻接点,访问w
                visited[w]=TRUE;     //对w做已访问标记
                EnQueue(Q,w);        //顶点w入队
            }
    }
}
```

辅助数组$visited[]$标志顶点是否被访问过,其初始状态为 FALSE。在图的遍历过程中,一旦某个顶点$v$被访问,就立即置$visited[i]$为TRUE,防止它被多次访问。

**命题追踪** 广度优先遍历的过程(2013)

下面通过实例演示广度优先搜索的过程,给定图 G 如图 6.11 所示。假设从顶点 a 开始访问,a 先入队。此时队列非空,取出队头元素 a,因为 b,c 与 a 邻接且未被访问过,于是依次访问 b, c,并将 b,c 依次入队。队列非空,取出队头元素 b,依次访问与 b 邻接且未被访问的顶点 d, e,并将 d, e 入队(注意:a 与 b 也邻接,但 a 已置访问标记,所以不再重复访问)。此时队列非空,取出队头元素 c,访问与 c 邻接且未被访问的顶点 f,g,并将 f,g 入队。此时,取出队头元素 d,但与 d 邻接且未被访问的顶点为空,所以不进行任何操作。继续取出队头元素 e,将 h 入队列……最终取出队头元素 h 后,队列为空,从而循环自动跳出。遍历结果为 abcdefgh。

图6.11 一个无向图G

从上例不难看出,图的广度优先搜索的过程与二叉树的层序遍历是完全一致的,这也说明了图的广度优先搜索遍历算法是二叉树的层次遍历算法的扩展。

#### 1. BFS算法的性能分析

无论是邻接表还是邻接矩阵的存储方式,BFS算法都需要借助一个辅助队列Q,$n$个顶点均需入队一次,在最坏的情况下,空间复杂度为$O(|V|)$。

**命题追踪** 基于邻接表存储的BFS的效率(2012)

遍历图的过程实质上是对每个顶点查找其邻接点的过程,耗费的时间取决于所采用的存储结构。采用邻接表存储时,每个顶点均需搜索(或入队)一次,时间复杂度为$O(|V|)$,在搜索每个顶点的邻接点时,每条边至少访问一次,时间复杂度为$O(|E|)$,总的时间复杂度为$O(|V|+|E|)$。采用邻接矩阵存储时,查找每个顶点的邻接点所需的时间为$O(|V|)$,总时间复杂度为$O(|V|^2)$。

#### 2. BFS算法求解单源最短路径问题

若图$G=(V, E)$为非带权图,定义从顶点$u$到顶点$v$的最短路径$d(u,v)$为从$u$到$v$的任何路径中最少的边数;若从$u$到$v$没有通路,则$d(u,v)=\infty$。

使用 BFS,我们可以求解一个满足上述定义的非带权图的单源最短路径问题,这是由广度优先搜索总是按照距离由近到远来遍历图中每个顶点的性质决定的。

BFS算法求解单源最短路径问题的算法如下:

```c
void BFS_MIN_Distance(Graph G,int u) {
    //d[i]表示从u到i结点的最短路径
    for(i=0;i<G.vexnum;++i)
        d[i]=∞;                          //初始化路径长度
    visited[u]=TRUE; d[u]=0;
    EnQueue(Q,u);
    while(!IsEmpty(Q)){                  //BFS 算法主过程
        DeQueue(Q,u);                    //队头元素u出队
        for (w=FirstNeighbor(G,u);w>=0;w=NextNeighbor(G,u,w))
            if(!visited[w]) {            //w为u的尚未访问的邻接顶点
                visited[w]=TRUE;         //设已访问标记
                d[w]=d[u]+1;             //路径长度加1
                EnQueue(Q,w);            //顶点w入队
            }
    }
}
```

#### 3. 广度优先生成树

在广度遍历的过程中,我们可以得到一棵遍历树,称为广度优先生成树,如图 6.12 所示。需要注意的是,同一个图的邻接矩阵存储表示是唯一的,所以其广度优先生成树也是唯一的,但因为邻接表存储表示不唯一,所以其广度优先生成树也是不唯一的。

图6.12 图的广度优先生成树

### 6.3.2 深度优先搜索

与广度优先搜索不同,深度优先搜索(Depth-First-Search,DFS)类似于树的先序遍历。如其名称中所暗含的意思一样,这种搜索算法所遵循的策略是尽可能“深”地搜索一个图。

它的基本思想如下:首先访问图中某一起始顶点$v$,然后由$v$出发,访问与$v$邻接且未被访问的任意一个顶点$w_1$,再访问与$w_1$邻接且未被访问的任意一个顶点$w_2 \dots \dots$重复上述过程。当不能再继续向下访问时,依次退回到最近被访问的顶点,若它还有邻接顶点未被访问过,则从该点开始继续上述搜索过程,直至图中所有顶点均被访问过为止。

一般情况下,其递归形式的算法十分简洁,算法过程如下:

```c
bool visited[MAX_VERTEX_NUM];        //访问标记数组
void DFSTraverse(Graph G) {          //对图G进行深度优先遍历
    for(i=0;i<G.vexnum;i++)
        visited[i]=FALSE;            //初始化已访问标记数组
    for(i=0;i<G.vexnum;i++)          //本代码中是从v0开始遍历
        if(!visited[i])
            DFS(G,i);                //对尚未访问的顶点调用DFS()
}
```

用邻接表实现深度优先搜索的算法如下:

```c
void DFS(ALGraph G, int i) {
    visit(i);                        //访问初始顶点i
    visited[i]=TRUE;                 //对i做已访问标记
    for(p=G.vertices[i].firstarc;p;p=p->nextarc){ //检测i的所有邻接点
        j=p->adjvex;
        if(!visited[j]==FALSE)
            DFS(G,j);                //j为i的尚未访问的邻接点,递归访问j
    }
}
```

用邻接矩阵实现深度优先搜索的算法如下:

```c
void DFS(MGraph G,int i) {
    visit(i);                        //访问初始顶点i
    visited[i]=TRUE;                 //对i做已访问标记
    for(j=0;j<G.vexnum;j++) {        //检测i的所有邻接点
        if(visited[j]==FALSE&&G.edge[i][j]==1)
            DFS(G,j);                //j为i的尚未访问的邻接点,递归访问j
    }
}
```

**命题追踪** 深度优先遍历的过程(2015、2016)

以图6.11的无向图为例,深度优先搜索的过程:首先访问a,并置a访问标记;然后访问与a邻接且未被访问的顶点b,置b访问标记;然后访问与b邻接且未被访问的顶点d,置d访问标记。此时d已没有未被访问过的邻接点,所以返回上一个访问的顶点b,访问与其邻接且未被访问的顶点e,置e访问标记,以此类推,直至图中所有顶点都被访问一次。遍历结果为 abdehcfg。

**注意**

图的邻接矩阵表示是唯一的,但对邻接表来说,若边的输入次序不同,则生成的邻接表也不同。因此,对同样一个图,基于邻接矩阵的遍历得到的DFS序列和BFS序列是唯一的,基于邻接表的遍历得到的DFS序列和BFS序列是不唯一的。

#### 1. DFS算法的性能分析

DFS 算法是一个递归算法,需要借助一个递归工作栈,所以其空间复杂度为$O(|V|)$。

遍历图的过程实质上是通过边查找邻接点的过程,因此两种遍历方式的时间复杂度都相同,不同之处仅在于对顶点访问顺序的不同。采用邻接矩阵存储时,总时间复杂度为$O(|V|^2)$。采用邻接表存储时,总的时间复杂度为$O(|V|+|E|)$。

#### 2. 深度优先的生成树和生成森林

与广度优先搜索一样,深度优先搜索也会产生一棵深度优先生成树。当然,这是有条件的,即对连通图调用DFS才能产生深度优先生成树,否则产生的将是深度优先生成森林,如图6.13所示。与BFS类似,基于邻接表存储的深度优先生成树是不唯一的。

图6.13 图的深度优先生成森林

### 6.3.3 图的遍历与图的连通性

图的遍历算法可以用来判断图的连通性。对于无向图来说,若无向图是连通的,则从任意一个结点出发,仅需一次遍历就能够访问图中的所有顶点;若无向图是非连通的,则从某一个顶点出发,一次遍历只能访问到该顶点所在连通分量的所有顶点,而对于图中其他连通分量的顶点,则无法通过这次遍历访问。对于有向图来说,若从初始顶点到图中的每个顶点都有路径,则能够访问到图中的所有顶点,否则不能访问到所有顶点。

因此,在$BFSTraverse()$或$DFSTraverse()$中添加了第二个 for循环,再选取初始点,继续进行遍历,以防止一次无法遍历图的所有顶点。对于无向图,上述两个函数调用$BFS(G,i)$或$DFS(G,i)$的次数等于该图的连通分量数;而对于有向图则不是这样,因为一个连通的有向图分为强连通的和非强连通的,它的连通子图也分为强连通分量和非强连通分量,非强连通分量一次调用$BFS(G,i)$或$DFS(G,i)$不一定能访问到该子图的所有顶点,如图6.14所示。

图6.14 有向图的非强连通分量

### 6.3.4 本节试题精选

#### 一、单项选择题

01. 下列关于广度优先算法的说法中,正确的是()。
    I. 当各边的权值相等时,广度优先算法可以解决单源最短路径问题
    II. 当各边的权值不等时,广度优先算法可用来解决单源最短路径问题
    III. 广度优先遍历算法类似于树中的后序遍历算法
    IV. 实现图的广度优先算法时,使用的数据结构是队列
    A. I、IV
    B. II、III、IV
    C. II、IV
    D. I、III、IV

02. 下列关于图的说法中,错误的是()。
    I. 对一个无向图进行深度优先遍历时,得到的深度优先遍历序列是唯一的
    II. 若有向图不存在回路,即使不用访问标志位,同一结点也不会被访问两次
    III. 采用深度优先遍历或拓扑排序算法可以判断一个有向图中是否有环(回路)
    IV. 对任何非强连通图必须2次或以上调用广度优先遍历算法才可访问所有的顶点
    A. I、II、III
    B. II、III
    C. I、II
    D. I、II、IV

03. 对于一个非连通无向图G,采用深度优先遍历访问所有顶点,在$DFSTraverse$函数(见考点讲解 DFS部分)中调用DFS的次数正好等于()。
    A. 顶点数
    B. 边数
    C. 连通分量数
    D. 不确定

04. 对一个有$n$个顶点、$e$条边的图采用邻接表表示时,进行DFS遍历的时间复杂度为(),空间复杂度为();进行BFS遍历的时间复杂度为(),空间复杂度为()。
    A. $O(n)$
    B. $O(e)$
    C. $O(n+e)$
    D. $O(1)$

05. 图的广度优先遍历算法中使用队列作为其辅助数据结构,那么在算法执行过程中,每个顶点的入队次数最多为()。
    A. 1
    B. 2
    C. 3
    D. 4

06. 对有$n$个顶点、$e$条边的图采用邻接矩阵表示时,进行DFS遍历的时间复杂度为(),进行BFS遍历的时间复杂度为()。
    A. $O(n^2)$
    B. $O(e)$
    C. $O(n+e)$
    D. $O(e^2)$

07. 无向图$G=(V,E)$,其中$V=\{a, b, c, d, e, f\}$, $E=\{(a,b),(a,e),(a,c),(b,e),(c,f),(f,d),(e,d)\}$,对该图从a开始进行深度优先遍历,得到的顶点序列正确的是()。
    A. a, b, e, c, d, f
    B. a, c, f, e, b, d
    C. a, e, b, c, f, d
    D. a, e, d, f, c, b

08. 如下图所示,在下面的5个序列中,符合深度优先遍历的序列个数是()。

    (图示：一个无向图，顶点为a, b, c, d, e, f。边为(a,b), (a,c), (b,e), (b,d), (c,f))

    1. aebfdc 2. acfdeb 3. aedfcb 4. aefdbc 5. aecfdb
    A. 5
    B. 4
    C. 3
    D. 2

09. 用邻接表存储的图的深度优先遍历算法类似于树的( ),而其广度优先遍历算法类似于树的()。
    A. 中序遍历
    B. 先序遍历
    C. 后序遍历
    D. 按层次遍历

10. 一个有向图G的邻接表存储如下图所示,从顶点1出发,对图G调用深度优先遍历所得顶点序列是();按广度优先遍历所得顶点序列是()。

| 顶点 | 邻接点 |
| :--: | :--- |
| 1 | 2 -> 4 -> / |
| 2 | 5 -> / |
| 3 | 6 -> 5 -> / |
| 4 | 2 -> / |
| 5 | 4 -> / |
| 6 | 6 -> / |

(图示：一个有向图，顶点为1,2,3,4,5,6。边为1->2, 1->4, 2->5, 3->6, 3->5, 4->2, 5->4, 6->6)

    A. 125436
    B. 124536
    C. 124563
    D. 362514

11. 无向图$G=(V, E)$,其中$V=\{a, b, c, d, e, f\}$, $E=\{(a,b),(a,e),(a,c),(b,e),(c,f),(f,d),(e,d)\}$。对该图进行深度优先遍历,不能得到的序列是()。
    A. acfdeb
    B. aebdfc
    C. aedfcb
    D. abecdf

12. 判断有向图中是否存在回路,除拓扑排序外,还可以利用()。(注:涉及下节内容)
    A. 求关键路径的方法
    B. 求最短路径的Dijkstra算法
    C. 深度优先遍历算法
    D. 广度优先遍历算法

13. 设无向图$G=(V,E)$和$G'=(V',E')$,若$G'$是$G$的生成树,则下列说法错误的是( )。
    A. $G'$为$G$的子图
    B. $G'$为$G$的连通分量
    C. $G'$为$G$的极小连通子图且$V=V'$
    D. $G'$是$G$的一个无环子图

14. 图的广度优先生成树的树高比深度优先生成树的树高( )。
    A. 小或相等
    B. 小
    C. 大或相等
    D. 大

15. 【2012统考真题】对有$n$个顶点、$e$条边且使用邻接表存储的有向图进行广度优先遍历,其算法的时间复杂度是()。
    A. $O(n)$
    B. $O(e)$
    C. $O(n+e)$
    D. $O(ne)$

16. 【2013统考真题】下列选项中,不是如下无向图的广度优先遍历序列的是()。

    (图示：一个无向图,顶点为a,b,c,d,e,f,g,h。边为(a,b), (a,c), (a,h), (b,d), (c,g), (c,h), (d,e), (e,f), (f,g), (f,h))

    A. h, c, a, b, d, e, g, f
    B. e, a, f, g, b, h, c, d
    C. d, b, c, a, h, e, f, g
    D. a, b, c, d, h, e, f, g

17. 【2015统考真题】设有向图$G=(V,E)$,顶点集$V=\{v_0,v_1,v_2,v_3\}$,边集$E=\{<v_0,v_1>,<v_0,v_2>,<v_0,v_3>,<v_1,v_3>\}$。若从顶点$v_0$开始对图进行深度优先遍历,则可能得到的不同遍历序列个数是()。
    A. 2
    B. 3
    C. 4
    D. 5

18. 【2016统考真题】下列选项中,不是下图深度优先搜索序列的是()。

    (图示：一个无向图，顶点为v1,v2,v3,v4,v5。边为(v1,v2), (v1,v3), (v1,v5), (v2,v3), (v3,v4), (v4,v5))

    A. $V_1, V_5, V_4, V_3, V_2$
    B. $V_1, V_3, V_2, V_5, V_4$
    C. $V_1, V_2, V_5, V_4, V_3$
    D. $V_1, V_2, V_3, V_4, V_5$

#### 二、综合应用题

01. 图$G=(V,E)$以邻接表存储,如下图所示,试画出图G的深度优先生成树和广度优先生成树(假设从结点1开始遍历)。
| 顶点 | 邻接点 |
| :--: | :--- |
| 1 | 2 -> 3 -> 4^ |
| 2 | 1 -> 3 -> 5^ |
| 3 | 1 -> 2 -> 4^ |
| 4 | 1 -> 3 -> 5^ |
| 5 | 2 -> 4^ |

02. 给定一个连通无向图,采用邻接表存储,将图的所有顶点分别染成红色或蓝色,若存在一种染色方法使图中每条边的两个顶点的颜色都不相同,则称这个图能被二分。

(图示：左图为一个四边形加一条对角线，右图为一个三角形)

1) 判断上面两个无向图是否能被二分,若能被二分,则请标出每个顶点的颜色。
2) 请设计一种算法用来判断图是否能被二分,仅用语言描述算法的思想即可。
3) 给出你设计的算法的时间复杂度和空间复杂度。

03. 试设计一个算法,判断一个无向图G是否为一棵树。若是一棵树,则算法返回true,否则返回false。

04. 分别采用基于深度优先遍历和广度优先遍历算法判别以邻接表方式存储的有向图中是否存在由顶点$v_i$到顶点$v_j$的路径($i \neq j$)。注意,算法中涉及的图的基本操作必须在此存储结构上实现。

05. 假设图用邻接表表示,设计一个算法,输出从顶点$V_i$到顶点$V_j$的所有简单路径。

### 6.3.5 答案与解析

#### 一、单项选择题

**01. A**
广度优先搜索以起始结点为中心,一层一层地向外层扩展遍历图的顶点,因此无法考虑到边权值,只适合求边权值相等的图的单源最短路径。广度优先搜索相当于树的层序遍历,选项III 错误。广度优先搜索需要用到队列,深度优先搜索需要用到栈,选项IV 正确。

**02. D**
图的深度优先遍历序列通常是不唯一的,选项I错误。图1是一个不存在回路的有向图,从顶点1开始执行广度优先遍历,若不设置访问标志位,则会重复访问顶点3,选项II错误。深度优先遍历(见本节后面习题的解析)或拓扑排序算法可以判断有向图中是否有环,选项III 正确。图2是一个非强连通图,但从顶点1开始调用一次广度优先遍历算法就可访问所有顶点,选项IV错误。

(图1: 有向图 1->2, 1->3, 2->3)
(图2: 有向图 1->2, 2->1, 1->3)

**03. C**
DFS(或BFS)可用来计算无向图的连通分量数,因为一次遍历必然会将一个连通图中的所有顶点都访问到,所以计算图的连通分量数正好是$DFSTraverse()$中DFS被调用的次数。

**04. C、A、C、A**
深度优先遍历时,每个顶点表结点和每个边表结点均查找一次,每个顶点递归调用一次,需要借助一个递归工作栈;而广度优先遍历时,也是每个顶点表结点和每个边表结点均查找一次,需要借助一个辅助队列。因此,时间复杂度都是$O(n+e)$,空间复杂度都是$O(n)$。

**05. A**
在图的广度优先遍历算法中,每个顶点被访问后立即做访问标记并入队。若队列不空,则队首顶点出队,若该顶点的邻接顶点未被访问,则访问之,做访问标记并入队;若被访问过,则跳过,如此反复,直至队空。因此,在广度优先遍历过程中,每个顶点最多入队一次。

**06. A、A**
采用邻接矩阵表示时,查找一个顶点所有出边的时间复杂度为$O(n)$,共有n个顶点,所以时间复杂度均为$O(n^2)$。

**07. D**
画出草图后,此类题可以根据边的邻接关系快速排除错误选项。以选项 A 为例,在遍历到e之后,应该访问与e邻接但未被访问的结点,$(e,c)$显然不在边集中。

**08. D**
仅1和4正确。以2为例,遍历到c之后,与c邻接且未被访问的结点为空集,所以应为a的邻接点b或e入栈。以3为例,因为遍历要按栈退回,所以是先b后c,而不能先c后b。

**09. B、D**
图的深度优先搜索类似于树的先根遍历,即先访问结点,再递归向外层结点遍历,都采用回溯算法。图的广度优先搜索类似于树的层序遍历,即一层一层向外层扩展遍历,都需要采用队列来辅助算法的实现。

**10. A、B**
DFS 序列产生的路径为$<1, 2>, <2, 5>, <5, 4>, <3, 6>$; BFS序列产生的路径为$<1, 2>, <1, 4>, <2, 5>, <3, 6>$。

**11. D**
画出V和E对应的图G,然后根据搜索算法求解。

**注意**
为什么本题序列是不唯一的,而上题序列却是唯一的呢?
因为上题给出了具体的存储结构,此时就必须按照算法的过程来执行,每个顶点的邻接点的顺序已固定,但本题中每个顶点的邻接点的顺序是非固定的。

**12. C**
利用深度优先遍历可以判断图G中是否存在回路。
对于无向图来说,若深度优先遍历过程中遇到了回边,则必定存在环;对于有向图来说,这条回边可能是指向深度优先森林中另一棵生成树上的顶点的弧;但是,从有向图的某个顶点$v$出发进行深度优先遍历时,若在$DFS(v)$结束之前出现一条从顶点$u$到顶点$v$的回边,且$u$在生成树上是$v$的子孙,则有向图必定存在包含顶点$v$和顶点$u$的环。

**13. B**
连通分量是无向图的极大连通子图,其中极大的含义是将依附于连通分量中顶点的所有边都加上,所以连通分量中可能存在回路,这样就不是生成树了。

**注意**
极大连通子图是无向图(不一定连通)的连通分量,极小连通子图是连通无向图的生成树。极小和极大是在满足连通的前提下,针对边的数目而言的。极大连通子图包含连通分量的全部边;极小连通子图(生成树)包含连通图的全部顶点,且使其连通的边数最少。

**14. A**
对于无向图的广度优先搜索生成树,起点到其他顶点的路径是图中对应的最短路径,即所有生成树中树高最小。此外,深度优先总是尽可能“深”地搜索图,因此其路径也尽可能长,所以深度优先生成树的树高总是大于或等于广度优先生成树的树高。

**15. C**
广度优先遍历需要借助队列实现。采用邻接表存储方式对图进行广度优先遍历时,每个顶点均需入队一次(顶点表遍历),所以时间复杂度为$O(n)$,在搜索所有顶点的邻接点的过程中,每条边至少访问一次(出边表遍历),所以时间复杂度为$O(e)$,算法总的时间复杂度为$O(n+e)$。

**16. D**
只要掌握 DFS和BFS的遍历过程,便能轻易解决。逐个代入,手工模拟,选项D是深度优先遍历,而不是广度优先遍历。

**17. D**
画出该有向图,如下图所示。采用图的深度优先遍历,共有5种可能: $<v_0, v_1, v_3, v_2>, <v_0, v_2, v_3, v_1>, <v_0, v_2, v_1, v_3>, <v_0, v_3, v_2, v_1>, <v_0, v_3, v_1, v_2>$。

(图示: 有向图，顶点为0,1,2,3。边为0->1, 0->2, 0->3, 1->3)

**18. D**
按深度优先遍历的策略进行遍历。对于选项A:先访问$V_1$,然后访问与$V_1$邻接且未被访问的任意一个顶点(满足的有$V_2, V_3$和$V_5$),此时访问$V_5$,然后从$V_5$出发,访问与$V_5$邻接且未被访问的任意一个顶点(满足的只有$V_4$),然后从$V_4$出发,访问与$V_4$邻接且未被访问的任意一个顶点(满足的只有$V_3$),然后从$V_3$出发,访问与$V_3$邻接且未被访问的任意一个顶点(满足的只有$V_2$),结束遍历。选项B和C的分析方法与A相同。对于选项D,首先访问$V_1$,然后从$V_1$出发,访问与$V_1$邻接且未被访问的任意一个顶点(满足的有$V_2, V_3$和$V_5$),然后从$V_2$出发,访问与$V_2$邻接且未被访问的任意一个顶点(满足的只有$V_3$),按规则本应该访问$V_3$,但选项D却访问了$V_5$,错误。

#### 二、综合应用题

**01.【解答】**
根据G的邻接表不难画出图(a)。

(图(a) 图G: 顶点1,2,3,4,5, 边(1,2),(1,3),(1,4),(2,3),(2,5),(3,4),(4,5))
(图(b) 深度优先生成树: 顶点1,2,3,4,5, 边(1,2),(2,3),(3,4),(4,5))
(图(c) 广度优先生成树: 顶点1,2,3,4,5, 边(1,2),(1,3),(1,4),(2,5))

1) 采用深度优先遍历。深度优先搜索总是尽可能“深”地搜索图,根据存储结构可知深度优先搜索的路径次序为(1,2),(2,3),(3,4),(4,5),深度优先生成树如图(b)所示。需要注意的是,当存储结构固定时,生成树的树形也就固定了,比如不能先搜索(1,3)。
2) 采用广度优先遍历。广度优先搜索总是尽可能“广”地搜索图,一层一层地向外扩展,根据存储结构可知广度优先搜索的路径次序为(1,2),(1,3),(1,4),(2,5),广度优先生成树如图(c)所示。

**02.【解答】**
1) 右图不能被二分,左图能被二分,染色情况如下图所示。
(图示: 左图被染成两种颜色，相邻顶点颜色不同。右图为三角形，中心顶点为红色，周围两个顶点为蓝色)
蓝色 红色

2) 从任意一个结点开始,将其染成红色,并从该结点开始对整个图进行遍历,在遍历过程中,若当前遍历的结点a有一条边指向b,则可能出现三种情况:①b未被染色,将它染成与结点a不同的颜色,并且继续遍历与b相连的结点;②a与b的颜色相同,说明该图不能被二分,直接返回;③a与b的颜色不同,跳过b点。
3) 上述遍历无论是使用深度优先还是使用广度优先,时间复杂度都为$O(n+m)$,其中的n和m分别是顶点数和边数。需要一个数组来存储各结点的颜色及是否已访问,空间复杂度为$O(n)$。

**03.【解答】**
一个无向图 G是一棵树的条件是,G必须是无回路的连通图或有$n-1$条边的连通图。这里采用后者作为判断条件。对连通的判定,可以用能否一次遍历全部顶点来实现。可以采用深度优先搜索算法在遍历图的过程中统计可能访问到的顶点个数和边的条数,若一次遍历就能访问到n个顶点和n-1条边,则可断定此图是一棵树。算法实现如下:

```c
bool isTree(Graph& G) {
    for(i=1;i<=G.vexnum;i++)
        visited[i]=FALSE;           //访问标记 visited[]初始化
    int Vnum=0,Enum=0;              //记录顶点数和边数
    DFS(G,1,Vnum,Enum,visited);
    if(Vnum==G.vexnum&&Enum==2*(G.vexnum-1))
        return true;                //符合树的条件
    else
        return false;               //不符合树的条件
}
void DFS(Graph& G,int v, int& Vnum, int& Enum, int visited[]) {
    //深度优先遍历图G,统计访问过的顶点数和边数,通过Vnum 和 Enum 返回
    visited[v]=TRUE;Vnum++;         //作访问标记,顶点计数
    int w=FirstNeighbor(G,v);       //取v的第一个邻接顶点
    while(w!=-1){                   //当邻接顶点存在
        Enum++;                     //边存在,边计数
        if(!visited[w])             //当该邻接顶点未访问过
            DFS(G,w,Vnum,Enum,visited);
        w=NextNeighbor(G,v,w);
    }
}
```

**04.【解答】**
两个不同的遍历算法都采用从顶点$v_i$出发,依次遍历图中每个顶点,直到搜索到顶点$v_j$,若能够搜索到$v_j$,则说明存在由顶点$v_i$到顶点$v_j$的路径。
深度优先遍历算法的实现如下:

```c
int visited[MAXSIZE]={0}; //访问标记数组
void DFS(ALGraph G,int i,int j,bool &can_reach){
    //深度优先判断有向图G中顶点vi到顶点vj是否有路径,用can_reach来标识
    if(i==j) {
        can_reach=true;
        return;               //i就是j
    }
    visited[i]=1;             //置访问标记
    for(int p=FirstNeighbor(G,i);p>=0;p=NextNeighbor(G,i,p))
        if(!visited[p]&&!can_reach) //递归检测邻接点
            DFS(G,p,j,can_reach);
}
```

广度优先遍历算法的实现如下:

```c
int visited[MAXSIZE]={0};     //访问标记数组
int BFS(ALGraph G,int i,int j){
    //广度优先判断有向图G中顶点vi到顶点vj是否有路径,若是,则返回1,否则返回0
    InitQueue(Q); EnQueue(Q,i); //顶点i入队
    while(!isEmpty(Q)){           //非空循环
        DeQueue(Q,i);             //队头顶点出队
        visited[i]=1;             //置访问标记
        if(i==j) return 1;
        for(int p=FirstNeighbor(G,i);p;p=NextNeighbor(G,i,p)){ //检查所有邻接点
            if(p==j)              //若p==j,则查找成功
                return 1;
            if(!visited[p]){      //否则,顶点p入队
                EnQueue(Q,p);
                visited[p]=1;
            }
        }
    }
    return 0;
}
```

本题也可以这样解答:调用以$i$为参数的$DFS(G,i)$或$BFS(G,i)$,执行结束后判断$visited[j]$是否为TRUE,若是,则说明$v_j$已被遍历,图中必存在由$v_i$到$v_j$的路径。但此种解法每次都耗费最坏时间复杂度对应的时间,需要遍历与$v_i$连通的所有顶点。

**05.【解答】**
本题采用基于递归的深度优先遍历算法,从结点$u$出发,递归深度优先遍历图中结点,若访问到结点$v$,则输出该搜索路径上的结点。为此,设置一个path数组来存放路径上的结点(初始为空),d表示路径长度(初始为-1)。查找从顶点$u$到$v$的简单路径过程说明如下(假设查找函数名为$FindPath()$):
1) $FindPath(G,u,v,path,d): d++;path[d]=u$;若找到$u$的未访问过的相邻结点$u1$,则继续下去,否则置$visited[u]=0$并返回。
2) $FindPath(G,u1,v,path,d): d++;path[d]=u1$;若找到$u1$的未访问过的相邻结点$u2$,则继续下去,否则置$visited[u1]=0$。
3) 以此类推,继续上述递归过程,直到$ui=v$,输出path。
算法实现如下:

```c
void FindPath(AGraph *G,int u, int v,int path[], int d) {
    int w;
    ArcNode *p;
    d++;                       //路径长度增1
    path[d]=u;                 //将当前顶点添加到路径中
    visited[u]=1;              //置已访问标记
    if(u==v)
        print(path[]);         //找到一条路径则输出
                               //输出路径上的结点
    p=G->adjlist[u].firstarc;  //p指向u的第一个相邻点
    while(p!=NULL){
        w=p->adjvex;           //若顶点w未访问,递归访问它
        if(visited[w]==0)
            FindPath(G,w,v,path,d);
        p=p->nextarc;          //p指向u的下一个相邻点
    }
    visited[u]=0;              //恢复环境,使该顶点可重新使用
}
```

## 6.4 图的应用

本节是历年考查的重点。图的应用主要包括:最小生成(代价)树、最短路径、拓扑排序和关键路径。一般而言,这部分内容直接以算法设计题形式考查的可能性偏小,而更多的是结合图的实例来考查算法的具体操作过程,读者必须学会手工模拟给定图的各个算法的执行过程。此外,还需掌握对给定模型建立相应的图去解决问题的方法。

### 6.4.1 最小生成树

一个连通图的生成树包含图的所有顶点,并且只含尽可能少的边。对于生成树来说,若砍去一条边,则会使生成树变成非连通图;若增加一条边,则会在图中形成一条回路。
对于一个带权连通无向图G,生成树不同,每棵树的权(树中所有边的权值之和)也可能不同。权值之和最小的那棵生成树称为G的最小生成树(Minimum-Spanning-Tree, MST)。

**命题追踪** 最小生成树的性质(2012、2017)

不难看出,最小生成树具有如下性质:
1) 若图G中存在权值相同的边,则G的最小生成树可能不唯一,即最小生成树的树形不唯一。当图G中的各边权值互不相等时,G的最小生成树是唯一的;若无向连通图G的边数比顶点数少1,即G本身是一棵树时,则G的最小生成树就是它本身。
2) 虽然最小生成树不唯一,但其对应的边的权值之和总是唯一的,而且是最小的。
3) 最小生成树的边数为顶点数减1。

**命题追踪** 最小生成树中某顶点到其他顶点是否具有最短路径的分析(2023)

**注意**
最小生成树中所有边的权值之和最小,但不能保证任意两个顶点之间的路径是最短路径。如下图所示,最小生成树中A到C的路径长度为5,但图中A到C的最短路径长度为4。

(图示:左图为一个四边形ABCD,边权为AB(3),AD(1),BC(2),CD(4),AC(4),右图为最小生成树，边为AD(1),AB(3),BC(2))
```