# 26王道数据结构_content目录

### 第1章 绪论 (1)
- 1.1 数据结构的基本概念 (1)
  - 1.1.1 基本概念和术语 (1)
  - 1.1.2 数据结构三要素 (2)
  - 1.1.3 本节试题精选 (3)
  - 1.1.4 答案与解析 (4)
- 1.2 算法和算法评价 (4)
  - 1.2.1 算法的基本概念 (4)
  - 1.2.2 算法效率的度量 (5)
  - 1.2.3 本节试题精选 (6)
  - 1.2.4 答案与解析 (8)
- 归纳总结 (11)
- 思维拓展 (11)

### 第2章 线性表 (12)
- 2.1 线性表的定义和基本操作 (12)
  - 2.1.1 线性表的定义 (12)
  - 2.1.2 线性表的基本操作 (13)
  - 2.1.3 本节试题精选 (13)
  - 2.1.4 答案与解析 (14)
- 2.2 线性表的顺序表示 (14)
  - 2.2.1 顺序表的定义 (14)
  - 2.2.2 顺序表上基本操作的实现 (15)
  - 2.2.3 本节试题精选 (17)
  - 2.2.4 答案与解析 (20)
- 2.3 线性表的链式表示 (29)
  - 2.3.1 单链表的定义 (29)
  - 2.3.2 单链表上基本操作的实现 (30)
  - 2.3.3 双链表 (35)
  - 2.3.4 循环链表 (36)
  - 2.3.5 静态链表 (37)
  - 2.3.6 顺序表和链表的比较 (37)
  - 2.3.7 本节试题精选 (38)
  - 2.3.8 答案与解析 (44)
- 归纳总结 (61)
- 思维拓展 (61)

### 第3章 栈、队列和数组 (62)
- 3.1 栈 (62)
  - 3.1.1 栈的基本概念 (62)
  - 3.1.2 栈的顺序存储结构 (63)
  - 3.1.3 栈的链式存储结构 (65)
  - 3.1.4 本节试题精选 (66)
  - 3.1.5 答案与解析 (69)
- 3.2 队列 (75)
  - 3.2.1 队列的基本概念 (75)
  - 3.2.2 队列的顺序存储结构 (76)
  - 3.2.3 队列的链式存储结构 (78)
  - 3.2.4 双端队列 (80)
  - 3.2.5 本节试题精选 (81)
  - 3.2.6 答案与解析 (84)
- 3.3 栈和队列的应用 (89)
  - 3.3.1 栈在括号匹配中的应用 (89)
  - 3.3.2 栈在表达式求值中的应用 (90)
  - 3.3.3 栈在递归中的应用 (92)
  - 3.3.4 队列在层次遍历中的应用 (93)
  - 3.3.5 队列在计算机系统中的应用 (94)
  - 3.3.6 本节试题精选 (94)
  - 3.3.7 答案与解析 (96)
- 3.4 数组和特殊矩阵 (100)
  - 3.4.1 数组的定义 (100)
  - 3.4.2 数组的存储结构 (100)
  - 3.4.3 特殊矩阵的压缩存储 (101)
  - 3.4.4 稀疏矩阵 (104)
  - 3.4.5 本节试题精选 (104)
  - 3.4.6 答案与解析 (106)
- 归纳总结 (108)
- 思维拓展 (108)

### 第4章 串 (109)
- *4.1 串的定义和实现 (109)
  - 4.1.1 串的定义 (109)
  - 4.1.2 串的基本操作 (110)
  - 4.1.3 串的存储结构 (110)
- 4.2 串的模式匹配 (111)
  - 4.2.1 简单的模式匹配算法 (111)
  - 4.2.2 串的模式匹配算法——KMP算法 (112)
  - 4.2.3 KMP算法的进一步优化 (117)
  - 4.2.4 本节试题精选 (118)
  - 4.2.5 答案与解析 (119)
- 归纳总结 (123)
- 思维拓展 (123)

### 第5章 树与二叉树 (124)
- 5.1 树的基本概念 (124)
  - 5.1.1 树的定义 (124)
  - 5.1.2 基本术语 (125)
  - 5.1.3 树的性质 (126)
  - 5.1.4 本节试题精选 (126)
  - 5.1.5 答案与解析 (127)
- 5.2 二叉树的概念 (130)
  - 5.2.1 二叉树的定义及其主要特性 (130)
  - 5.2.2 二叉树的存储结构 (132)
  - 5.2.3 本节试题精选 (133)
  - 5.2.4 答案与解析 (136)
- 5.3 二叉树的遍历和线索二叉树 (140)
  - 5.3.1 二叉树的遍历 (140)
  - 5.3.2 线索二叉树 (145)
  - 5.3.3 本节试题精选 (148)
  - 5.3.4 答案与解析 (154)
- 5.4 树、森林 (171)
  - 5.4.1 树的存储结构 (171)
  - 5.4.2 树、森林与二叉树的转换 (172)
  - 5.4.3 树和森林的遍历 (174)
  - 5.4.4 本节试题精选 (175)
  - 5.4.5 答案与解析 (177)
- 5.5 树与二叉树的应用 (183)
  - 5.5.1 哈夫曼树和哈夫曼编码 (183)
  - 5.5.2 并查集 (186)
  - 5.5.3 本节试题精选 (188)
  - 5.5.4 答案与解析 (190)
- 归纳总结 (196)
- 思维拓展 (196)

### 第6章 图 (198)
- 6.1 图的基本概念 (198)
  - 6.1.1 图的定义 (198)
  - 6.1.2 本节试题精选 (201)
  - 6.1.3 答案与解析 (203)
- 6.2 图的存储及基本操作 (206)
  - 6.2.1 邻接矩阵法 (206)
  - 6.2.2 邻接表法 (207)
  - 6.2.3 十字链表 (209)
  - 6.2.4 邻接多重表 (209)
  - 6.2.5 图的基本操作 (210)
  - 6.2.6 本节试题精选 (211)
  - 6.2.7 答案与解析 (214)
- 6.3 图的遍历 (219)
  - 6.3.1 广度优先搜索 (219)
  - 6.3.2 深度优先搜索 (222)
  - 6.3.3 图的遍历与图的连通性 (223)
  - 6.3.4 本节试题精选 (223)
  - 6.3.5 答案与解析 (226)
- 6.4 图的应用 (231)
  - 6.4.1 最小生成树 (231)
  - 6.4.2 最短路径 (234)
  - 6.4.3 有向无环图描述表达式 (237)
  - 6.4.4 拓扑排序 (238)
  - 6.4.5 关键路径 (240)
  - 6.4.6 本节试题精选 (242)
  - 6.4.7 答案与解析 (252)
- 归纳总结 (267)
- 思维拓展 (268)

### 第7章 查找 (269)
- 7.1 查找的基本概念 (269)
- 7.2 顺序查找和折半查找 (270)
  - 7.2.1 顺序查找 (270)
  - 7.2.2 折半查找 (272)
  - 7.2.3 分块查找 (273)
  - 7.2.4 本节试题精选 (274)
  - 7.2.5 答案与解析 (277)
- 7.3 树形查找 (283)
  - 7.3.1 二叉排序树(BST) (283)
  - 7.3.2 平衡二叉树 (287)
  - 7.3.3 红黑树 (291)
  - 7.3.4 本节试题精选 (296)
  - 7.3.5 答案与解析 (300)
- 7.4 B树和B+树 (310)
  - 7.4.1 B树及其基本操作 (311)
  - 7.4.2 B+树的基本概念 (314)
  - 7.4.3 本节试题精选 (315)
  - 7.4.4 答案与解析 (317)
- 7.5 散列(Hash) 表 (323)
  - 7.5.1 散列表的基本概念 (323)
  - 7.5.2 散列函数的构造方法 (324)
  - 7.5.3 处理冲突的方法 (324)
  - 7.5.4 散列查找及性能分析的应用 (326)
  - 7.5.5 本节试题精选 (327)
  - 7.5.6 答案与解析 (330)
- 归纳总结 (336)
- 思维拓展 (336)

### 第8章 排序 (337)
- 8.1 排序的基本概念 (338)
  - 8.1.1 排序的定义 (338)
  - 8.1.2 本节试题精选 (338)
  - 8.1.3 答案与解析 (339)
- 8.2 插入排序 (339)
  - 8.2.1 直接插入排序 (339)
  - 8.2.2 折半插入排序 (340)
  - 8.2.3 希尔排序 (341)
  - 8.2.4 本节试题精选 (342)
  - 8.2.5 答案与解析 (344)
- 8.3 交换排序 (346)
  - 8.3.1 冒泡排序 (347)
  - 8.3.2 快速排序 (348)
  - 8.3.3 本节试题精选 (350)
  - 8.3.4 答案与解析 (353)
- 8.4 选择排序 (357)
  - 8.4.1 简单选择排序 (358)
  - 8.4.2 堆排序 (358)
  - 8.4.3 本节试题精选 (361)
  - 8.4.4 答案与解析 (364)
- 8.5 归并排序、基数排序和计数排序 (370)
  - 8.5.1 归并排序 (370)
  - 8.5.2 基数排序 (371)
  - *8.5.3 计数排序 (373)
  - 8.5.4 本节试题精选 (375)
  - 8.5.5 答案与解析 (377)
- 8.6 各种内部排序算法的比较及应用 (380)
  - 8.6.1 内部排序算法的比较 (380)
  - 8.6.2 内部排序算法的应用 (381)
  - 8.6.3 本节试题精选 (382)
  - 8.6.4 答案与解析 (384)
- 8.7 外部排序 (387)
  - 8.7.1 外部排序的基本概念 (387)
  - 8.7.2 外部排序的方法 (387)
  - 8.7.3 多路平衡归并与败者树 (388)
  - 8.7.4 置换-选择排序(生成初始归并段) (389)
  - 8.7.5 最佳归并树 (390)
  - 8.7.6 本节试题精选 (392)
  - 8.7.7 答案与解析 (394)
- 归纳总结 (398)
- 思维拓展 (399)

### 参考文献 (400)
