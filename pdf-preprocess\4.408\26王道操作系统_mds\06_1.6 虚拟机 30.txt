## 第1章 计算机系统概述

**命题追踪**
> 操作系统引导过程中创建的数据结构(2022)

②硬件自检。BIOS 程序在内存最开始的空间构建中断向量表,接下来的POST(通电自检)过程要用到中断功能。然后进行通电自检,检查硬件是否出现故障。如有故障,主板会发出不同含义的蜂鸣,启动中止;如无故障,屏幕会显示CPU、内存、硬盘等信息。
③加载带有操作系统的硬盘。通电自检后,BIOS 开始读取 Boot Sequence(通过CMOS 里保存的启动顺序,或者通过与用户交互的方式),将控制权交给启动顺序排在第一位的存储设备,然后CPU将该存储设备引导扇区的内容加载到内存中。
④加载主引导记录(MBR)。硬盘以特定的标识符区分引导硬盘和非引导硬盘。若发现一个存储设备不是可引导盘,就检查下一个存储设备。如无其他启动设备,就会死机。主引导记录 MBR的作用是告诉CPU去硬盘的哪个主分区去找操作系统。
⑤扫描硬盘分区表,并加载硬盘活动分区。MBR 包含硬盘分区表,硬盘分区表以特定的标识符区分活动分区和非活动分区。主引导记录扫描硬盘分区表,进而识别含有操作系统的硬盘分区(活动分区)。找到硬盘活动分区后,开始加载硬盘活动分区,将控制权交给活动分区。
⑥加载分区引导记录(PBR)。读取活动分区的第一个扇区,这个扇区称为分区引导记录(PBR),其作用是寻找并激活分区根目录下用于引导操作系统的程序(启动管理器)。
⑦加载启动管理器。分区引导记录搜索活动分区中的启动管理器,加载启动管理器。

**命题追踪**
> 操作系统运行的存储器(2013)

⑧加载操作系统。将操作系统的初始化程序加载到内存中执行。

### 1.6 虚拟机

#### 1.6.1 虚拟机的基本概念
虚拟机是指利用虚拟化技术,将一台物理机器虚拟化为多台虚拟机器,通过隐藏特定计算平台的实际物理特性,为用户提供抽象的、统一的、模拟的计算环境。有两类虚拟化方法。

1. **第一类虚拟机管理程序**
从技术上讲,第一类虚拟机管理程序就像一个操作系统,因为它是唯一一个运行在最高特权级的程序。它在裸机上运行并且具备多道程序功能。虚拟机管理程序向上层提供若干虚拟机,这些虚拟机是裸机硬件的精确复制品。因为每台虚拟机都与裸机相同,所以在不同的虚拟机上可以运行任何不同的操作系统。图1.7(a)中显示了第一类虚拟机管理程序。

* * *
**(a)**

| Excel | Word | Mplayer | Emacs |
| :---: | :---: | :---: | :--: |
| Windows | Linux | 控制域 |
| 第一类虚拟机管理程序 |
| 硬件(CPU、磁盘、网络、中断等) |

**(b)**

| 客户操作系统进程 |
| :---: |
| 宿主操作系统进程 |
| 客户操作系统(如Windows) |
| 第二类虚拟机管理程序 |
| 宿主操作系统(如Linux) |
| 硬件(CPU、磁盘、网络、中断等) |

**图1.7 两类虚拟机管理程序在系统中的位置**
* * *

虚拟机作为用户态的一个进程运行,不允许执行敏感指令。然而,虚拟机上的操作系统认为自己运行在内核态(实际上不是),称为虚拟内核态。虚拟机中的用户进程认为自己运行在用户态(实际上确实是)。当虚拟机操作系统执行了一条CPU处于内核态才允许执行的指令时,会陷入虚拟机管理程序。在支持虚拟化的CPU上,虚拟机管理程序检查这条指令是由虚拟机中的操作系统执行的还是由用户程序执行的。若是前者,则虚拟机管理程序将安排这条指令功能的正确执行。否则,虚拟机管理程序将模拟真实硬件面对用户态执行敏感指令时的行为。
在过去不支持虚拟化的CPU上,真实硬件不会直接执行虚拟机中的敏感指令,这些敏感指令被转为对虚拟机管理程序的调用,由虚拟机管理程序模拟这些指令的功能。

2. **第二类虚拟机管理程序**
图1.7(b)中显示了第二类虚拟机管理程序。它是一个依赖于 Windows、Linux等操作系统分配和调度资源的程序,很像一个普通的进程。第二类虚拟机管理程序仍然伪装成具有CPU和各种设备的完整计算机。VMware Workstation 是首个x86平台上的第二类虚拟机管理程序。
运行在两类虚拟机管理程序上的操作系统都称为客户操作系统。对于第二类虚拟机管理程序,运行在底层硬件上的操作系统称为宿主操作系统。
首次启动时,第二类虚拟机管理程序像一台刚启动的计算机那样运转,期望找到的驱动器可以是虚拟设备。然后将操作系统安装到虚拟磁盘上(其实只是宿主操作系统中的一个文件)。客户操作系统安装完成后,就能启动并运行。
虚拟化在 Web 主机领域很流行。没有虚拟化,服务商只能提供共享托管(不能控制服务器的软件)和独占托管(成本较高)。当服务商提供租用虚拟机时,一台物理服务器就可以运行多个虚拟机,每个虚拟机看起来都是一台完整的服务器,客户可以在虚拟机上安装自己想用的操作系统和软件,但是只需支付较低的费用,这就是市面上常见的“云”主机。
有的教材将第一类虚拟化技术称为裸金属架构,将第二类虚拟化技术称为寄居架构。

### 1.6.2 本节习题精选
**单项选择题**
01. 用( )设计的操作系统结构清晰且便于调试。
A. 分层式构架 B. 模块化构架 C. 微内核构架 D. 宏内核构架
02. 下列关于分层式结构操作系统的说法中,( )是错误的。
A. 各层之间只能是单向依赖或单向调用
B. 容易实现在系统中增加或替换一层而不影响其他层
C. 具有非常灵活的依赖关系
D. 系统效率较低
03. 下列选项中,( )不属于模块化操作系统的特点。
A. 很多模块化的操作系统,可以支持动态加载新模块到内核,适应性强
B. 内核中的某个功能模块出错不会导致整个系统崩溃,可靠性高
C. 内核中的各个模块,可以相互调用,无须通过消息传递进行通信,效率高
D. 各模块间相互依赖,相比于分层式操作系统,模块化操作系统更难调试和验证
04. 相对于微内核系统,( )不属于大内核操作系统的缺点。
A. 占用内存空间大 B. 缺乏可扩展性而不方便移植
C. 内核切换太慢 D. 可靠性较低
05. 下列说法中,( )不适合描述微内核操作系统。
A. 内核足够小 B. 功能分层设计
C. 基于C/S模式 D. 策略与机制分离
06. 对于以下五种服务,在采用微内核结构的操作系统中,( )不宜放在微内核中。
I. 进程间通信机制 II. 低级 I/O III. 低级进程管理和调度
IV. 中断和陷入处理 V. 文件系统服务
A. I、II和III B. II和V C. 仅V D. IV和V
07. 相对于传统操作系统结构,采用微内核结构设计和实现操作系统有诸多好处,下列( )是微内核结构的特点。
I. 使系统更高效 II. 添加系统服务时,不必修改内核
III. 微内核结构没有单一内核稳定 IV.使系统更可靠
A. I、III、IV B. I、II、IV C. II、IV D. I、IV
08. 下列关于操作系统结构的说法中,正确的是( )。
I. 当前广泛使用的Windows操作系统,采用的是分层式OS 结构
II. 模块化的OS 结构设计的基本原则是,每一层都仅使用其底层所提供的功能和服务,这样就使系统的调试和验证都变得容易
III. 因为微内核结构能有效支持多处理机运行,所以非常适合于分布式系统环境
IV. 采用微内核结构设计和实现操作系统具有诸多好处,如添加系统服务时,不必修改内核、使系统更高效。
A. I和II B. I和III C. III D. III和IV
09. 下列关于微内核操作系统的描述中,不正确的是( )。
A. 可增加操作系统的可靠性 B. 可提高操作系统的执行效率
C. 可提高操作系统的可移植性 D. 可提高操作系统的可拓展性
10. 下列关于操作系统外核(exokernel)的说法中,错误的是( )。
A. 外核可以给用户进程分配未经抽象的硬件资源
B. 用户进程通过调用“库”请求操作系统外核的服务
C. 外核负责完成进程调度
D. 外核可以减少虚拟硬件资源的“映射”开销,提升系统效率
11. 对于计算机操作系统引导,描述不正确的是( )。
A. 计算机的引导程序驻留在ROM中,开机后自动执行
B. 引导程序先做关键部位的自检,并识别已连接的外设
C. 引导程序会将硬盘中存储的操作系统全部加载到内存中
D. 若计算机中安装了双系统,引导程序会与用户交互加载有关系统
12. 存放操作系统自举程序的芯片是( )。
A. SRAM B. DRAM C. ROM D. CMOS
13. 计算机操作系统的引导程序位于( )中。
A. 主板BIOS B. 片外Cache C. 主存ROM区 D. 硬盘
14. 计算机的启动过程是( )。①CPU加电,CS:IP 指向FFFF0H;②进行操作系统引导;③执行JMP指令跳转到BIOS;④登记 BIOS中断程序入口地址;⑤硬件自检。
A. ①②③④⑤ B. ③④②①⑤ C. ①④⑤③② D. ①④②⑤③
15. 检查分区表是否正确,确定哪个分区为活动分区,并在程序结束时将该分区的启动程序(操作系统引导扇区)调入内存加以执行,这是( )的任务。
A. MBR B. 引导程序 C. 操作系统 D. BIOS
16. 下列关于虚拟机的说法中,正确的是( )。
I. 虚拟机可以用软件实现 II. 虚拟机可以用硬件实现
III. 多台虚拟机可同时运行在同一物理机器上,它实现了真正的并行
A. I和II B. I和III C. 仅I D. I、II和III
17. 下列关于VMware Workstation 虚拟机的说法中,错误的是( )。
A. 真实硬件不会直接执行虚拟机中的敏感指令
B. 虚拟机中只能安装一种操作系统
C. 虚拟机是运行在计算机中的一个应用程序
D. 虚拟机文件封装在一个文件夹中,并存储在数据存储器中
18. 虚拟机的实现离不开虚拟机管理程序(VMM),下列关于VMM的说法中正确的是( )。
I. 第一类 VMM 直接运行在硬件上,其效率通常高于第二类VMM
II. VMM的上层需要支持操作系统的运行、应用程序的运行,因此实现VMM的代码量通常大于实现一个完整操作系统的代码量
III. VMM可将一台物理机器虚拟化为多台虚拟机器
IV. 为了支持客户操作系统的运行,第二类VMM需要完全运行在最高特权级
A. I、II和III B. I和III C. I、III和IV D. I、II、III和IV
19. 【2013统考真题】计算机开机后,操作系统最终被加载到( )。
A. BIOS B. ROM C. EPROM D. RAM
20. 【2022统考真题】下列选项中,需要在操作系统进行初始化过程中创建的是( )。
A. 中断向量表 B. 文件系统的根目录
C. 硬盘分区表 D. 文件系统的索引节点表
21. 【2023统考真题】与宏内核操作系统相比,下列特征中,微内核操作系统具有的是( )。
I. 较好的性能 II. 较高的可靠性 III. 较高的安全性 IV. 较强的可扩展性
A. 仅II、IV B. 仅I、II、III C. 仅I、III、IV D. 仅II、III、IV

### 1.6.3 答案与解析
**单项选择题**
01. A
分层式结构简化了系统的设计和实现,每层只能调用紧邻它的低层的功能和服务,便于系统的调试和验证,若在调试某层时发现错误,则错误应在这层上,这是因为其低层都已调试好。
02. C
单向依赖是分层式OS的特点。分层式 OS中增加或替换一个层中的模块或整层时,只要不改变相应层间的接口,就不会影响其他层,因而易于扩充和维护。层次定义好后,相当于各层之间的依赖关系也就固定了,因此往往显得不够灵活,选项C错误。每执行一个功能,通常都要自上而下地穿越多层,增加了额外的开销,导致系统效率降低。
03. B
模块化操作系统的各功能模块都在内核中,且模块之间相互调用、相互依赖,任何一个模块出错,都可能导致整个内核崩溃。选项B的设置属于“移花接木”,正确的说法应该是:在微内核操作系统中,内核外的某个功能模块出错不会导致整个系统崩溃,可靠性高。
04. C
微内核和宏内核作为两种对立的结构,它们的优缺点也是对立的。微内核OS的主要缺点是性能问题,因为需要频繁地在内核态和用户态之间进行切换,因而切换开销偏大。
05. B
功能分层设计是分层式OS的特点。通常可以从四个方面来描述微内核OS:①内核足够小;②基于客户/服务器模式;③应用“机制与策略分离”原理;④采用面向对象技术。
06. C
进程(线程)之间的通信功能是微内核最频繁使用的功能,因此几乎所有微内核OS都将其放入微内核。低级I/O 和硬件紧密相关,因此应放入微内核。低级进程管理和调度属于调度功能的机制部分,应将它放入微内核。微内核 OS 将与硬件紧密相关的一小部分放入微内核处理,此时微内核的主要功能是捕获所发生的中断和陷入事件,并进行中断响应处理,识别中断或陷入的事件后,再发送给相关的服务器处理,所以中断和陷入处理也应放入微内核。而文件系统服务是放在微内核外的文件服务器中实现的,所以仅选项V不宜放在微内核中。
07. C
微内核结构需要频繁地在内核态和用户态之间进行切换,操作系统的执行开销相对偏大,那些移出内核的操作系统代码根据分层的原则被划分成若干服务程序,它们的执行相互独立,交互则都借助于微内核进行通信,影响了系统的效率,因此选项I不是优势。因为内核的服务变少,且一般来说内核的服务越少内核越稳定,所以选项III 错误。而选项II、IV正是微内核结构的优点。
08. C
Windows 是融合了宏内核和微内核的操作系统,选项I错误。选项II描述的是层次化构架的原则。微内核架构将操作系统的核心功能和其他服务分离,使不同的服务可在不同的处理器上并行执行,提高了系统的并发性和可扩展性;微内核架构可以方便地实现进程间的通信和同步,支持服务器之间的消息传递和远程过程调用,使得分布式系统的开发和管理更简单和高效,选项 III正确。添加系统服务时不必修改内核,这就使得微内核构架的可扩展性和灵活性更强;微内核构架的主要问题是性能问题,“使系统更高效”显然错误。
09. B
微内核会增加一些开销,如上下文切换、消息传递、数据拷贝等。这些开销会降低操作系统的执行效率,尤其是对一些频繁调用的服务。选项A、C、D均正确。
10. C
在拥有外核的操作系统中,外核只负责硬件资源的分配、回收、保护等,进程管理相关的工作仍然由内核负责。
11. C
常驻内存的只是操作系统内核,其他部分仅在需要时才调入。
12. C
BIOS(基本输入/输出系统)是一组固化在主板的ROM芯片上的程序,它包含系统设置程序、基本输入/输出程序、开机自检程序和系统启动自举程序等。
13. D
操作系统的引导程序位于磁盘活动分区的引导扇区中。引导程序分为两种:一种是位于 ROM中的自举程序(BIOS的组成部分),用于启动具体的设备;另一种是位于装有操作系统硬盘的活动分区的引导扇区中的引导程序(称为启动管理器),用于引导操作系统。
14. C
CPU 激活后,从顶端的地址FFFF0H 获得第一条执行的指令,这个地址仅有16字节,放不下一段程序,所以是一条JMP指令,以跳到更低地址去执行 BIOS程序。BIOS 程序在内存最开始的空间构建中断向量表和相应服务程序,在后续 POST 过程中要用到中断调用等功能。然后进行通电自检(Power-on Self Test, POST)以检测硬件是否有故障。完成POST后,BIOS 需要在硬盘、光驱或软驱等存储设备搜寻操作系统内核的位置以启动操作系统。
15. A
BIOS 将控制权交给排在首位的启动设备后,CPU 将该设备主引导扇区的内容[主引导记录(MBR)]加载到内存中,然后由MBR检查分区表,查找活动分区,并将该分区的引导扇区的内容[分区引导记录(PBR)]加载到内存加以执行。
16. A
软件能实现的功能也能由硬件实现,因为虚拟机软件能实现的功能也能由硬件实现,软件和硬件的分界面是系统结构设计者的任务,选项I和II正确。实现真正并行的是多核处理机,多台虚拟机同时运行在同一物理机器上,类似于多个程序运行在同一个系统中。
17. B
VMware Workstation 虚拟机属于第二类虚拟机管理程序,若真实硬件直接执行虚拟机中的敏感指令,则该指令非法时可能导致宿主操作系统崩溃,而这是不可能的,实际上是由第二类虚拟机管理程序模拟真实硬件环境。虚拟机看起来和真实物理计算机没什么两样,因此当然可以安装多个操作系统。VMware Workstation 就是一个安装在计算机上的程序,在创建虚拟机时,会为该虚拟机创建一组文件,这些虚拟机文件都存储在主机的磁盘上。
18. B
第一类VMM 直接运行在硬件上;第二类VMM 运行在宿主操作系统上,不能直接和硬件打交道,因此第一类VMM的效率通常更高。VMM的功能没有操作系统的功能复杂,其代码量少于一个完整的操作系统。选项III是基本概念。第一类VMM 运行在最高特权级(内核态),而第二类 VMM和普通应用程序的地位相同,通常运行在较低特权级(用户态)。
19. D
系统开机后,操作系统的程序会被自动加载到内存中的系统区,这段区域是RAM。部分未复习计组的读者对该内容可能不太熟悉,但熟悉了各类存储介质后,解答本题并不难。
20. A
在操作系统初始化的过程中需要创建中断向量表,以实现通电自检(POST), CPU 检测到中断信号后,根据中断号查询中断向量表,跳转到相应的中断处理程序,选项A正确。在硬盘逻辑格式化之前,需要先对硬盘进行分区,即创建硬盘分区表。分区完成后,对物理分区进行逻辑格式化(创建文件系统),为每个分区初始化一个特定的文件系统,并创建文件系统的根目录。若某个分区采用UNIX文件系统,则还要在该分区中建立文件系统的索引节点表。
21. D
微内核构架将内核中最基本的功能保留在内核,只有微内核运行在内核态,其余模块都运行在用户态,一个模块中的错误只会使这个模块崩溃,而不会使整个系统崩溃,因此具有较高的可靠性和安全性。微内核的非核心功能运行在用户空间,可通过插件或模块的方式进行扩展,无须改动内核代码,因此具有较强的可扩展性。微内核需要频繁地在用户态和内核态之间进行切换,操作系统的执行开销偏大,从而影响系统性能。

### 1.7 本章疑难点

1. **并行性与并发性的区别和联系**
并行性和并发性是既相似又有区别的两个概念。并行性是指两个或多个事件在同一时刻发生,并发性是指两个或多个事件在同一时间间隔内发生。
在多道程序环境下,并发性是指在一段时间内,宏观上有多个程序同时运行,但在单处理器系统中每个时刻却仅能有一道程序执行,因此微观上这些程序只能分时地交替执行。若在计算机