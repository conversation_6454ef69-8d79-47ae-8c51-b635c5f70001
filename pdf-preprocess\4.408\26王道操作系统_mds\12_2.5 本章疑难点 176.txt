**注意**
在银行家算法中,当实际计算分析系统安全状态时,并不需要逐个进程进行。如本题中,在情况1)下,当计算到进程$P_2$结束并释放资源时,系统当前空闲资源可满足余下任意一个进程的最大需求量,这时已经不需要考虑进程的执行顺序。系统分配任意一个进程所需的最大需求资源,在其执行结束释放资源后,系统当前空闲资源会增加,所以余下的进程仍然可以满足最大需求量。因此,在这里可以直接判断系统处于安全状态。在情况2)下,系统当前可满足进程$P_0, P_3$的需求,所以可以直接让系统推进到$P_0, P_3$执行完并释放资源后的情形,这时系统出现死锁;因为此时是系统空闲资源所能达到的最大值,所以按照其他方式推进,系统必然还是出现死锁。因此,在计算过程中,将每步中可满足需求的进程作为一个集合,同时执行并释放资源,可以简化银行家算法的计算。

### 2.5 本章疑难点
1. 进程与程序的区别与联系
1) 进程是程序及其数据在计算机上的一次运行活动,是一个动态的概念。进程的运行实体是程序,离开程序的进程没有存在的意义。从静态角度看,进程是由程序、数据和进程控制块(PCB)三部分组成的。而程序是一组有序的指令集合,是一种静态的概念。
2) 进程是程序的一次执行过程,它是动态地创建和消亡的,具有一定的生命周期,是暂时存在的;而程序则是一组代码的集合,是永久存在的,可长期保存。
3) 一个进程可以执行一个或几个程序,一个程序也可构成多个进程。进程可创建进程,而程序不可能形成新的程序。
4) 进程与程序的组成不同。进程的组成包括程序、数据和PCB。
2. 银行家算法的工作原理
银行家算法的主要思想是避免系统进入不安全状态。在每次进行资源分配时,它首先检查系统是否有足够的资源满足要求,若有则先进行试分配,并对分配后的新状态进行安全性检查。若新状态安全,则正式分配上述资源,否则拒绝分配上述资源。这样,它保证系统始终处于安全状态,从而避免了死锁现象的发生。
3. 进程同步、互斥的区别与联系
并发进程的执行会产生相互制约的关系:一种是进程之间竞争使用临界资源,只能让它们逐个使用,这种现象称为互斥,是一种竞争关系;另一种是进程之间协同完成任务,在关键点上等待另一个进程发来的消息,以便协同一致,是一种协作关系。

---
## 第3章 内存管理

**【考纲内容】**
(一) 内存管理基础
内存管理概念:逻辑地址与物理地址空间,地址变换,内存共享,内存保护,
内存分配与回收
连续分配管理方式;页式管理;段式管理;段页式管理
(二) 虚拟内存管理
虚拟内存基本概念;请求页式管理;页框分配与回收;页置换算法
内存映射文件(Memory-Mapped Files);虚拟存储器性能的影响因素及改进方式

扫一扫
视频讲解

**【复习提示】**
内存管理和进程管理是操作系统的核心内容,需要重点复习。本章围绕分页机制展开:通过分页管理方式在物理内存大小的基础上提高内存的利用率,再进一步引入请求分页管理方式,实现虚拟内存,使内存脱离物理大小的限制,从而提高处理器的利用率。

### 3.1 内存管理概念

在学习本节时,请读者思考以下问题:
1) 为什么要进行内存管理?
2) 多级页表解决了什么问题?又会带来什么问题?
在学习经典的管理方法前,同样希望读者先思考,自己给出一些内存管理的想法,并在学习过程中和经典方案进行比较。注意本节给出的内存管理是循序渐进的,后一种方法通常会解决前一种方法的不足。希望读者多多思考,比较每种方法的异同,着重掌握页式管理。

### 3.1.1 内存管理的基本原理和要求
内存管理(Memory Management)是操作系统设计中最重要和最复杂的内容之一。虽然计算机硬件技术一直在飞速发展,内存容量也在不断增大,但仍然不可能将所有用户进程和系统所需要的全部程序与数据放入主存,因此操作系统必须对内存空间进行合理的划分和有效的动态分配。操作系统对内存的划分和动态分配,就是内存管理的概念。
有效的内存管理在多道程序设计中非常重要,它不仅可以方便用户使用存储器、提高内存利用率,还可以通过虚拟技术从逻辑上扩充存储器。内存管理的主要功能有:
* 内存空间的分配与回收。由操作系统负责内存空间的分配和管理,记录内存的空闲空间、内存的分配情况,并回收已结束进程所占用的内存空间。