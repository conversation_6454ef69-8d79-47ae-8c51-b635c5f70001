2) 用C语言描述的算法如下:
```c
int uniquely (MGraph G){//判定有向图是否存在唯一的拓扑序列
    int *degree,i,j,count=0,in0=-1,prev_in0;
    degree= (int*)malloc(G.numVertices*sizeof(int));
    for(j=0;j<G.numVertices;j++){//计算各顶点的入度
        degree[j]=0;
        for(i=0;i<G.numVertices;i++)
            degree[j]+=G.Edge[i][j];
        if (degree[j]==0){
            if (in0==-1) in0=j;        //入度为0的顶点
            else in0=-2;            //有多个入度为0的顶点
        }
    }
    while (in0>=0) {
        count++;
        prev_in0=in0;
        in0=-1;
        for(j=0;j<G.numVertices;j++)
            if (G.Edge[prev_in0][j]>0){
                if(--degree[j]==0){ //邻接点入度值减1
                    if (in0==-1) in0=j; //入度为0的顶点
                    else in0=-2;    //有多个入度为0的顶点
                }
            }
    }
    free(degree);
    if (count==G.numVertices) return 1;
    else return 0;
}
```

### 归纳总结

1. 关于图的基本操作
本章中的很多程序对采用邻接表或邻接矩阵的存储结构都适用,主要原因是在图的基本操作函数中保持了相同的参数和返回值,而封闭了内部实现细节。
例如,取x邻接顶点y的下一个邻接顶点的函数 NextNeighbor(G,x,y)。

1) 用邻接矩阵作为存储结构
```c
int NextNeighbor(MGraph& G, int x, int y) {
    if (x!=-1 && y!=-1){
        for (int col=y+1; col<G.vexnum; col++)
            if (G.Edge[x][col]>0 && G.Edge[x][col]<maxWeight) //maxWeight 代表∞
                return col;
    }
    return -1;
}
```

2) 用邻接表作为存储结构
```c
int NextNeighbor(ALGraph& G, int x, int y) {
    if (x!=-1) { //顶点x存在
        ArcNode *p=G.vertices[x].first; //对应边链表第一个边结点
        while (p!=NULL && p->data!=y)   //寻找邻接顶点y
            p=p->next;
        if (p!=NULL && p->next!=NULL)
            return p->next->data;       //返回下一个邻接顶点
    }
    return -1;
}
```

2. 关于图的遍历、连通性、生成树、关键路径的几个要点
1) 在执行图的遍历时,因为图中可能存在回路,且图的任意一个顶点都可能与其他顶点相连,所以在访问完某个顶点后可能会沿某些边又回到了曾经访问过的顶点。因此,需要设置一个辅助数组`visited[]`标记顶点是否已被访问过,避免重复访问。
2) 深度优先搜索时利用回溯法对图遍历,一般利用递归方法实现,每当向前递归查找某一邻接结点之前,必须判断该结点是否访问过。另外,递归算法均可借助栈来实现非递归算法,深度优先搜索也不例外,具体程序见6.3.4节的综合应用题03。
3) 广度优先搜索是一种分层的遍历过程,每向前走一步可能访问一批顶点,不像深度优先搜索那样有回退的情况。因此,它不是一个递归的过程。
4) 一个给定的图的邻接矩阵表示是唯一的,但对于邻接表来说,若边的输入先后次序不同,则生成的邻接表表示也不同。
5) 图的最小生成树首先必须是带权连通图,其次要在$n$个顶点的图中选择$n-1$条边将其连通,使得其权值总和达到最小,且不出现回路。
6) 加速某一关键活动不一定能缩短整个工程的工期,因为AOE网中可能存在多条关键路径。可能存在称为桥的一种特殊关键活动,它位于所有的关键路径上,只有它加速才会缩短整个工期。

### 思维拓展

【网易有道笔试题】求一个无向连通图的割点。割点的定义是,若除去此结点和与其相关的边,无向图不再连通,描述算法。

**提示**
要判断一个点是否为割点,最简单直接的方法是,先把这个点和所有与它相关的边从图中去掉,再用深搜或广搜来判断剩下的图的连通性,这种方法适合判断给定结点是否为割点;还有一种比较复杂的方法可以快速找出所有割点,有兴趣的读者可自行搜索相关资料。