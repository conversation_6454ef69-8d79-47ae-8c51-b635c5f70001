03.【解答】
cmpCountSort 算法基于计数排序的思想,对序列进行排序。cmpCountSort 算法遍历数组中的元素,count 数组记录比对应待排序数组元素下标大的元素个数,例如,count[1]=3 的意思是数组a中有三个元素比a[1]小,即a[1]是第四大元素,a[1]的正确位置应是b[3]。
1) 排序结果为b[6]={-10,10,11,19,25,25}。
2) 由代码 for(i=0;i<n-1;i++)和 for(j=i+1;j<n;j++)可知,在循环过程中,每个元素都与它后面的所有元素比较一次(所有元素都两两比较一次),比较次数之和为$(n-1)+(n-2)+……+1$,所以总比较次数是$n(n-1)/2$。
3) 不是。需要将程序中的if语句修改如下:
```
if (a[i]<=a[j]) count[j]++;
else count[i]++;
```
若不加等号,两个相等的元素比较时,前面元素的count值会加1,则导致原序列中靠前的元素在排序后的序列中处于靠后的位置。

## 8.6 各种内部排序算法的比较及应用

### 8.6.1 内部排序算法的比较
前面讨论的排序算法很多,对各种排序算法的比较是考研常考的内容。一般基于五个因素进行对比:时间复杂度、空间复杂度、稳定性、适用性和过程特征。

**命题追踪** ▶ 各种排序算法的特点、比较和适用场景(2017、2020、2022)

**从时间复杂度看：**简单选择排序、直接插入排序和冒泡排序平均情况下的时间复杂度都为$O(m^2)$,且实现过程也较为简单,但直接插入排序和冒泡排序最好情况下的时间复杂度可以达到$O(n)$,而简单选择排序则与序列的初始状态无关。希尔排序作为插入排序的拓展,对较大规模的数据都可以达到很高的效率,但目前未得出其精确的渐近时间。堆排序利用了一种称为堆的数据结构,可以在线性时间内完成建堆,且在$O(n\log n)$内完成排序过程。快速排序基于分治的思想,虽然最坏情况下的时间复杂度会达到$O(m^2)$,但快速排序的平均性能可以达到$O(n\log_2n)$,在实际应用中常常优于其他排序算法。归并排序同样基于分治的思想,但其分割子序列与初始序列的排列无关,因此它的最好、最坏和平均时间复杂度均为$O(n\log_2n)$。

**从空间复杂度看：**简单选择排序、插入排序、冒泡排序、希尔排序和堆排序都仅需借助常数个辅助空间。快速排序需要借助一个递归工作栈,平均大小为$O(\log_2n)$,当然在最坏情况下可能会增长到$O(n)$。二路归并排序在合并操作中需要借助较多的辅助空间用于元素复制,大小为$O(n)$,虽然有方法能克服这个缺点,但其代价是算法会很复杂而且时间复杂度会增加。

**命题追踪** ▶ 排序算法的稳定性判断及改进(2021、2023)

**从稳定性看：**插入排序、冒泡排序、归并排序和基数排序是稳定的排序算法,而简单选择排序、快速排序、希尔排序和堆排序都是不稳定的排序算法。平均时间复杂度为$O(n\log_2n)$的稳定排序算法只有归并排序,对于不稳定的排序算法,只需举出一个不稳定的实例即可。对于排序算法的稳定性,读者应能从算法本身的原理上去理解,而不应拘泥于死记硬背。

**命题追踪** ▶ 更适合采用顺序存储的排序算法(2017)

**从适用性看：**折半插入排序、希尔排序、快速排序和堆排序适用于顺序存储。直接插入排序、冒泡排序、简单选择排序、归并排序和基数排序既适用于顺序存储,又适用于链式存储。

**命题追踪** ▶ 根据排序的中间过程判断所采用的排序算法(2009、2010)

**命题追踪** ▶ 每趟排序后都至少能确定一个元素的最终位置的排序算法(2012)

**从过程特征看：**采用不同的排序算法,在一趟或几趟处理后的排序结果通常是不同的,考研题中经常出现给出一个待排序的初始序列和已部分排序的序列,问其采用何种排序算法。这就要对各类排序算法的过程特征十分熟悉,如冒泡排序、简单选择排序和堆排序在每趟处理后都能产生当前的最大值或最小值,而快速排序一趟处理至少能确定一个元素的最终位置等。

表8.1列出了各种排序算法的时空复杂度和稳定性情况,其中空间复杂度仅列举了平均情况的复杂度,因为希尔排序的时间复杂度依赖于增量函数,所以无法准确给出其时间复杂度。

**表8.1 各种排序算法的性质**
| 算法种类 | 最好情况 | 平均情况 | 最坏情况 | 空间复杂度 | 是否稳定 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 直接插入排序 | $O(n)$ | $O(n^2)$ | $O(n^2)$ | $O(1)$ | 是 |
| 冒泡排序 | $O(n)$ | $O(n^2)$ | $O(n^2)$ | $O(1)$ | 是 |
| 简单选择排序 | $O(n^2)$ | $O(n^2)$ | $O(n^2)$ | $O(1)$ | 否 |
| 希尔排序 | | | | $O(1)$ | 否 |
| 快速排序 | $O(n\log n)$ | $O(n\log n)$ | $O(n^2)$ | $O(\log n)$ | 否 |
| 堆排序 | $O(n\log_2n)$ | $O(n\log_2n)$ | $O(n\log_2n)$ | $O(1)$ | 否 |
| 二路归并排序 | $O(n\log_2n)$ | $O(n\log_2n)$ | $O(n\log_2n)$ | $O(n)$ | 是 |
| 基数排序 | $O(d(n+r))$ | $O(d(n+r))$ | $O(d(n+r))$ | $O(r)$ | 是 |

### 8.6.2 内部排序算法的应用
通常情况,对排序算法的比较和应用应考虑以下情况。

**命题追踪** ▶ 选取排序算法时需要考虑的因素(2019)

**1. 选取排序算法需要考虑的因素**
1) 待排序的元素个数$n$。
2) 待排序的元素的初始状态。
3) 关键字的结构及其分布情况。
4) 稳定性的要求。
5) 存储结构及辅助空间的大小限制等。
**2. 排序算法小结**
1) 若$n$较小,可采用直接插入排序或简单选择排序。直接插入排序所需的记录移动次数较简单选择排序的多,因此当记录本身信息量较大时,用简单选择排序较好。
2) 若$n$较大,应采用时间复杂度为$O(n\log n)$的排序算法:快速排序、堆排序或归并排序。当待排序的关键字随机分布时,快速排序被认为是目前基于比较的内部排序算法中最好的算法。堆排序所需的辅助空间少于快速排序,且不会出现快速排序可能的最坏情况,这两种排序都是不稳定的。若要求稳定且时间复杂度为$O(n\log_2n)$,可选用归并排序。
3) 若文件的初始状态已按关键字基本有序,则选用直接插入或冒泡排序为宜。
4) 在基于比较的排序算法中,每次比较两个关键字的大小之后,仅出现两种可能的转移,因此可以用一棵二叉树来描述比较判定过程,由此可以证明:当文件的$n$个关键字随机分布时,任何借助于“比较”的排序算法,至少需要$O(n\log n)$的时间。
5) 若$n$很大,记录的关键字位数较少且可以分解时,采用基数排序较好。
6) 当记录本身信息量较大时,为避免耗费大量时间移动记录,可用链表作为存储结构。

### 8.6.3 本节试题精选
**一、单项选择题**
01. 若要求排序是稳定的,且关键字为实数,则在下列排序算法中应选()。
A. 直接插入排序 B. 选择排序 C. 基数排序 D. 快速排序
02. 以下排序算法中时间复杂度为$O(n\log n)$且稳定的是( )。
A. 堆排序 B. 快速排序 C. 归并排序 D. 直接插入排序
03. 设被排序的结点序列共有$n$个结点,在该序列中的结点已十分接近有序的情况下,用直接插入排序、归并排序和快速排序对其进行排序,这些算法的时间复杂度应为( )。
A. $O(n)$, $O(n)$, $O(n)$
B. $O(n)$, $O(n\log_2n)$, $O(n\log_2n)$
C. $O(n)$, $O(n\log n)$, $O(n^2)$
D. $O(n^2)$, $O(n\log n)$, $O(n^2)$
04. 下列排序算法中属于稳定排序的是(①),平均时间复杂度为$O(n\log_2n)$的是(②),在最好的情况下,时间复杂度可以达到线性时间的有(③)。(注:多选题)
I. 冒泡排序 II. 堆排序 III. 选择排序 IV. 直接插入排序
V. 希尔排序 VI. 归并排序 VII. 快速排序
05. 就排序算法所用的辅助空间而言,堆排序、快速排序和归并排序的关系是( )。
A. 堆排序<快速排序<归并排序 B. 堆排序<归并排序<快速排序
C. 堆排序>归并排序>快速排序 D. 堆排序>快速排序>归并排序
06. 排序趟数与序列的初始状态无关的排序算法是( )。
I. 快速排序 II. 简单选择排序 III. 冒泡排序 IV. 基数排序
A. I、III B. II、IV C. I、II、IV D. I、IV
07. 排序趟数与序列的初始状态有关的排序算法是( )。
A. 直接插入排序 B. 二路归并排序 C. 快速排序 D. 堆排序
08. 对$n$个元素进行排序,其排序趟数肯定为$n-1$趟的排序算法是( )。
A. 直接插入排序和快速排序 B. 冒泡排序和快速排序
C. 简单选择排序和直接插入排序 D. 简单选择排序和冒泡排序
09. 若序列的初始状态为{1, 2, 3, 4, 5, 10, 6, 7, 8, 9},要想使得排序过程中的元素比较次数最少,则应该采用()方法。
A. 插入排序 B. 选择排序 C. 希尔排序 D. 冒泡排序
10. 对于元素个数相同的不同初始序列,总比较次数相同的排序算法是( )。
A. 折半插入排序和简单选择排序 B. 基数排序和归并排序
C. 冒泡排序和快速排序 D. 堆排序
11. 一般情况下,以下查找效率最低的数据结构是( )。
A. 有序顺序表 B. 二叉排序树 C. 堆 D. 平衡二叉树
12. 一台计算机具有多核CPU,可以同时执行相互独立的任务。归并排序的各个归并段可以并行执行,在下列排序算法中,不可以并行执行的有( )。
Ⅰ. 基数排序 II. 快速排序 III. 冒泡排序 IV. 堆排序
A. I、III
B. I、II
C. I、III、IV
D. II、IV
13. 【2009统考真题】若数据元素序列{11, 12, 13, 7, 8, 9, 23, 4, 5}是采用下列排序算法之一得到的第二趟排序后的结果,则该排序算法只能是( )。
A. 冒泡排序 B. 插入排序 C. 选择排序 D. 二路归并排序
14. 【2010统考真题】对一组数据{2, 12, 16, 88, 5, 10}进行排序,若前3趟排序结果如下:
第一趟排序结果: 2, 12, 16, 5, 10, 88
第二趟排序结果: 2, 12, 5, 10, 16, 88
第三趟排序结果: 2, 5, 10, 12, 16, 88
则采用的排序算法可能是( )。
A. 冒泡排序 B. 希尔排序 C. 归并排序 D. 基数排序
15. 【2012统考真题】在内部排序过程中,对尚未确定最终位置的所有元素进行一遍处理称为一趟排序。下列排序算法中,每趟排序结束都至少能够确定一个元素最终位置的方法是( )。
I. 简单选择排序 II. 希尔排序 III. 快速排序
IV. 堆排序 V. 二路归并排序
A. 仅I、III、IV B. 仅I、III、V C. 仅II、III、IV D. 仅III、IV、V
16. 【2017统考真题】在内部排序时,若选择了归并排序而未选择插入排序,则可能的理由是( )。
I. 归并排序的程序代码更短 II. 归并排序的占用空间更少
III. 归并排序的运行效率更高
A. 仅II B. 仅III C. 仅I、II D. 仅I、III
17. 【2017统考真题】下列排序算法中,若将顺序存储更换为链式存储,则算法的时间效率会降低的是( )。
I. 插入排序 II. 选择排序 III. 冒泡排序 IV. 希尔排序 V. 堆排序
A. 仅I、II B. 仅II、III C. 仅III、IV D. 仅IV、V
18. 【2019统考真题】选择一个排序算法时,除算法的时空效率外,下列因素中,还需要考虑的是( )。
Ⅰ. 数据的规模 II. 数据的存储方式 III. 算法的稳定性 IV. 数据的初始状态
A. 仅III B. 仅I、II C. 仅II、III、IV D. I、II、III、IV
19. 【2020统考真题】对大部分元素已有序的数组排序时,直接插入排序比简单选择排序效率更高,其原因是( )。
I. 直接插入排序过程中元素之间的比较次数更少
II. 直接插入排序过程中所需的辅助空间更少
III. 直接插入排序过程中元素的移动次数更少
A. 仅Ⅰ B. 仅III C. 仅I、II D. I、II和III
20. 【2022统考真题】对数据进行排序时,若采用直接插入排序而不采用快速排序,则可能的原因是( )。
I. 大部分元素已有序 II. 待排序元素数量很少
III. 要求空间复杂度为$O(1)$ IV. 要求排序算法是稳定的
A. 仅I、II B. 仅III、IV C. 仅I、II、IV D. I、II、III、IV
21. 【2023统考真题】下列排序算法中,不稳定的是( )。
I. 希尔排序 II. 归并排序 III. 快速排序 IV. 堆排序 V. 基数排序
A. 仅I、II B. 仅II、V C. 仅I、III、IV D. 仅III、IV、V

**二、综合应用题**
01. 设关键字序列为{3, 7, 6, 9, 7, 1, 4, 5, 20},对其进行排序的最小交换次数是多少?
02. 设顺序表用数组A[]表示,表中元素存储在数组下标1~m+n的范围内,前m个元素递增有序,后n个元素递增有序,设计一个算法,使得整个顺序表有序。
1) 给出算法的基本设计思想。
2) 根据设计思想,采用C/C++描述算法,关键之处给出注释。
3) 说明你所设计算法的时间复杂度与空间复杂度。
03. 设有一个数组中存放了一个无序的关键序列$K_1, K_2, \dots, K_n$。现要求将$K_s$放在将元素排序后的正确位置上,试编写实现该功能的算法,要求比较关键字的次数不超过$n$。

### 8.6.4 答案与解析
**一、单项选择题**
01. A
采用排除法。题目要求是稳定排序,因此排除选项 B 和 D,又基数排序不能对 float 和 double 类型的实数进行排序,因此排除选项 C。
02. C
堆排序和快速排序不是稳定排序算法,而直接插入排序算法的时间复杂度为$O(n^2)$。
03. C
各种排序算法的时间和空间复杂度、稳定性等见表8.1。
04. ① I、IV、VI ② II、VI、VII ③ I、IV
读者应能从算法的原理上理解算法的稳定性情况。堆排序和归并排序在最坏情况下的时间复杂度与最好情况下的时间复杂度是同一数量级的,都是$O(n\log_2n)$。
05. A
堆排序的空间复杂度为$O(1)$,因此快速排序的空间复杂度在最坏情况下为$O(n)$,平均空间复杂度为$O(\log_2n)$,归并排序的空间复杂度为$O(n)$。
06. B
冒泡排序的趟数为$1 \sim n-1$,和序列初态有关。简单选择排序每趟都选出一个最小(或最大)的元素,排序趟数固定为$n-1$。基数排序每趟都要进行分配和收集,排序趟数固定为$d$。快速排序的趟数和枢轴的选取有关,即和划分是否对称有关,当划分的两个区域分别包含0个元素和$n-1$个元素时,这种最大限度的不对称性发生在序列初态基本有序时。
07. C
直接插入排序的趟数始终为$n-1$,而与序列的初始状态无关。二路归并排序的趟数始终为$\lceil\log_2n\rceil$,而与序列的初始状态无关。堆排序每趟选出一个最大元素或最小元素,然后调整堆,初始建好堆后,需要$n-1$趟输出和调整堆的操作,而与序列的初始状态无关。
08. C
简单选择排序和直接插入排序的趟数始终为$n-1$。冒泡排序的趟数为$1 \sim n-1$,快速排序的趟数为$\log_2n \sim n-1$,具体取决于序列的初始状态(快速排序还取决于划分方法)。
09. A
选择排序和序列初态无关,直接排除。初始序列基本有序时,插入排序比较次数较少。本题中,插入排序仅需比较$n-1+4$次,而希尔排序和冒泡排序的比较次数均远大于此。
10. A
简单选择排序的总比较次数显然是确定的。折半插入排序每趟的比较次数都为$O(\log_2m)$ ($m$为当前已排好序的子序列的长度),因此总比较次数也是确定的。基数排序不是基于比较的排序算法。其他几种排序算法的比较次数显然和序列的初始状态有关。
11. C
堆是用于排序的,在查找时它是无序的,所以效率没有其他查找结构的高。
12. A
基数排序每趟需要利用前一趟已排好的序列,无法并行执行。快速排序每趟划分的子序列互不影响,可以并行执行。冒泡排序每趟对未排序的所有元素进行一趟处理,无法并行执行。堆排序可以并行执行,因为根结点的左右子树构成的子堆在执行过程中是互不影响的。
13. B
每趟冒泡和选择排序后,总会有一个元素被放置在最终位置上。显然,这里{11, 12}和{4, 5}所处的位置并不是最终位置,因此不可能是冒泡和选择排序。二路归并算法经过第二趟后应该是每4个元素有序的,但{11, 12, 13, 7}并非有序,因此也不可能是二路归并排序。
14. A
题中给出的排序过程,每一趟都是从前往后依次比较使最大值沉底,符合冒泡排序的特点。分别用其他3种排序算法尝试,归并排序第一趟后的结果为{2, 12, 16, 88, 5, 10},基数排序第一趟后的结果为{10, 2, 12, 5, 16, 88},希尔排序显然不符合。
15. A
对于选项I,简单选择排序每次选择未排序序列中的最小元素放入其最终位置。对于选项 II,希尔排序每次对划分的子表进行排序,得到局部有序的结果,所以不能保证每趟结束都能确定一个元素的最终位置。对于选项 III,快速排序每趟结束后都将枢轴元素放到最终位置。对于选项 IV,堆排序属于选择排序,每次都将大根堆的根结点与表尾结点交换,确定其最终位置。对于选项 V,二路归并排序每趟对子表进行两两归并,从而得到若干局部有序的结果,但无法确定最终位置。
16. B
归并排序的代码比插入排序的代码更为复杂,前者的空间复杂度是$O(n)$,后者是$O(1)$。但是前者的时间复杂度是$O(n\log n)$,后者是$O(n^2)$。
17. D
在顺序存储的条件下,插入排序、选择排序、冒泡排序的时间复杂度都是$O(n^2)$,更换为链式存储后的时间复杂度还是$O(n^2)$。希尔排序和堆排序都利用了顺序存储的随机访问特性,而链式存储不支持这种性质,所以时间复杂度会增加。
18. D
当数据规模较小时可选择复杂度为$O(n^2)$的简单排序算法,当数据规模较大时应选择复杂度为$O(n\log n)$的排序算法,当数据规模大到内存无法放下时需选择外部排序算法,选项I正确。数据的存储方式主要分为顺序存储和链式存储,有些排序算法(如堆排序)只能用于顺序存储方式,选项II正确。若对数据稳定性有要求,则不能选择不稳定的排序算法,选项III显然正确。当数据初始基本有序时,直接插入排序的效率最高,冒泡排序和直接插入排序的时间复杂度都是$O(n)$,而归并排序的时间复杂度依旧是$O(n\log n)$,选项IV正确。
19. A
考虑比较极端的情况,对于有序数组,直接插入排序的比较次数为$n-1$,简单选择排序的比较次数始终为$1+2+\dots+n-1=n(n-1)/2$,选项I正确。两种排序算法的辅助空间都是$O(1)$,无差别,选项II错误。初始有序时,移动次数均为0;对于通常情况,直接插入排序每趟插入都需要依次向后挪位,而简单选择排序只需与找到的最小元素交换位置,后者的移动次数少很多,选项 III 错误。
20. D
直接插入排序和快速排序的特点如下表所示。
| | 适合初始序列情况 | 适合元素数量 | 空间复杂度 | 稳定性 |
|---|---|---|---|---|
| 直接插入排序 | 大部分元素有序 | 较少 | $O(1)$ | 稳定 |
| 快速排序 | 基本无序 | 较多 | $O(\log n)$ | 不稳定 |
可见,选项I、II、III、IV 都是采用直接插入排序而不采用快速排序的可能原因。
21. C
稳定的内部排序算法:插入排序、冒泡排序、归并排序和基数排序。不稳定的内部排序算法:简单选择排序、快速排序、希尔排序和堆排序。

**二、综合应用题**
**01.【解答】**
直接插入排序的交换次数更多,因此应当采用简单选择排序。
初始序列: 3, 7, 6, 9, 7, 1, 4, 5, 20
第一次: 1, 7, 6, 9, 7, 3, 4, 5, 20 交换 1, 3
第二次: 1, 3, 6, 9, 7, 7, 4, 5, 20 交换 3, 7
第三次: 1, 3, 4, 9, 7, 7, 6, 5, 20 交换 4, 6
第四次: 1, 3, 4, 5, 7, 7, 6, 9, 20 交换 5, 9
第五次: 1, 3, 4, 5, 6, 7, 7, 9, 20 交换 6, 7
所以最小交换次数为5(注意这里求的是交换次数,而不是移动次数或比较次数)。

**02.【解答】**
1) 算法的基本设计思想如下:将数组A[1...m+n]视为一个已经过m趟插入排序的表,则从m+1趟开始,将后n个元素依次插入前面的有序表中。
2) 算法的实现如下:
```cpp
void Insert_Sort(ElemType A[], int m, int n) {
    int i,j;
    for(i=m+1; i<=m+n; i++){        //依次将A[m+1...m+n]插入有序表
        A[0]=A[i];                  //复制为哨兵
        for(j=i-1; A[j]>A[0]; j--)  //从后往前插入
            A[j+1]=A[j];            //元素后移
        A[j+1]=A[0];                //插入
    }
}
```
3) 时间复杂度由$m$和$n$共同决定,从上面的算法不难看出,在最坏情况下元素的比较次数为$O(mn)$,而元素移动的次数为$O(mn)$,所以时间复杂度为$O(mn)$。
因为算法只用到了常数个辅助空间,所以空间复杂度为$O(1)$。
此外,本题也可采用归并排序,将A[1...m]和A[m+1...m+n]视为两个待归并的有序子序列,算法的时间复杂度为$O(m+n)$,空间复杂度为$O(m+n)$。

**03.【解答】**
基本思想:以$K_s$为枢轴进行一趟快速排序。将快速排序算法改为以最后一个元素为枢轴,先从前往后,再从后往前。算法的代码如下:
```c
int Partition (ElemType K[], int n) {
    //交换序列K[1..n]中的记录,使枢轴到位,并返回其所在位置
    int i=1,j=n;            //设置两个交替变量初值分别为1和n
    ElemType pivot=K[j];    //枢轴
    while(i<j){             //循环跳出条件
        while(i<j&&K[i]<=pivot)
            i++;            //从前往后找比枢轴大的元素
        if(i<j)
            K[j]=K[i];      //移动到右端
        while(i<j&&K[j]>=pivot)
            j--;            //从后往前找比枢轴小的元素
        if(i<j)
            K[i]=K[j];      //移动到左端
    } //while
    K[i]=pivot;             //枢轴存放在最终位置
    return i;               //返回存放枢轴的位置
}
```

## 8.7 外部排序
外部排序可能会考查相关概念、方法和排序过程,外部排序的算法比较复杂,不会在算法设计上进行考查。本节的主要内容有:
1. 外部排序指的是大文件的排序,即待排序的记录存储在外存中,待排序的文件无法一次性装入内存,需要在内存和外存之间进行多次数据交换,以达到排序整个文件的目的。
2. 为减少平衡归并中外存读/写次数所采取的方法:增大归并路数和减少归并段个数。
3. 利用败者树增大归并路数。
4. 利用置换-选择排序增大归并段长度来减少归并段个数。
5. 由长度不等的归并段进行多路平衡归并,需要构造最佳归并树。

### 8.7.1 外部排序的基本概念
前面介绍过的排序算法都是在内存中进行的(称为内部排序)。而在许多应用中,经常需要对大文件进行排序,因为文件中的记录很多,无法将整个文件复制进内存中进行排序。因此,需要将待排序的记录存储在外存上,排序时再把数据一部分一部分地调入内存进行排序,在排序过程中需要多次进行内存和外存之间的交换。这种排序算法就称为外部排序。

### 8.7.2 外部排序的方法
文件通常是按块存储在磁盘上的,操作系统也是按块对磁盘上的信息进行读/写的。因为磁盘读/写的机械动作所需的时间远远超过在内存中进行运算的时间(相比而言可以忽略不计),因此在外部排序过程中的时间代价主要考虑访问磁盘的次数,即I/O次数。

**命题追踪** ▶ 对大文件排序时使用的排序算法(2016)

外部排序通常采用归并排序算法。它包括两个阶段:①根据内存缓冲区大小,将外存上的文件分成若干长度为$l$的子文件,依次读入内存并利用内部排序算法对它们进行排序,并将排序后得到的有序子文件重新写回外存,称这些有序子文件为归并段或顺串;②对这些归并段进行逐趟归并,使得归并段逐渐变长,最后形成一个有序文件。