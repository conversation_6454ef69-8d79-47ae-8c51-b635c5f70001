## 8.1 排序的基本概念

### 8.1.1 排序的定义

排序，就是重新排列表中的元素，使表中的元素满足按关键字有序的过程。为了查找方便，通常希望计算机中的表是按关键字有序的。排序的确切定义如下：
输入：n个记录$R_1, R_2, \dots, R_n$，对应的关键字为$k_1, k_2, \dots, k_n$。
输出：输入序列的一个重排$R'_1, R'_2, \dots, R'_n$，使得$k'_1 \le k'_2 \le \dots \le k'_n$（其中“$\le$”可以换成其他的比较大小的符号）。
算法的稳定性。若待排序表中有两个元素$R_i$和$R_j$，其对应的关键字相同，即$key_i = key_j$，且在排序前$R_i$在$R_j$的前面，若使用某一排序算法排序后，$R_i$仍然在$R_j$的前面，则称这个排序算法是稳定的，否则称这个排序算法是不稳定的。需要注意的是，算法是否具有稳定性并不能衡量一个算法的优劣，它主要是对算法的性质进行描述。若待排序表中的关键字不允许重复，排序结果是唯一的，则对于排序算法的选择，稳定与否无关紧要。

**注意**
对于不稳定的排序算法，只需举出一组关键字的实例，说明它的不稳定性即可。

在排序过程中，根据数据元素是否完全存放在内存中，可将排序算法分为两类：①内部排序，是指在排序期间元素全部存放在内存中的排序；②外部排序，是指在排序期间元素无法全部同时存放在内存中，必须在排序的过程中根据要求不断地在内、外存之间移动的排序。

一般情况下，内部排序算法在执行过程中都要进行两种操作：比较和移动。通过比较两个关键字的大小，确定对应元素的前后关系，然后通过移动元素以达到有序。当然，并非所有的内部排序算法都要基于比较操作，事实上，基数排序就不基于比较操作。

每种排序算法都有各自的优缺点，适合在不同的环境下使用，就其全面性能而言，很难提出一种被认为是最好的算法。通常可以将排序算法分为插入排序、交换排序、选择排序、归并排序和基数排序五大类，后面几节会分别进行详细介绍。内部排序算法的性能取决于算法的时间复杂度和空间复杂度，而时间复杂度一般是由比较和移动的次数决定的。

**注意**
大多数的内部排序算法都更适用于顺序存储的线性表。

### 8.1.2 本节试题精选

**单项选择题**

01. 下述排序算法中，不属于内部排序算法的是( )。
A. 插入排序
B. 选择排序
C. 拓扑排序
D. 冒泡排序

02. 排序算法的稳定性是指( )。
A. 经过排序后，能使关键字相同的元素保持原顺序中的相对位置不变
B. 经过排序后，能使关键字相同的元素保持原顺序中的绝对位置不变
C. 排序算法的性能与被排序元素个数关系不大
D. 排序算法的性能与被排序元素的个数关系密切

03. 下列关于排序的叙述中，正确的是( )。
A. 稳定的排序算法优于不稳定的排序算法
B. 对同一线性表使用不同的排序算法进行排序，得到的排序结果可能不同
C. 排序算法都是在顺序表上实现的，在链表上无法实现排序算法
D. 在顺序表上实现的排序算法在链表上也可以实现

### 8.1.3 答案与解析

**单项选择题**

01. C
拓扑排序是将有向图中所有结点排成一个线性序列，虽然也是在内存中进行的，但它不属于我们这里所提到的内部排序范畴，也不满足前面排序的定义。

02. A
注意，这里的绝对位置是指若在排序前元素$R$在位置$i$，则绝对位置就是$i$，即排序后$R$的位置不发生变化，显然选项B是不对的。选项C、D与题目要求无关。

03. B
算法的稳定性与算法优劣无关，选项A排除。使用链表也可以进行排序，只是有些排序算法不再适用，因为这时定位元素只能顺序逐链查找，如折半插入排序。

## 8.2 插入排序

插入排序是一种简单直观的排序算法，其基本思想是每次将一个待排序的记录按其关键字大小插入到前面已排好序的子序列中，直到全部记录插入完成。由插入排序的思想可以引申出三个重要的排序算法：直接插入排序、折半插入排序和希尔排序。

### 8.2.1 直接插入排序①

根据上面的插入排序思想，不难得出一种最简单也最直观的直接插入排序算法。假设在排序过程中，待排序表$L[1..n]$在某次排序过程中的某一时刻状态如下：

有序序列$L[1..i-1]$ $L(i)$ 无序序列$L[i+1..n]$

要将元素$L(i)$插入已有序的子序列$L[1..i-1]$，需要执行以下操作（为避免混淆，下面用L[]表示一个表，而用L()表示一个元素）：
1) 查找出$L(i)$在$L[1..i-1]$中的插入位置$k$。
2) 将$L[k..i-1]$中的所有元素依次后移一个位置。
3) 将$L(i)$复制到$L(k)$。
为了实现对$L[1..n]$的排序，可以将$L(2)\sim L(n)$依次插入前面已排好序的子序列，初始$L[1]$可以视为一个已排好序的子序列。上述操作执行$n-1$次就能得到一个有序的表。插入排序在实现上通常采用原地排序（空间复杂度为$O(1)$），因而在从后往前的比较过程中，需要反复把已排序元素逐步向后挪位，为新元素提供插入空间。

---
①在本书中，凡是没有特殊注明的，通常默认排序结果为非递减有序序列。