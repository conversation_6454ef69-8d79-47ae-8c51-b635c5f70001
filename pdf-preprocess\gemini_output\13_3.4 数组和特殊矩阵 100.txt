```markdown
```c
bool BracketsCheck(char *str) {
    InitStack(S); //初始化栈
    int i=0;
    while (str[i]!='\0') {
        switch(str[i]){
        //左括号入栈
        case '(': push(S,'('); break;
        case '[': push(S,'['); break;
        case '{': push(S,'{'); break;
        //遇到右括号,检测栈顶
        case ')': Pop(S,e);
            if(e!='(') return false;
            break;
        case ']': Pop(S,e);
            if(e!='[') return false;
            break;
        case '}': Pop(S,e);
            if(e!='{') return false;
            break;
        default:
            break;
        }//switch
        i++;
    }//while
    if(!IsEmpty(S)){
        printf("括号不匹配\n");
        return false;
    }
    else {
        printf("括号匹配\n");
        return true;
    }
}
```

### 3.4 数组和特殊矩阵

矩阵在计算机图形学、工程计算中占有举足轻重的地位。在数据结构中考虑的是如何用最小的内存空间来存储同样的一组数据。所以,我们不研究矩阵及其运算等,而把精力放在如何将矩阵更有效地存储在内存中,并能方便地提取矩阵中的元素。

### 3.4.1 数组的定义

数组是由$n(n \ge 1)$个相同类型的数据元素构成的有限序列,每个数据元素称为一个数组元素,每个元素在$n$个线性关系中的序号称为该元素的下标,下标的取值范围称为数组的维界。
数组与线性表的关系:数组是线性表的推广。一维数组可视为一个线性表;二维数组可视为其元素是定长数组的线性表,以此类推。数组一旦被定义,其维数和维界就不再改变。因此,除结构的初始化和销毁外,数组只会有存取元素和修改元素的操作。

### 3.4.2 数组的存储结构

大多数计算机语言都提供了数组数据类型,逻辑意义上的数组可采用计算机语言中的数组数据类型进行存储,一个数组的所有元素在内存中占用一段连续的存储空间。
以一维数组 A[0...n-1]为例,其存储结构关系式为
$$
LOC(a_i) = LOC(a_0)+i \times L \quad (0 \le i < n)
$$
其中,$L$是每个数组元素所占的存储单元。

**命题追踪** 二维数组按行优先存储的下标对应关系(2021)

对于多维数组,有两种映射方法:按行优先和按列优先。以二维数组为例,按行优先存储的基本思想是:先行后列,先存储行号较小的元素,行号相等先存储列号较小的元素。设二维数组的行下标与列下标的范围分别为$[0,h_1]$与$[0,h_2]$,则存储结构关系式为
$$
LOC(a_{ij}) = LOC(a_{00})+[i \times (h_2 + 1) + j] \times L
$$
例如,对于数组$A[2][3]$,它按行优先方式在内存中的存储形式如图3.18所示。
$A[2][3] = \begin{bmatrix} a_{00} & a_{01} & a_{02} \\ a_{10} & a_{11} & a_{12} \end{bmatrix}$
内存布局:
$a_{00} \ a_{01} \ a_{02} \ | \ a_{10} \ a_{11} \ a_{12}$
$\underbrace{\qquad \qquad}_{第1行} \ \underbrace{\qquad \qquad}_{第2行}$

图3.18 二维数组按行优先顺序存放

当以列优先方式存储时,得出存储结构关系式为
$$
LOC(a_{ij}) = LOC(a_{00})+[j \times (h_1 + 1) + i] \times L
$$
例如,对于数组$A[2][3]$,它按列优先方式在内存中的存储形式如图3.19所示。
$A[2][3] = \begin{bmatrix} a_{00} & a_{01} & a_{02} \\ a_{10} & a_{11} & a_{12} \end{bmatrix}$
内存布局:
$a_{00} \ a_{10} \ | \ a_{01} \ a_{11} \ | \ a_{02} \ a_{12}$
$\underbrace{\quad \quad}_{第1列} \ \underbrace{\quad \quad}_{第2列} \ \underbrace{\quad \quad}_{第3列}$

图3.19 二维数组按列优先顺序存放

### 3.4.3 特殊矩阵的压缩存储

压缩存储:指为多个值相同的元素只分配一个存储空间,对零元素不分配空间。
特殊矩阵:指具有许多相同矩阵元素或零元素,并且这些相同矩阵元素或零元素的分布有一定规律性的矩阵。常见的特殊矩阵有对称矩阵、上(下)三角矩阵、对角矩阵等。
特殊矩阵的压缩存储方法:找出特殊矩阵中值相同的矩阵元素的分布规律,把那些呈现规律性分布的、值相同的多个矩阵元素压缩存储到一个存储空间中。

1.  **对称矩阵**

**命题追踪** 对称矩阵压缩存储的下标对应关系(2018、2020)

若对一个$n$阶矩阵$A$中的任意一个元素$a_{ij}$都有$a_{ij} = a_{ji}$ ($1 \le i, j \le n$),则称其为对称矩阵。其中的元素可以划分为3个部分,即上三角区、主对角线和下三角区,如图3.20所示。
$$
\begin{matrix}
a_{1,1} & a_{1,2} & \cdots & a_{1,n} \\
a_{2,1} & a_{2,2} & \cdots & a_{2,n} \\
\vdots & \vdots & \ddots & \vdots \\
a_{n,1} & a_{n,2} & \cdots & a_{n,n}
\end{matrix}
$$
$i<j$ 上三角区
$i=j$ 主对角线
$i>j$ 下三角区

图3.20 n阶矩阵的划分

对于$n$阶对称矩阵,上三角区的所有元素和下三角区的对应元素相同,若仍采用二维数组存放,则会浪费几乎一半的空间,为此将$n$阶对称矩阵$A$存放在一维数组$B[n(n+1)/2]$中,即元素$a_{ij}$存放在$b_k$中。比如只存放下三角部分(含主对角线)的元素。
在数组$B$中,位于元素$a_{ij}$ ($i \ge j$)前面的元素个数为
第1行:1个元素($a_{1,1}$)。
第2行:2个元素($a_{2,1}, a_{2,2}$)。
......
第$i-1$行:$i-1$个元素($a_{i-1,1}, a_{i-1,2}, \dots, a_{i-1,i-1}$)。
第$i$行:$j-1$个元素($a_{i1}, a_{i2}, \dots, a_{ij-1}$)。
因此,元素$a_{ij}$在数组$B$中的下标$k=1+2+\dots+(i-1)+j-1=i(i-1)/2+j-1$(数组下标从0开始)。因此,元素下标之间的对应关系如下:
$$
k = \begin{cases}
\frac{i(i-1)}{2}+j-1, & i \ge j \text{ (下三角区和主对角线元素)} \\
\frac{j(j-1)}{2}+i-1, & i < j \text{ (上三角区元素 } a_{ij}=a_{ji})
\end{cases}
$$
当数组下标从1开始时,可以采用同样的推导方法,请读者自行思考。

**注**
二维数组$A[n][n]$和$A[0...n-1][0...n-1]$的写法是等价的。若数组写成$A[1...n][1...n]$,则表示指定了下标是从1开始的。二维数组元素写为$a[i][j]$,注意数组元素下标$i$和$j$通常是从0开始的。矩阵元素通常写为$a_{i,j}$,行号$i$和列号$j$通常是从1开始的。

2.  **三角矩阵**

下三角矩阵[见图3.22(a)]中,上三角区的所有元素均为同一常量。其存储思想与对称矩阵类似,不同之处在于存储完下三角区和主对角线上的元素之后,紧接着存储对角线上方的常量一次,所以可以将$n$阶下三角矩阵$A$压缩存储在$B[n(n+1)/2+1]$中。
元素下标之间的对应关系为
$$
k = \begin{cases}
\frac{i(i-1)}{2}+j-1, & i \ge j \text{ (下三角区和主对角线元素)} \\
\frac{n(n+1)}{2}, & i<j \text{ (上三角区元素)}
\end{cases}
$$
下三角矩阵在内存中的压缩存储形式如图3.21所示。

| 0 | 1 | 2 | 3 | 4 | 5 | ... | n(n+1)/2 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| $a_{1,1}$ | $a_{2,1}$ | $a_{2,2}$ | $a_{3,1}$ | $a_{3,2}$ | $a_{3,3}$ | ... | $a_{n,1} \dots a_{n,n}$ | c |
| 第1行 | 第2行 | 第3行 | | | | 第$n$行 | 常数项 |

图3.21 下三角矩阵的压缩存储

**命题追踪** 上三角矩阵采用行优先存储的应用(2011)

上三角矩阵[见图3.22(b)]中,下三角区的所有元素均为同一常量。只需存储主对角线、上三角区上的元素和下三角区的常量一次,可将其压缩存储在$B[n(n+1)/2+1]$中。

$$
\begin{pmatrix}
a_{1,1} & & & \\
a_{2,1} & a_{2,2} & & \\
\vdots & \vdots & \ddots & \\
a_{n,1} & a_{n,2} & \cdots & a_{n,n}
\end{pmatrix}
\quad
\begin{pmatrix}
a_{1,1} & a_{1,2} & \cdots & a_{1,n} \\
& a_{2,2} & \cdots & a_{2,n} \\
& & \ddots & \vdots \\
& & & a_{n,n}
\end{pmatrix}
$$
(a) 下三角矩阵
(b) 上三角矩阵

图3.22 三角矩阵

在数组$B$中,位于元素$a_{ij}$ ($i \le j$)前面的元素个数为
第1行:$n$个元素
第2行:$n-1$个元素
...
第$i-1$行:$n-i+2$个元素
第$i$行:$j-i$个元素
因此,元素$a_{ij}$在数组$B$中的下标
$k=n+(n-1)+\dots+(n-i+2)+(j-i+1)-1 = (i-1)(2n-i+2)/2+(j-i)$
因此,元素下标之间的对应关系如下:
$$
k = \begin{cases}
\frac{(i-1)(2n-i+2)}{2}+(j-i), & i \le j \text{ (上三角区和主对角线元素)} \\
\frac{n(n+1)}{2}, & i>j \text{ (下三角区元素)}
\end{cases}
$$
上三角矩阵在内存中的压缩存储形式如图3.23所示。

| 0 | 1 | ... | ... | n(n+1)/2 |
| :--- | :--- | :--- | :--- | :--- |
| $a_{1,1}$ | $a_{1,2}$ | ... | $a_{2,2}$ $a_{2,3}$ $a_{2,4}$ ... | ... $a_{n,n}$ | c |
| 第1行 | 第2行 | ... | 第$n$行 | 常数项 |

图3.23 上三角矩阵的压缩存储

以上推导均假设数组的下标从0开始,若题设有具体要求,则应该灵活应对。

3.  **三对角矩阵**

对角矩阵也称带状矩阵。对$n$阶矩阵$A$中的任意一个元素$a_{ij}$,当$|i-j|>1$时,若有$a_{ij}=0$ ($1 \le i,j \le n$),则称为三对角矩阵,如图3.24所示。在三对角矩阵中,所有非零元素都集中在以主对角线为中心的3条对角线的区域,其他区域的元素都为零。
$$
\begin{pmatrix}
a_{1,1} & a_{1,2} & & & 0 \\
a_{2,1} & a_{2,2} & a_{2,3} & & \\
& a_{3,2} & a_{3,3} & a_{3,4} & \\
& & \ddots & & \\
0 & & a_{n-1,n-2} & a_{n-1,n-1} & a_{n-1,n} \\
& & & a_{n,n-1} & a_{n,n}
\end{pmatrix}
$$
图3.24 三对角矩阵A

三对角矩阵$A$也可以采用压缩存储,将3条对角线上的元素按行优先方式存放在一维数组$B$中,且$a_{1,1}$存放于$B[0]$中,其存储形式如图3.25所示。
$a_{1,1} \quad a_{1,2} \quad a_{2,1} \quad a_{2,2} \quad a_{2,3} \quad \dots \quad a_{n-1,n} \quad a_{n,n-1} \quad a_{n,n}$

图3.25 三对角矩阵的压缩存储

**命题追踪** 三对角矩阵压缩存储的下标对应关系(2016)

由此可以计算矩阵$A$中3条对角线上的元素$a_{ij}$ ($1 \le i, j \le n, |i-j| \le 1$)在一维数组$B$中存放的下标为$k=2i+j-3$。
反之,若已知三对角矩阵中的某个元素$a_{ij}$存放在一维数组$B$的第$k$个位置,则有$i = \lfloor (k+1)/3+1 \rfloor$, $j=k-2i+3$。例如,当$k=0$时,$i=\lfloor(0+1)/3+1\rfloor=1$,$j=0-2 \times 1+3=1$,存放的是$a_{1,1}$;当$k=2$时,$i=\lfloor(2+1)/3+1\rfloor=2$,$j=2-2 \times 2+3=1$,存放的是$a_{2,1}$;当$k=4$时,$i=\lfloor(4+1)/3+1\rfloor=2$,$j=4-2 \times 2+3=3$,存放的是$a_{2,3}$。

### 3.4.4 稀疏矩阵

矩阵中非零元素的个数$t$,相对矩阵元素的个数$s$来说非常少,即$s \gg t$的矩阵称为稀疏矩阵。例如,一个矩阵的阶为$100 \times 100$,该矩阵中只有少于100个非零元素。

**命题追踪** 存储稀疏矩阵需要保存的信息(2023)

若采用常规的方法存储稀疏矩阵,则相当浪费存储空间,因此仅存储非零元素。但通常非零元素的分布没有规律,所以仅存储非零元素的值是不够的,还要存储它所在的行和列。因此,将非零元素及其相应的行和列构成一个三元组(行标$i$,列标$j$,值$a_{ij}$),如图3.26所示。然后按照某种规律存储这些三元组线性表。稀疏矩阵压缩存储后便失去了随机存取特性。
$$
M=
\begin{bmatrix}
4 & 0 & 0 & 0 \\
0 & 0 & 6 & 0 \\
0 & 9 & 0 & 0 \\
0 & 23 & 0 & 0
\end{bmatrix}
\quad
\text{对应的三元组}
\quad
\begin{tabular}{|c|c|c|}
\hline
$i$ & $j$ & $a_{ij}$ \\
\hline
0 & 0 & 4 \\
1 & 2 & 6 \\
2 & 1 & 9 \\
3 & 1 & 23 \\
\hline
\end{tabular}
$$
图3.26 稀疏矩阵及其对应的三元组

**命题追踪** 适合稀疏矩阵压缩存储的存储结构(2017)

稀疏矩阵的三元组表既可以采用数组存储,又可以采用十字链表存储(见6.2节)。当存储稀疏矩阵时,不仅要保存三元组表,而且要保存稀疏矩阵的行数、列数和非零元素的个数。

### 3.4.5 本节试题精选

**单项选择题**
01. 对特殊矩阵采用压缩存储的主要目的是( )。
    A. 表达变得简单
    B. 对矩阵元素的存取变得简单
    C. 去掉矩阵中的多余元素
    D. 减少不必要的存储空间
02. 对$n$阶对称矩阵压缩存储时,需要表长为( )的顺序表。
    A. $n/2$
    B. $n \times n/2$
    C. $n(n+1)/2$
    D. $n(n-1)/2$
03. 有一个$n \times n$的对称矩阵$A$,将其下三角部分按行存放在一维数组$B$中,而$A[0][0]$存放于$B[0]$中,则元素$A[i][i]$存放于$B$中的( )处。
    A. $(i+3)/2$
    B. $(i+1)i/2$
    C. $(2n-i+1)i/2$
    D. $(2n-i-1)i/2$
04. 在二维数组$A$中,假设每个数组元素的长度为3个存储单元,行下标$i$为0~8,列下标$j$为0~9,从首地址SA开始连续存放。在这种情况下,元素$A[8][5]$的起始地址为( )。
    A. SA+141
    B. SA+144
    C. SA+222
    D. SA+255
05. 二维数组$A$按行优先存储,其中每个元素占1个存储单元。若$A[1][1]$的存储地址为420,$A[3][3]$的存储地址为446,则$A[5][5]$的存储地址为( )。
    A. 472
    B. 471
    C. 458
    D. 457
06. 将三对角矩阵即数组$A[1...100][1...100]$按行优先存入一维数组$B[1...298]$中,数组$A$中元素$A[66][65]$在数组$B$中的位置$k$为( )。
    A. 198
    B. 195
    C. 197
    D. 196
07. 若将$n$阶上三角矩阵$A$按列优先级压缩存放在一维数组$B[1...n(n+1)/2+1]$中,则存放到$B[k]$中的非零元素$a_{ij}$ ($1 \le i,j \le n$)的下标$i$、$j$与$k$的对应关系是( )。
    A. $i(i+1)/2+j$
    B. $i(i-1)/2+j-1$
    C. $j(j-1)/2+i$
    D. $j(j-1)/2+i-1$
08. 若将$n$阶下三角矩阵$A$按列优先顺序压缩存放在一维数组$B[1...n(n+1)/2+1]$中,则存放到$B[k]$中的非零元素$a_{ij}$ ($1 \le i,j \le n$)的下标$i,j$与$k$的对应关系是( )。
    A. $(j-1)(2n-j+1)/2+i-j$
    B. $(j-1)(2n-j+2)/2+i-j+1$
    C. $(j-1)(2n-j+2)/2+i-j$
    D. $(j-1)(2n-j+1)/2+i-j-1$
09. 稀疏矩阵采用压缩存储后的缺点主要是( )。
    A. 无法判断矩阵的行列数
    B. 丧失随机存取的特性
    C. 无法由行、列值查找某个矩阵元素
    D. 使矩阵元素之间的逻辑关系更复杂
10. 下列关于矩阵的说法中,正确的是( )。
    I. 在$n(n>3)$阶三对角矩阵中,每行都有3个非零元
    II. 稀疏矩阵的特点是矩阵中的元素较少
    A. 仅I
    B. 仅II
    C. I和II
    D. 无正确项
11. 【2016统考真题】有一个100阶的三对角矩阵$M$,其元素$m_{ij}$ ($1 \le i,j \le 100$)按行优先依次压缩存入下标从0开始的一维数组$N$中。元素$M_{30,30}$在$N$中的下标是( )。
    A. 86
    B. 87
    C. 88
    D. 89
12. 【2017统考真题】适用于压缩存储稀疏矩阵的两种存储结构是( )。
    A. 三元组表和十字链表
    B. 三元组表和邻接矩阵
    C. 十字链表和二叉链表
    D. 邻接矩阵和十字链表
13. 【2018统考真题】设有一个$12 \times 12$阶对称矩阵$M$,将其上三角部分的元素$m_{ij}$($1 \le i \le j \le 12$)按行优先存入C语言的一维数组$N$中,元素$m_{6,6}$在$N$中的下标是( )。
    A. 50
    B. 51
    C. 55
    D. 66
14. 【2020统考真题】将一个$10 \times 10$阶对称矩阵$M$的上三角部分的元素$m_{ij}$ ($1 \le i \le j \le 10$)按列优先存入C语言的一维数组$N$中,元素$m_{7,2}$在$N$中的下标是( )。
    A. 15
    B. 16
    C. 22
    D. 23
15. 【2021统考真题】二维数组$A$按行优先方式存储,每个元素占用1个存储单元。若元素$A[0][0]$的存储地址是100,$A[3][3]$的存储地址是220,则元素$A[5][5]$的存储地址是( )。
    A. 295
    B. 300
    C. 301
    D. 306
16. 【2023统考真题】若采用三元组表存储结构存储稀疏矩阵$M$,则除三元组表外,下列数据中还需要保存的是( )。
    I. $M$的行数
    II. $M$中包含非零元素的行数
    III. $M$的列数
    IV. $M$中包含非零元素的列数
    A. 仅I、III
    B. 仅I、IV
    C. 仅II、IV
    D. I、II、III、IV

### 3.4.6 答案与解析

**单项选择题**

01. **D**
    特殊矩阵中含有很多相同元素或零元素,所以可采用压缩存储,以节省存储空间。
02. **C**
    只需存储其上三角或下三角部分(含对角线),元素个数为$n+(n-1)+\dots+1 = n(n+1)/2$。
03. **A**
    此题要注意3个细节:矩阵的最小下标为0;数组下标也是从0开始的;矩阵按行优先存在数组中。注意到此三点,答案不难得到为选项A。此外,本类题建议采用特殊值代入法求解,例如,$A[1][1]$对应的下标应为2,代入后只有选项A满足条件。

**技巧**
> 对于特殊三角矩阵压缩存储的题,心中应有“平移”搬动的思想,并结合草图,这样会比较形象,在计算时再注意矩阵和数组的起始下标,就不容易出错。

04. **D**
    二维数组计算地址(按行优先顺序)的公式为
    $$
    LOC(i, j) = LOC(0,0)+(i \times m+j) \times L
    $$
    其中,$LOC(0,0)=SA$,是数组存放的首地址;$L=3$是每个数组元素的长度;$m=9-0+1=10$是数组的列数。因此有$LOC(8,5) = SA+(8 \times 10+5) \times 3 = SA+255$,所以选择选项D。
05. **A**
    本题未直接给出数组$A$的行数和列数,因此需要根据题目中的信息来推理。因为该二维数组按行优先存储,且$A[3][3]$的存储地址为446,所以$A[3][1]$的存储地址为444,又$A[1][1]$的存储地址为420,显然$A[1][1]$和$A[3][1]$正好相差2行,所以该矩阵的列数为12。而$A[5][3]$和$A[3][3]$正好相差2行,$A[5][5]$和$A[5][3]$又相差2个元素,所以$A[5][5]$的存储地址是$446+24+2=472$。
06. **B**
    对于三对角矩阵,将$A[1...n][1...n]$压缩至$B[1...3n-2]$时,$a_{ij}$与$b_k$的对应关系为$k=2i+j-2$。则$A$中的元素$A[66][65]$在数组$B$中的位置$k$为$2 \times 66+65-2=195$。
07. **C**
    按列优先存储,所以元素$a_{ij}$前面有$j-1$列,共有$1+2+3+\dots+j-1=j(j-1)/2$个元素,元素$a_{ij}$在第$j$列上是第$i$个元素,数组$B$的下标是从1开始,因此$k=j(j-1)/2+i$。
08. **B**
    按列优先存储,所以元素$a_{ij}$之前有$j-1$列,共有$n+(n-1)+\dots+(n-j+2)=(j-1)(2n-j+2)/2$个元素,元素$a_{ij}$是第$j$列上第$i-j+1$个元素,数组$B$的下标从1开始,$k=(j-1)(2n-j+2)/2+i-j+1$。
09. **B**
    稀疏矩阵通常采用三元组来压缩存储,存储矩阵元素的行列下标和相应的值,因此不能根据矩阵元素的行列下标快速定位矩阵元素,失去了随机存取的特性。
10. **D**
    在三对角矩阵中,第1行和最后1行只有2个非零元,其余各行均有3个非零元。稀疏矩阵的特点是矩阵中非零元的个数较少。
11. **B**
    三对角矩阵如下所示。
    $$
    \begin{pmatrix}
    a_{1,1} & a_{1,2} & & & & 0 \\
    a_{2,1} & a_{2,2} & a_{2,3} & & & \\
    & a_{3,2} & a_{3,3} & a_{3,4} & & \\
    & & \ddots & & & \\
    0 & & & a_{n-1,n-2} & a_{n-1,n-1} & a_{n-1,n} \\
    & & & & a_{n,n-1} & a_{n,n}
    \end{pmatrix}
    $$
    采用压缩存储,将3条对角线上的元素按行优先方式存放在一维数组$B$中,且$a_{1,1}$存放于$B[0]$中,其存储形式如下所示:
    $a_{1,1} \quad a_{1,2} \quad a_{2,1} \quad a_{2,2} \quad a_{2,3} \quad \dots \quad a_{n-1,n} \quad a_{n,n-1} \quad a_{n,n}$
    可以计算矩阵$A$中3条对角线上的元素$a_{ij}$($1 \le i,j \le n, |i-j| \le 1$)在一维数组$B$中存放的下标为$k=2i+j-3$,公式很难记忆,我们通常采用解法2。
    **解法1**: 针对该题,仅需将数字逐一代入公式:$k=2 \times 30+30-3=87$,结果为87。
    **解法2**: 观察上图的三对角矩阵不难发现,第一行有两个元素,剩下的在元素$m_{30,30}$所在行之前的28行(注意下标$1 \le i,j \le 100$)中,每行都有3个元素,而$m_{30,30}$之前仅有一个元素$m_{30,29}$,不难发现元素$m_{30,30}$在数组$N$中的下标是$2+28 \times 3+1 = 87$。

**注意**
> 矩阵和数组的下标从0或1开始(如矩阵可能从$a_{0,0}$或$a_{1,1}$开始,数组可能从$B[0]$或$B[1]$开始),这时就需要适时调整计算方法(方法无非是多计算1或少计算1的问题)。

12. **A**
    三元组表的结点存储了行(row)、列(col)、值(value)三种信息,是主要用来存储稀疏矩阵的一种数据结构。十字链表将行单链表和列单链表结合起来存储稀疏矩阵。邻接矩阵空间复杂度达$O(n^2)$,不适合于存储稀疏矩阵。二叉链表又名左孩子右兄弟表示法,可用于表示树或森林。
13. **A**
    在C语言中,数组$N$的下标从0开始。第一个元素$m_{1,1}$对应存入$N[0]$,矩阵$M$的第一行有12个元素,第二行有11个,第三行有10个,第四行有9个,第五行有8个,所以$m_{6,6}$是第$12+11+10+9+8+1=51$个元素,下标应为50。
14. **C**
    上三角矩阵按列优先存储,先存储只有1个元素的第一列,再存储有2个元素的第二列,以此类推。$m_{7,2}$位于左下角,对应右上角的元素为$m_{2,7}$,在$m_{2,7}$之前存有
    第1列:1
    第2列:2
    ......
    第6列:6
    第7列:1
    前面共存储有$1+2+3+4+5+6+1=22$个元素(数组下标范围为0~21),注意数组下标从0开始,所以$m_{2,7}$在数组$N$中的下标为22,即$m_{7,2}$在数组$N$中的下标为22。
15. **B**
    二维数组$A$按行优先存储,每个元素占用1个存储单元,由$A[0][0]$和$A[3][3]$的存储地址可知$A[3][3]$是二维数组$A$中的第121个元素,假设二维数组$A$的每行有$n$个元素,则$n \times 3+4=121$,求得$n=39$,所以元素$A[5][5]$的存储地址为$100+39 \times 5+6-1=300$。
16. **A**
    用三元组表存储结构存储稀疏矩阵$M$时,每个非零元素都由三元组(行标、列标、关键字值)组成。但是,仅通过三元组表中的元素无法判断稀疏矩阵$M$的大小,因此还要保存$M$的行数和列数。此外,还可以保存$M$的非零元素个数。例如,如下两个稀疏矩阵的三元组表是相同的,若不保存行数和列数,则无法判断两个稀疏矩阵的大小。
    $$
    \begin{pmatrix} 0 & 2 & 0 \\ 0 & 0 & 5 \\ 0 & 0 & 0 \end{pmatrix}
    \quad
    \begin{pmatrix} 0 & 2 & 0 & 0 \\ 0 & 0 & 5 & 0 \\ 0 & 0 & 0 & 0 \end{pmatrix}
    \quad \text{对应相同的三元组表} \quad
    \begin{tabular}{|c|c|c|}
    \hline
    0 & 2 & 0 \\
    0 & 0 & 5 \\
    \hline
    \end{tabular}
    $$

### 归纳总结

本章所讲的几种数据结构类型是线性表的应用和推广,在考试中主要以选择题形式进行考查,但栈和队列也仍然有可能出现在算法设计题中。很多读者看到课本上有好多个函数时很恐惧,若考到了栈或队列的大题,难道要把每个操作的函数都写出来吗?
其实,在考试中,栈或队列都是作为一个工具来解决其他问题的,我们可以把栈或队列的声明和操作写得很简单,而不必分函数写出。以顺序栈的操作为例:
(1) 声明一个栈并初始化:
    `Elemtype stack[maxSize]; int top=-1;` //两句话连声明带初始化都有了
(2) 元素入栈:
    `stack[++top]=x;` //仅一句话即实现入栈操作
(3) 元素$x$出栈:
    `x=stack[top--];` //单目运算符在变量之前表示“先运算后使用”,之后则相反
对于链式栈,同样只需定义一个结构体,然后从讲解中摘取必要的语句组合在自己的函数代码中即可。另外,在考研真题中,链式栈出现的概率要比顺序栈低得多,因此大家应该有所侧重,多训练与顺序栈相关的题目。

### 思维拓展

设计一个栈,使它可以在$O(1)$的时间复杂度内实现Push、Pop和min操作。所谓min操作,是指得到栈中最小的元素。

**提示**
> 使用双栈,两个栈是同步关系。主栈是普通栈,用来实现栈的基本操作Push和Pop;辅助栈用来记录同步的最小值min,例如元素x入栈,则辅助栈`stack_min[top++]=(x<min)?x:min`;即在每次Push中,都将当前最小元素放到stack_min的栈顶。在主栈中Pop最小元素y时,stack_min栈中相同位置的最小元素y也会随着top--而出栈。因此stack_min的栈顶元素必然是y之前入栈的最小元素。本题是典型的以空间换时间的算法。
```