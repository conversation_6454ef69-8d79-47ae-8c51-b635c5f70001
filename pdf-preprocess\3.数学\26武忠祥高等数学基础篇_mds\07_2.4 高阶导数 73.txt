### 三、高阶导数

1.  高阶导数的概念

**定义(高阶导数)** 如果$y' = f'(x)$作为$x$的函数在点$x$可导, 则称$y'$的导数为$y = f(x)$的二阶导数, 记为$y''$, 或$f''(x)$, 或$\frac{\mathrm{d}^2 y}{\mathrm{d}x^2}$.

一般地, 函数$y=f(x)$的$n$阶导数为$y^{(n)} = [f^{(n-1)}(x)]'$, 也可记为$f^{(n)}(x)$或$\frac{\mathrm{d}^n y}{\mathrm{d}x^n}$, 即$n$阶导数就是$n-1$阶导函数的导数,
$$
\begin{aligned}
f^{(n)}(x_0) &= \lim_{\Delta x\to 0} \frac{f^{(n-1)}(x_0 + \Delta x) - f^{(n-1)}(x_0)}{\Delta x} \\
&= \lim_{x\to x_0} \frac{f^{(n-1)}(x) - f^{(n-1)}(x_0)}{x - x_0}.
\end{aligned}
$$

**【注】** 如果函数$f(x)$在点$x$处$n$阶可导, 则在点$x$的某邻域内$f(x)$必定具有一切低于$n$阶的导数.

2.  常用的高阶导数公式

(1) $(\sin x)^{(n)} = \sin(x+n\cdot\frac{\pi}{2})$.
(2) $(\cos x)^{(n)} = \cos(x+n\cdot\frac{\pi}{2})$.
(3) $(u \pm v)^{(n)} = u^{(n)} \pm v^{(n)}$.
(4) $(uv)^{(n)} = \sum_{k=0}^n C_n^k u^{(k)}v^{(n-k)}$.

**【例 14】** 设$y = \sin 3x$, 求$y^{(n)}$.

解 令$3x = u$, 则$y = \sin u$,
$$
\begin{aligned}
\frac{\mathrm{d}y}{\mathrm{d}x} &= \frac{\mathrm{d}y}{\mathrm{d}u}\cdot\frac{\mathrm{d}u}{\mathrm{d}x} = \frac{\mathrm{d}y}{\mathrm{d}u}\cdot 3, \\
\frac{\mathrm{d}^2 y}{\mathrm{d}x^2} &= \frac{\mathrm{d}^2 y}{\mathrm{d}u^2}\cdot\frac{\mathrm{d}u}{\mathrm{d}x}\cdot 3 = \frac{\mathrm{d}^2 y}{\mathrm{d}u^2}\cdot 3^2 \\
&= \sin(u+2\cdot\frac{\pi}{2})\cdot 3^2 \\
&= 3^2\sin(3x+2\cdot\frac{\pi}{2}).
\end{aligned}
$$
归纳法可知
$y^{(n)} = 3^n \sin(3x+n\cdot\frac{\pi}{2})$.

***

笔记区

**【例 15】** 设$y = x^2 \cos x$, 求$y^{(n)}$.

本题特别设置为不提供解析,请同学们带着问题去学习,
关注微信公众号“金榜时代考研”,后期会发布电子版解析.

$$
【x^2\cos(x+\frac{n\pi}{2})+2nx\cos(x+\frac{(n-1)\pi}{2})+n(n-1)\cos(x+\frac{(n-2)\pi}{2})】
$$

### 常考题型与典型例题

**常考题型**
1. 导数定义
2. 复合函数、隐函数、参数方程求导
3. 高阶导数
4. 导数应用