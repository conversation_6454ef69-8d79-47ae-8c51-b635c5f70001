## 第七章 查找
【考纲内容】
(一)查找的基本概念
(二)顺序查找法
(三)分块查找法
(四)折半查找法
(五)树形查找
二叉搜索树;平衡二叉树;红黑树
(六)B树及其基本操作、B+树的基本概念
(七)散列(Hash)表
(八)查找算法的分析及应用

【知识框架】
*   基本概念：静态查找、动态查找
*   查找
    *   线性结构
        *   顺序查找
        *   折半查找
        *   分块查找
    *   树形结构
        *   二叉排序树
        *   二叉平衡树
        *   红黑树
        *   B树、B+树
    *   散列结构——散列表
        *   性能分析
        *   冲突处理
*   效率指标——平均查找长度
    *   查找成功
    *   查找失败

【复习提示】
本章是考研命题的重点。对于折半查找,应掌握折半查找的过程、构造判定树、分析平均查找长度等。对于二叉排序树、二叉平衡树和红黑树,要了解它们的概念、性质和相关操作等。B树和B+树是本章的难点。对于B树,考研大纲要求掌握插入、删除和查找的操作过程;对于B+树,仅要求了解其基本概念和性质。对于散列查找,应掌握散列表的构造、冲突处理方法(各种方法的处理过程)、查找成功和查找失败的平均查找长度、散列查找的特征和性能分析。

### 7.1 查找的基本概念
1) **查找**。在数据集合中寻找满足某种条件的数据元素的过程称为查找。查找的结果一般分为两种:一是查找成功,即在数据集合中找到了满足条件的数据元素;二是查找失败。
2) **查找表**。用于查找的数据集合称为查找表,它由同一类型的数据元素(或记录)组成。对查找表的常见操作有:①查询符合条件的数据元素;②插入、删除数据元素。
3) **静态查找表**。若一个查找表的操作只涉及查找操作,则无须动态地修改查找表,此类查找表称为静态查找表。与此对应,需要动态地插入或删除的查找表称为动态查找表。适合静态查找表的查找方法有顺序查找、折半查找、散列查找等;适合动态查找表的查找方法有二叉排序树的查找、散列查找等。
4) **关键字**。数据元素中唯一标识该元素的某个数据项的值,使用基于关键字的查找,查找结果应该是唯一的。例如,在由一个学生元素构成的数据集合中,学生元素中“学号”这一数据项的值唯一地标识一名学生。
5) **平均查找长度**。在查找过程中,一次查找的长度是指需要比较的关键字次数,而平均查找长度则是所有查找过程中进行关键字的比较次数的平均值,其数学定义为
$$
ASL = \sum_{i=1}^{n} P_i C_i
$$
式中,$n$是查找表的长度;$P_i$是查找第$i$个数据元素的概率,一般认为每个数据元素的查找概率相等,即$P_i=1/n$;$C_i$是找到第$i$个数据元素所需进行的比较次数。平均查找长度是衡量查找算法效率的最主要的指标。

### 7.2 顺序查找和折半查找
#### 7.2.1 顺序查找
顺序查找也称线性查找,它对顺序表和链表都是适用的。对于顺序表,可通过数组下标递增来顺序扫描每个元素;对于链表,可通过指针next来依次扫描每个元素。顺序查找通常分为对一般的无序线性表的顺序查找和对按关键字有序的线性表的顺序查找。下面分别进行讨论。

**1. 一般线性表的顺序查找**
作为一种最直观的查找方法,其基本思想:①从线性表的一端开始,逐个检查关键字是否满足给定的条件;②若查找到某个元素的关键字满足给定条件,则查找成功,返回该元素在线性表中的位置;③若已经查找到表的另一端,但还没有查找到符合给定条件的元素,则返回查找失败的信息。下面给出其算法,后面说明了算法中引入的“哨兵”的作用。
```c
typedef struct {              //查找表的数据结构(顺序表)
    ElemType *elem;           //动态数组基址
    int TableLen;             //表的长度
}SSTable;
int Search_Seq(SSTable ST, ElemType key) {
    ST.elem[0]=key;           //“哨兵”
    for(int i=ST.TableLen; ST.elem[i]!=key; --i);//从后往前找
    return i; //若查找成功,则返回元素下标;若查找失败,则返回0
}
```
上述算法中,将$ST.elem[0]$称为哨兵,引入它的目的是使得`Search_Seq`内的循环不必判断数组是否会越界。算法从尾部开始查找,若找到$ST.elem[i]==key$则返回$i$值,查找成功。否则一定在查找到$ST.elem[0]==key$时跳出循环,此时返回的是0,查找失败。在程序中引入“哨兵”,可以避免很多不必要的判断语句,从而提高程序效率。
对于有$n$个元素的表,给定值$key$与表中第$i$个元素相等,即定位第$i$个元素时,需进行$n-i+1$