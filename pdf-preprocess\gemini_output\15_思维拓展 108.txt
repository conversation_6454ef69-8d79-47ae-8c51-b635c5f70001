15. B
二维数组$A$按行优先存储,每个元素占用1个存储单元,由$A[0][0]$和$A[3][3]$的存储地址可知$A[3][3]$是二维数组A中的第121个元素,假设二维数组$A$的每行有$n$个元素,则$n×3+4=121$,求得$n=39$,所以元素$A[5][5]$的存储地址为$100+39×5+6-1=300$。

16. A
用三元组表存储结构存储稀疏矩阵$M$时,每个非零元素都由三元组(行标、列标、关键字值)组成。但是,仅通过三元组表中的元素无法判断稀疏矩阵$M$的大小,因此还要保存$M$的行数和列数。此外,还可以保存$M$的非零元素个数。例如,如下两个稀疏矩阵的三元组表是相同的,若不保存行数和列数,则无法判断两个稀疏矩阵的大小。

```
0 2 0 0
0 0 5 0
0 0 0 0
```
```
0 2 0
0 0 5
0 0 0
0 0 0
```

### 归纳总结
本章所讲的几种数据结构类型是线性表的应用和推广,在考试中主要以选择题形式进行考查,但栈和队列也仍然有可能出现在算法设计题中。很多读者看到课本上有好多个函数时很恐惧,若考到了栈或队列的大题,难道要把每个操作的函数都写出来吗?
其实,在考试中,栈或队列都是作为一个工具来解决其他问题的,我们可以把栈或队列的声明和操作写得很简单,而不必分函数写出。以顺序栈的操作为例:

(1) 声明一个栈并初始化:
```c
Elemtype stack[maxSize]; int top=-1; //两句话连声明带初始化都有了
```
(2) 元素入栈:
```c
stack[++top]=x; //仅一句话即实现入栈操作
```
(3) 元素x出栈:
```c
x=stack[top--]; //单目运算符在变量之前表示“先运算后使用”,之后则相反
```
对于链式栈,同样只需定义一个结构体,然后从讲解中摘取必要的语句组合在自己的函数代码中即可。另外,在考研真题中,链式栈出现的概率要比顺序栈低得多,因此大家应该有所侧重,多训练与顺序栈相关的题目。

### 思维拓展
设计一个栈,使它可以在$O(1)$的时间复杂度内实现Push、Pop和min操作。所谓min操作,是指得到栈中最小的元素。

**提示**
使用双栈,两个栈是同步关系。主栈是普通栈,用来实现栈的基本操作Push和Pop;辅助栈用来记录同步的最小值min,例如元素$x$入栈,则辅助栈`stack_min[top++]=(x<min)?x:min`;即在每次Push中,都将当前最小元素放到`stack_min`的栈顶。在主栈中Pop最小元素$y$时,`stack_min`栈中相同位置的最小元素$y$也会随着`top--`而出栈。因此`stack_min`的栈顶元素必然是$y$之前入栈的最小元素。本题是典型的以空间换时间的算法。

## 第四章 串

### 【考纲内容】
字符串模式匹配

### 【知识框架】
*   串
    *   基本概念:主串、子串、串长
    *   存储结构
        *   定长顺序存储
        *   堆分配存储
        *   块链存储
    *   模式匹配算法
        *   暴力匹配法
        *   KMP算法
            *   部分匹配值表
            *   next 数组
            *   next 函数的推理过程
        *   KMP算法的进一步改进———————nextval数组

### 【复习提示】
本章是统考大纲第6章内容,采纳读者建议单独作为一章,大纲只要求掌握字符串模式匹配,重点掌握KMP匹配算法的原理及next数组的推理过程,手工求next数组可以先计算出部分匹配值表然后变形,或根据公式来求解。了解nextval数组的求解方法。

### *4.1 串的定义和实现①
字符串简称串,计算机上非数值处理的对象基本都是字符串数据。我们常见的信息检索系统(如搜索引擎)、文本编辑程序(如Word)、问答系统、自然语言翻译系统等,都是以字符串数据作为处理对象的。本章详细介绍字符串的存储结构及相应的操作。

#### 4.1.1 串的定义
串(string)是由零个或多个字符组成的有限序列。一般记为
$S='a_1a_2\dots a_n' (n\ge0)$
其中,$S$是串名,单引号括起来的字符序列是串的值;$a_i$可以是字母、数字或其他字符;串中字符的个数$n$称为串的长度。$n=0$时的串称为空串(用`''`表示)。
串中任意多个连续的字符组成的子序列称为该串的子串,包含子串的串称为主串。某个字

---
①本节不在统考大纲范围,仅供学习参考。