## 第5章 输入/输出管理

【考纲内容】
**(一) 输入/输出(I/O)管理基础**
设备：设备的基本概念，设备的分类，I/O 接口
I/O 控制方式：轮询方式，中断方式，DMA 方式
I/O 软件层次结构：中断处理程序，驱动程序，设备独立性软件，用户层 I/O 软件
输入/输出应用程序接口：字符设备接口，块设备接口，网络设备接口，阻塞/非阻塞 I/O
**(二) 设备独立软件**
缓冲区管理；设备分配与回收；假脱机技术(SPOOLing)；设备驱动程序接口
**(三) 外存管理**
磁盘：磁盘结构，格式化，分区，磁盘调度算法
固态硬盘：读/写性能特效，磨损均衡

【复习提示】
本章的内容较为分散，重点掌握I/O接口、I/O软件、三种 I/O 控制方式、高速缓存与缓冲区、SPOOLing 技术，磁盘特性和调度算法。本章很多知识点与硬件高度相关，建议与计算机组成原理的对应章节结合复习。已复习过计算机组成原理的读者遇到比较熟悉的内容时也可适当跳过。另外，未复习过计算机组成原理的读者可能觉得本章的习题较难，但无须担心。
本章内容在历年统考真题中所占的比重不大，若统考中出现本章的题目，则基本上可以断定一定较为简单，看过相关内容的读者就一定会做，而未看过的读者基本上只能靠“蒙”。考研成功的秘诀是复习要反复多次且全面，偷工减料是要吃亏的，希望读者重视本章的内容。

### 5.1 I/O 管理概述
在学习本节时，请读者思考I/O 管理要完成哪些功能。

#### 5.1.1 I/O 设备
I/O 设备管理是操作系统设计中最凌乱也最具挑战性的部分。它包含了很多领域的不同设备及与设备相关的应用程序，因此很难有一个通用且一致的设计方案。

**1. 设备的分类**
I/O设备是指可以将数据输入计算机的外部设备，或者可以接收计算机输出数据的外部设备。I/O 设备的类型繁多，从不同的角度可将它们分为不同的类型。
按信息交换的单位分类，I/O 设备可分为：
1) **块设备**。信息交换以数据块为单位，如磁盘、磁带等。磁盘设备的基本特征是传输速率较高、可寻址，即对它可随机地读/写任意一块。
2) **字符设备**。信息交换以字符为单位，如交互式终端机、打印机等。它们的基本特征是传输速率低、不可寻址，并且通常采用中断I/O方式。
按设备的传输速率分类，I/O设备可分为：
1) **低速设备**。传输速率仅为每秒几字节到数百字节，如键盘、鼠标等。
2) **中速设备**。传输速率为每秒数千字节至数万字节，如激光打印机等。
3) **高速设备**。传输速率在数百千字节至千兆字节，如磁盘、光盘等。
按设备的使用特性分类，I/O设备可分为：
1) **人机交互设备**。用于用户和计算机之间交互信息的设备，如键盘、显示器、打印机等。
2) **存储设备**。用于存储信息的设备，如磁盘、磁带、光盘等。
3) **网络通信设备**。用于计算机和计算机之间的通信，如网卡、调制解调器等。
按设备的共享属性分类，I/O设备可分为：
1) **独占设备**。同一时刻只能由一个进程占用的设备。一旦将这类设备分配给某进程，便由该进程独占，直至用完释放。低速设备一般是独占设备，如打印机。
2) **共享设备**。同一时间段内允许多个进程同时访问的设备。对于共享设备，可同时分配给多个进程，通过分时的方式共享使用。典型的共享设备是磁盘。
3) **虚拟设备**。通过SPOOLing 技术将独占设备改造为共享设备，将一个物理设备变为多个逻辑设备，从而可将设备同时分配给多个进程。

**2. I/O接口**
I/O接口（也称设备控制器）是CPU与设备之间的“桥梁”，以实现设备和计算机之间的数据交换。它接收发自CPU的命令，控制设备工作，使CPU能从繁杂的设备控制事务中解脱出来。设备控制器主要由三部分组成，如图5.1所示。
```
                      CPU与控制器的接口
               ┌──────────────────┐
数据线 ───────┤   数据寄存器   ├─────────┐
               ├──────────────────┤         │
               │  控制/状态   │         │
               │    寄存器    │         │
地址线 ───────┤              ├───────┤ I/O逻辑 ├──────
               └──────────────────┘         │
                                             │
控制线 ───────┬────────────────────────┘
              │
              └────────────────────────────────────────────────
```
```
                      控制器与设备的接口
                 ┌────────────┐
                 │ 控制器     │
───────────────┤ 与设备     ├───── ·数据
                 │ 接口1      │          状态
                 └────────────┘───── ·控制

                 ┌────────────┐
                 │ 控制器     │
───────────────┤ 与设备     ├───── ·数据
                 │ 接口n      │          状态
                 └────────────┘───── ·控制
```
图5.1 设备控制器的组成
1) **设备控制器与CPU的接口**。用于实现CPU与设备控制器之间的通信。该接口有三类信号线：数据线、地址线和控制线。数据线传送的是读/写数据、控制信息和状态信息；地址线传送的是要访问I/O接口中的寄存器编号；控制线传送的是读/写等控制信号。
2) **设备控制器与设备的接口**。一个设备控制器可以连接一个或多个设备，因此控制器中有一个或多个设备接口。每个接口都可传输数据、控制和状态三种类型的信号。
3) **I/O逻辑**。用于实现对设备的控制。它通过一组控制线与CPU交互，对从CPU收到的I/O命令进行译码。CPU启动设备时，将启动命令发送给控制器，同时通过地址线将地址发送给控制器，由控制器的I/O 逻辑对地址进行译码，并对所选设备进行控制。
设备控制器的主要功能有：①接收和识别命令，如磁盘控制器能接收 CPU发来的读、写、查找等命令；②数据交换，包括CPU和控制器之间的数据传输，以及控制器和设备之间的数据传输；③标识和报告设备的状态，以供CPU处理；④地址识别；⑤数据缓冲；⑥差错控制。

**3. I/O接口的类型**
从不同的角度看，I/O接口可以分为不同的类型。
1) 按数据传送方式（外设和接口一侧），可分为并行接口（一个字节或者一个字的所有位同时传送）和串行接口（一位一位地有序传送），接口要完成数据格式的转换。
2) 按主机访问I/O 设备的控制方式，可分为程序查询接口、中断接口和DMA接口等。
3) 按功能选择的灵活性，可分为可编程接口（通过编程改变接口功能）和不可编程接口。

**4. I/O端口**
I/O 端口是指设备控制器中可被CPU直接访问的寄存器，主要有以下三类寄存器。
* **数据寄存器**：用于缓存从设备送来的输入数据，或从CPU送来的输出数据。
* **状态寄存器**：保存设备的执行结果或状态信息，以供CPU 读取。
* **控制寄存器**：由CPU写入，以便启动命令或更改设备模式。
I/O 端口要想能够被CPU访问，就要对各个端口进行编址，每个端口对应一个端口地址。而对I/O 端口的编址方式有与存储器独立编址和统一编址两种，如图5.2所示。
```
   两个地址空间                一个地址空间
┌──────────────┐            ┌──────────────┐
│  0xFFFF...   │            │              │
│              │            │    内存      │
│     内存     │            │              │
│              │            │              │
├──────────────┤            ├──────────────┤
│              │            │   I/O端口    │
│    I/O端口   │            │              │
│              │            │              │
│      0       │            │      0       │
└──────────────┘            └──────────────┘
 (1)独立编址                 (2)统一编址
```
图5.2 独立编址I/O和内存映射 I/O
**(1) 独立编址**
独立编址是指为每个端口分配一个I/O端口号。I/O端口的地址空间与主存地址空间是两个独立的地址空间，它们的范围可以重叠，相同地址可能属于不同的地址空间。普通用户程序不能对端口进行访问，只有操作系统使用特殊的I/O 指令才能访问端口。
**优点**：I/O端口数比主存单元少得多，只需少量地址线，使得I/O 端口译码简单，寻址速度更快。使用专用I/O指令，可使程序更加清晰，便于理解和检查。
**缺点**：I/O 指令少，只提供简单的传输操作，所以程序设计的灵活性较差。此外，CPU需要提供两组独立的存储器和设备的读/写控制信号，增加了控制的复杂性。
**(2) 统一编址**
统一编址也称内存映射I/O，是指将主存地址空间分出一部分给I/O端口进行编址，I/O端口和主存单元在同一地址空间的不同分段中，根据地址范围就能区分访问的是I/O 端口还是主存单元，因此无须设置专门的I/O指令，用统一的访存指令就可访问I/O端口。
**优点**：不需要专门的I/O指令，使得CPU访问I/O 的操作更加灵活和方便，还使得端口有较大的编址空间。I/O访问的保护机制可由虚拟存储管理系统来实现，无须专门设置。
**缺点**：端口地址占用了部分主存地址空间，使主存的可用容量变小。此外，由于在识别 I/O端口时全部地址线都需参加译码，使得译码电路更加复杂，降低了寻址速度。

#### 5.1.2 I/O 控制方式①
I/O 控制是指控制设备和主机之间的数据传送。在I/O 控制方式的发展过程中，始终贯穿着这样一个宗旨：尽量减少CPU对I/O 控制的干预，将CPU从繁杂的I/O 控制事务中解脱出来，以便其能更多地去执行运算任务。I/O控制方式共有4种，下面分别加以介绍。

**1. 程序直接控制方式**
CPU 对 I/O 设备的控制采取轮询的I/O方式，也称程序轮询方式。如图5.3(a)所示，CPU向设备控制器发出一条I/O指令，启动从I/O 设备读取一个字(节)，然后不断地循环测试设备状态(称为轮询)，直到确定该字(节)已在设备控制器的数据寄存器中。于是CPU 将数据寄存器中的数据取出，送入内存的指定单元，这样便完成了一个字(节)的I/O操作。
这种方式简单且易于实现，但缺点也很明显。CPU的绝大部分时间都处于等待I/O设备状态的循环测试中，CPU和I/O设备只能串行工作，由于CPU和I/O 设备的速度差异很大，导致CPU的利用率相当低。而CPU之所以要不断地测试 I/O 设备的状态，就是因为在CPU中未采用中断机制，使I/O 设备无法向CPU报告它已完成了一个字(节)的输入操作。

**2. 中断驱动方式**
中断驱动方式的思想是允许I/O 设备主动打断CPU的运行并请求服务，从而“解放”CPU，使得CPU 向设备控制器发出一条I/O 指令后可以继续做其他有用的工作。如图5.3(b)所示，我们从设备控制器和CPU两个角度分别来看中断驱动方式的工作过程。
从设备控制器的角度来看：设备控制器从 CPU 接收一个读命令，然后从设备读数据。一旦数据读入设备控制器的数据寄存器，便通过控制线给CPU发出中断信号，表示数据已准备好，然后等待 CPU 请求该数据。设备控制器收到CPU发出的取数据请求后，将数据放到数据总线上，传到CPU的寄存器中。至此，本次I/O操作完成，设备控制器又可开始下一次I/O操作。

**图5.3 I/O控制方式的操作流程**

**(a) 程序直接控制方式**
1. 给I/O控制器发出读命令 (CPU→I/O)
2. 读I/O控制器的状态 (I/O→CPU)
3. 检查状态：
   - 未准备好：循环返回步骤2
   - 错误条件：处理错误
   - 准备好：继续
4. 从I/O控制器中读取字 (I/O→CPU)
5. 往存储器中写入字 (CPU→存储器)
6. 检查是否完成？
   - 否：返回步骤2 (读取下一个字)
   - 是：执行下一条指令

**(b) 中断驱动方式**
1. 给I/O控制器发出读命令 (CPU→I/O)
2. CPU做其他事情
3. I/O设备准备好数据，发出中断
4. CPU读I/O控制器的状态 (I/O→CPU)
5. 检查状态：
   - 错误条件：处理错误
   - 准备好：继续
6. 从I/O控制器中读取字 (I/O→CPU)
7. 往存储器中写入字 (CPU→存储器)
8. 检查是否完成？
   - 否：返回步骤2 (CPU继续做其他事，等待下一次中断)
   - 是：执行下一条指令

**(c) DMA方式**
1. 给I/O控制器发出读块命令 (CPU→DMA)
2. CPU做其他事情
3. DMA控制器与内存直接传输数据
4. 传输完成，DMA控制器向CPU发中断
5. CPU读DMA控制器的状态 (DMA→CPU)
6. 执行下一条指令

①本节的内容在《计算机组成原理考研复习指导》书的7.3节中描述得比较详细，建议读者结合复习。

**命题追踪**
> 中断处理程序执行时请求进程的状态(2017、2023)
**命题追踪**
> 键盘接收数据的中断过程分析(2010、2024)

从CPU的角度来看：当前运行进程发出读命令，该进程将被阻塞，然后保存该进程的上下文，转去执行其他程序。在每个指令周期的末尾，CPU检查中断信号。当有来自设备控制器的中断时，CPU保存当前运行进程的上下文，转去执行中断处理程序以处理该中断请求。这时，CPU从设备控制器读一个字的数据传送到寄存器，并存入主存。中断处理完后解除发出 I/O 命令的进程的阻塞状态，然后恢复该进程（或其他进程）的上下文，然后继续运行。
相比于程序轮询I/O方式，在中断驱动I/O方式中，设备控制器通过中断主动向 CPU报告 I/O操作已完成，不再需要轮询，在设备准备数据期间，CPU和设备并行工作，CPU的利用率得到明显提升。但是，中断驱动方式仍有两个明显的问题：①设备与内存之间的数据交换都必须经过 CPU中的寄存器；②CPU是以字（节）为单位进行干预的，若将这种方式用于块设备的I/O操作，则显然是极其低效的。因此，中断驱动I/O方式的速度仍然受限。

**3. DMA方式**
DMA（直接存储器存取）方式的基本思想是，在I/O设备和内存之间开辟直接的数据交换通路，彻底“解放”CPU。DMA方式的特点如下：
1) 基本传送单位是数据块，而不再是字（节）。
2) 所传送的数据，是从设备直接送入内存的，或者相反，而不再经过CPU。
3) 仅在传送一个或多个数据块的开始和结束时，才需要CPU干预。
图5.4列出了DMA控制器的组成。
```
        ┌──────┐   内存   ┌──────────────────┐
        │      │<───────>┌────┐
        │ CPU  │          │ DR │        I/O
        │      │          ├────┤      控制
        └──────┘          │MAR │      逻辑
          │               ├────┤
          │               │ DC │
          命令             ├────┤
          │               │ CR │
          ▼               └────┘
┌─────────┴─────────┐       ▲
│      系统总线       ├───────┴──DMA控制器
└───────────────────┘
```
图5.4 DMA 控制器的组成

为了实现主机和控制器之间直接交换成块的数据，须在DMA 控制器中设置如下4类寄存器：
1) **命令/状态寄存器(CR)**。暂存从CPU发来的I/O命令，或设备的状态信息。
2) **内存地址寄存器(MAR)**。在输入时，它存放将数据从设备传送到内存的起始目标地址；在输出时，它存放由内存到设备的内存源地址。
3) **数据寄存器(DR)**。暂存从设备到内存，或从内存到设备的数据。
4) **数据计数器(DC)**。存放本次要传送的字（节）数。

**命题追踪**
> DMA方式的工作流程(2017)

如图5.3(c)所示，DMA方式的工作过程是：CPU接收到设备的DMA请求时，它向DMA控制器发出一条命令，同时设置MAR和DC 初值，启动DMA 控制器，然后继续其他工作。之后CPU 就将 I/O 控制权交给DMA控制器，由DMA 控制器负责数据传送。DMA控制器直接与内存交互，每次传送一个字，这个过程不需要 CPU参与。整个数据传送结束后，DMA 控制器向 CPU发送一个中断信号。因此只有在传送开始和结束时才需要CPU的参与。
DMA方式的优点：数据传输以“块”为单位，CPU介入的频率进一步降低；数据传送不再经过CPU的寄存器，CPU和设备的并行操作程度得到了进一步提升。

**\*4. 通道控制方式**
I/O 通道是一种特殊的处理机，它可执行一系列通道指令。设置通道后，CPU只需向通道发送一条 I/O 指令，指明通道程序在内存中的位置和要访问的I/O设备，通道收到该指令后，执行通道程序，完成规定的I/O任务后，向CPU发出中断请求。通道方式可以实现 CPU、通道和 I/O设备三者的并行工作，从而更有效地提高整个系统的资源利用率。
通道与一般处理机的区别是：通道指令的类型单一，没有自己的内存，通道所执行的通道程序是放在主机的内存中的，也就是说通道与CPU共享内存。
通道与DMA方式的区别是：DMA方式需要CPU来控制传输的数据块大小、传输的内存位置，而通道方式中这些信息是由通道控制的。另外，每个DMA 控制器对应一台设备与内存传递数据，而一个通道可以控制多台设备与内存的数据交换。
每种方式的优点都是解决了上一种方式的最大缺点。总体而言，I/O控制方式的发展过程就是 要尽量减少CPU对I/O 过程的干预，将CPU从烦杂的I/O控制事务中解脱出来。下面用一个例子来总结这4种I/O方式。想象一位客户要去裁缝店做一批衣服的情形。
采用程序直接控制方式时，裁缝没有客户的联系方式，客户必须每隔一段时间去裁缝店看看裁缝将衣服做好了没，这就浪费了客户不少的时间。采用中断驱动方式时，裁缝有客户的联系方式，每当他做完一件衣服后，就给客户打电话，让客户去取，与程序直接控制方式相比能省去客户不少麻烦，但每完成一件衣服就让客户去取一次，仍然很浪费客户的时间。采用DMA方式时，客户花钱雇一位秘书，并向秘书交代好将衣服暂存在某个仓库，裁缝直接跟秘书联系，秘书负责将衣服取回并放在指定的仓库，每处理完100件衣服，秘书就要向客户报告一次，大大节省了客户的时间。采用通道控制方式时，秘书拥有更高的自主权，与DMA方式相比，秘书可以决定将衣服存放在何处，而不需要客户操心。而且，何时向客户报告，是处理完100件衣服就报告，还是处理完10000件衣服才报告，秘书可以自行决定。客户有可能在多个裁缝那里订了货，一位DMA类的秘书只能负责与一位裁缝沟通，但通道类秘书却可以与多名裁缝进行交互。

#### 5.1.3 I/O 软件层次结构
I/O 软件涉及的面很宽，往下与硬件有着密切关系，往上又与虚拟存储器系统、文件系统和用户直接交互，它们都需要I/O软件来实现I/O操作。
**命题追踪**
> I/O 子系统各层次的排序及功能(2012、2013)
为使复杂的I/O 软件能具有清晰的结构、良好的可移植性和易适应性，目前普遍采用层次结构的I/O软件。将系统中的设备管理模块分为若干层次，每层都是利用其下层提供的服务，完成输入/输出功能中的某些子功能，并屏蔽这些功能实现的细节，向高层提供服务。在层次结构的I/O软件中，只要层次间的接口不变，对某一层次中的软件的修改都不会引起其下层或高层代码的变更，仅最低层才涉及硬件的具体特性。一个比较合理的层次划分如图5.5所示。整个I/O 软件可以视为具有4个层次的系统结构，各层次及其功能如下：
**(1) 用户层软件**
实现与用户交互的接口，用户可直接调用在用户层提供的、与I/O操作有关的库函数，对设备进行操作。通常大部分的I/O 软件都在操作系统内核，但仍有一小部分在用户层，包括与用户程序链接在一起的库函数。用户层I/O 软件必须通过一组系统调用来获取操作系统服务。

```
     I/O请求  ↓       ↑ I/O应答
┌──────────────────┐
│     用户层软件     │   产生I/O请求；格式化I/O；SPOOLing
├──────────────────┤
│   设备独立性软件   │   映射；保护；分块；缓冲；分配
├──────────────────┤
│   设备驱动程序     │   设置设备寄存器；检查状态
├──────────────────┤
│   中断处理程序     │
├──────────────────┤
│       硬件         │   执行I/O操作
└──────────────────┘
```
图5.5 I/O层次结构

**(2) 设备独立性软件**
**命题追踪**
> 设备独立性所涵盖的内容(2020)
用于实现用户程序与设备驱动器的统一接口、设备命名、设备保护，以及设备的分配与释放等，同时为设备管理和数据传送提供必要的存储空间。
设备独立性也称设备无关性，其含义是指应用程序所用的设备不局限于某个具体的物理设备。为实现设备独立性而引入了逻辑设备和物理设备这两个概念。在应用程序中，使用逻辑设备名来请求使用某类设备；而在系统实际执行时，必须将逻辑设备名映射成物理设备名。
使用逻辑设备名的好处是：①增加设备分配的灵活性；②易于实现 I/O 重定向，所谓 I/O 重定向，是指用于I/O操作的设备可以更换（重定向），而不必改变应用程序。
为了实现设备独立性，必须再在驱动程序之上设置一层设备独立性软件。总体而言，设备独立性软件的主要功能可分为以下两个方面。①执行所有设备的公有操作，包括：对设备的分配与回收；将逻辑设备名映射为物理设备名；对设备进行保护，禁止用户直接访问设备；缓冲管理；差错控制；提供独立于设备的大小统一的逻辑块，屏蔽设备之间信息交换单位大小和传输速率的差异。②向用户层（或文件层）提供统一接口。无论何种设备，它们向用户所提供的接口应是相同的。例如，对各种设备的读/写操作，在应用程序中都统一使用read/write命令等。
**(3) 设备驱动程序**
与硬件直接相关，每类设备需要配置一个设备驱动程序。设备驱动程序负责具体实现系统对设备发出的操作指令，驱动I/O 设备工作的驱动程序，它是I/O 进程与设备控制器之间的通信程序，通常以进程的形式存在。设备驱动程序向上层用户程序提供一组标准接口，设备具体的差别被设备驱动程序所封装，用于接收上层软件发来的抽象 I/O 要求，如read 和write 命令，转换为具体要求后，发送给设备控制器，控制I/O 设备工作；它也将由设备控制器发来的信号传送给上层软件，从而为I/O 内核子系统隐藏设备控制器之间的差异。
**(4) 中断处理程序**
当I/O 操作完成时，设备控制器发送一个中断信号，CPU响应中断后，根据中断类型号找到相应的中断处理程序进行处理，处理完后，再返回到被中断的进程。
中断处理层的主要任务有：进行进程上下文的切换，对处理中断信号源进行测试，读取设备状态和修改进程状态等。中断处理与硬件紧密相关，对用户而言，应尽量加以屏蔽，因此应放在操作系统的底层，系统的其余部分尽可能少地与之发生联系。
类似于文件系统的层次结构，I/O子系统的层次结构也是我们需要记忆的内容，但记忆不是死记硬背，我们以用户对设备的一次命令来总结各层次的功能，帮助各位读者记忆。

**命题追踪**
> 磁盘 VO 操作中各层次的处理过程(2011)
例如，①当用户要读取某设备的内容时，通过操作系统提供的read命令接口，这就经过了用户层。②操作系统提供给用户使用的接口一般是统一的通用接口，也就是几乎每个设备都可以响应的统一命令，如read命令，用户发出的read命令，首先经过设备独立层进行解析，然后交往下层。③接下来，不同类型的设备对 read 命令的行为有所不同，如磁盘接收 read 命令后的行为与打印机接收 read 命令后的行为是不同的。因此，需要针对不同的设备，将read命令解析成不同的指令，这就经过了设备驱动层。④命令解析完毕后，需要中断正在运行的进程，转而执行read 命令，这就需要中断处理程序。⑤最后，命令真正抵达硬件设备，硬件设备的控制器按照上层传达的命令操控硬件设备，完成相应的功能。

#### 5.1.4 应用程序 I/O 接口
**1. I/O接口的分类**
在I/O 系统与高层之间的接口中，根据设备类型的不同，又进一步分为若干类。
**(1) 字符设备接口**
字符设备是指数据的存取和传输是以字符为单位的设备，如键盘、打印机等。基本特征是传输速率较低、不可寻址，并且在输入/输出时通常采用中断驱动方式。
**get 和 put 操作**，由于字符设备不可寻址，只能采取顺序存取方式，通常为字符设备建立一个字符缓冲区，用户程序通过 get 操作从缓冲区获取字符，通过put 操作将字符输出到缓冲区。
**in-control 指令**，字符设备类型繁多，差异甚大，因此在接口中提供一种通用的 in-control 指令来处理它们（包含了许多参数，每个参数表示一个与具体设备相关的特定功能）。
字符设备都属于独占设备，为此接口中还需要提供打开和关闭操作，以实现互斥共享。
**(2) 块设备接口**
块设备是指数据的存取和传输是以数据块为单位的设备，典型的块设备是磁盘。基本特征是传输速率较高、可寻址。磁盘设备的I/O常采用DMA方式。
块设备接口支持将上层发来的打开、读、写和关闭等抽象命令，映射为设备能识别的较低层的具体操作。块设备接口还隐藏了磁盘的二维结构，每个扇区的地址需要用磁道号和扇区号来表示，该接口将所有扇区从0到n-1依次编号，这样，将二维结构变为了一种线性序列。
**内存映射接口**通过内存的字节数组来访问磁盘，而不提供读/写磁盘操作。映射文件到内存的系统调用返回包含文件副本的一个虚拟内存地址。只在需要访问内存映像时，才由虚拟存储器实际调页。内存映射文件的访问如同内存读/写一样简单，极大地方便了程序员。
**(3) 网络设备接口**
现代操作系统都提供面向网络的功能，因此还需要提供相应的网络软件和网络通信接口，使计算机能够通过网络与网络上的其他计算机进行通信或上网浏览。
许多操作系统提供的网络I/O接口为网络套接字接口，套接字接口的系统调用使应用程序创建的本地套接字连接到远程应用程序创建的套接字，通过此连接发送和接收数据。

**2. 阻塞 I/O 和非阻塞 I/O**
操作系统的I/O接口还涉及两种模式：阻塞和非阻塞。
**阻塞 I/O** 是指当用户进程调用I/O 操作时，进程就被阻塞，并移到阻塞队列，I/O操作完成后，进程才被唤醒，移到就绪队列。当进程恢复执行时，它收到系统调用的返回值，并继续处理数据。大多数操作系统提供的I/O接口都是采用阻塞 I/O。例如，你和朋友去咖啡店买咖啡，点完单后，因为不知道咖啡什么时候做好，所以只能一直等待，其他什么事也不能干。
**优点**：操作简单，实现难度低，适合并发量小的应用开发。
**缺点**：I/O 执行阶段进程会一直阻塞下去。
**非阻塞 I/O** 是指当用户进程调用I/O操作时，不阻塞该进程，但进程需要不断询问I/O 操作是否完成，在I/O执行阶段，进程还可以做其他事情。当问到I/O 操作完成后，系统将数据从内核复制到用户空间，进程继续处理数据。例如，你和朋友去咖啡店买咖啡，吸取了上次的教训，点完单后顺便逛逛商场，因为担心错过取餐，所以每隔一段时间就过来询问服务员。
**优点**：进程在等待 I/O 期间不会阻塞，可以做其他事情，适合并发量大的应用开发。
**缺点**：轮询方式询问I/O结果，会占用CPU的时间。

#### 5.1.5 本节小结
本节开头提出的问题的参考答案如下。
**I/O 管理要完成哪些功能？**
I/O 管理需要完成以下4部分内容：
1) **状态跟踪**。要能实时掌握外部设备的状态。
2) **设备存取**。要实现对设备的存取操作。
3) **设备分配**。在多用户环境下，负责设备的分配与回收。
4) **设备控制**。包括设备的驱动、完成和故障的中断处理。

### 5.1.6 本节习题精选
**一、单项选择题**
**01.** 下列关于设备属性的叙述中，正确的是()。
A. 字符设备的基本特征是可寻址到字节，即能指定读/写操作的字节地址
B. 共享设备必须是可寻址的和可随机访问的设备
C. 共享设备是指同一时刻内允许多个进程同时访问的设备
D. 在分配共享设备和独占设备时都可能引起进程死锁
**02.** 下列关于虚拟设备的含义的描述中，正确的是()。
A. 允许用户使用比系统中具有的物理设备更多的设备
B. 允许用户以标准化方式来使用物理设备
C. 把一个物理设备变换成多个对应的逻辑设备
D. 允许用户程序不必全部装入主存便可使用系统中的设备
**03.** 磁盘设备的I/O控制主要采取( )方式。
A. 位
B. 字节
C. 帧
D. DMA
**04.** 为了便于上层软件的编制，设备控制器通常需要提供( )。
A. 控制寄存器、状态寄存器和控制命令
B. I/O 地址寄存器、工作方式状态寄存器和控制命令
C. 中断寄存器、控制寄存器和控制命令
D. 控制寄存器、编程空间和控制逻辑寄存器
**05.** 在设备控制器中用于实现设备控制功能的是()。
A. CPU
B. 设备控制器与处理器的接口
C. I/O 逻辑
D. 设备控制器与设备的接口
**06.** DMA方式是在( )之间建立一条直接数据通路。
A. I/O设备和主存 
B. 两个I/O设备 
C. I/O设备和CPU
D. CPU和主存
**07.** 在操作系统中，( )指的是一种硬件机制。
A. 通道技术 
B. 缓冲池
C. SPOOLing 技术 
D. 内存覆盖技术
**08.** 若I/O设备与存储设备进行数据交换不经过CPU来完成，则这种数据交换方式是()。
A. 程序查询 
B. 中断方式
C. DMA方式
D. 直接存取方式
**09.** 下列关于DMA方式的描述中，正确的是()。
A. DMA是一个专门负责输入/输出的处理机
B. 数据传输过程由DMA控制器负责，CPU只在预处理和后处理阶段进行干预
C. CPU通过程序的方式给出DMA可以解释的程序
D. DMA不需要CPU指出所取数据的地址与长度
**10.** 计算机系统中，不属于DMA控制器的是()。
A. 命令/状态寄存器
B. 内存地址寄存器
C. 数据寄存器
D. 堆栈指针寄存器
**11.** DMA 传输前需要进行预处理，传输后需要进行后处理，则下列说法中正确的是()。
A. 预处理程序运行在用户态，后处理程序运行在内核态
B. 负责预处理和后处理程序的进程都是请求I/O的进程
C. 预处理阶段不需要CPU参与，后处理阶段需要CPU参与
D. 预处理阶段请求I/O的进程处于运行态，后处理阶段处于阻塞态
**12.** 在下列问题中，( )不是设备分配中应考虑的问题。
A. 及时性
B. 设备的固有属性
C. 设备独立性
D. 安全性
**13.** 将系统中的每台设备按某种原则统一进行编号，这些编号作为区分硬件和识别设备的代号，该编号称为设备的( )。
A. 绝对号
B. 相对号
C. 类型号
D. 符号
**14.** 关于通道、设备控制器和设备之间的关系，以下叙述中正确的是( )。
A. 设备控制器和通道可以分别控制设备
B. 对于同一组输入/输出命令，设备控制器、通道和设备可以并行工作
C. 通道控制设备控制器、设备控制器控制设备工作
D. 以上答案都不对
**15.** 一个计算机系统配置了2台相同类型的绘图机和3台相同类型的打印机，为了正确驱动这些设备，系统应该提供( )个设备驱动程序。
A. 5
B. 3
C. 2
D. 1
**16.** 将系统调用参数翻译成设备操作命令的工作由()完成。
A. 用户层I/O
B. 设备无关的操作系统软件
C. 中断处理
D. 设备驱动程序
**17.** 向设备寄存器的写命令是在I/O软件的()中完成的。
A. 用户层软件 
B. 设备独立性软件 
C. 设备驱动程序
D. 中断处理程序
**18.** 一个典型的文本打印页面有50行，每行80个字符，假定一台标准的打印机每分钟能打印6页，向打印机的输出寄存器中写一个字符的时间很短，可忽略不计。若每打印一个字符都需要花费50$\mu$s的中断处理时间(包括所有服务)，使用中断驱动I/O方式运行这台打印机，中断的系统开销占CPU的百分比为( )。
A. 2%
B. 5%
C. 20%
D. 50%
**19.** 在接收和处理一个输入设备的中断的过程中，一定不由硬件来完成的工作是( )。
A. 判断产生中断的类型
B. CPU模式由用户态切换到内核态
C. 主机获取设备输入
D. 保存用户程序的断点
**20.** 下列几种 I/O方式中，会导致用户进程进入阻塞态的是()。
I. 程序直接控制 II. 中断方式 III. DMA方式
A. II
B. I, III
C. II, III
D. I、II、III
**21.** 当一个进程请求I/O操作时，该进程将被挂起，直到I/O 设备完成I/O操作后，设备控制器便向 CPU发送一个中断请求，CPU响应后便转向中断处理程序，下列关于中断处理程序的说法中，错误的是()。
A. 中断处理程序将设备控制器中的数据传送到内存的缓冲区（读入），或将要输出的数据传送到设备控制器（输出）。
B. 对于不同的设备，有不同的中断处理程序
C. 中断处理结束后，需要恢复CPU现场，此时一定会返回到被中断的进程
D. I/O 操作完成后，驱动程序必须检查本次I/O操作中是否发生了错误
**22.** 在I/O系统与高层之间的接口中，根据设备类型的不同，又进一步分为若干类接口，若某设备的数据传输速率较高，且可寻址，则比较适合采用()。
A. 块设备接口
B. 网络设备接口 
C. 字符设备接口
D. 流设备接口
**23.** 【2010统考真题】本地用户通过键盘登录系统时，首先获得键盘输入信息的程序是( )。
A. 命令解释程序
B. 中断处理程序
C. 系统调用服务程序
D. 用户登录程序
**24.** 【2011统考真题】用户程序发出磁盘I/O请求后，系统的正确处理流程是()。
A. 用户程序→系统调用处理程序→中断处理程序→设备驱动程序
B. 用户程序→系统调用处理程序→设备驱动程序→中断处理程序
C. 用户程序→设备驱动程序→系统调用处理程序→中断处理程序
D. 用户程序→设备驱动程序→中断处理程序→系统调用处理程序
**25.** 【2012统考真题】操作系统的I/O子系统通常由4个层次组成，每层明确定义了与邻近层次的接口，其合理的层次组织排列顺序是()。
A. 用户级 I/O软件、设备无关软件、设备驱动程序、中断处理程序
B. 用户级I/O软件、设备无关软件、中断处理程序、设备驱动程序
C. 用户级I/O软件、设备驱动程序、设备无关软件、中断处理程序
D. 用户级I/O软件、中断处理程序、设备无关软件、设备驱动程序
**26.** 【2017统考真题】系统将数据从磁盘读到内存的过程包括以下操作:
① DMA 控制器发出中断请求
② 初始化DMA控制器并启动磁盘
③ 从磁盘传输一块数据到内存缓冲区
④ 执行“DMA结束”中断服务程序
正确的执行顺序是()。
A. ③→①→②→④
B. ②→③→①→④
C. ②→①→③→④
D. ①→②→④→③

**二、综合应用题**
**01.** 某计算机系统中，时钟中断处理程序每次执行时间为2ms（包括进程切换开销），若时钟中断频率为60Hz，则CPU用于时钟中断处理的时间比率为多少？
**02.** 考虑56kb/s 调制解调器的性能，驱动程序输出一个字符后就阻塞，当一个字符打印完毕后，产生一个中断通知阻塞的驱动程序，输出下一个字符，然后阻塞。若发消息、输出一个字符和阻塞的时间总和为0.1ms，则由于处理调制解调器而占用的CPU时间比率是多少？假设每个字符有一个开始位和一个结束位，共占10位。

### 5.1.7 答案与解析
**一、单项选择题**
**01. B**
可寻址是块设备的基本特征，选项A错误。若共享设备不是可寻址的和可随机访问的，则不能保证数据的完整性和一致性，也不能提高设备的利用率，选项B正确。共享设备是指一段时间内允许多个进程同时访问的设备，选项C错误。分配共享设备是不会引起进程死锁的，选项D错误。
**02. C**
虚拟设备是指采用虚拟技术将一台独占设备转换为若干逻辑设备。引入虚拟设备是为了克服独占设备速度慢、利用率低的特点。这种设备并不是物理地变成共享设备，一般的独享设备也不能转换为共享设备，只是用户在使用它们时“感觉”是共享设备，是逻辑的概念。
**03. D**
DMA 方式主要用于块设备，磁盘是典型的块设备。这道题也要求读者了解什么是I/O 控制方式，选项A、B、C显然都不是I/O控制方式。
**04. A**
中断寄存器位于主机内；不存在I/O 地址寄存器；编程空间一般是由体系结构和操作系统共同决定的。控制寄存器和状态寄存器分别用于接收上层发来的命令并存放设备状态信号，是设备控制器与上层的接口；至于控制命令，它虽然是由CPU发出的，用来控制设备控制器，但控制命令是由设备控制器提供的，每种设备控制器都对应一组相应的控制命令。
**05. C**
接口用来传输信号，I/O逻辑即设备控制器，用来实现对设备的控制。
**06. A**
DMA 是一种不经过CPU 而直接从主存存取数据的数据交换模式，它在I/O 设备和主存之间建立了一条直接数据通路，如磁盘。当然，这条数据通路只是逻辑上的，实际并未直接建立一条物理线路，而通常是通过总线进行的。
**07. A**
通道是一种特殊的处理器，所以属于硬件技术。SPOOLing、缓冲池、内存覆盖都是在内存的基础上通过软件实现的。
**08. C**
在DMA方式中，设备和内存之间可以成批地进行数据交换而不用CPU干预，CPU只参与预处理和结束过程。
**09. B**
DMA 不是一个处理机，而是一个控制器，选项A错误。CPU无须给出DMA可以解释的程序，而是给DMA发出一条命令，同时设置DMA控制器中寄存器的值，来启动DMA，选项C错误。DMA需要CPU指出所取数据的地址与长度，这些参数存放在DMA控制器的寄存器中，选项D错误。
**10. D**
命令/状态寄存器控制DMA的工作模式并给CPU反映它当前的状态，地址寄存器存放 DMA作业时的源地址和目标地址，数据寄存器存放要DMA转移的数据，只有堆栈指针寄存器不需要在DMA 控制器中存放。
**11. D**
在预处理之前，请求 I/O 的进程通过系统调用进入内核态，因此预处理程序和后处理程序都运行在内核态。负责预处理的进程是请求 I/O 的进程，负责后处理的进程是中断服务例程，中断服务例程不是一个单独的进程。在预处理阶段，CPU执行相应的设备驱动程序来设置相关的传输参数，需要CPU的参与。预处理由请求I/O 的进程在内核态执行相关操作，之后阻塞其自身，直到系统调用返回，因此在后处理阶段请求 I/O 的进程仍处于阻塞态。
**12. A**
设备的固有属性决定了设备的使用方式；设备独立性可以提高设备分配的灵活性和设备的利用率；设备安全性可以保证分配设备时不会导致永久阻塞。设备分配时一般无须考虑及时性，及时性是一个与系统性能和用户需求相关的因素，设备分配时应该考虑的问题是如何在保证系统安全和正确运行的前提下，合理地分配和利用设备资源。
**13. A**
系统为每台设备确定一个编号以便区分和识别设备，这个确定的编号称为设备的绝对号。
**14. C**
三者的控制关系是层层递进的，选项C正确。对于同一组输入/输出命令，要么CPU给通道发出命令，要么CPU 直接给设备控制器发出命令，不存在并行的可能，选项B错误。
**15. C**
因为绘图机和打印机属于两种不同类型的设备，系统只要按设备类型配置设备驱动程序即可，即每类设备只需一个设备驱动程序。
**16. B**
将系统调用参数翻译成设备操作命令的工作由设备无关的操作系统软件完成。设备无关的操作系统软件是 I/O 软件的一部分，它向上层提供系统调用的接口，根据设备类型选择调用相应的驱动程序。设备驱动程序负责执行操作系统发出的I/O命令，因设备的不同而不同。
**17. C**
设备驱动程序负责将上层软件发来的抽象 I/O 要求转换为具体要求，发送给设备控制器，控制设备工作。设备驱动程序需要向设备寄存器写入命令，以控制设备的工作状态和数据传输方式。
**18. A**
这台打印机每分钟打印50×80×6 = 24000个字符，即每秒打印400个字符。每个字符打印中断需要占用CPU时间50µs，所以每秒用于中断的系统开销为400×50µs = 20ms。若使用中断驱动I/O，则 CPU 剩余的980ms可用于其他处理，中断的开销占CPU的2%。因此，使用中断驱动 I/O方式运行这台打印机是有意义的。
**19. C**
在中断 I/O方式下，由中断服务程序来完成数据的输入和输出，C错误。在中断响应阶段，由硬件完成CPU模式的转换，并保存用户程序的断点，中断源的识别可以采用硬件识别法。
**20. C**
在程序直接控制方式下，用户进程在I/O 过程中不会被阻塞，驱动程序完成用户进程的I/O请求后才结束，CPU和I/O操作串行。在中断控制方式下，驱动程序启动第一次I/O操作后，将调出其他进程执行，而当前用户进程被阻塞，CPU和设备准备并行。在DMA方式下，驱动程序对DMA 控制器初始化后，便发送“启动DMA”命令，在外设和主存之间传送数据，同时 CPU执行调度程序，转其他进程执行，当前用户进程被阻塞时，CPU和数据传送并行。
**21. C**
中断处理结束后，是否返回到被中断的进程，有两种情况：①采用的是屏蔽中断方式（单重中断），此时会返回被中断的进程。②采用的是中断嵌套方式（多重中断），若没有更高优先级的中断请求，则会返回被中断的进程；否则，系统将处理更高优先级的中断请求。
**22. A**
块设备是指数据的存取和传输都是以数据块为单位的设备，其特征是传输速率较高且可寻址，典型的块设备（如磁盘）通常采用DMA方式。字符设备（也称流设备）是指数据的存取和传输是以字符为单位的设备，如键盘、打印机等，字符设备的传输速率较低且不可寻址。
**23. B**
键盘是典型的通过中断I/O方式工作的外设，当用户输入信息时，计算机响应中断并通过中断处理程序获得输入信息。
**24. B**
输入/输出软件一般从上到下分为4个层次：用户层、与设备无关的软件层、设备驱动程序及中断处理程序。与设备无关的软件层也就是系统调用的处理程序。
当用户使用设备时，首先在用户程序中发起一次系统调用，操作系统的内核接到该调用请求后，请求调用处理程序进行处理，再转到相应的设备驱动程序，当设备准备好或所需数据到达后，设备硬件发出中断，将数据按上述调用顺序逆向回传到用户程序中。
**25. A**
考查内容同上题。设备管理软件一般分为4个层次：用户层、与设备无关的系统调用处理层、设备驱动程序及中断处理程序。
**26. B**
DMA 的传送过程分为预处理、数据传送和后处理三个阶段。在预处理阶段，由CPU 初始化DMA 控制器中的有关寄存器、设置传送方向、测试并启动设备等。在数据传送阶段，完全由DMA控制，DMA 控制器接管系统总线。在后处理阶段，DMA控制器向CPU发送中断请求，CPU执行中断服务程序做DMA结束处理。因此，正确的执行顺序是②③①④。

**二、综合应用题**
**01.【解答】**
时钟中断频率为60Hz，因此中断周期为1/60s，每个时钟周期中用于中断处理的时间为2ms，因此比率为0.002/(1/60) = 12%。
**02.【解答】**
因为一个字符占10位，因此在56kb/s的速率下，每秒传送56000/10=5600个字符，即产生5600次中断。每次中断需0.1ms，因此处理调制解调器占用的CPU时间共为5600×0.1ms = 560ms，占56%的CPU时间。

### 5.2 设备独立性软件
在学习本节时，请读者思考以下问题：