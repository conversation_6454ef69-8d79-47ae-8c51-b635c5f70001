# 26王道操作系统_content目录

### 第1章 计算机系统概述 (1)
- 1.1 操作系统的基本概念 (1)
  - 1.1.1 操作系统的概念 (1)
  - 1.1.2 操作系统的功能和目标 (2)
  - 1.1.3 操作系统的特征 (3)
  - 1.1.4 本节习题精选 (5)
  - 1.1.5 答案与解析 (6)
- 1.2 操作系统发展历程 (7)
  - 1.2.1 手工操作阶段(此阶段无操作系统) (7)
  - 1.2.2 批处理阶段(操作系统开始出现) (7)
  - 1.2.3 分时操作系统 (8)
  - 1.2.4 实时操作系统 (9)
  - 1.2.5 网络操作系统和分布式计算机系统 (9)
  - 1.2.6 个人计算机操作系统 (9)
  - 1.2.7 本节习题精选 (10)
  - 1.2.8 答案与解析 (12)
- 1.3 操作系统的运行环境 (15)
  - 1.3.1 处理器运行模式 (15)
  - 1.3.2 中断和异常的概念 (16)
  - 1.3.3 系统调用 (18)
  - 1.3.4 本节习题精选 (19)
  - 1.3.5 答案与解析 (22)
- 1.4 操作系统结构 (26)
- 1.5 操作系统引导 (29)
- 1.6 虚拟机 (30)
  - 1.6.1 虚拟机的基本概念 (30)
  - 1.6.2 本节习题精选 (31)
  - 1.6.3 答案与解析 (33)
- 1.7 本章疑难点 (35)

### 第2章 进程与线程 (37)
- 2.1 进程与线程 (37)
  - 2.1.1 进程的概念和特征 (38)
  - 2.1.2 进程的组成 (38)
  - 2.1.3 进程的状态与转换 (40)
  - 2.1.4 进程控制 (41)
  - 2.1.5 进程的通信 (42)
  - 2.1.6 线程和多线程模型 (44)
  - 2.1.7 本节小结 (48)
  - 2.1.8 本节习题精选 (49)
  - 2.1.9 答案与解析 (57)
- 2.2 CPU调度 (66)
  - 2.2.1 调度的概念 (66)
  - 2.2.2 调度的实现 (67)
  - 2.2.3 调度的目标 (69)
  - 2.2.4 进程切换 (70)
  - 2.2.5 CPU 调度算法 (71)
  - 2.2.6 多处理机调度 (76)
  - 2.2.7 本节小结 (77)
  - 2.2.8 本节习题精选 (77)
  - 2.2.9 答案与解析 (85)
- 2.3 同步与互斥 (97)
  - 2.3.1 同步与互斥的基本概念 (98)
  - 2.3.2 实现临界区互斥的基本方法 (99)
  - 2.3.3 互斥锁 (102)
  - 2.3.4 信号量 (102)
  - 2.3.5 经典同步问题 (106)
  - 2.3.6 管程 (112)
  - 2.3.7 本节小结 (113)
  - 2.3.8 本节习题精选 (114)
  - 2.3.9 答案与解析 (126)
- 2.4 死锁 (149)
  - 2.4.1 死锁的概念 (149)
  - 2.4.2 死锁预防 (152)
  - 2.4.3 死锁避免 (152)
  - 2.4.4 死锁检测和解除 (157)
  - 2.4.5 本节小结 (158)
  - 2.4.6 本节习题精选 (158)
  - 2.4.7 答案与解析 (165)
- 2.5 本章疑难点 (176)

### 第3章 内存管理 (177)
- 3.1 内存管理概念 (177)
  - 3.1.1 内存管理的基本原理和要求 (177)
  - 3.1.2 连续分配管理方式 (181)
  - 3.1.3 基本分页存储管理 (184)
  - 3.1.4 基本分段存储管理 (188)
  - 3.1.5 段页式存储管理 (191)
  - 3.1.6 本节小结 (192)
  - 3.1.7 本节习题精选 (192)
  - 3.1.8 答案与解析 (202)
- 3.2 虚拟内存管理 (213)
  - 3.2.1 虚拟内存的基本概念 (213)
  - 3.2.2 请求分页管理方式 (215)
  - 3.2.3 页框分配 (216)
  - 3.2.4 页面置換算法 (218)
  - *3.2.5 抖动和工作集 (222)
  - 3.2.6 页框回收 (222)
  - 3.2.7 内存映射文件 (223)
  - 3.2.8 虚拟存储器性能影响因素 (224)
  - 3.2.9 地址翻译 (224)
  - 3.2.10 本节小结 (226)
  - 3.2.11 本节习题精选 (227)
  - 3.2.12 答案与解析 (238)
- 3.3 本章疑难点 (251)

### 第4章 文件管理 (252)
- 4.1 文件系统基础 (252)
  - 4.1.1 文件的基本概念 (252)
  - 4.1.2 文件控制块和索引节点 (254)
  - 4.1.3 文件的操作 (255)
  - 4.1.4 文件的逻辑结构 (257)
  - 4.1.5 文件的物理结构 (259)
  - 4.1.6 文件保护 (263)
  - 4.1.7 本节小结 (265)
  - 4.1.8 本节习题精选 (265)
  - 4.1.9 答案与解析 (272)
- 4.2 目录 (282)
  - 4.2.1 目录的基本概念 (282)
  - 4.2.2 目录的操作 (282)
  - 4.2.3 目录结构 (282)
  - *4.2.4 目录实现 (284)
  - 4.2.5 文件共享 (285)
  - 4.2.6 本节小结 (287)
  - 4.2.7 本节习题精选 (287)
  - 4.2.8 答案与解析 (291)
- 4.3 文件系统 (294)
  - 4.3.1 文件系统结构 (294)
  - 4.3.2 文件系统布局 (295)
  - 4.3.3 文件存储空间管理 (296)
  - 4.3.4 虚拟文件系统 (299)
  - 4.3.5 文件系统挂载 (300)
  - 4.3.6 本节小结 (301)
  - 4.3.7 本节习题精选 (301)
  - 4.3.8 答案与解析 (303)
- 4.4 本章疑难点 (305)

### 第5章 输入/输出管理 (306)
- 5.1 I/O 管理概述 (306)
  - 5.1.1 I/O 设备 (306)
  - 5.1.2 I/O控制方式 (309)
  - 5.1.3 I/O 软件层次结构 (311)
  - 5.1.4 应用程序I/O接口 (313)
  - 5.1.5 本节小结 (314)
  - 5.1.6 本节习题精选 (314)
  - 5.1.7 答案与解析 (317)
- 5.2 设备独立性软件 (319)
  - 5.2.1 设备独立性软件 (320)
  - 5.2.2 高速缓存与缓冲区 (320)
  - 5.2.3 设备分配与回收 (323)
  - 5.2.4 SPOOLing技术(假脱机技术) (326)
  - 5.2.5 设备驱动程序接口 (327)
  - 5.2.6 I/O 操作举例 (327)
  - 5.2.7 本节小结 (329)
  - 5.2.8 本节习题精选 (329)
  - 5.2.9 答案与解析 (334)
- 5.3 磁盘和固态硬盘 (340)
  - 5.3.1 磁盘 (340)
  - 5.3.2 磁盘的管理 (341)
  - 5.3.3 磁盘调度算法 (342)
  - 5.3.4 固态硬盘 (346)
  - 5.3.5 本节小结 (347)
  - 5.3.6 本节习题精选 (348)
  - 5.3.7 答案与解析 (352)
- 5.4 本章疑难点 (359)

### 参考文献 (360)






