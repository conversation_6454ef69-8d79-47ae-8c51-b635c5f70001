微内核操作系统将进程管理、存储器管理以及I/O 管理这些功能一分为二,属于机制的很小一部分放入微内核,而绝大部分放入微内核外的各种服务器实现,大多数服务器都要比微内核大。因此,在采用客户/服务器模式时,能将微内核做得很小。
(3)微内核的特点

**命题追踪** 微内核操作系统的特点(2023)

微内核结构的主要优点如下所示。
①扩展性和灵活性。许多功能从内核中分离出来,当要修改某些功能或增加新功能时,只需在相应的服务器中修改或新增功能,或再增加一个专用的服务器,而无须改动内核代码。
②可靠性和安全性。前面已举例说明。
③可移植性。与CPU和I/O硬件有关的代码均放在内核中,而其他各种服务器均与硬件平台无关,因而将操作系统移植到另一个平台上所需做的修改是比较小的。
④分布式计算。客户和服务器之间、服务器和服务器之间的通信采用消息传递机制,这就使得微内核系统能很好地支持分布式系统和网络系统。
微内核结构的主要问题是性能问题,因为需要频繁地在内核态和用户态之间进行切换,操作系统的执行开销偏大。为了改善运行效率,可以将那些频繁使用的系统服务移回内核,从而保证系统性能,但这又会使微内核的容量明显地增大。
虽然宏内核在桌面操作系统中取得了绝对的胜利,但是微内核在实时、工业、航空及军事应用中特别流行,这些领域都是关键任务,需要有高度的可靠性。

5. 外核
不同于虚拟机克隆真实机器,另一种策略是对资源进行划分,为每个用户分配整个资源的一个子集。例如,某虚拟机可能得到磁盘的0~1023盘块,而另一虚拟机得到磁盘的1024~2047盘块等。在底层,一种称为外核(exokernel)的程序在内核态中运行。它的任务是为虚拟机分配资源,并检查这些资源使用的安全性,以确保没有机器会使用他人的资源。每个用户的虚拟机可以运行自己的操作系统,但限制只能使用已经申请并且获得分配的那部分资源。
外核机制的优点是减少了资源的“映射层”。在其他设计中,每个虚拟机系统都认为它拥有完整的磁盘(或其他资源),这样虚拟机监控程序就必须维护一张表格以重映像磁盘地址,有了外核,这个重映射处理就不需要了。外核只需要记录已分配给各个虚拟机的有关资源即可。这种方法还有一个优点,它将多道程序(在外核内)与用户操作系统代码(在用户空间内)加以分离,而且相应的负载并不重,因为外核所做的只是保持多个虚拟机彼此不发生冲突。

### 1.5 操作系统引导
操作系统(如 Windows、Linux等)是一种程序,程序以数据的形式存放在硬盘中,而硬盘通常分为多个区,一台计算机中又可能有多个或多种外部存储设备。操作系统引导是指计算机利用 CPU 运行特定程序,通过程序识别硬盘,识别硬盘分区,识别硬盘分区上的操作系统,最后通过程序启动操作系统,一环扣一环地完成上述过程。

**命题追踪** 操作系统的引导过程(2021)

常见操作系统的引导过程如下:
①激活 CPU。激活的CPU读取ROM 中的 boot程序,将指令寄存器置为 BIOS (基本输入/输出系统)的第一条指令,即开始执行BIOS的指令。
②硬件自检。BIOS 程序在内存最开始的空间构建中断向量表,接下来的POST(通电自检)过程要用到中断功能。然后进行通电自检,检查硬件是否出现故障。如有故障,主板会发出不同含义的蜂鸣,启动中止;如无故障,屏幕会显示CPU、内存、硬盘等信息。
③加载带有操作系统的硬盘。通电自检后,BIOS 开始读取 Boot Sequence(通过CMOS 里保存的启动顺序,或者通过与用户交互的方式),将控制权交给启动顺序排在第一位的存储设备,然后CPU将该存储设备引导扇区的内容加载到内存中。
④加载主引导记录(MBR)。硬盘以特定的标识符区分引导硬盘和非引导硬盘。若发现一个存储设备不是可引导盘,就检查下一个存储设备。如无其他启动设备,就会死机。主引导记录MBR的作用是告诉CPU去硬盘的哪个主分区去找操作系统。
⑤扫描硬盘分区表,并加载硬盘活动分区。MBR 包含硬盘分区表,硬盘分区表以特定的标识符区分活动分区和非活动分区。主引导记录扫描硬盘分区表,进而识别含有操作系统的硬盘分区(活动分区)。找到硬盘活动分区后,开始加载硬盘活动分区,将控制权交给活动分区。
⑥加载分区引导记录(PBR)。读取活动分区的第一个扇区,这个扇区称为分区引导记录(PBR),其作用是寻找并激活分区根目录下用于引导操作系统的程序(启动管理器)。
⑦加载启动管理器。分区引导记录搜索活动分区中的启动管理器,加载启动管理器。

**命题追踪** 操作系统运行的存储器(2013)

⑧加载操作系统。将操作系统的初始化程序加载到内存中执行。

### 1.6 虚拟机

#### 1.6.1 虚拟机的基本概念
虚拟机是指利用虚拟化技术,将一台物理机器虚拟化为多台虚拟机器,通过隐藏特定计算平台的实际物理特性,为用户提供抽象的、统一的、模拟的计算环境。有两类虚拟化方法。

1. **第一类虚拟机管理程序**
从技术上讲,第一类虚拟机管理程序就像一个操作系统,因为它是唯一一个运行在最高特权级的程序。它在裸机上运行并且具备多道程序功能。虚拟机管理程序向上层提供若干虚拟机,这些虚拟机是裸机硬件的精确复制品。因为每台虚拟机都与裸机相同,所以在不同的虚拟机上可以运行任何不同的操作系统。图1.7(a)中显示了第一类虚拟机管理程序。

Excel Word Mplayer Emacs
ㅎㅎㅎㅎ
Windows Linux 控制域
第一类虚拟机管理程序
硬件(CPU、磁盘、网络、中断等)
(a)

客户操作系统进程
ㅇㅇㅇ
宿主操作系统进程
客户操作系统(如Windows)
第二类虚拟机管理程序
宿主操作系统(如Linux)
硬件(CPU、磁盘、网络、中断等)
(b)

图1.7 两类虚拟机管理程序在系统中的位置