### 二、综合应用题

**01. 【解答】**
① 基本语句`k=k+10*i`共执行了$n-2$次，所以$T(n)=O(n)$。
② 设循环体共执行$t$次，每循环一次，循环变量$y$加1，最终$t=y$。故$t^2 \le n$，得$T(n)=O(n^{1/2})$。
③ 内循环执行$m$次，外循环执行$n$次，根据乘法原理，共执行了$m \times n$次，故$T(m,n)=O(m \times n)$。

### 归纳总结

本章的重点是分析程序的时间复杂度。一定要掌握分析时间复杂度的方法和步骤，很多读者在做题时一眼就能看出程序的时间复杂度，但就是无法规范地表述其推导过程。为此，编者查阅众多资料，总结出了此类题型的两种形式，供大家参考。

1. 循环主体中的变量参与循环条件的判断
在用于递推实现的算法中，首先找出基本运算的执行次数$x$与问题规模$n$之间的关系式，解得$x=f(n)$，$f(n)$的最高次幂为$k$，则算法的时间复杂度为$O(n^k)$。例如，

1.
```
int i=1;
while(i<=n)
    i=i*2;
```
2.
```
int y=5;
while((y+1)*(y+1)<n)
    y=y+1;
```
在例1中，设基本运算`i=i*2`的执行次数为$t$，则$2^t \le n$，解得$t \le \log_2 n$，故$T(n)=O(\log_2 n)$。
在例2中，设基本运算`y=y+1`的执行次数为$t$，则$t=y-5$，且$(t+5+1)(t+5+1)<n$，解得$t<\sqrt{n}-6$，即$T(n)=O(\sqrt{n})$。

2. 循环主体中的变量与循环条件无关
此类题可采用数学归纳法或直接累计循环次数。多层循环时从内到外分析，忽略单步语句、条件判断语句，只关注主体语句的执行次数。此类问题又可分为递归程序和非递归程序：
*   递归程序一般使用公式进行递推。时间复杂度的分析如下：
    $$
    T(n)=1+T(n-1)=1+1+T(n-2)=\dots=n-1+T(1)
    $$
    即$T(n)=O(n)$。
*   非递归程序的分析比较简单，可以直接累计次数。本节前面给出了相关的习题。

### 思维拓展

求解斐波那契数列
$$
F(n)=\begin{cases}
0, & n=0 \\
1, & n=1 \\
F(n-1)+F(n-2), & n>1
\end{cases}
$$
有两种常用的算法：递归算法和非递归算法。试分别分析两种算法的时间复杂度。

**提示**
请结合归纳总结中的两种方法进行解答。