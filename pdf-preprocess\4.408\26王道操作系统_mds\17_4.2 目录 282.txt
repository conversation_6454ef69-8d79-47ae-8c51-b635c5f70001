## 4.2 目录

在学习本节时,请读者思考以下问题:
1) 目录管理的要求是什么?
2) 在目录中查找某个文件可以使用什么方法?
上节介绍了文件的逻辑结构和物理结构,本节将介绍目录的实现。文件目录也是一种数据结构,用于标识系统中的文件及其物理地址,供检索时使用。通常,一个文件目录也被视为一个文件。在学习本节的内容时,读者可以围绕目录管理的要求来思考。

### 4.2.1 目录的基本概念

上节说过,FCB的有序集合称为文件目录,一个FCB就是一个文件目录项。与文件管理系统和文件集合相关联的是文件目录,它包含有关文件的属性、位置和所有权等。

首先来看目录管理的基本要求:从用户的角度看,目录在用户(应用程序)所需要的文件名和文件之间提供一种映射,所以目录管理要实现“按名存取”;目录存取的效率直接影响到系统的性能,所以要提高对目录的检索速度;在多用户系统中,应允许多个用户共享一个文件,因此目录还需要提供用于控制访问文件的信息。此外,应允许不同用户对不同文件采用相同的名字,以便于用户按自己的习惯给文件命名,目录管理通过树形结构来解决和实现。

### 4.2.2 目录的操作

在理解一个文件系统的需求前,我们首先考虑在目录这个层次上所需要执行的操作,这有助于后面文件系统的整体理解。
*   搜索。当用户使用一个文件时,需要搜索目录,以找到该文件的对应目录项。
*   创建文件。当创建一个新文件时,需要在目录中增加一个目录项。
*   删除文件。当删除一个文件时,需要在目录中删除相应的目录项。
*   创建目录。在树形目录结构中,用户可创建自己的用户文件目录,并可再创建子目录。
*   删除目录。有两种方式:①不删除非空目录,删除时要先删除目录中的所有文件,并递归地删除子目录。②可删除非空目录,目录中的文件和子目录同时被删除。
*   移动目录。将文件或子目录在不同的父目录之间移动,文件的路径名也会随之改变。
*   显示目录。用户可以请求显示目录的内容,如显示该用户目录中的所有文件及属性。
*   修改目录。某些文件属性保存在目录中,因此这些属性的变化需要改变相应的目录项。

### 4.2.3 目录结构

**1. 单级目录结构**
在整个文件系统中只建立一张目录表,每个文件占一个目录项,如图4.12所示。

FCB₁ | FCB₂ | FCB₃ | ... | FCBₙ
---|---|---|---|---
↓ | ↓ | ↓ | ... | ↓
文件1 | 文件2 | 文件3 | ... | 文件n

图4.12 单级目录结构

当建立一个新文件时,必须先检索所有目录项,以确保没有“重名”的情况,然后在该目录中增设一项,将新文件的属性信息填入该项。当访问一个文件时,先按文件名在该目录中查找到相应的FCB,经合法性检查后执行相应的操作。当删除一个文件时,先从该目录中找到该文件的目录项,回收该文件所占用的存储空间,然后清除该目录项。
单级目录结构实现了“按名存取”,但是存在查找速度慢、文件不允许重名、不便于文件共享等缺点,而且对于多用户的操作系统显然是不适用的。

**2. 两级目录结构**
为了克服单级目录所存在的缺点,可以采用两级方案,将文件目录分成主文件目录(Master File Directory, MFD)和用户文件目录(User File Directory, UFD)两级,如图4.13所示。

| User₁ | User₂ | User₃ | User₄ | 主文件目录 |
|---|---|---|---|---|
| ↓ | ↓ | ↓ | ↓ | |
| CAT | BO | A | TEST | A | TEST | A | DATA | X | DATA | A | 用户文件目录 |
| ↓ | ↓ | ↓ | ↓ | ↓ | ↓ | ↓ | ↓ | ↓ | ↓ | ↓ | |
| ⚪ | ⚪ | ⚪ | ⚪ | ⚪ | ⚪ | ⚪ | ⚪ | ⚪ | ⚪ | ⚪ | 文件 |

图4.13 两级目录结构

主文件目录项记录用户名及相应用户文件目录所在的存储位置。用户文件目录项记录该用户所有文件的FCB。当某用户欲对其文件进行访问时,只需搜索该用户对应的UFD,这既解决了不同用户文件的“重名”问题,又在一定程度上保证了文件的安全。
两级目录结构提高了检索的速度,解决了多用户之间的文件重名问题,文件系统可以在目录上实现访问限制。但是两级目录结构缺乏灵活性,不能对文件分类。

**3. 树形目录结构**
**命题追踪** 设置当前工作目录的作用 (2010)
将两级目录结构加以推广,就形成了树形目录结构,如图4.14所示。它可以明显地提高对目录的检索速度和文件系统的性能。当用户要访问某个文件时,用文件的路径名标识文件,文件路径名是个字符串,由从根目录出发到所找文件通路上所有目录名与数据文件名用分隔符“/”链接而成。从根目录出发的路径称为绝对路径,系统中的每个文件都有唯一的路径名。一个进程在运行时,其所访问的文件大多局限于某个范围,当层次较多时,每次从根目录查询会浪费时间,因此可为每个进程设置一个当前目录(也称工作目录),此时进程对各文件的访问都只需相对于当前目录而进行。当用户要访问某个文件时,使用相对路径名标识文件,相对路径由从当前目录出发到所找文件通路上所有目录名与数据文件名用分隔符“/”链接而成。
图4.14所示是Linux操作系统的树形目录结构,“/dev/hda”就是一个绝对路径。若当前目录为“/bin”,则“./ls”就是一个相对路径,其中符号“.”表示当前工作目录。
通常,每个用户都有各自的“当前目录”,登录后自动进入该用户的“当前目录”。操作系统提供一个专门的系统调用,供用户随时改变“当前目录”。例如,在UNIX系统中,“/etc/passwd”文件就包含有用户登录时默认的“当前目录”,可用cd命令改变“当前目录”。
树形目录结构可以很方便地对文件进行分类,层次结构清晰,也能够更有效地进行文件的管理和保护。在树形目录中,不同性质、不同用户的文件,可以分别呈现在系统目录树的不同层次或不同子树中,很容易地赋予不同的存取权限。但是,在树形目录中查找一个文件,需要按路径名逐级访问中间节点,增加了磁盘访问次数,这无疑会影响查询速度。目前,大多数操作系统如UNIX、Linux和Windows系统都都采用了树形文件目录。

/
├── dev
│   ├── fd0
│   └── hda
├── home
├── bin
│   ├── ls
│   └── cp
└── usr

图4.14 树形目录结构

**4. 无环图目录结构**
树形目录结构能便于实现文件分类,但不便于实现文件共享,为此在树形目录结构的基础上增加一些指向同一节点的有向边,使整个目录成为一个有向无环图,如图4.15所示。这种结构允许目录共享子目录或文件,同一个文件或子目录可以出现在两个或多个目录中。
当某用户要求删除一个共享节点时,若系统只是简单地将它删除,则当另一共享用户需要访问时,会因无法找到这个文件而发生错误。为此,可为每个共享节点设置一个共享计数器,每当图中增加对该节点的共享链时,计数器加1;每当某用户提出删除该节点时,计数器减1。仅当共享计数器为0时,才真正删除该节点,否则仅删除请求用户的共享链。
无环图目录结构方便地实现了文件的共享,但使得系统的管理变得更加复杂。

root
├── dict
│   ├── list
│   └── count -> P
├── spell
│   └── list
└── P
    └── root rade W7

图4.15 无环图目录结构

### *4.2.4 目录实现
在访问一个文件时,操作系统利用路径名找到相应目录项,目录项中提供了查找文件磁盘块所需要的信息。目录实现的基本方法有线性列表和哈希表两种,要注意目录的实现就是为了查找,因此线性列表实现对应线性查找,哈希表的实现对应散列查找。

**1. 线性列表**
最简单的目录实现方法是,采用文件名和数据块指针的线性列表。当创建新文件时,必须首先搜索目录以确定没有同名的文件存在,然后在目录中增加一个新的目录项。当删除文件时,则根据给定的文件名搜索目录,然后释放分配给它的空间。当要重用目录项时有许多种方法:可以将目录项标记为不再使用,或将它加到空闲目录项的列表上,还可以将目录的最后一个目录项复制到空闲位置,并减少目录的长度。采用链表结构可以减少删除文件的时间。
线性列表的优点在于实现简单,不过由于线性表的特殊性,查找比较费时。

**2. 哈希表**
除了采用线性列表存储文件目录项,还可以采用哈希数据结构。哈希表根据文件名得到一个值,并返回一个指向线性列表中元素的指针。这种方法的优点是查找非常迅速,插入和删除也较简单,不过需要一些措施来避免冲突(两个文件名称哈希到同一位置)。
目录查询是通过在磁盘上反复搜索完成的,需要不断地进行I/O操作,开销较大。所以如前所述,为了减少I/O操作,将当前使用的文件目录复制到内存,以后要使用该文件时只需在内存中操作,因此降低了磁盘操作次数,提高了系统速度。

### 4.2.5 文件共享

文件共享使多个用户共享同一个文件,系统中只需保留该文件的一个副本。若系统不能提供共享功能,则每个需要该文件的用户都要有各自的副本,会造成对存储空间的极大浪费。
前面介绍了无环图目录,基于该结构可实现文件共享,当建立链接关系时,必须将被共享文件的物理地址(盘块号)复制到相应的目录。若某个用户向该文件添加新数据,且需要增加新盘块,则这些新增的盘块只会出现在执行操作的目录中,对其他共享用户是不可见的。

**1. 基于索引节点的共享方式(硬链接)**
**命题追踪** 硬链接和软链接文件中引用计数值的分析(2009)
**命题追踪** 硬链接的原理(2017)
硬链接是基于索引节点的共享方式,它将文件的物理地址和属性等信息不再放在目录项中,而是放在索引节点中,在目录中只设置文件名及指向相应索引节点的指针。如图4.16所示,在用户A和B的目录中,都设置有指向共享文件的索引节点指针。在索引节点中还有一个链接计数count,也称引用计数,表示链接到本索引节点(文件)上的用户目录项的数量。当count=2时,表示有两个用户目录项链接到本文件上,即有两个用户共享此文件。

A用户文件目录
| Test r | -> |
|---|---|

B用户文件目录
| Test r | -> |

索引节点
| count = 2 | -> | Test |
| 文件物理地址 | |

图4.16 基于索引节点的共享方式

当用户A创建一个新文件时,他是该文件的所有者,此时将count置为1。当用户B要共享此文件时,在B的目录中增加一个目录项,并设置一个指针指向该文件的索引节点。此时,文件主仍是用户A, count=2。当用户A不再需要此文件时,能否直接将其删除?答案是否定的。因为若删除该文件,必然也删除该文件的索引节点,这样便使用户B的指针悬空,而B可能正在此文件上执行写操作,此时将因此半途而废。因此用户A不能删除此文件,只是将该文件的count减1,然后删除自己目录中的相应目录项。用户B仍可以使用该文件。当count=0时,表示没有用户使用该文件,才删除该文件。如图4.17所示为文件共享中的链接计数。

用户A的目录 -> | owner = A | -> 用户A的目录 | owner = A | -> 用户B的目录 | owner = A | -> 用户B的目录
| count = 1 | | count = 2 | | count = 1 |
| 索引节点 | | 索引节点 | | 索引节点 |

图4.17 文件共享中的链接计数

**2. 利用符号链实现文件共享(软链接)**
为使用户B能共享用户A的一个文件F,可由系统创建一个LINK类型的新文件L,并将文件L写入用户B的目录,以实现B的目录与文件F的链接。文件L中只含有被链接文件F的路径名,如图4.18所示。这种链接方法称为符号链接或软链接,它类似于Windows系统中的快捷方式。当用户B访问文件L时,操作系统看到要读的文件属于LINK类型,则根据其中记录的路径名去查询文件F,然后对F进行读/写操作,从而实现用户B对文件F的共享。

A用户文件目录
| F | -> | 索引节点 | -> 文件F |
|---|---|---|---|
| | | count = 1 | |
| | | 文件物理地址 | |

B用户文件目录
| L | -> | count = 1 | -> 文件L (Link类型文件,记录了文件F的路径: "/Wang/F") |
|---|---|---|---|
| | | 文件物理地址 | |

图4.18 利用符号链的共享方式

**命题追踪** 软链接方式删除共享文件后的情况 (2021)
利用符号链方式实现文件共享时,只有文件主才拥有指向其索引节点的指针。而共享该文件的其他用户只有该文件的路径名,并不拥有指向其索引节点的指针。这样,也就不会发生在文件主删除一个共享文件后留下一个悬空指针的情况。当文件主将一个共享文件删除后,若其他用户又试图通过符号链去访问它时,则会访问失败,于是再将符号链删除,此时不会产生任何影响。
在符号链的共享方式中,当其他用户读共享文件时,系统根据文件路径名依次查找目录,直至找到该文件的索引节点。因此,每次访问共享文件时,都可能要多次地读盘,增大了访问文件的开销。此外,符号链接也是一个文件,其索引节点也要耗费一定的磁盘空间。
利用符号链实现网络文件共享时,只需提供该文件所在机器的网络地址及文件路径名。

可以这样说:文件共享,“软硬”兼施。硬链接就是多个指针指向一个索引节点,保证只要还有一个指针指向索引节点,索引节点就不能删除;软链接就是将到达共享文件的路径保存下来,当要访问文件时,根据路径寻找文件。可见,硬链接的查找速度要比软链接的快。

### 4.2.6 本节小结

本节开头提出的问题的参考答案如下。
1) 目录管理的要求是什么?
①实现“按名存取”,这是目录管理最基本的功能。②提高对目录的检索速度,从而提高对文件的存取速度。③为了方便用户共享文件,目录还需要提供用于控制访问文件的信息。④允许不同用户对不同文件采用相同的名字,以便用户按自己的习惯给文件命名。
2) 在目录中查找某个文件可以使用什么方法?
可以采用线性列表法或哈希表法。线性列表将文件名组织成一个线性表,查找时依次与线性表中的每个表项进行比较。若将文件名按序排列,则使用折半查找法可以降低平均的查找时间,但建立新文件时会增加维护线性表的开销。哈希表用文件名通过哈希函数得到一个指向文件的指针,这种方法非常迅速,但要注意避免冲突。

### 4.2.7 本节习题精选

**一、单项选择题**
**01.** 下列关于目录检索的论述中,正确的是( )。
A. 散列法具有较快的检索速度,因此现代操作系统中都用它替代传统的顺序检索方法
B. 在利用顺序检索法时,对树形目录应采用文件的路径名,且应从根目录开始逐级检索
C. 在利用顺序检索法时,只要路径名的一个分量名未找到,就应停止查找
D. 利用顺序检索法查找完成后,即可得到文件的物理地址
**02.** 一个文件的相对路径名是从( )开始,逐步沿着各级子目录追溯,最后到指定文件的整个通路上所有子目录名组成的一个字符串。
A. 当前目录 B. 根目录 C. 多级目录 D. 二级目录
**03.** 文件系统采用多级目录结构的目的是( )。
A. 减少系统开销 B. 节省存储空间 C. 解决命名冲突 D. 缩短传送时间
**04.** 若文件系统中有两个文件重名,则不应采用( )。
A. 单级目录结构 B. 两级目录结构 C. 树形目录结构 D. 多级目录结构
**05.** 下面的说法中,错误的是( )。
I. 一个文件在同一系统中、不同的存储介质上的复制文件,应采用同一种物理结构
II. 对一个文件的访问,常由用户访问权限和用户优先级共同限制
III. 文件系统采用树形目录结构后,对于不同用户的文件,其文件名应该不同
IV. 为防止系统故障造成系统内文件受损,常采用存取控制矩阵方法保护文件
A. II B. I、III C. I、III、IV D. 全选
**06.** 设文件F1的当前引用计数为1,先建立F1的硬链接文件F2,再建立F1的符号链接文件 F3,然后删除F2,则此时文件F1、F3的引用计数值分别是( )。
A. 1、1 B. 1、2 C. 1、0 D. 2、2
**07.** 设文件 F1的当前引用计数值为1,先建立F1的硬链接文件F2,再建立F2的符号链接文件F3,现有两个进程P₁和P₂分别打开了F1和F2,则下列说法中正确的是( )。
A. 两次打开操作只涉及一次文件索引节点的磁盘读取操作
B. 进程P₁和P₂对F1具有相同的访问权限
C. 若删除文件F3,则F2的引用计数值减1
D. 进程 P₁读取F1时需要提供F1的绝对路径作为系统调用参数
**08.** 在树形目录结构中,文件已被打开后,对文件的访问采用( )。
A. 文件符号名 B. 从根目录开始的路径名
C. 从当前目录开始的路径名 D. 文件描述符
**09.** 在访问文件时,需要根据文件名对目录文件进行检索,其检索性能主要由( )决定。
Ⅰ. 文件大小 Ⅱ. 目录项数量 Ⅲ. 目录项的大小 Ⅳ. 目录项在目录中的位置
A. I、II和III B. II、III和IV
C. I、III和IV D. I、II和IV
**10.** 在计算机中,不允许两个文件名重名主要指的是( )。
A. 不同磁盘的不同目录下 B. 不同磁盘里的同名目录下
C. 同一个磁盘的不同目录下 D. 同一个磁盘的同一目录下
**11.** 文件系统实现按名存取主要是靠( )实现的。
A. 查找位示图 B. 查找文件目录 C. 查找作业表 D. 地址转换机构
**12.** 在一个文件系统中,FCB占64B,盘块大小为1KB,采用一级目录。假定文件目录中有3200个目录项,则查找一个文件平均需要( )次访问磁盘。
A. 50 B. 54 C. 100 D. 200
**13.** 在一个采用索引节点的文件系统中,目录项分为文件名和索引节点编号两部分,文件名和索引节点编号各占8B,盘块大小为1KB,采用一级目录,假定文件目录中有3200个目录项,则读入一个文件的索引节点平均需要( )次访问磁盘。
A. 25 B. 26 C. 51 D. 52
**14.** 【2009统考真题】设文件F1的当前引用计数值为1,先建立文件F1的符号链接(软链接)文件F2,再建立文件F1的硬链接文件F3,然后删除文件F1。此时,文件F2和文件F3的引用计数值分别是( )。
A. 0,1 B. 1,1 C. 1,2 D. 2,1
**15.** 【2010统考真题】设置当前工作目录的主要目的是( )。
A. 节省外存空间 B. 节省内存空间
C. 加快文件的检索速度 D. 加快文件的读/写速度
**16.** 【2017统考真题】若文件f1的硬链接为f2,两个进程分别打开f1和f2,获得对应的文件描述符为fd1和fd2,则下列叙述中正确的是( )。
I. f1和f2的读/写指针位置保持相同
II. f1和f2共享同一个内存索引节点
III. fd1和fd2分别指向各自的用户打开文件表中的一项
A. 仅III B. 仅II、III C. 仅I、II D. I、II 和 III
**17.** 【2021统考真题】若目录dir下有文件file1,则为删除该文件内核不必完成的工作是( )。
A. 删除 file1的快捷方式
B. 释放 file1 的文件控制块
C. 释放 file1占用的磁盘空间
D. 删除目录 dir中与file1对应的目录项

**二、综合应用题**
**01.** 设某文件系统采用两级目录的结构,主目录中有10个子目录,每个子目录中有10个目录项。在同样多目录的情况下,若采用单级目录结构,所需平均检索目录项数是两级目录结构平均检索目录项数的多少倍?
**02.** 有文件系统如下图所示,图中的框表示目录,圆圈表示普通文件。
1) 可否建立F与R的链接?试加以说明。
2) 能否删除R?为什么?
3) 能否删除N?为什么?
根目录
├── A
│   ├── D
│   ├── E
│   └── F
│       ├── J
│       ├── K
│       └── L
├── B
│   ├── G
│   ├── H
│   └── I
│       ├── M
│       ├── N
│       │   ├── P
│       │   └── Q
│       └── O
└── C
    └── ...

R
S

**03.** 某树形目录结构的文件系统如下图所示。该图中的方框表示目录,圆圈表示文件。
根目录
├── A
│   └── E
│       └── I
├── B
│   └── F
│       ├── J
│       └── K
│           ├── O
│           └── P
├── C
│   └── G
│       ├── L
│       │   └── Q
│       ├── M
│       │   ├── R
│       │   └── S
│       └── N
│           └── T
└── D
    └── H

1) 可否进行下列操作?
①在目录D中建立一个文件,取名为A。
②将目录C改名为A。
2) 若E和G分别为两个用户的目录:
①在一段时间内用户G主要使用文件S和T。为简化操作和提高速度,应如何处理?
②用户E欲对文件I加以保护,不许别人使用,能否实现?如何实现?
**04.** 有一个文件系统如图A所示。图中的方框表示目录,圆圈表示普通文件。根目录常驻内存,目录文件组织成链接文件,不设FCB,普通文件组织成索引文件。目录表指示下一级文件名及其磁盘地址(各占2B,共4B)。下级文件是目录文件时,指示其第一个磁盘块地址。下级文件是普通文件时,指示其FCB的磁盘地址。每个目录的文件磁盘块的最后4B供拉链使用。下级文件在上级目录文件中的次序在图中为从左至右。每个磁盘块有512B,与普通文件的一页等长。

图A 某树形结构文件系统框图
根目录
├── A
│   ├── D
│   │   ├── J
│   │   └── K
│   └── E
├── B
│   ├── F
│   │   └── Q
│   ├── G
│   │   ├── L
│   │   ├── M
│   │   └── R
│   │       └── S
│   └── H
│       ├── N
│       │   └── T
│       │       └── U
│       │           ├── V
│       │           └── W
│       └── P
└── C
    └── ...

普通文件的FCB组织如图B所示。其中,每个磁盘地址占2B,前10个地址直接指示该文件前10页的地址。第11个地址指示一级索引表地址,一级索引表中的每个磁盘地址指示一个文件页地址;第12个地址指示二级索引表地址,二级索引表中的每个地址指示一个一级索引表地址;第13个地址指示三级索引表地址,三级索引表中的每个地址指示一个二级索引表地址。请问:

图B FCB组织
| 该文件的有关描述信息 |
|---|
| 1 | 磁盘地址 |
| 2 | 磁盘地址 |
| 3 | 磁盘地址 |
| : | : |
| 11 | 磁盘地址 |
| 12 | 磁盘地址 |
| 13 | 磁盘地址 |

1) 一个普通文件最多可有多少个文件页?
2) 若要读文件J中的某一页,最多启动磁盘多少次?
3) 若要读文件W中的某一页,最少启动磁盘多少次?
4) 根据3),为最大限度地减少启动磁盘的次数,可采用什么方法?此时,磁盘最多启动多少次?
**05.** 在某个文件系统中,外存为硬盘。物理块大小为512B,有文件A包含598条记录,每条记录占255B,每个物理块放2条记录。文件A所在的目录如下图所示。文件目录采用多级树形目录结构,由根目录节点、作为目录文件的中间节点和作为信息文件的树叶组成,每个目录项占127B,每个物理块放4个目录项,根目录的第一块常驻内存。试问:
1) 若文件的物理结构采用链式存储方式,链指针地址占2B,则要将文件A读入内存,至少需要存取几次硬盘?
2) 若文件为连续文件,则要读文件A的第487条记录至少要存取几次硬盘?

root
├── bin
├── dev
├── etc
├── boot
├── usr
│   ├── you
│   │   ├── file1
│   │   │   ├── A
│   │   │   └── B
│   │   └── dirl
│   │       ├── C
│   │       └── D
│   └── he
│       └── dir2
│           └── E
└── tmp
    ├── like
    ├── marg
    ├── mik
    └── za

### 4.2.8 答案与解析

**一、单项选择题**
**01. C**
实现用户对文件的按名存取,系统先利用用户提供的文件名形成检索路径,对目录进行检索。在顺序检索中,路径名的一个分量未找到,说明路径名中的某个目录或文件不存在,不需要继续检索,选项C正确。目录的查询方式有顺序检索法和散列法两种,散列法并不适用于所有的目录结构,而且有冲突和溢出的缺点,解决的开销也较大,因此通常更多采用的是顺序检索法,选项A错误。在树形目录中,为了加快文件检索速度,可设置当前目录,于是文件路径可以从当前目录开始查找,选项B错误。在顺序检索法查找完成后,得到的是文件的逻辑地址,选项D错误。
**02. A**
相对路径是从当前目录出发到所找文件通路上所有目录名和数据文件名用分隔符连接起来而形成的,注意与绝对路径的区别。
**03. C**
在文件系统中采用多级目录结构后,符合了多层次管理的需要,提高了文件查找的速度,还允许用户建立同名文件。因此,多级目录结构的采用解决了命名冲突。
**04. A**
在单级目录文件中,每当新建一个文件时,必须先检索所有的目录项,以保证新文件名在目录中是唯一的。所以单级目录结构无法解决文件重名问题。
**05. D**
文件在磁带上通常采用连续存放方法,在硬盘上通常不采用连续存放方法,在内存上采用随机存放方法,选项I错误。对文件的访问控制,常由用户访问权限和文件属性共同限制,选项II错误。在树形目录结构中,对于不同用户的文件,文件名可以不同也可以相同,选项III错误。防止文件受损常采用备份的方法,而存取控制矩阵方法用于多用户之间的存取权限保护,选项IV错误。
**06. A**
建立F2时,F1和F2的引用计数值都变为2。建立F3时,符号链接文件的引用计数值不受被链接文件的影响,始终为1,所以F1和F3的引用计数值为2和1。删除F2时,F1的引用计数值变为$2-1=1$,F3的引用计数值仍保持不变,因此F1、F3的引用计数值分别是1、1。
**07. A**
文件F2是F1的硬链接文件,指向同一个索引节点,当进程P₁第一次打开F1时,会将磁盘上的索引节点读入内存,之后进程P₁打开F2及进程P₂打开F1和F2都共享之前已读入内存的索引节点,选项A正确。P₁和P₂对同一个文件的访问权限可能有所不同,选项B错误。F3是F2的软链接文件,删除F3不影响F2的引用计数值,选项C错误。读取F1只需传入F1在进程P₁中的文件描述符(F1在进程P₁的进程打开文件表中的索引号),并不需要其路径名,选项D错误。
**08. D**
文件被打开后,系统为每个打开的文件分配一个文件描述符(索引号),用来标识该文件。之后用户不再使用文件名,而使用文件描述符,来对该文件进行读、写、定位等操作。
**09. B**
文件大小与对目录进行检索无关。目录项数量越多,检索目录时的平均比较次数就越多,影响检索性能;目录项的大小越大,占用的盘块就越多,I/O时间也就越长,影响检索性能;目录项在目录中的位置不同,可能导致不同的访问路径,影响检索性能。
**10. D**
A、B和C项的同名文件都有不同的路径名,因此不会造成文件名重名问题。在同一个磁盘的同一目录下,若两个文件名重名,则它们具有相同的路径名,因此无法区别。
**11. B**
文件目录是实现按名存取的关键,通过查找目录可以获得文件的物理地址和其他属性。
**12. C**
FCB占64B,盘块大小为1KB,一个盘块能存放$1024/64=16$个FCB。文件目录有3200个目录项,即3200个FCB(每个目录项一个FCB),文件目录需要占用$3200/16=200$个盘块。查找一个文件需要在200个盘块中顺序查找目标FCB,平均查找次数为$200/2=100$,即平均访问磁盘的次数。
**注意**
在操作系统教材中,平均查找长度通常描述为“总长度/2”;在数据结构教材中,平均查找长度通常描述为“(总长度+1)/2”,相对而言后者更严谨。
**13. B**
一个目录项的大小是16B,一个磁盘块可以存放$1024B/16B=64$个目录项,存放3200个目录项共需50个磁盘块。因此,找到一个文件对应的目录项平均读磁盘的次数为$50/2=25$,之后根据对应目录项的索引节点编号再启动磁盘读入索引节点,总读磁盘次数为26。
**14. B**
建立符号链接时,引用计数值直接设置为1,不受被链接文件的影响;建立硬链接时,引用计数值加1。删除文件时,删除操作对于符号链接是不可见的,这并不影响文件系统,当以后再通过符号链接访问时,发现文件不存在,直接删除符号链接;但对于硬链接则不可直接删除,引用计数值减1,若值不为0,则不能删除此文件,因为还有其他硬链接指向此文件。
当建立F2时,F1和F2的引用计数值都为1。当再建立F3时,F1和F3的引用计数值就都变成了2。当后来删除F1时,F3的引用计数值为$2-1=1$,F2的引用计数值不变。
**15. C**
当一个文件系统含有多级目录时,每访问一个文件,都要使用从树根开始到树叶为止、包括各中间节点名的全路径名。当前目录也称工作目录,进程对各个文件的访问都相对于当前目录进行,而不需要从根目录一层一层地检索,加快了文件的检索速度。选项A、B都与相对目录无关;选项D,文件的读/写速度取决于磁盘的性能。
**16. B**
多个进程打开一个文件时,读/写指针的位置不同,它们保存在各自的用户打开文件表中,选项I错误。硬链接是基于索引节点的共享方式,指向同一个内存索引节点,选项II正确。不同进程获得的文件描述符是各自独立的,分别指向各自的用户打开文件表中的一项,选项III正确。
**17. A**
删除一个文件时,会根据文件控制块回收相应的磁盘空间,将文件控制块回收,并删除目录中对应的目录项。选项B、C、D正确。快捷方式属于文件共享中的软连接,本质上创建的是一个链接文件,其中存放的是访问该文件的路径,删除文件并不会导致文件的快捷方式被删除,正如在Windows中删除一个程序后,其快捷方式可能仍留在桌面上,但已无法打开。

**二、综合应用题**
**01.【解答】**
文件系统共有$10 \times 10 = 100$个目录,若采用单级目录结构,目录表中有100个目录项,在检索一个文件时,平均检索的目录项数 = 目录项$/2=50$。采用两级目录结构时,主目录有10个目录项,每个子目录均有10个目录项,每级平均检索5个目录项,即检索一个文件时平均检索10个目录项,因此采用单级目录结构所需检索目录项数是采用两级目录结构的$50/10=5$倍。
**02.【解答】**
1) 可以建立链接。因为F是目录而R是文件,所以可以建立R到F的符号链接。除了符号链接,也可以通过硬链接的方式。
2) 不一定能删除R。由于R被多个目录共享,能否删除R取决于文件系统实现共享的方法。若采用基于索引节点的共享方法,则因删除后存在指针悬空问题而不能删除R节点。若采用基于符号共享的方法,则可以删除R节点。
3) 不一定能删除N。因为N的子目录中存在共享文件R,而R节点本身不一定能被删除,所以N也不一定能被删除。
**03.【解答】**
1) ①因为目录D中没有已命名为A的文件,所以在目录D中,可以建立一个名为A的文件。②因为在文件系统的根目录下已存在一个名为A的目录,所以根目录下的目录C不能改名为A。
2) ①用户G需要通过依次访问目录K和目录P,才能访问文件S和文件T。为了提高文件访问速度,可在目录G下建立两个链接文件,分别链接到文件S和文件T上。这样用户G就可直接访问这两个文件。②用户E可以修改文件I的存取控制表来对文件I加以保护,不让别的用户使用。具体实现方法是:在文件I的存取控制表中,只留下用户E的访问权限,其他用户对该文件无操作权限,从而达到不让其他用户访问的目的。
**04.【解答】**
1) 因为磁盘块大小为512B,所以索引块大小也为512B,每个磁盘地址大小为2B。因此,一个一级索引表可容纳256个磁盘地址。同样,一个二级索引表可容纳256个一级索引表地址,一个三级索引表可容纳256个二级索引表地址。这样,一个普通文件最多可有的文件页数为$10+256+256 \times 256+256 \times 256 \times 256=16843018$。
2) 由图可知,目录文件A和D中的目录项都只有两个,因此这两个目录文件都只占用一个物理块。要读文件J中的某一页,先从内存的根目录中找到目录文件A的磁盘地址,将其读入内存(已访问磁盘1次)。然后从目录A中找出目录文件D的磁盘地址读入内存(已访问磁盘2次)。再从目录D中找出文件J的FCB地址读入内存(已访问磁盘3次)。在最坏情况下,该访问页存放在三级索引下,这时候需要一级级地读三级索引块才能得到文件J的地址(已访问磁盘6次)。最后读入文件J中的相应页(共访问磁盘7次)。所以,若要读文件J中的某一页,最多启动磁盘7次。
3) 由图可知,目录文件C和U的目录项较多,可能存放在多个链接在一起的磁盘块中。在最好情况下,所需的目录项都在目录文件的第一个磁盘块中。先从内存的根目录中找到目录文件C的磁盘地址并读入内存(已访问磁盘1次)。在C中找出目录文件I的磁盘地址并读入内存(已访问磁盘2次)。在I中找出目录文件P的磁盘地址并读入内存(已访问磁盘3次)。从P中找到目录文件U的磁盘地址并读入内存(已访问磁盘4次)。从U的第一个磁盘块中找出文件W的FCB地址并读入内存(已访问磁盘5次)。在最好情况下,要访问的页在FCB的前10个直接块中,按照直接块指示的地址读文件W的相应页(已访问磁盘6次)。所以,若要读文件W中的某页,最少启动磁盘6次。
4) 为了减少启动磁盘的次数,可以将需要访问的W文件挂在根目录的最前面的目录项中。此时,只需读内存中的根目录就可找到W的FCB,将FCB读入内存(已访问磁盘1次),最差情况下,需要的W文件的那个页挂在FCB的三级索引下,因此读3个索引块需要访问磁盘3次(已访问磁盘4次)得到该页的物理地址,再去读这个页即可(已访问磁盘5次)。此时,磁盘最多启动5次。
**05.【解答】**
1) 根目录的第一块常驻内存(root 所指的/bin, /dev, /etc, /boot 等可直接获得),根目录找到文件A需要5次读盘。由$255 \times 2+2=512$可知,一个物理块在链式存储结构下可放2条记录及下一个物理块地址,而文件A共有598条记录,因此读取A的所有记录所需的读盘次数为$598/2=299$,所以将文件A读到内存至少需读盘$299+5=304$次。
2) 当文件为连续文件时,找到文件A同样需要5次读盘,且知道文件A的地址后通过计算只需一次读盘即可读出第487条记录,所以至少需要$5+1=6$次读盘。

## 4.3 文件系统

在学习本节时,请读者思考以下问题:
1) 什么是文件系统?
2) 文件系统要完成哪些功能?
本节除了“外存空闲空间管理”,其他都是2022年统考大纲的新增考点,这些内容都是比较抽象的、看不见也摸不着的原理,在常用的国内教材(如汤小丹的教材)中都鲜有涉及。若觉得不太好理解这些内容,则建议读者结合王道的最新课程进行学习。

### 4.3.1 文件系统结构

文件系统(File system)提供高效和便捷的磁盘访问,以便允许存储、定位、提取数据。文件系统有两个不同的设计问题:第一个问题是,定义文件系统的用户接口,它涉及定义文件及其属性、所允许的文件操作、如何组织文件的目录结构。第二个问题是,创建算法和数据结构,以便映射逻辑文件系统到物理外存设备。现代操作系统有多种文件系统类型,因此文件系统的层次结构也不尽相同。图4.19是一个合理的文件系统层次结构。