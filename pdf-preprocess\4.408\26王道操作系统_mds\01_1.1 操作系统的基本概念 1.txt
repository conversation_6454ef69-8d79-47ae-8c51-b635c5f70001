## 第1章 计算机系统概述

【考纲内容】
(一)操作系统的基本概念
(二)操作系统的发展历程
(三)程序运行环境
CPU运行模式：内核模式与用户模式；中断和异常的处理；系统调用；程序的链接与装入；程序运行时内存映像与地址空间①
(四)操作系统结构
分层、模块化、宏内核、微内核、外核
(五)操作系统引导
(六)虚拟机

扫一扫
视频讲解

【复习提示】
本章通常以选择题的形式考查，重点考查操作系统的功能、运行环境和提供的服务。要求读者能从宏观上把握操作系统各部分的功能，微观上掌握细微的知识点。因此，复习操作系统时，首先要形成大体框架，并通过反复复习和做题巩固知识体系，然后将操作系统的所有内容串成一个整体。本章的内容有助于读者整体上初步认识操作系统，为后面掌握各章节的知识点奠定基础，进而整体把握课程，不要因为本章的内容在历年考题中出现的比例不高而忽视它。

### 1.1 操作系统的基本概念

#### 1.1.1 操作系统的概念
在信息化时代，软件是计算机系统的灵魂，而作为软件核心的操作系统，已与现代计算机系统密不可分、融为一体。计算机系统自下而上可以大致分为4部分：硬件、操作系统、应用程序和用户(这里的划分与计算机组成原理中的分层不同)。操作系统管理各种计算机硬件，为应用程序提供基础，并且充当计算机硬件与用户之间的中介。
硬件如中央处理器、内存、输入/输出设备等，提供基本的计算资源。应用程序如字处理程序、电子制表软件、编译器、网络浏览器等，规定按何种方式使用这些资源来解决用户的计算问题。操作系统控制和协调各用户的应用程序对硬件的分配与使用。
在计算机系统的运行过程中，操作系统提供了正确使用这些资源的方法。
综上所述，操作系统(Operating System, OS)是指控制和管理整个计算机系统的硬件与软件资源，合理地组织、调度计算机的工作与资源的分配，进而为用户和其他软件提供方便接口与环境的程序集合。操作系统是计算机系统中最基本的系统软件。

①这两个考点将在第3章的3.1节中介绍。

#### 1.1.2 操作系统的功能和目标
为给多道程序提供良好的运行环境，操作系统应具有以下几方面的功能：处理机管理、存储器管理、设备管理和文件管理。为方便用户使用操作系统，还要向用户提供接口。同时，操作系统可用来扩充机器，以提供更方便的服务、更高的资源利用率。
下面用一个直观的例子来理解这种情况。例如，用户是雇主，操作系统是工人(用来操作机器)，计算机是机器(由处理机、存储器、设备、文件几个部件构成)，工人有熟练的技能，能够控制和协调各个部件的工作，这就是操作系统对资源的管理；同时，工人必须接收雇主的命令，这就是“接口”；有了工人，机器就能发挥更大的作用，因此工人就成了“扩充机器”。

**1. 操作系统作为计算机系统资源的管理者**
(1)处理机管理
在多道程序环境下，处理机的分配和运行都以进程(或线程)为基本单位，因此对处理机的管理可归结为对进程的管理。并发是指在计算机内同时运行多个进程，因此进程何时创建、何时撤销、如何管理、如何避免冲突、合理共享就是进程管理最主要的任务。进程管理的主要功能包括进程控制、进程同步、进程通信、死锁处理、处理机调度等。
(2)存储器管理
存储器管理是为了给多道程序的运行提供良好的环境，方便用户使用及提高内存的利用率，主要包括内存分配与回收、地址映射、内存保护与共享和内存扩充等功能。
(3)文件管理
计算机中的信息都是以文件的形式存在的，操作系统中负责文件管理的部分称为文件系统。文件管理包括文件存储空间的管理、目录管理及文件读/写管理和保护等。
(4)设备管理
设备管理的主要任务是完成用户的I/O请求，方便用户使用各种设备，提高设备的利用率，主要包括缓冲管理、设备分配、设备处理和虚拟设备等功能。
这些工作都由“工人”负责，“雇主”无须关注。

**2. 操作系统作为用户与计算机硬件系统之间的接口**
为让用户方便、快捷、可靠地操纵计算机硬件并运行自己的程序，操作系统还提供用户接口。操作系统提供的接口主要分为两类：一类是命令接口，用户利用这些操作命令来组织和控制作业的执行；另一类是程序接口，编程人员可用来请求操作系统服务。
(1)命令接口
使用命令接口进行作业控制的主要方式有两种，即联机控制方式和脱机控制方式。按作业控制方式的不同，可将命令接口分为联机命令接口和脱机命令接口。
联机命令接口也称交互式命令接口，适用于分时或实时系统的接口。联机命令由一组键盘操作命令组成。用户通过控制台或终端输入操作命令，向系统提出各种服务要求。用户每输入一条命令，控制权就转给操作系统的命令解释程序，然后由命令解释程序解释并执行输入的命令，进而完成指定的功能。之后，控制权转回控制台或终端，此时用户又可输入下一条命令。联机命令接口可以这样理解：“雇主”说一句话，“工人”做一件事，并做出反馈，这就强调了交互性。
脱机命令接口也称批处理命令接口，适用于批处理系统。脱机命令由一组作业控制命令组成。脱机用户不能直接干预作业的运行，而应事先用相应的作业控制命令编写一份作业操作说明书，连同作业一起提交给系统。当系统调度到该作业时，由系统中的命令解释程序逐条解释执行作业说明书上的命令，进而间接地控制作业的运行。脱机命令接口可以这样理解：“雇主”将要“工人”做的事情写在清单上，“工人”按照清单逐条完成这些事情，这就是批处理。
(2)程序接口
**命题追踪** 操作系统为应用程序提供的接口(2010)
程序接口由一组系统调用(也称广义指令)组成。用户通过在程序中使用这些系统调用来请求操作系统为其提供服务，如使用各种外部设备、申请分配和回收内存及其他各种要求。
当前最流行的是图形用户界面(GUI)，即图形接口。GUI最终是通过调用程序接口实现的，用户通过鼠标和键盘在图形界面上单击或使用快捷键，就能方便地使用操作系统。严格来说，图形接口不是操作系统的一部分，但图形接口所调用的系统调用命令是操作系统的一部分。

**3. 操作系统实现了对计算机资源的扩充**
没有任何软件支持的计算机称为裸机，它仅构成计算机系统的物质基础，而实际呈现在用户面前的计算机系统是经过若干层软件改造的计算机。裸机在最内层，外面是操作系统。操作系统所提供的资源管理功能和方便用户的各种服务功能将裸机改造成功能更强、使用更方便的机器；因此，我们通常将覆盖了软件的机器称为扩充机器或虚拟机。
“工人”操作机器，机器就有更大的作用，于是“工人”便成了“扩充机器”。
**注**意，本课程关注的是操作系统如何控制和协调处理机、存储器、设备和文件，而不关注接口和扩充机器，对于后两者，读者只需要有个印象，能够理解即可。

#### 1.1.3 操作系统的特征
操作系统是一种系统软件，但与其他系统软件和应用软件有很大的不同，它有自己的特殊性即基本特征。操作系统的基本特征包括并发、共享、虚拟和异步。这些概念对理解和掌握操作系统的核心至关重要，将一直贯穿于各个章节中。

**1. 并发(Concurrence)**
并发是指两个或多个事件在同一时间间隔内发生。在多道程序环境下，在内存中同时装有若干道程序，以便当运行某道程序时，利用其因I/O操作而暂停执行时的CPU空档时间，再调度另一道程序运行，从而实现多道程序交替运行，使CPU保持忙碌状态。

**命题追踪** 并行性的定义及分析(2009)
并行性是指系统具有同时进行运算或操作的特性，在同一时刻能完成两种或两种以上的工作。在支持多道程序的单处理机环境下，一段时间内，宏观上有多道程序在同时执行，而在每个时刻，实际仅能有一道程序执行，因此微观上这些程序仍是分时交替执行的。可见，操作系统的并发性是通过分时得以实现的。而CPU与I/O设备、I/O设备和I/O设备则能实现真正的并行。
若要实现进程的并行，则需要有相关硬件的支持，如多流水线或多处理机环境。
**注**意同一时间间隔(并发)和同一时刻(并行)的区别，下面以生活中的例子来理解这种区别。例如，若你在9:00-9:10仅吃面包，在9:10-9:20仅写字，在9:20-9:30仅吃面包，在9:30-10:00仅写字，则在9:00-10:00吃面包和写字这两种行为就是并发执行的；又如，若你在9:00-10:00右手写字，左手同时拿着面包吃，则这两个动作就是并行执行的。
在操作系统中，引入进程的目的是使程序能并发执行。

**2. 共享(Sharing)**
资源共享即共享，是指系统中的资源可供内存中多个并发执行的进程共同使用。资源共享主要可分为互斥共享和同时访问两种方式。
(1)互斥共享方式
系统中的某些资源，如打印机、磁带机，虽然可供多个进程使用，但为使得所打印或记录的结果不致造成混淆，应规定在一段时间内只允许一个进程访问该资源。
为此，当进程A访问某个资源时，必须先提出请求，若此时该资源空闲，则系统便将之分配给A使用，此后有其他进程也要访问该资源时(只要A未用完)就必须等待。仅当A访问完并释放该资源后，才允许另一个进程对该资源进行访问。我们将这种资源共享方式称为互斥共享，而将在一段时间内只允许一个进程访问的资源称为临界资源。计算机系统中的大多数物理设备及某些软件中所用的栈、变量和表格，都属于临界资源，它们都要求被互斥地共享。
(2)同时访问方式
系统中还有另一类资源，这类资源允许一段时间内由多个进程“同时”访问。这里所说的“同时”通常是宏观上的，而在微观上，这些进程可能是交替地对该资源进行访问，即“分时共享”的。可供多个进程“同时”访问的典型资源是磁盘设备，一些用重入代码编写的文件也可被“同时”共享，即允许若干用户同时访问该文件。
**注**意，互斥共享要求一种资源在一段时间内(哪怕是一段很短的时间)只能满足一个请求，否则就会出现严重的问题(你能想象打印机第一行打印文档A的内容、第二行打印文档B的内容的效果吗？)，而同时访问共享通常要求一个请求分几个时间片段间隔地完成，其效果与连续完成的效果相同。
并发和共享是操作系统两个最基本的特征，两者之间互为存在的条件：①资源共享是以程序的并发为条件的，若系统不允许程序并发执行，则自然不存在资源共享问题；②若系统不能对资源共享实施有效的管理，则必将影响到程序的并发执行，甚至根本无法并发执行。

**3. 虚拟(Virtual)**
虚拟是指将一个物理上的实体变为若干逻辑上的对应物。物理实体(前者)是实的，即实际存在的；而后者是虚的，是用户感觉上的事物。用于实现虚拟的技术称为虚拟技术。操作系统的虚拟技术可归纳为：时分复用技术，如虚拟处理器；空分复用技术，如虚拟存储器。
通过多道程序设计技术，让多道程序并发执行，来分时使用一个处理器。此时，虽然只有一个处理器，但它能同时为多个用户服务，使每个终端用户都感觉有一个CPU在专门为它服务。利用多道程序设计技术将一个物理上的CPU虚拟为多个逻辑上的CPU，称为虚拟处理器。
采用虚拟存储器技术将一台机器的物理存储器变为虚拟存储器，以便从逻辑上扩充存储器的容量。当然，这时用户所感觉到的内存容量是虚的。我们将用户感觉到(但实际不存在)的存储器称为虚拟存储器。
还可采用虚拟设备技术将一台物理I/O设备虚拟为多台逻辑上的I/O设备，并允许每个用户占用一台逻辑上的I/O设备，使原来仅允许在一段时间内由一个用户访问的设备(临界资源)变为在一段时间内允许多个用户同时访问的共享设备。

**4. 异步(Asynchronism)**
多道程序环境允许多个程序并发执行，但由于资源有限，进程的执行并不是一贯到底的，而是走走停停的，它以不可预知的速度向前推进，这就是进程的异步性。
异步性使得操作系统运行在一种随机的环境下，可能导致进程产生与时间有关的错误(就像对全局变量的访问顺序不当会导致程序出错一样)。然而，只要运行环境相同，操作系统就须保证多次运行进程后都能获得相同的结果。

### 1.1.4 本节习题精选

**一、单项选择题**
**01.** 操作系统是对( )进行管理的软件。
A. 软件
B. 硬件
C. 计算机资源
D. 应用程序
**02.** 下面的( )资源不是操作系统应该管理的。
A. CPU
B. 内存
C. 外存
D. 源程序
**03.** 下列选项中，( )不是操作系统关心的问题。
A. 管理计算机裸机
B. 设计、提供用户程序与硬件系统的界面
C. 管理计算机系统资源
D. 高级程序设计语言的编译器
**04.** 操作系统的基本功能是( )。
A. 提供功能强大的网络管理工具
B. 提供用户界面方便用户使用
C. 提供方便的可视化编辑程序
D. 控制和管理系统内的各种资源
**05.** 现代操作系统中最基本的两个特征是( )。
A. 并发和不确定
B. 并发和共享
C. 共享和虚拟
D. 虚拟和不确定
**06.** 下列关于并发性的叙述中，正确的是( )。
A. 并发性是指若干事件在同一时刻发生
B. 并发性是指若干事件在不同时刻发生
C. 并发性是指若干事件在同一时间间隔内发生
D. 并发性是指若干事件在不同时间间隔内发生
**07.** 用户可以通过( )两种方式来使用计算机。
A. 命令接口和函数
B. 命令接口和系统调用
C. 命令接口和文件管理
D. 设备管理方式和系统调用
**08.** 系统调用是由操作系统提供给用户的，它( )。
A. 直接通过键盘交互方式使用
B. 只能通过用户程序间接使用
C. 是命令接口中的命令
D. 与系统的命令一样
**09.** 操作系统提供给编程人员的接口是( )。
A. 库函数
B. 高级语言
C. 系统调用
D. 子程序
**10.** 系统调用的目的是( )。
A. 请求系统服务
B. 中止系统服务
C. 申请系统资源
D. 释放系统资源
**11.** 为了方便用户直接或间接地控制自己的作业，操作系统向用户提供了命令接口，该接口又可进一步分为( )。
A. 联机用户接口和脱机用户接口
B. 程序接口和图形接口
C. 联机用户接口和程序接口
D. 脱机用户接口和图形接口
**12.** 以下关于操作系统的叙述中，错误的是( )。
A. 操作系统是管理资源的程序
B. 操作系统是管理用户程序执行的程序
C. 操作系统是能使系统资源提高效率的程序
D. 操作系统是用来编程的程序
**13.** 【2009统考真题】单处理机系统中，可并行的是( )。
I. 进程与进程 II. 处理机与设备 III. 处理机与通道 IV. 设备与设备
A. I、II、III
B. I、II、IV
C. I、III、IV
D. II、III、IV
**14.** 【2010统考真题】下列选项中，操作系统提供给应用程序的接口是( )。
A. 系统调用
B. 中断
C. 库函数
D. 原语

**二、综合应用题**
**01.** 说明库函数与系统调用的区别和联系。

### 1.1.5 答案与解析

**一、单项选择题**
**01. C**
操作系统管理计算机的硬件和软件资源，这些资源统称为计算机资源。注意，操作系统不仅管理处理机、存储器等硬件资源，还管理文件，文件不属于硬件资源，但属于计算机资源。
**02. D**
源程序是一种计算机代码，是用程序设计语言编写的程序，经编译或解释后可形成具有一定功能的可执行文件，是直接面向程序员用户的，而不是操作系统的管理内容。本题采用排除法可轻易得到答案，但有人会问操作系统不是也管理“文件”吗？源程序也存储在文件中吧？出现这种疑问的原因是，对操作系统管理文件的理解存在偏颇。操作系统管理文件，是指操作系统关心计算机中的文件的逻辑结构、物理结构、文件内部结构、多文件之间如何组织的问题，而不是关心文件的具体内容。这就好比你是操作系统，有十个水杯让你管理，你负责的是将这些水杯放在何处比较合适，而不关心水杯中的是水还是饮料。后续章节会详细介绍文件的管理。
**03. D**
操作系统管理计算机软/硬件资源，扩充裸机以提供功能更强大的扩充机器，并充当用户与硬件交互的中介。高级程序设计语言的编译器显然不是操作系统关心的问题。编译器的实质是一段程序指令，它存储在计算机中，是上述水杯中的水。
**04. D**
操作系统是指控制和管理整个计算机系统的硬件和软件资源，合理地组织、调度计算机的工作和资源的分配，以便为用户和其他软件提供方便的接口与环境的程序集合。选项A、B、C都可理解成应用程序为用户提供的服务，是应用程序的功能，而不是操作系统的功能。
**05. B**
操作系统最基本的特征是并发和共享，两者互为存在条件。
**06. C**
并发性是指若干事件在同一时间间隔内发生，而并行性是指若干事件在同一时刻发生。
**07. B**
操作系统主要向用户提供命令接口和程序接口(系统调用)，此外还提供图形接口；当然，图形接口其实是调用了系统调用而实现的功能。
**08. B**
系统调用是操作系统为应用程序使用内核功能所提供的接口。
**09. C**
操作系统为编程人员提供的接口是程序接口，即系统调用。
**10. A**
操作系统不允许用户直接操作各种硬件资源，因此用户程序只能通过系统调用的方式来请求内核为其服务，间接地使用各种资源。
**11. A**
程序接口、图形接口与命令接口三者并没有从属关系。按命令控制方式的不同，命令接口分为联机用户接口和脱机用户接口。
**12. D**
操作系统是用来管理资源的程序，用户程序也是在操作系统的管理下完成的。配置了操作系统的机器与裸机相比，资源利用率大大提高。操作系统不能直接用来编程，选项D错误。
**13. D**
在单CPU系统中，同一时刻只能有一个进程占用CPU，因此进程之间不能并行执行。通道是独立于CPU、控制输入/输出的设备，两者可以并行。显然，处理器与设备是可以并行的。设备与设备也是可以并行的，比如显示屏与打印机是可以并行工作的。
**14. A**
操作系统接口主要有命令接口和程序接口(也称系统调用)。库函数是高级语言中提供的与系统调用对应的函数(也有些库函数与系统调用无关)，目的是隐藏“访管”指令的细节，使系统调用更为方便、抽象。但是，库函数属于用户程序而非系统调用，是系统调用的上层。

**二、综合应用题**
**01.** **【解答】**
库函数是语言或应用程序的一部分，可以运行在用户空间中。系统调用是操作系统的一部分，是内核为用户提供的程序接口，运行在内核空间中，并且许多库函数都使用系统调用来实现功能。未使用系统调用的库函数，其执行效率通常要比系统调用的高。因为使用系统调用时，需要上下文的切换及状态的转换(由用户态转向内核态)。

### 1.2 操作系统发展历程

#### 1.2.1 手工操作阶段(此阶段无操作系统)
用户在计算机上算题的所有工作都要人工干预，如程序的装入、运行、结果的输出等。随着计算机硬件的发展，人机矛盾(速度和资源利用)越来越大，必须寻求新的解决办法。
手工操作阶段有两个突出的缺点：①用户独占全机，虽然不会出现因资源已被其他用户占用而等待的现象，但资源利用率低。②CPU等待手工操作，CPU的利用不充分。
唯一的解决办法就是用高速的机器代替相对较慢的手工操作来对作业进行控制。

#### 1.2.2 批处理阶段(操作系统开始出现)
为了解决人机矛盾及CPU和I/O设备之间速度不匹配的矛盾，出现了批处理系统。按发展历程又分为单道批处理系统、多道批处理系统(多道程序设计技术出现以后)。