③若在处理C的最后一个编码位时创建了新结点,则继续验证下一个编码。
若所有编码均通过验证,则编码具有前缀特性。

### 归纳总结

本章的内容较多,其中二叉树是极其重要的考查点。关于二叉树的有关操作,在 2014 年的统考中首次出现了线性表以外的算法设计题,需要引起读者的注意。
遍历是二叉树的各种操作的基础,统考时会考查遍历过程中对结点的各种其他操作,而且容易结合递归算法和利用栈或队列的非递归算法。读者需重点掌握各种遍历方法的代码书写,并学会在遍历的基础上,进行一些其他的相关操作。其中递归算法短小精悍,出现的概率较大,请读者不要掉以轻心,要做到对几种遍历方式的程序模板烂熟于心,并结合一定数量的习题,才可以在考试中快速地写出漂亮的代码。
二叉树遍历算法的递归程序:
```c
void Track(BiTree *p){
    if(p!=NULL){
        //(1)
        Track(p->lchild);
        //(2)
        Track(p->rchild);
        //(3)
    }
}
```
访问函数 visit()位于(1)、(2)、(3)的位置,分别对应于先序、中序、后序遍历。但对于具体题目来说,设计算法时要灵活应用。请读者认真练习下面的例题。
**例题**：设二叉树的存储结构为二叉链表,编写有关二叉树的递归算法。
1) 统计二叉树中度为1的结点个数。
2) 统计二叉树中度为2的结点个数。
3) 统计二叉树中度为0的结点个数。
4) 统计二叉树的高度。
5) 统计二叉树的宽度。
6) 从二叉树中删去所有叶结点。
7) 计算指定结点*p所在的层次。
8) 计算二叉树中各结点中的最大元素的值。
9) 交换二叉树中每个结点的两个子女。
10) 以先序次序输出一棵二叉树中所有结点的数据值及结点所在的层次。

### 思维拓展

输入一个整数 data 和一棵二元树。从树的根结点开始往下访问一直到叶结点,所经过的所有结点形成一条路径。打印出路径及与 data 相等的所有路径。例如,输入整数 22 和下图所示的二元树,则打印出两条路径 10,12 和 10,5,7。
```
      10
     /  \
    5    12
   / \
  4   7
```
**注意**
使用数组或栈保存访问的路径,并记录当前路径上所有元素的和sum。若当前结点为叶结点,且当前结点值与 sum 的和等于 data,则满足条件,打印当前路径。然后递归返回到父结点,注意在递归返回之前要先减去当前结点元素的值。使用前序遍历操作的递归算法模板可以简化程序。

## 第六章 图

**【考纲内容】**
(一) 图的基本概念
(二) 图的存储及基本操作
邻接矩阵;邻接表;邻接多重表;十字链表
(三) 图的遍历
深度优先搜索;广度优先搜索
(四) 图的基本应用
最小(代价)生成树;最短路径;拓扑排序;关键路径

**【知识框架】**
*   图
    *   图的定义
    *   图结构的存储
        *   邻接矩阵法、邻接表法
        *   邻接多重表、十字链表
    *   图的遍历
        *   深度优先遍历
        *   广度优先遍历
    *   图的相关应用
        *   最小生成树: Prim算法、Kruskal 算法
        *   最短路径: Dijkstra算法、Floyd 算法
        *   拓扑排序: AOV 网
        *   关键路径: AOE 网

**【复习提示】**
图算法的难度较大,主要掌握深度优先搜索与广度优先搜索。掌握图的基本概念及基本性质、图的存储结构(邻接矩阵、邻接表、邻接多重表和十字链表)及特性、存储结构之间的转化、基于存储结构上的各种遍历操作和各种应用(拓扑排序、最小生成树、最短路径和关键路径)等。图的相关算法较多,通常只需掌握其基本思想和实现步骤,而实现代码不是重点。

### 6.1 图的基本概念

#### 6.1.1 图的定义

**定义** 图$G$由顶点集$V$和边集$E$组成,记为$G=(V, E)$,其中$V(G)$表示图$G$中顶点的有限非空集;$E(G)$表示图$G$中顶点之间的关系(边)集合。若$V=\{v_1, v_2, ..., v_n\}$,则用$|V|$表示图$G$中顶点的个数,$E=\{(u, v)|u \in V, v \in V\}$,用$|E|$表示图$G$中边的条数。