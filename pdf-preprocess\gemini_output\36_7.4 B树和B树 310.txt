均小于根结点值。为了从大到小输出,先遍历右子树,再访问根结点,后遍历左子树。算法如下:
```c
void OutPut(BSTNode *bt, KeyType k)
{ //本算法从大到小输出二叉排序树中所有值不小于k的关键字
    if(bt==NULL)
        return;
    if(bt->rchild!=NULL)
        OutPut(bt->rchild, k); //递归输出右子树结点
    if(bt->data>=k)
        printf("%d", bt->data); //只输出大于或等于k的结点值
    if(bt->lchild!=NULL)
        OutPut(bt->lchild, k); //递归输出左子树的结点
}
```
本题也可采用中序遍历加辅助栈的方法实现。

**10.【解答】**
设二叉排序树的根结点为*t,根据结点存储的信息,有以下几种情况:
当t->lchild为空时,情况如下:
1) 若t->rchild 非空且k==1,则*t即第k小的元素,查找成功。
2) 若t->rchild 非空且k!=1,则第k小的元素必在*t的右子树。
当t->lchild非空时,情况如下:
1) t->lchild->count==k-1, *t即第k小的元素,查找成功
2) t->lchild->count>k-1,第k小的元素必在*t的左子树,继续到*t的左子树中查找。
3) t->lchild->count<k-1,第k小的元素必在右子树,继续搜索右子树,寻找第k-(t->lchild->count+1)小的元素。
对左右子树的搜索采用相同的规则,递归实现的算法描述如下:
```c
BSTNode *Search_Small(BSTNode*t, int k) {
//在以t为根的子树上寻找第k小的元素,返回其所在结点的指针。k从1开始计算
//在树结点中增加一个count数据成员,存储以该结点为根的子树的结点个数
    if(k<1 || k>t->count) return NULL;
    if (t->lchild==NULL) {
        if (k==1) return t;
        else return Search_Small(t->rchild, k-1);
    }
    else{
        if (t->lchild->count==k-1) return t;
        if (t->lchild->count>k-1) return Search_Small(t->lchild,k);
        if (t->lchild->count<k-1)
            return Search_Small(t->rchild, k-(t->lchild->count+1));
    }
}
```
最大查找长度取决于树的高度。由于二叉排序树是随机生成的,其高度应是$O(\log_2 n)$,算法的时间复杂度为$O(\log_2 n)$。

### 7.4 B树①和B+树
考研大纲对B树和B+树的要求各不相同,重点在于考查B树,不仅要求理解B树的基本特点,还要求掌握B树的建立、插入和删除操作,而对B+树则只考查基本概念。
①也可写成“B-树”,注意这里的“-”是连接词,不能读作“减”。

### 7.4.1 B树及其基本操作
所谓m阶B树是所有结点的平衡因子均等于0的m路平衡查找树。

**命题追踪 B树的定义和特点(2009)**
一棵m阶B树或为空树,或为满足如下特性的m叉树:
1) 树中每个结点至多有m棵子树,即至多有m-1个关键字。
2) 若根结点不是叶结点,则至少有2棵子树,即至少有1个关键字。
3) 除根结点外的所有非叶结点至少有$\lceil m/2 \rceil$棵子树,即至少有$\lceil m/2 \rceil-1$个关键字。
4) 所有非叶结点的结构如下:

| n | $P_0$ | $K_1$ | $P_1$ | $K_2$ | $P_2$ | ... | $K_n$ | $P_n$ |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |

其中，$K_i(i=1,2,...,n)$为结点的关键字,且满足$K_1<K_2<...<K_n$; $P_i(i=0,1,...,n)$为指向子树根结点的指针,且指针$P_0$所指子树中所有结点的关键字均小于$K_1$,$P_n$所指子树中所有结点的关键字均大于$K_n$; $n(\lceil m/2 \rceil-1 \le n \le m-1)$为结点中关键字的个数。
5) 所有的叶结点①都出现在同一层次上,并且不带信息(可以视为外部结点或类似于折半查找判定树的失败结点,实际上这些结点并不存在,指向这些结点的指针为空)。

**命题追踪 B树中关键字数和结点数的分析(2013、2014、2018、2021)**
图7.28所示为一棵5阶B树,可以借助该实例来分析上述性质:
```
                      [ 22 ]
                     /      \
             [ 5  11 ]         [ 36  45 ]
            /    |   \         /    |   \
        [1 3] [6 8 9] [13 15] [30 35] [40 42] [47 48 50 56]
```
> 图7.28 一棵5阶B树的实例
> 内部结点
> 外部结点 (叶结点)

1) 结点的孩子个数等于该结点中关键字个数加1。
2) 若根结点没有关键字就没有子树,则此时B树为空;若根结点有关键字,则其子树个数必然大于或等于2,因为子树个数等于关键字个数加1。
3) 除根结点外的所有非叶结点至少有$\lceil m/2 \rceil = \lceil 5/2 \rceil = 3$棵子树(至少有$\lceil m/2 \rceil-1 = \lceil 5/2 \rceil-1 = 2$个关键字);至多有5棵子树(至多有4个关键字)。
4) 结点中的关键字从左到右递增有序,关键字两侧均有指向子树的指针,左侧指针所指子树的所有关键字均小于该关键字,右侧指针所指子树的所有关键字均大于该关键字。或者看成下层结点的关键字总是落在由上层结点的关键字所划分的区间内,如第二层最左结点的关键字划分成了3个区间:$(-\infty, 5), (5, 11), (11, +\infty)$,该结点中的3个指针所指子树的关键字均分别落在这3个区间内。
5) 所有叶结点均在第4层,代表查找失败的位置。

**1. B树的查找**
在B树上进行查找与二叉排序树很相似,只是每个结点都是多个关键字的有序表,在每个结点上所做的不是两路分支决定,而是根据该结点的子树所做的多路分支决定。
B树的查找包含两个基本操作:①在B树中找结点;②在结点内找关键字。B树常存储在磁盘上,因此前一查找操作是在磁盘上进行的,而后一查找操作是在内存中进行的,即在磁盘上找到目标结点后,先将结点信息读入内存,然后再采用顺序查找法或折半查找法。因此,在磁盘上进行查找的次数即目标结点在B树上的层次数,决定了B树的查找效率。
在B树上查找到某个结点后,先在有序表中进行查找,若找到则查找成功,否则按照对应的指针信息到所指的子树中去查找(例如,在图7.28中查找关键字42,首先从根结点开始,根结点只有一个关键字,且42>22,若存在,必在关键字22的右边子树上,右孩子结点有两个关键字,而36<42<45,则若存在,必在36和45中间的子树上,在该子树结点中查到关键字42,查找成功)。查找到叶结点时(对应指针为空),则说明树中没有对应的关键字,查找失败。

**2. B树的高度(磁盘存取次数)**
由上一节得知,B树中的大部分操作所需的磁盘存取次数与B树的高度成正比。
下面来分析B树在不同情况下的高度。当然,首先应该明确B树的高度不包括最后的不带任何信息的叶结点所处的那一层(有些书对B树的高度的定义中,包含最后的那一层)。
若$n \ge 1$,则对任意一棵包含n个关键字、高度为h、阶数为m的B树:
1) 若让每个结点中的关键字个数达到最多,则容纳同样多关键字的B树的高度达到最小。因为B树中每个结点最多有m棵子树,m-1个关键字,所以在一棵高度为h的m阶B树中关键字的个数应满足$n \le (m-1)(1+m+m^2+...+m^{h-1}) = m^h-1$,因此有
$$
h \ge \log_m(n+1)
$$
2) 若让每个结点中的关键字个数达到最少,则容纳同样多关键字的B树的高度达到最大。第一层至少有1个结点;第二层至少有2个结点;除根结点外的每个非叶结点至少有$\lceil m/2 \rceil$棵子树,则第三层至少有$2\lceil m/2 \rceil$个结点...第h+1层至少有$2(\lceil m/2 \rceil)^{h-1}$个结点,注意到第h+1层是不包含任何信息的叶结点。对于关键字个数为n的B树,叶结点即查找不成功的结点为n+1,由此有$n+1 \ge 2(\lceil m/2 \rceil)^{h-1}$,即$h \le \log_{\lceil m/2 \rceil}((n+1)/2)+1$。
**例**如,假设一棵3阶B树共有8个关键字,则其高度范围为$2 \le h \le 3.17$,取整数。

**3. B树的插入**

**命题追踪 通过插入操作构造一棵初始为空的B树(2020)**
与二叉排序树的插入操作相比,B树的插入操作要复杂得多。在B树中查找到插入的位置后,并不能简单地将其添加到终端结点(最底层的非叶结点)中,因为此时可能会导致整棵树不再满足B树定义中的要求。将关键字 key 插入B树的过程如下:
1) **定位**。利用前述的B树查找算法,找出插入该关键字的终端结点(在B树中查找 key时,会找到表示查找失败的叶结点,因此插入位置一定是最底层的非叶结点)。
2) **插入**。每个非根结点的关键字个数都在$[\lceil m/2 \rceil-1, m-1]$。若结点插入后的关键字个数小于m,可以直接插入;若结点插入后的关键字个数大于m-1,必须对结点进行分裂。
分裂的方法是:取一个新结点,在插入 key 后的原结点,从中间位置($\lceil m/2 \rceil$)将其中的关键字分为两部分,左部分包含的关键字放在原结点中,右部分包含的关键字放到新结点中,中间位置($\lceil m/2 \rceil$)的结点插入原结点的父结点。若此时导致其父结点的关键字个数也超过了上限,则继续进行这种分裂操作,直至这个过程传到根结点为止,进而导致B树高度增1。
对于m=3的B树,所有结点中最多有m-1=2个关键字,若某结点中已有两个关键字,则结点已满,如图7.29(a)所示。插入一个关键字60后,结点内的关键字个数超过了m-1,如图7.29(b)所示,此时必须进行结点分裂,分裂的结果如图7.29(c)所示。

```
       [ 30 ]                  [ 30 ]                [ 30  52 ]
         |                       |                     /    \
    [ 20 50 52 ]  ->  [ 20 50 52 60 ]  ->    [ 20  50 ]   [ 60 ]
     (a) 插入前         (b) 插入后，结点溢出          (c) 结点分裂
```
> 图7.29 结点的“分裂”示意

**4. B树的删除**
B树的删除操作与插入操作类似,但要稍微复杂一些,即要使得删除后的结点中的关键字个数$\ge \lceil m/2 \rceil-1$,因此将涉及结点的“合并”问题。

**命题追踪 B树的删除操作的实例(2012、2022)**
当被删关键字k不在终端结点中时,可以用k的前驱(或后继)k',即k的左侧子树中“最右下”的元素(或右侧子树中“最左下”的元素),来替代k,然后在相应的结点中删除k',关键字k'必定落在某个终端结点中,则转换成了被删关键字在终端结点中的情形。在图7.30的4阶B树中,删除关键字80,用其前驱78替代,然后在终端结点中删除 78。

```
      [ 60  80 ]                   [ 60  78 ]
        /   |   \         删除80         /   |   \
      ... [68 78] ...       ==>        ... [ 68 ] ...
```
> 图7.30 B树中删除非终端结点关键字的取代

因此只需讨论被删关键字在终端结点中的情形,有下列三种情况:
1) **直接删除关键字**。若被删关键字所在结点删除前的关键字个数$\ge \lceil m/2 \rceil$,表明删除该关键字后仍满足B树的定义,则直接删去该关键字。
2) **兄弟够借**。若被删关键字所在结点删除前的关键字个数$=\lceil m/2 \rceil-1$,且与该结点相邻的右(或左)兄弟结点的关键字个数$\ge \lceil m/2 \rceil$,则需要调整该结点、右(或左)兄弟结点及其双亲结点(父子换位法),以达到新的平衡。在图7.31(a)中删除4阶B树的关键字65,右兄弟关键字个数$\ge \lceil m/2 \rceil=2$,将71取代原65的位置,将74调整到71的位置。

```
       [ 60  71 ]                   [ 60  74 ]
         /   |   \         删除65         /   |   \
       ... [ 65 ] [74 86]       ==>       ... [ 71 ] [ 86 ]
(a) 兄弟够借

       [ 60  71 ]                     [ 71 ]
         /   |   \          删除5         /    \
       [ 5 ] [65] [74 86]       ==>     [60 65] [74 86]
(b) 兄弟不够借
```
> 图7.31 4阶B树中删除终端结点关键字的示意图

3) **兄弟不够借**。若被删关键字所在结点删除前的关键字个数$=\lceil m/2 \rceil-1$,且此时与该结点相邻的左、右兄弟结点的关键字个数都$=\lceil m/2 \rceil-1$,则将关键字删除后与左(或右)兄弟结点及双亲结点中的关键字进行合并。在图7.31(b)中删除4阶B树的关键字5,它及其右兄弟结点的关键字个数$=\lceil m/2 \rceil-1=1$,所以在5删除后将60合并到65结点中。

**命题追踪 非空B树的查找、插入、删除操作的特点(2023)**
在合并过程中,双亲结点中的关键字个数会减1。若其双亲结点是根结点且关键字个数减少至0(根结点关键字个数为1时,有2棵子树),则直接将根结点删除,合并后的新结点成为根;若双亲结点不是根结点,且关键字个数减少到$\lceil m/2 \rceil-2$,则又要与它自己的兄弟结点进行调整或合并操作,并重复上述步骤,直至符合B树的要求为止。

### 7.4.2 B+树的基本概念

**命题追踪 B+树的应用场合(2017)**
B+树是应数据库所需而出现的一种B树的变形树。
一棵m阶B+树应满足下列条件:
1) 每个分支结点最多有m棵子树(孩子结点)。
2) 非叶根结点至少有两棵子树,其他每个分支结点至少有$\lceil m/2 \rceil$棵子树。
3) 结点的子树个数与关键字个数相等。
4) 所有叶结点包含全部关键字及指向相应记录的指针,叶结点中将关键字按大小顺序排列,并且相邻叶结点按大小顺序相互链接起来(支持顺序查找)。
5) 所有分支结点(可视为索引的索引)中仅包含它的各个子结点(下一级的索引块)中关键字的最大值及指向其子结点的指针。

**命题追踪 B树和B+树的差异的分析(2016)**
m阶B+树与m阶B树的主要差异如下:
1) 在B+树中,具有n个关键字的结点只含有n棵子树,即每个关键字对应一棵子树;而在B树中,具有n个关键字的结点含有n+1棵子树。
2) 在B+树中,每个结点(非根内部结点)的关键字个数n的范围是$\lceil m/2 \rceil \le n \le m$(非叶根结点:$2 \le n \le m$);而在B树中,每个结点(非根内部结点)的关键字个数n的范围是$\lceil m/2 \rceil-1 \le n \le m-1$(根结点:$1 \le n \le m-1$)。
3) 在B+树中,叶结点包含了全部关键字,非叶结点中出现的关键字也会出现在叶结点中;而在B树中,最外层的终端结点包含的关键字和其他结点包含的关键字是不重复的。
4) 在B+树中,叶结点包含信息,所有非叶结点仅起索引作用,非叶结点的每个索引项只含有对应子树的最大关键字和指向该子树的指针,不含有对应记录的存储地址。这样能使一个磁盘块存储更多的关键字,使得磁盘读/写次数更少,查找速度更快。
5) 在B+树中,用一个指针指向关键字最小的叶结点,将所有叶结点串成一个线性链表。
图7.32 所示为一棵4阶B+树。可以看出,分支结点的关键字是其子树中最大关键字的副本。通常在B+树中有两个头指针:一个指向根结点,另一个指向关键字最小的叶结点。因此,可以对B+树进行两种查找运算:一种是从最小关键字开始的顺序查找,另一种是从根结点开始的多路查找。

```
                      [ 60   85 ]
                     /           \
         [ 10 20 50 60 ]           [ 77   85 ]
        /      |     |     \          /       \
[->8->10]->[16->20]->[40->50]->[55->60]->[69->77]->[80->85]
(记录指针) (记录指针) (记录指针) (记录指针) (记录指针) (记录指针)
```
> 图7.32 B+树结构示意图

B+树的查找、插入和删除操作和B树的基本类似。只是在查找过程中,非叶结点上的关键字值等于给定值时并不终止,而是继续向下查找,直到叶结点上的该关键字为止。因此,在B+树中查找成功或失败(≤最大关键字)时,每次查找都是一条从根结点到叶结点的路径。

### 7.4.3 本节试题精选
**一、单项选择题**
**01.** 下图所示是一棵()。
```
        [ 12  25 ]
       /    |     \
 [ 05 10 ] [ 15 19 22 ] [ 26 28 ]
```
A. 4阶B树
B. 3阶B树
C. 4阶B+树
D. 无法确定

**02.** 下列关于m阶B树的说法中,错误的是( )。
A. 根结点至多有m棵子树
B. 所有叶结点都在同一层次上
C. 非叶结点至少有m/2(m为偶数)或(m+1)/2(m为奇数)棵子树
D. 根结点中的数据是有序的

**03.** 下列关于高度为3的3阶B树的说法中,正确的是( )。
I. 每个结点至少有两棵非空子树
II. 树中每个结点至多有2个关键字
III. 树中最多能存储26个关键字
IV. 插入一个元素引起B树结点分裂后,树的高度变为4
A. I、II
B. II、III
C. III、IV
D. I、II、IV

**04.** 在一棵m阶B树中做插入操作前,若一个结点中的关键字个数等于( ),则插入操作后必须分裂成两个结点;在一棵m阶B树中做删除操作前,若一个结点中的关键字个数等于( ),则删除操作后可能需要同它的左兄弟或右兄弟结点合并成一个结点。
A. m, $\lceil m/2 \rceil-2$
B. m-1, $\lceil m/2 \rceil-1$
C. m+1, $\lceil m/2 \rceil$
D. m/2, $\lceil m/2 \rceil+1$

**05.** 具有n个关键字的m阶B树,应有( )个叶结点。
A. n+1
B. n-1
C. mn
D. nm/2

**06.** 高度为5的3阶B树至少有( )个结点,至多有( )个结点。
A. 32
B. 31
C. 120
D. 121

**07.** 含有n个非叶结点的m阶B树中至少包含( )个关键字。
A. n(m+1)
B. n
C. $n(\lceil m/2 \rceil-1)$
D. $(n-1)(\lceil m/2 \rceil-1)+1$

**08.** 已知一棵5阶B树中共有53个关键字,则树的最大高度为( ),最小高度为( )。
A. 2
B. 3
C. 4
D. 5

**09.** 已知一棵3阶B树中共有2047个关键字,则树的最大高度为( ),最小高度为( )。
A. 11
B. 10
C. 8
D. 7

**10.** 在7阶B树中搜索第2016个关键字,若根结点已读入内存,则最多需启动()次I/O。
A. 4
B. 5
C. 6
D. 7

**11.** 在一棵高度为h的B树中插入一个新关键字,假设在插入过程中读入的结点一直在内存中,根结点的高度为1,且初始时未读入内存,则下列叙述中错误的是( )。(注意,本题中的新结点是指新产生的结点,如一次分裂才产生一个新结点。)
A. 若插入操作导致树的高度变为h+1,则本次插入一定导致了根结点的分裂
B. 若插入操作导致旧结点的分裂,则树的高度一定会变为h+1
C. 由于本次插入操作而产生的新结点的个数最多为h+1
D. 由于本次插入操作而产生的读/写磁盘的次数最多为3h+1

**12.** 下列关于B树和B+树的叙述中,错误的是( )。
A. B树和B+树都能有效地支持顺序查找
B. B树和B+树都能有效地支持随机查找
C. B树和B+树都是平衡的多叉树
D. B树和B+树都可以用于文件索引结构

**13.** 下列关于B树和B+树的查找操作的叙述中,错误的是( )。
A. B树查找成功时,不一定需要查找到最后一层的内部结点
B. B树查找失败时,一定需要查找到叶结点
C. B+树查找成功时,不一定需要查找到叶结点
D. B+树查找成功时,每次查找的长度都相等

**14.【2009统考真题】** 下列叙述中,不符合m阶B树定义要求的是( )。
A. 根结点至多有m棵子树
B. 所有叶结点都在同一层上
C. 各结点内关键字均升序或降序排列
D. 叶结点之间通过指针链接

**15.【2012统考真题】** 已知一棵3阶B树,如下图所示。删除关键字78得到一棵新B树,其最右叶结点中的关键字是( )。
```
                      [ 45 ]
                    /        \
            [ 17  35 ]          [ 55  65 ]
           /    |   \            /    |    \
        [ 10 ] [ 21 ] [ 37 ]      [ 47 ] [ 60 62 78 ]
```
A. 60
B. 60, 62
C. 62, 65
D. 65

**16.【2013统考真题】** 在一棵高度为2的5阶B树中,所含关键字的个数至少是( )。
A. 5
B. 7
C. 8
D. 14

**17.【2014统考真题】** 在一棵有15个关键字的4阶B树中,含关键字的结点个数最多是( )。
A. 5
B. 6
C. 10
D. 15

**18.【2016统考真题】** B+树不同于B树的特点之一是( )。
A. 能支持顺序查找
B. 结点中含有关键字
C. 根结点至少有两个分支
D. 所有叶结点都在同一层上

**19.【2017统考真题】** 下列应用中,适合使用B+树的是( )。
A. 编译器中的词法分析
B. 关系数据库系统中的索引
C. 网络中的路由表快速查找
D. 操作系统的磁盘空闲块管理

**20.【2018统考真题】** 高度为5的3阶B树含有的关键字个数至少是( )。
A. 15
B. 31
C. 62
D. 242

**21.【2020统考真题】** 依次将关键字5, 6, 9, 13, 8, 2, 12, 15插入初始为空的4阶B树后,根结点中包含的关键字是( )。
A. 8
B. 6, 9
C. 8, 13
D. 9, 12

**22.【2021统考真题】** 在一棵高度为3的3阶B树中,根为第1层,若第2层中有4个关键字,则该树的结点数最多是( )。
A. 11
B. 10
C. 9
D. 8

**23.【2022统考真题】** 在下图所示的5阶B树T中,删除关键字260之后需要进行必要的调整,得到新的B树T₁。下列选项中,不可能是T₁根结点中关键字序列的是( )。
```
                          [ 60  90  260  350 ]
                         /    /     |    \     \
                   [30 50] [70 80 85] [100 110] [280 300] [400 500]
```
A. 60, 90, 280
B. 60, 90, 350
C. 60, 85, 110, 350
D. 60, 90, 110, 350

**24.【2023统考真题】** 下列关于非空B树的叙述中,正确的是( )。
I. 插入操作可能增加树的高度
II. 删除操作一定会导致叶结点的变化
III. 查找某关键字总是要查找到叶结点
IV. 插入的新关键字最终位于叶结点中
A. 仅Ⅰ
B. 仅I、II
C. 仅III、IV
D. 仅I、II、IV

**二、综合应用题**
**01.** 给定一组关键字{20,30,50, 52, 60, 68, 70},给出创建一棵3阶B树的过程。
**02.** 对如下图所示的3阶B树,依次执行下列操作,画出各步操作的结果。
1) 插入90 2) 插入25 3) 插入45 4) 删除60 5) 删除80
```
                [ 50 ]
               /      \
            [ 30 ]      [ 80 ]
           /      \    /      \
        [ 8 20 ] [ 35 40 ] [ 60 ] [ 100 ]
```
**03.** 利用B树做文件索引时,若假设磁盘页块的大小是4000B(实际应是2的次幂,此处是为了计算方便),指示磁盘地址的指针需要5B。现有20000000个记录构成的文件,每个记录为200B,其中包括关键字5B。
试问在这个采用B树作索引的文件中,B树的阶数应为多少?假定文件数据部分未按关键字有序排列,则索引部分需要占用多少磁盘页块?

### 7.4.4 答案与解析
**一、单项选择题**
**01. D**
关键字数目比子树数目少1,首先可排除B+树。对于4阶B树,根结点至少有2棵子树(关键字数至少为1),其他非叶结点至少有$\lceil n/2 \rceil=2$棵子树(关键字数至少为1)、至多有4棵子树(关键字数至多为3)。5阶B树和6阶B树的分析也类似。题目所示的B树,同时满足4阶B树、5阶B树和6阶B树的要求,因此不能确定是哪种类型的B树。
**02. C**
除根结点外的所有非叶结点至少有$\lceil m/2 \rceil$棵子树。对于根结点,最多有m棵子树,若其不是叶结点,则至少有2棵子树。
**03. B**
每个非根的内部结点必须至少有$\lceil m/2 \rceil$棵子树,而本题中的根结点不是叶结点,也至少要有两棵子树,选项Ⅰ正确。每个结点至多有m-1=2个关键字,选项Ⅱ正确。在高度为h的m阶B树中,关键字个数至多为$(m-1)(1+m+m^2+...+m^{h-1}) = m^h-1$,代入m=3,h=3,即树中最多能存储26个关键字,选项Ⅲ正确。对于选项IV,插入一个元素引起B树结点分裂后,只要从根结点到该元素插入位置的路径上至少有一个结点未满,B树就不会长高,如图1所示;只有当结点的分裂传到根结点,并使根结点也分裂时,才会导致树高增1,如图2所示,选项IV错误。
```
      [ 30 ]                  [ 30 ]                [ 30  52 ]
        |         插入60后,溢出      |         结点分裂        /     \
   [ 20 50 52 ]      ==>     [ 20 50 52 60 ]     ==>      [ 20  50 ] [ 60 ]
```
> 图1 结点分裂不导致树高增1(3阶B树)

```
       [ 30 52 ]              [ 30 52 ]                 [ 52 ]
         /    \      插入70       /    \        结点分裂        /    \
 [ 20 50 60 68 ] ==> [ 20 50 60 68 70 ] ==> [ 30 52 68 ] ==> ...
 ...再分裂
       [ 30 ]  [ 68 ]
      /    \  /    \
 [ 20 50 ] [ 60 70 ]
```
> 图2 结点分裂导致树高增1(3阶B树)

**04. B**
因为B树每个结点内的关键字个数最多为m-1,所以当关键字个数大于m-1时,则应该分裂。每个结点内的关键字个数至少为$\lceil m/2 \rceil-1$个,所以当关键字个数少于$\lceil m/2 \rceil-1$时,则可能与其他结点合并(除非只有根结点)。若将本题题干改为B+树,请读者思考上述问题的解答。
**05. A**
B树的叶结点对应查找失败的情况,对有n个关键字的查找集合进行查找,失败可能性有n+1种。
**06. B、D**
由m阶B树的性质可知,根结点至少有2棵子树;根结点外的所有非终端结点至少有$\lceil m/2 \rceil$棵子树,结点数最少时,3阶B树形状至少类似于一棵满二叉树,即高度为5的B树至少有$2^5-1=31$个结点。因为每个结点最多有m棵子树,所以当结点数最多时,3阶B树形状类似于满三叉树,结点数为$(3^5-1)/2=121$(注意,这里求的是结点数而非关键字数,若求的是关键字数,则还应把每个结点中关键字数的上下界确定出来)。
**07. D**
除根结点外,m阶B树中的每个非叶结点至少有$\lceil m/2 \rceil-1$个关键字,根结点至少有一个关键字,所以总共包含的关键字最少个数=$(n-1)(\lceil m/2 \rceil-1)+1$。
> **注意**
> 由以上题目可知B树和B+树的定义与性质尤为重要,需要熟练掌握。

**08. C、B**
5阶B树中共有53个关键字,由最大高度公式$H \le \log_{\lceil m/2 \rceil}((n+1)/2)+1$得最大高度$H \le \log_3[(53+1)/2]+1=4$,即最大高度为4;由最小高度公式$h \ge \log_m(n+1)$得最小高度$h \ge \log_5 54 \approx 2.5$,从而最小高度为3。
**09. A、D**
利用前面的公式即最小高度$h \ge \log_m(n+1)$和最大高度$H \le \log_{\lceil m/2 \rceil}[(n+1)/2]+1$,易算出最大高度$H \le \log_2[(2047+1)/2]+1 = 11$,最小高度$h \ge \log_3 2048 \approx 6.9$,从而最小高度取7(注意,有些辅导书针对本题算出的高度要比这里给出的答案多1,因为它们在对B树的高度定义中,把最底层不包含任何关键字的叶结点也算进去了)。
**10. B**
本题要计算的是最坏情况下第2016个关键字在7阶B树中的最大深度,即考查B树结点中关键字数最少的情况。按照B树的定义,根结点至少有1个关键字,第二层至少有2个结点,即2×3个关键字;第三层至少有$2 \times 4$个结点,即$2 \times 4 \times 3$个关键字;第四层至少有$2 \times 4^2$个结点,即$2 \times 4^2 \times 3$个关键字;以此类推,第h层至少有$2 \times 4^{h-2}$个结点,即$2 \times 4^{h-2} \times 3$个关键字,故前h层的关键字总数$n = 1+2 \times 3 \times(1+4+...+4^{h-2}) = 1+2 \times (4^{h-1}-1)$。故当h=5时,$n=511$;当h=6时,$n=2047$,说明5层高的7阶B树最少有511个关键字,而6层高的7阶B树最少有2047个关键字,故最坏情况下第2016个关键字在7阶B树的第6层,需启动5次I/O操作。
**11. B**
考虑最坏情况,在待插入结点中插入一个关键码后,导致结点分裂,在该层分裂成2个结点,因此需要2次写磁盘操作;分裂操作逐层向上传导,导致每层都有结点分裂,因为结点分裂而导致的写磁盘操作共有2h次,加上最后一次结点分裂形成新根结点也需要1次写磁盘操作,写磁盘的总次数为2h+1,即由于本次插入而产生的新结点个数为h+1,加上寻找插入位置而引起的h次的读磁盘操作,整个过程中读/写磁盘的总次数为3h+1次,选项C、D正确。若插入操作导致了B树的高度增加,则分裂操作一定是从最底层传导至根结点的,即前面分析的最坏情况,选项A正确。若分裂操作没有传导至根结点,则B树的高度不变,选项B错误。
**12. A**
B树和B+树的差异主要体现在:①结点关键字和子树的个数;②B+树非叶结点仅起索引作用;③B树叶结点关键字和其他结点包含的关键字是不重复的;④B+树支持顺序查找和随机查找,而B树仅支持随机查找。B+树的所有叶结点中包含了全部的关键字信息,且叶结点本身依关键字从小到大顺序链接,因此可以进行顺序查找,而B树不支持顺序查找。B树和B+树都可用于文件索引结构,但B+树更适合做数据库索引和文件索引,因为它的磁盘读/写代价更低。
**13. C**
在B树的查找操作中,若目标元素在某个内部结点中,则查找结束,不需要进入叶结点;查找失败时,叶结点是所有路径的终点,选项A、B正确。B+树中仅叶结点包含信息,非叶结点仅起到索引作用,查找成功时一定会找到相应的叶结点,所经过的路径长度都相等,选项C错误,选项D正确。
**14. D**
m阶B树不要求将各叶结点之间用指针链接。选项D描述的实际上是B+树。
**15. D**
对于图中所示的3阶B树,被删关键字78所在的结点在删除前的关键字个数$=1=\lceil 3/2 \rceil-1$,且其左兄弟结点的关键字个数$=2 \ge \lceil 3/2 \rceil$,属于“兄弟够借”的情况,因此要把该结点的左兄弟结点中的最大关键字上移到双亲结点中,同时把双亲结点中大于上移关键字的关键字下移到要删除关键字的结点中,这样就达到了新的平衡,如下图所示。
```
      [ 45 ]                    [ 45 ]
      /    \          ==>         /    \
[ 17 35 ] [ 55 65 ]           [ 17 35 ] [ 55 62 ]
...    [ 47 ] [ 60 62 78 ]   ...       [ 47 ] [ 60 ] [ 65 ]
```

**16. A**
对于5阶B树,根结点的分支数最少为2(关键字数最少为1),其他非叶结点的分支数最少为$\lceil n/2 \rceil=3$(关键字数最少为2),因此关键字个数最少的情况如下图所示(叶结点不计入高度)。
```
       [ X ]
      /     \
    [ XX ]   [ XX ]
```
**注意**
一般对于某个具体的B树图形,并不能确定是几阶B树。对于本题所述的5阶B树,不要误认为:“存在至少有一个含关键字结点中的关键字达到4”才符合5阶B树的要求,因为5阶B树中各个结点包含的关键字个数最少为2($\lceil 5/2 \rceil-1=2$),最多为4($5-1=4$)。当5阶B树中各个结点包含的关键字个数为2时,也满足5阶B树的要求。

**17. D**
关键字数量不变,要求结点数量最多,即要求每个结点中含关键字的数量最少。根据4阶B树的定义,根结点最少含1个关键字,非根结点中最少含$\lceil 4/2 \rceil-1=1$个关键字,所以每个结点中关键字数量最少都为1个,即每个结点都有2个分支,类似于排序二叉树,而15个结点正好可以构造一个4层的4阶B树,使得终端结点全在第四层,符合B树的定义。
**18. A**
B+树的所有叶结点中包含了全部的关键字信息,且叶结点本身依关键字从小到大顺序链接,因此可以进行顺序查找,而B树不支持顺序查找(只支持多路查找)。
**19. B**
B+树是应文件系统所需而产生的B树的变形,前者比后者更加适用于实际应用中的操作系统的文件索引和数据库索引,因为前者的磁盘读/写代价更低,查询效率更加稳定。编译器中的词法分析使用有穷自动机和语法树。网络中的路由表快速查找主要靠高速缓存、路由表压缩技术和快速查找算法。系统一般使用空闲空间链表管理磁盘空闲块。
**20. B**
m阶B树的基本性质:根结点以外的非叶结点最少含有$\lceil m/2 \rceil-1$个关键字,代入m=3得到每个非叶结点中最少包含1个关键字,而根结点含有1个关键字,因此所有非叶结点都有两个孩子。此时其树形与h=5的满二叉树相同,可求得关键字最少为31个。
**21. B**
一个4阶B树的任意非叶结点至多含有m-1=3个关键字,在关键字依次插入的过程中,会导致结点的不断分裂,插入过程如下图所示。得到根结点包含的关键字为6,9。
```
插入5,6,9: [5 6 9]
插入13: [5 6 9 13] -> 分裂 -> 根[6], 左右孩子[5], [9 13]
插入8,2: 根[6], 孩子[2 5], [8 9 13]
插入12: 根[6], 孩子[2 5], [8 9 12 13] -> 分裂 -> 根[6 9], 孩子[2 5], [8], [12 13]
插入15: 根[6 9], 孩子[2 5], [8], [12 13 15]
```
**22. A**
在阶为3的B树中,每个结点至多含有2个关键字(至少1个),至多有3棵子树。本题规定第二层有4个关键字,欲使B树的结点数达到最多,则这4个关键字包含在3个结点中,B树树形如下图所示,其中A, B, C, ..., M表示关键字,最多有11个结点。
```
        A B
       / | \
      C D  E  F
     /|\/|\/|\/|
    G H I J K L M
```

**23. D**
在5阶B树中,除根结点外的非叶结点的关键字数k需要满足$2 \le k \le 4$。当被删关键字x不在终端结点(最底层非叶结点)时,可以用x的前驱(或后继)关键字y来替代x,然后在相应结点中删除y。**情况①**:删除260,将其前驱110放入260处,删除110后的结点<100>不满足5阶B树定义,从左兄弟中借85,将85放入根中,将根中的90移入结点<100>变为<90, 100>。**情况②**:删除260,将其后继280放入260处,结点<300>不满足5阶B树定义且左右兄弟都不够借,结点<300>可以和左兄弟<100, 110>以及关键字280合并成一个新的结点<100, 110, 280, 300>。**情况③**:在情况②中,结点<300>也可以和右兄弟<400, 500>以及关键字350合并成一个新的结点<300, 350, 400, 500>。综上,$T_1$根结点中的关键字序列可能是<60, 85, 110, 350>或<60, 90, 350>或<60, 90, 280>,仅选项D不可能。
快速解法:假如选项D的60, 90, 110, 350作为根结点,则在90和110之间只有100这一个数据,显然不符合5阶B树的定义,因此选项D不可能。
**24. B**
B树的插入操作可能导致叶结点分裂,叶结点分裂可能导致父结点分裂,甚至会传导到根结点,从而导致B树高度增1,选项I正确。若被删结点是叶结点,则显然会导致叶结点变化;若被删结点不是叶结点,则要先将被删结点和它的前驱或后继交换,最终转换为删除叶结点,还是导致叶结点变化,选项II正确。若在非叶结点中查找到了给定的关键字,则不用向下继续查找,选项III错误。插入关键字的初始位置是最底层叶结点,但可能因结点分裂而被转移到父结点中,选项IV错误。
> **注意**
> 由本题可知,与大多数教材不同,统考真题中称最底层终端结点为叶结点。

**二、综合应用题**
**01.【解答】**
m=3,因此除根结点外,非叶结点关键字个数为1~2。
```
插入20,30: [20 30]
插入50: [20 30 50] -> 结点分裂 -> 根[30], 孩子[20], [50]
```
如上图所示,首先插入20,30,结点内关键字个数不超过m-1=2,不会引起分裂;插入50,插入20,30所在的结点,引起分裂,结点内第$\lceil m/2 \rceil$个关键字30上升为父结点。
```
当前树: 根[30], 孩子[20], [50]
插入52: 根[30], 孩子[20], [50 52]
插入60: 根[30], 孩子[20], [50 52 60] -> 结点分裂 -> 根[30 52], 孩子[20], [50], [60]
```
如上图所示,插入52,插入50所在的结点,不会引起分裂;继续插入60,插入50,52所在的结点,引起分裂,52上升到父结点中,不会引起父结点的分裂。
```
当前树: 根[30 52], 孩子[20], [50], [60]
插入68: 根[30 52], 孩子[20], [50], [60 68]
插入70: 根[30 52], 孩子[20], [50], [60 68 70] -> 结点分裂 -> 根[30 52 68], 孩子[20], [50], [60], [70]
```
如上图所示,插入68,插入60所在的结点,不会引起分裂;继续插入70,插入60,68所在的结点,引起分裂,68上升为新的父结点,68上升到30,52所在的结点后,会继续引起该结点的分裂,所以52上升为新的根结点。最后得到的B树如下图所示。
```
结点再分裂: 根[30 52 68] -> 根[52], 孩子[30], [68]
最终树:
          [ 52 ]
         /      \
      [ 30 ]      [ 68 ]
     /      \    /      \
  [ 20 ] [ 50 ] [ 60 ] [ 70 ]
```
**02.【解答】**
1) **插入90**:将90插入100所在的结点,插入90后该结点中的元素个数不超过$\lceil 3/2 \rceil=2$,不会引起结点的分裂,插入后的B树如下图所示。
```
                [ 50 ]
               /      \
            [ 30 ]      [ 80 ]
           /      \    /      \
        [ 8 20 ] [ 35 40 ] [ 60 ] [ 90 100 ]
```
2) **插入25**:将25插入8,20所在的结点,插入后结点内的元素个数为3,引起分裂。所以将结点内的中间元素20上升到父结点中,此时父结点中的元素个数为2(元素20和30),不会引起继续分裂,插入25后的B树如下图所示。
```
             [ 50 ]
            /      \
      [ 20  30 ]      [ 80 ]
     /    |     \    /      \
  [ 8 ] [ 25 ] [ 35 40 ] [ 60 ] [ 90 100 ]
```
3) **插入45**:将45插入35,40所在的结点,引起分裂,中间元素40上升到父结点(20,30所在的结点)中,引起父结点分裂,中间元素30上升到父结点(50所在的结点)中,两次分裂后的B树如下图所示。
```
                     [ 30  50 ]
                   /      |      \
                [ 20 ]    [ 40 ]    [ 80 ]
               /      \  /      \  /      \
            [ 8 ] [ 25 ] [ 35 ] [ 45 ] [ 60 ] [ 90 100 ]
```
4) **删除60**:删除60后,其所在的结点元素为空,从而导致借用右兄弟结点的元素,调整后的B树如下图所示。
```
                     [ 30  50 ]
                   /      |      \
                [ 20 ]    [ 40 ]    [ 90 ]
               /      \  /      \  /      \
            [ 8 ] [ 25 ] [ 35 ] [ 45 ] [ 80 ] [ 100 ]
```
5) **删除80**:删除80后,导致80所在结点的父结点与其右兄弟结点合并,这时父结点元素个数为0,再次对父结点进行调整。将50与40合并成一个新结点,则90,100所在结点为这个结点的子结点。从而构造的B树如下图所示。注意,这次调整的过程实际上包含多次调整过程,希望读者对照考点讲解中的删除过程仔细思考。
```
                          [ 30 ]
                         /      \
                      [ 20 ]      [ 40 50 ]
                     /      \    /    |    \
                  [ 8 ] [ 25 ] [ 35 ] [ 45 ] [ 90 100 ]
```
> **注意**
> B树中结点的插入、删除操作(特别是插入、删除后的结点分裂与合并)是本节的重点,也是难点,请读者务必熟练掌握。

**03.【解答】**
根据B树的概念,一个索引结点应适应操作系统一次读/写的物理记录大小,其大小应取不超过但最接近一个磁盘页块的大小。假设B树为m阶,一个B树结点最多存放m-1个关键字(5B)和对应的记录地址(5B)、m个子树指针(5B)和1个指示结点中的实际关键字个数的整数(2B),则有
$(m-1) \times (5+5) + m \times 5 + 2 \le 4000$ (注：原文公式为$(2 \times (m-1)+m) \times 5+2 \le 4000$, 疑有误，此处按文意校正)
$15m - 8 \le 4000$
计算结果为$m \le 267.2$, 取整 $m=267$。
一个索引结点最多可以存放$m-1=266$个索引项,最少可以存放$\lceil m/2 \rceil-1 = \lceil 267/2 \rceil - 1 = 134-1=133$个索引项。
全部有$n=20000000$个记录,每个记录占用空间200B,每个页块可以存放$4000/200=20$个记录,则全部记录分布在$20000000/20=1000000$个页块中,最多需要占用$1000000/133 \approx 7519$个磁盘页块作为B树索引,最少需要占用$1000000/266 \approx 3760$个磁盘页块作为B树索引(注意B树与B+树的不同,B树所有对数据记录的索引项分布在各个层次的结点中,B+树所有对数据记录的索引项都在叶结点中)。

### 7.5 散列(Hash)表
#### 7.5.1 散列表的基本概念
在前面介绍的线性表和树表的查找中,查找记录需进行一系列的关键字比较,记录在表中的位置与记录的关键字之间不存在映射关系,因此在这些表中的查找效率取决于比较的次数。
**散列函数**(也称哈希函数):一个把查找表中的关键字映射成该关键字对应的地址的函数,记为$Hash(key) = Addr$(这里的地址可以是数组下标、索引或内存地址等)。