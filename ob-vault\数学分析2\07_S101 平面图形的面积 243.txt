## 第十章 定积分的应用

### §1 平面图形的面积

在上一章开头讨论过由连续曲线$y=f(x) (\ge 0)$,以及直线$x=a,x=b(a<b)$和$x$轴所围曲边梯形的面积为
$$
A = \int_a^b f(x)dx = \int_a^b ydx.
$$
如果$f(x)$在$[a,b]$上不都是非负的,则所围图形的面积为
$$
A = \int_a^b |f(x)|dx = \int_a^b |y|dx.
$$
一般地,由上、下两条连续曲线$y=f_2(x)$与$y=f_1(x)$以及两条直线$x=a$与$x=b(a<b)$所围的平面图形(图 10-1),它的面积计算公式为
$$
A = \int_a^b [f_2(x) - f_1(x)] dx. \tag{1}
$$

<table>
    <tr>
        <td style="text-align:center;"><img src="https://cdn.mathpix.com/snip/images/F85eB_BfLz9zS5R21L90j7G4X_J_7vJ8uOq0W15NlGk.original.fullwidth.png" alt="图 10-1"></td>
        <td style="text-align:center;"><img src="https://cdn.mathpix.com/snip/images/hT_5V-4P5y95W5Yl81cE4WlW3sQ8Qk-jKz8nQzI34C4.original.fullwidth.png" alt="图 10-2"></td>
    </tr>
    <tr>
        <td style="text-align:center;">图 10-1</td>
        <td style="text-align:center;">图 10-2</td>
    </tr>
</table>

**例 1** 求由抛物线$y^2=x$与直线$x-2y-3=0$所围平面图形的面积$A$.

**解** 该平面图形如图 10-2 所示. 先求出抛物线与直线的交点$P(1,-1)$与$Q(9,3)$.用$x=1$把图形分为左、右两部分,应用公式(1)分别求得它们的面积为
$$
A_1 = \int_0^1 [\sqrt{x} - (-\sqrt{x})] dx = 2\int_0^1 \sqrt{x}dx = \frac{4}{3},
$$
$$
A_2 = \int_1^9 \left(\sqrt{x} - \frac{x-3}{2}\right) dx = \frac{28}{3}.
$$
所以$A=A_1+A_2=\frac{32}{3}$.

本题也可把抛物线方程和直线方程改写成
$$
x = y^2 = g_1(y), x = 2y + 3 = g_2(y), y \in [-1,3].
$$
并改取积分变量为$y$,便得
$$
\begin{aligned}
A &= \int_{-1}^3 [g_2(y) - g_1(y)] dy \\
&= \int_{-1}^3 (2y + 3 - y^2) dy = \frac{32}{3}.
\end{aligned}
$$
设曲线$C$由参数方程
$$
x = x(t), y = y(t), t \in [\alpha,\beta] \tag{2}
$$
给出,在$[\alpha,\beta]$上$y(t)$连续,$x(t)$连续可微且$x'(t) \ne 0$(对于$y(t)$连续可微且$y'(t) \ne 0$的情形可类似地讨论).记$a=x(\alpha),b=x(\beta)$ ($a<b$或$b<a$),则由曲线$C$及直线$x=a,x=b$和$x$轴所围的图形,其面积计算公式为
$$
A = \int_\alpha^\beta |y(t)x'(t)| dt. \tag{3}
$$

**例 2** 求由摆线$x=a(t-\sin t),y=a(1-\cos t) (a>0)$的一拱与$x$轴所围平面图形(图 10-3)的面积.

<img src="https://cdn.mathpix.com/snip/images/h298B0wN2s6u0X6-s-kSgD7W4U3rC8iW1H0o3H9s_d0.original.fullwidth.png" alt="图 10-3">
图 10-3

**解** 摆线的一拱可取$t \in [0,2\pi]$. 所求面积为
$$
\begin{aligned}
A &= \int_0^{2\pi} a(1-\cos t) [a(t - \sin t)]' dt \\
&= a^2 \int_0^{2\pi} (1 - \cos t)^2 dt = 3\pi a^2.
\end{aligned}
$$
如果由参数方程(2)所表示的曲线是封闭的,即有
$$
x(\alpha) = x(\beta), y(\alpha) = y(\beta),
$$
且在$(\alpha,\beta)$上曲线自身不再相交,那么由曲线自身所围图形的面积为
$$
A = \left| \int_\alpha^\beta y(t)x'(t) dt \right|
$$
$$
\left(\text{或} \left| \int_\alpha^\beta x(t)y'(t) dt \right| \right) \tag{4}
$$
此公式可由公式(1)和(3)推出,绝对值内的积分,其正、负由曲线(2)的旋转方向所确定.

**例 3** 求椭圆$\frac{x^2}{a^2} + \frac{y^2}{b^2} = 1$所围的面积.

**解** 化椭圆为参数方程
$$
x = a\cos t, y = b\sin t, t \in [0,2\pi].
$$
由公式(4),求得椭圆所围面积为
$$
\begin{aligned}
A &= \left| \int_0^{2\pi} b\sin t (a\cos t)' dt \right| \\
&= ab \int_0^{2\pi} \sin^2 t dt = \pi ab.
\end{aligned}
$$
显然,当$a=b=r$时,这就等于圆面积$\pi r^2$.

设曲线$C$由极坐标方程
$$
r=r(\theta), \theta \in [\alpha,\beta]
$$
给出,其中$r(\theta)$在$[\alpha,\beta]$上连续,$\beta-\alpha \le 2\pi$. 由曲线$C$与两条射线$\theta=\alpha, \theta=\beta$所围成的平面图形,通常也称为扇形(图 10-4).此扇形的面积计算公式为
$$
A = \frac{1}{2} \int_\alpha^\beta r^2(\theta) d\theta. \tag{5}
$$

<table>
    <tr>
        <td style="text-align:center;"><img src="https://cdn.mathpix.com/snip/images/h6YvI-6NfQ3N66Iu0D-jK7P5W1fT5l-M1r9c6pXk0w8.original.fullwidth.png" alt="图 10-4"></td>
        <td style="text-align:center;"><img src="https://cdn.mathpix.com/snip/images/T1K0r1z1e9c2j3u4S9E4W6J8Z0V4K7h3F7s4N5x4w5Q.original.fullwidth.png" alt="图 10-5"></td>
    </tr>
    <tr>
        <td style="text-align:center;">图 10-4</td>
        <td style="text-align:center;">图 10-5</td>
    </tr>
</table>

这仍可由定积分的基本思想而得. 如图 10-5 所示,对区间$[\alpha,\beta]$作任意分割
$$
T: \alpha = \theta_0 < \theta_1 < \dots < \theta_{n-1} < \theta_n = \beta,
$$
射线$\theta=\theta_i(i=1,2,\dots,n-1)$把扇形分成$n$个小扇形. 由于$r(\theta)$是连续的,因此当$||T||$很小时,在每一个$\Delta_i = [\theta_{i-1}, \theta_i]$上$r(\theta)$的值变化也很小.任取$\xi_i \in \Delta_i$,便有
$$
r(\theta) \approx r(\xi_i), \theta \in \Delta_i, i=1,2,\dots,n.
$$
这时,第$i$个小扇形的面积
$$
\Delta A_i \approx \frac{1}{2} r^2(\xi_i) \Delta \theta_i,
$$
于是
$$
A \approx \sum_{i=1}^n \frac{1}{2} r^2(\xi_i) \Delta \theta_i.
$$
由定积分的定义和连续函数的可积性,当$||T|| \to 0$时,上式右边的极限即为公式(5)中的定积分.

**例 4** 求双纽线$r^2=a^2\cos 2\theta$所围平面图形的面积.

**解** 如图 10-6 所示,因为$r^2 \ge 0$,所以$\theta$的取值范围是$[-\frac{\pi}{4}, \frac{\pi}{4}]$与$[\frac{3\pi}{4}, \frac{5\pi}{4}]$.由图形的对称性及公式(5),得到

<img src="https://cdn.mathpix.com/snip/images/K6g2R0n4D4P4Y3S5b4j6U3v3S5Z2Q4u3F8V3Z8R2e3Q.original.fullwidth.png" alt="图 10-6">
图 10-6

$$
\begin{aligned}
A &= 4 \cdot \frac{1}{2} \int_0^{\pi/4} a^2 \cos 2\theta d\theta \\
&= a^2 [\sin 2\theta]_0^{\pi/4} = a^2.
\end{aligned}
$$

### 习 题

1. 求由抛物线$y=x^2$与$y=2-x^2$所围图形的面积.
2. 求由曲线$y=|\ln x|$与直线$x=\frac{1}{10}, x=10, y=0$所围图形的面积.
3. 抛物线$y^2=2x$把圆$x^2+y^2 \le 8$分成两部分,求这两部分面积之比.
4. 求内摆线$x=a\cos^3 t, y=a\sin^3 t (a>0)$所围图形的面积(图 10-7).
5. 求心形线$r=a(1+\cos\theta) (a>0)$所围图形的面积.
6. 求三叶形曲线$r=a\sin 3\theta (a>0)$所围图形的面积.
7. 求由曲线$\sqrt{\frac{x}{a}}+\sqrt{\frac{y}{b}}=1(a,b>0)$与坐标轴所围图形的面积.
8. 求由曲线$x=t-t^3, y=1-t^4$所围图形的面积.
9. 求二曲线$r=\sin\theta$与$r=\sqrt{3}\cos\theta$所围公共部分的面积.

<img src="https://cdn.mathpix.com/snip/images/x6t6G4p5v8W2h7W8Y6C5X3B2c5f1F5B4S4A4t4x3k3A.original.fullwidth.png" alt="图 10-7">
图 10-7

10. 求两椭圆$\frac{x^2}{a^2}+\frac{y^2}{b^2}=1$与$\frac{x^2}{b^2}+\frac{y^2}{a^2}=1(a>0,b>0)$所围公共部分的面积.
11. *11. 证明:对于由上、下两条连续曲线$y=f_2(x)$与$y=f_1(x)$以及两条直线$x=a$与$x=b(a<b)$所围的平面图形$A$(图 10-1),存在包含$A$的多边形$\{U_n\}$以及被$A$包含的多边形$\{W_n\}$,使得当$n \to \infty$时,它们的面积的极限存在且相等.

### §2 由平行截面面积求体积

设$\Omega$为三维空间中的一立体,它夹在垂直于$x$轴的两平面$x=a$与$x=b$之间$(a<b)$.为方便起见称$\Omega$为位于$[a,b]$上的立体.若在任意一点$x \in [a,b]$处作垂直于$x$轴的平面,它截得$\Omega$的截面面积显然是$x$的函数,记为$A(x), x \in [a,b]$,并称之为$\Omega$的截面面积函数(见图 10-8).本节将导出由截面面积函数求立体体积的一般计算公式和旋转体的体积公式.

设截面面积函数$A(x)$是$[a,b]$上的一个连续函数,且把$\Omega$的上述平行截面投影到某一垂直于$x$轴的平面上,它们永远是一个含在另一个的里面①.对$[a,b]$作分割

<table>
    <tr>
        <td style="text-align:center;"><img src="https://cdn.mathpix.com/snip/images/B3b1C5w4j6Q5b3S8v3b1Z1t4j5Z8k6t2Q8u3n4U5j3S.original.fullwidth.png" alt="图 10-8"></td>
        <td style="text-align:center;"><img src="https://cdn.mathpix.com/snip/images/Q7J6X3w5t4Z8Y5u3W4J8t3B2r5S3s5R2P5t3V5c4p5S.original.fullwidth.png" alt="图 10-9"></td>
    </tr>
    <tr>
        <td style="text-align:center;">图 10-8</td>
        <td style="text-align:center;">图 10-9</td>
    </tr>
</table>

$$
T:a = x_0 < x_1 < \dots < x_n = b.
$$
过各个分点作垂直于$x$轴的平面$x=x_i, i=1,2,\dots,n$,它们把$\Omega$切割成$n$个薄片$\Omega_i, i=1,2,\dots,n$. 任取$\xi_i \in [x_{i-1}, x_i]$,那么每一薄片的体积(见图 10-9).
$$
\Delta V_i \approx A(\xi_i) \Delta x_i.
$$
于是
$$
V \approx \sum_{i=1}^n A(\xi_i) \Delta x_i.
$$

---
① 一般还可推广到$\Omega$由满足这种假设的若干个立体相加或相减而得的情形,例如后面将要讨论的旋转体就是满足该条件的重要特例.