方法。思考:为什么 case RED 语句不用考虑交换后a[j]仍为红色,而case BLUE 语句中却需要考虑交换后a[j]仍为蓝色?
**04.【解答】**
1) 算法的基本设计思想
由题意可知,将最小的$\lfloor n/2 \rfloor$个元素放在$A_1$中,其余的元素放在$A_2$中,分组结果即可满足题目要求。仿照快速排序的思想,基于枢轴将$n$个整数划分为两个子集。根据划分后枢轴所处的位置$i$分别处理:
① 若$i=\lfloor n/2 \rfloor$,则分组完成,算法结束。
② 若$i<\lfloor n/2 \rfloor$,则枢轴及之前的所有元素均属于$A_1$,继续对$i$之后的元素进行划分。
③ 若$i>\lfloor n/2 \rfloor$,则枢轴及之后的所有元素均属于$A_2$,继续对$i$之前的元素进行划分。
基于该设计思想实现的算法,无须对全部元素进行全排序,其平均时间复杂度是$O(n)$,空间复杂度是$O(1)$。
2) 算法实现
```
int setPartition(int a[], int n) {
    int pivotkey, low=0,low0=0,high=n-1,high0=n-1,flag=1,k=n/2,i;
    int s1=0,s2=0;
    while (flag) {
        pivotkey=a[low];                        //选择枢轴
        while (low<high) {                      //基于枢轴对数据进行划分
            while (low<high && a[high]>=pivotkey) --high;
            if (low!=high) a[low]=a[high];
            while (low<high && a[low]<=pivotkey) ++low;
            if (low!=high) a[high]=a[low];
        }                                       //end of while(low<high)
        a[low]=pivotkey;
        if (low==k-1)                           //若枢轴是第 n/2 小的元素,划分成功
            flag=0;
        else{                                   //是否继续划分
            if (low<k-1){
                low0=++low;
                high=high0;
            }
            else{
                high0=--high;
                low=low0;
            }
        }
    }
    for(i=0;i<k;i++) s1+=a[i];
    for(i=k;i<n;i++) s2+=a[i];
    return s2-s1;
}
```
3) 本算法的平均时间复杂度是$O(n)$,空间复杂度是$O(1)$。

## 8.4 选择排序
选择排序的基本思想是:每一趟(如第$i$趟)在后面$n-i+1$ ($i=1,2,...,n-1$)个待排序元素中选取关键字最小的元素,作为有序子序列的第$i$个元素,直到第$n-1$趟做完,待排序元素只剩下1个,就不用再选。选择排序中的堆排序是历年统考考查的重点。

### 8.4.1 简单选择排序
根据上面选择排序的思想,可以很直观地得出简单选择排序算法的思想:假设排序表为$L[1...n]$,第$i$趟排序即从$L[i...n]$中选择关键字最小的元素与$L(i)$交换,每一趟排序可以确定一个元素的最终位置,这样经过$n-1$趟排序就可使得整个排序表有序。
简单选择排序算法的过程较为简单,相关举例见本书配套课程,其代码如下:
```
void SelectSort(ElemType A[], int n) {
    for(int i=0;i<n-1;i++){                     //一共进行n-1趟
        int min=i;                              //记录最小元素位置
        for(int j=i+1;j<n;j++)                  //在A[i...n-1]中选择最小的元素
            if(A[j]<A[min]) min=j;               //更新最小元素位置
        if(min!=i) swap(A[i],A[min]);            //封装的swap()函数共移动元素3次
    }
}
```
简单选择排序算法的性能分析如下:
**空间效率**：仅使用常数个辅助单元,所以空间效率为$O(1)$。
**时间效率**：从上述伪码中不难看出,在简单选择排序过程中,元素移动的操作次数很少,不会超过$3(n-1)$次,最好的情况是移动0次,此时对应的表已经有序;但元素间比较的次数与序列的初始状态无关,始终是$n(n-1)/2$次,因此时间复杂度始终是$O(n^2)$。
**稳定性**：在第$i$趟找到最小元素后,和第$i$个元素交换,可能会导致第$i$个元素与含有相同关键字的元素的相对位置发生改变。例如,表$L=\{2,2,1\}$,经过一趟排序后$L=\{1,2,2\}$,最终排序序列也是$L=\{1,2,2\}$,显然,2与2的相对次序已发生变化。因此,简单选择排序是一种不稳定的排序算法。
**适用性**：简单选择排序适用于顺序存储和链式存储的线性表,以及关键字较少的情况。

### 8.4.2 堆排序
堆的定义如下,$n$个关键字序列$L[1...n]$称为堆,当且仅当该序列满足:
① $L(i) \ge L(2i)$且$L(i) \ge L(2i+1)$ 或
② $L(i) \le L(2i)$且$L(i) \le L(2i+1)$ ($1 \le i \le \lfloor n/2 \rfloor$)
**命题追踪** 堆的性质与特点(2020)
可以将堆视为一棵完全二叉树,满足条件①的堆称为大根堆(大顶堆),大根堆的最大元素存放在根结点,且其任意一个非根结点的值小于或等于其双亲结点值。满足条件②的堆称为小根堆(小顶堆),小根堆的定义刚好相反,根结点是最小元素。图8.5所示为一个大根堆。
```
      87
     /  \
    /    \
   45    78
  / \   / \
 32 17 65  53
 /
09
```
`87 45 78 32 17 65 53 09`
图8.5 一个大根堆示意图
最新 408 统考大纲增加了考点“堆的应用”,堆的应用只有两个:堆排序和优先队列(见本节综合题7),优先队列也利用了堆排序的思想,因此本节仅介绍堆排序。
堆排序的思路很简单:首先将存放在$L[1...n]$中的$n$个元素建成初始堆,因为堆本身的特点(以大顶堆为例),所以堆顶元素就是最大值。输出堆顶元素后,通常将堆底元素送入堆顶,此时根结点已不满足大顶堆的性质,堆被破坏,将堆顶元素向下调整使其继续保持大顶堆的性质,再输出堆顶元素。如此重复,直到堆中仅剩一个元素为止。可见,堆排序需要解决两个问题:①如何将无序序列构造成初始堆?②输出堆顶元素后,如何将剩余元素调整成新的堆?
**命题追踪** 初始建堆的操作(2018、2021)
堆排序的关键是构造初始堆。建堆思路是从后往前检查所有分支结点,看是否满足堆的要求,若不满足,则对以该分支结点为根的子树进行调整。$n$个结点的完全二叉树,最后一个结点是第$\lfloor n/2 \rfloor$个结点的孩子。对以第$\lfloor n/2 \rfloor$个结点为根的子树筛选(对于大根堆,若根结点的关键字小于左右孩子中关键字较大者,则交换),使该子树成为堆。之后向前依次对以各结点($\lfloor n/2 \rfloor-1 \sim 1$)为根的子树进行筛选,看该结点值是否大于其左右子结点的值,若不大于,则将左右子结点中的较大值与之交换,交换后可能会破坏下一级的堆,于是继续采用上述方法构造下一级的堆,直到以该结点为根的子树构成堆为止。反复利用上述调整堆的方法建堆,直到根结点。
如图 8.6所示,初始时调整$L(4)$子树,$09<32$,交换,交换后满足堆的定义;向前继续调整$L(3)$子树,$78<$左右孩子的较大者$87$,交换,交换后满足堆的定义;向前调整$L(2)$子树,$17<$左右孩子的较大者$45$,交换后满足堆的定义;向前调整至根结点$L(1)$,$53<$左右孩子的较大者$87$,交换,交换后破坏了$L(3)$子树的堆,采用上述方法对$L(3)$进行调整,$53<$左右孩子的较大者$78$,交换,至此该完全二叉树满足堆的定义。
(a)初始i=4
```
      53
     /  \
   17    78
  / \   / \
 09 45 65 87
 /
32
```
(b)i=3
```
      53
     /  \
   17    87
  / \   / \
 32 45 65 78
 /
09
```
(c)i=2
```
      53
     /  \
   45    87
  / \   / \
 32 17 65 78
 /
09
```
(d)i=1
```
      87
     /  \
   45    78
  / \   / \
 32 17 65 53
 /
09
```
(e)结果
```
      87
     /  \
   45    78
  / \   / \
 32 17 65 53
 /
09
```
图8.6 自下往上逐步调整为大根堆
**命题追踪** 堆的删除操作及调整操作分析(2015、2024)
输出堆顶元素后,将堆的最后一个元素与堆顶元素交换,此时堆的性质被破坏,需要向下进行筛选。将09和左右孩子的较大者78交换,交换后破坏了$L(3)$子树的堆,继续对$L(3)$子树向下筛选,将09和左右孩子的较大者65交换,交换后得到了新堆,调整过程如图8.7所示。
```
      09                   78                   78
     /  \                 /  \                 /  \
   45    78      -->    45    09      -->    45    65
  / \   / \            / \   / \            / \   / \
 32 17 65  53         32 17 65  53         32 17 09  53
```
图8.7 输出堆顶元素后再将剩余元素调整成新堆
下面是建立大根堆的算法:
```
void BuildMaxHeap(ElemType A[], int len) {
    for(int i=len/2;i>0;i--)        //从i=⌊n/2⌋~1,反复调整堆
        HeadAdjust(A,i,len);
}
void HeadAdjust(ElemType A[], int k, int len) { //函数 HeadAdjust 对以元素k为根的子树进行调整
    A[0]=A[k];                                //A[0]暂存子树的根结点
    for(int i=2*k;i<=len;i*=2){               //沿 key 较大的子结点向下筛选
        if(i<len&&A[i]<A[i+1])
            i++;                              //取 key 较大的子结点的下标
        if(A[0]>=A[i]) break;                 //筛选结束
        else{
            A[k]=A[i];                        //将A[i]调整到双亲结点上
            k=i;                              //修改k值,以便继续向下筛选
        }
    }
    A[k]=A[0];                                //被筛选结点的值放入最终位置
}
```
调整的时间与树高有关,为$O(h)$。在建含$n$个元素的堆时,关键字的比较总次数不超过$4n$,时间复杂度为$O(n)$,这说明可以在线性时间内将一个无序数组建成一个堆。
下面是堆排序算法:
```
void HeapSort(ElemType A[], int len) {
    BuildMaxHeap(A,len);                  //初始建堆
    for(int i=len;i>1;i--){               //n-1趟的交换和建堆过程
        Swap(A[i],A[1]);                  //输出堆顶元素(和堆底元素交换)
        HeadAdjust(A,1,i-1);              //调整,把剩余的i-1个元素整理成堆
    }
}
```
**命题追踪** 堆的插入操作及调整操作分析(2009、2011)
同时,堆也支持插入操作。对堆进行插入操作时,先将新结点放在堆的末端,再对这个新结点向上执行调整操作。大根堆的插入操作示例如图8.8所示。
(a)初始,尾部加 63
```
      87
     /  \
   45    78
  / \   / \
 32 17 65  53
 / \
09 63
```
(b)父结点关键字32 下降
```
      87
     /  \
   45    78
  / \   / \
 63 17 65  53
 / \
09 32
```
(c)父结点关键字45 下降
```
      87
     /  \
   63    78
  / \   / \
 45 17 65  53
 / \
09 32
```
(d)调整完成
```
      87
     /  \
   63    78
  / \   / \
 45 17 65  53
 / \
09 32
```
图8.8 大根堆的插入操作示例
**命题追踪** 堆在海量数据中选出最小k个数的应用及效率分析(2022)
堆排序适合关键字较多的情况。例如,在1亿个数中选出前100个最大值。首先使用一个大小为100的数组,读入前100个数,建立小顶堆,而后依次读入余下的数,若小于堆顶则舍弃,否则用该数取代堆顶并重新调整堆,待数据读取完毕,堆中100个数为所求。
堆排序算法的性能分析如下:
**空间效率**：仅使用了常数个辅助单元,所以空间复杂度为$O(1)$。
**时间效率**：建堆时间为$O(n)$,之后有$n-1$次向下调整操作,每次调整的时间复杂度为$O(h)$,所以在最好、最坏和平均情况下,堆排序的时间复杂度为$O(n\log_2 n)$。
**稳定性**：进行筛选时,有可能把后面相同关键字的元素调整到前面,所以堆排序算法是一种不稳定的排序算法。例如,表$L=\{1,2,2\}$,构造初始堆时可能将2交换到堆顶,此时$L=\{2,1,2\}$,最终排序序列为$L=\{1,2,2\}$,显然,2与2的相对次序已发生变化。
**适用性**：堆排序仅适用于顺序存储的线性表。

### 8.4.3 本节试题精选
一、单项选择题
01. 在以下排序算法中,每次从未排序的记录中选取最小关键字的记录,加入已排序记录的末尾,该排序算法是( )。
    A. 简单选择排序 B. 冒泡排序 C. 堆排序 D. 直接插入排序
02. 简单选择排序算法的比较次数和移动次数分别为( )。
    A. $O(n)$, $O(\log_2 n)$
    B. $O(\log_2 n)$, $O(n^2)$
    C. $O(n^2)$, $O(n)$
    D. $O(n\log_2 n)$, $O(n)$
03. 若只想得到100000个元素组成的序列中第10个最小元素之前的部分排序的序列,用( )方法最快。
    A. 冒泡排序 B. 快速排序 C. 归并排序 D. 堆排序
04. 下列( )是一个堆。
    A. 19, 75, 34, 26, 97, 56
    B. 97, 26, 34, 75, 19, 56
    C. 19, 56, 26, 97, 34, 75
    D. 19, 34, 26, 97, 56, 75
05. 在含有n个元素的小根堆中(下标从1开始),关键字最大的元素可能存储在( )位置。
    A. $n/2$
    B. $n/2 + 2$
    C. 1
    D. $n/2 - 1$
06. 向具有n个结点的堆中插入一个新元素的时间复杂度为( ),删除一个元素的时间复杂度为( )。
    A. $O(1)$
    B. $O(n)$
    C. $O(\log n)$
    D. $O(n\log_2 n)$
07. 构建n个记录的初始堆,其时间复杂度为( );对n个记录进行堆排序,最坏情况下其时间复杂度为( )。
    A. $O(n)$
    B. $O(n^2)$
    C. $O(\log n)$
    D. $O(n\log_2 n)$
08. 下列4种排序算法中,排序过程中的比较次数与序列初始状态无关的是( )。
    A. 简单选择排序 B. 直接插入排序 C. 快速排序 D. 冒泡排序
09. 对由相同的n个整数构成的二叉排序树和小根堆,下列说法中不正确的是( )。
    A. 二叉排序树的高度大于或等于小根堆的高度
    B. 对二叉排序树进行中序遍历可以得到从小到大的序列
    C. 从小根堆的根结点到任意叶结点的路径构成从小到大的序列
    D. 对小根堆进行层序遍历可以得到从小到大的序列
10. 有一组数据(15, 9, 7, 8, 20, -1, 7, 4),用堆排序的筛选方法建立的初始小根堆为( )。
    A. -1, 4, 8, 9, 20, 7, 15, 7
    B. -1, 7, 15, 7, 4, 8, 20, 9
    C. -1, 4, 7, 8, 20, 15, 7, 9
    D. A、B、C均不对
11. 对关键字序列{23, 17, 72, 60, 25, 8, 68, 71, 52}进行堆排序,输出两个最小关键字后的剩余堆是( )。
    A. {23, 72, 60, 25, 68, 71, 52}
    B. {23, 25, 52, 60, 71, 72, 68}
    C. {71, 25, 23, 52, 60, 72, 68}
    D. {23, 25, 68, 52, 60, 72, 71}
12. 堆排序分为两个阶段:第一阶段将给定的序列构造成一个初始堆,第二阶段逐次输出堆顶元素,并调整使其保持堆的性质。设有给定序列{48, 62, 35, 77, 55, 14, 35, 98},若在堆排序的第一阶段将该序列构造成一个大根堆,则交换元素的次数为( )。
    A. 5
    B. 6
    C. 7
    D. 8
13. 已知大根堆{62, 34, 53, 12, 8, 46, 22},删除堆顶元素后需要重新调整堆,则在此过程中关键字的比较次数为( )。
    A. 2
    B. 3
    C. 4
    D. 5
14. 从根结点到任意叶结点的路径都是有序的数据结构是( )。
    A. 红黑树
    B. 二叉查找树
    C. 哈夫曼树
    D. 堆
15. **【2009统考真题】** 已知关键字序列{5, 8, 12, 19, 28, 20, 15, 22}是小根堆,插入关键字3,调整好后得到的小根堆是( )。
    A. 3, 5, 12, 8, 28, 20, 15, 22, 19
    B. 3, 5, 12, 19, 20, 15, 22, 8, 28
    C. 3, 8, 12, 5, 20, 15, 22, 28, 19
    D. 3, 12, 5, 8, 28, 20, 15, 22, 19
16. **【2011统考真题】** 已知序列{25, 13, 10, 12, 9}是大根堆,在序列尾部插入新元素18,再将其调整为大根堆,调整过程中元素之间进行的比较次数是( )。
    A. 1
    B. 2
    C. 4
    D. 5
17. **【2015统考真题】** 已知小根堆为8, 15, 10, 21, 34, 16, 12,删除关键字8之后需重建堆,在此过程中,关键字之间的比较次数是( )。
    A. 1
    B. 2
    C. 3
    D. 4
18. **【2018统考真题】** 在将序列(6, 1, 5, 9, 8, 4, 7)建成大根堆时,正确的序列变化过程是( )。
    A. 6,1,7,9,8,4,5 → 6,9,7,1,8,4,5 → 9,6,7,1,8,4,5 → 9,8,7,1,6,4,5
    B. 6,9,5,1,8,4,7 → 6,9,7,1,8,4,5 → 9,6,7,1,8,4,5 → 9,8,7,1,6,4,5
    C. 6,9,5,1,8,4,7 → 9,6,5,1,8,4,7 → 9,6,7,1,8,4,5 → 9,8,7,1,6,4,5
    D. 6,1,7,9,8,4,5 → 7,1,6,9,8,4,5 → 7,9,6,1,8,4,5 → 9,7,6,1,8,4,5 → 9,8,6,1,7,4,5
19. **【2020统考真题】** 下列关于大根堆(至少含2个元素)的叙述中,正确的是( )。
    I. 可以将堆视为一棵完全二叉树
    II. 可以采用顺序存储方式保存堆
    III. 可以将堆视为一棵二叉排序树
    IV. 堆中的次大值一定在根的下一层
    A. 仅I、II
    B. 仅II、III
    C. 仅I、II和IV
    D. I、III和IV
20. **【2021统考真题】** 将关键字6,9,1,5,8,4,7依次插入初始为空的大根堆H,得到的H是( )。
    A. 9,8,7,6,5,4,1
    B. 9,8,7,5,6,1,4
    C. 9,8,7,5,6,4,1
    D. 9,6,7,5,8,4,1
21. **【2024统考真题】** 已知关键字序列28, 22, 20, 19, 8, 12, 15, 5是大根堆(最大堆),对该堆进行两次删除操作后,得到的新堆是( )。
    A. 20, 19, 15, 12, 8, 5
    B. 20, 19, 15, 5, 8, 12
    C. 20, 19, 12, 15, 8, 5
    D. 20, 19, 8, 12, 15, 5

二、综合应用题
01. 指出堆和二叉排序树的区别?
02. 画出一棵二叉树,使得它既满足大根堆的要求又满足二叉排序树的要求。
03. 若只想得到一个序列中第$k(k \ge 5)$个最小元素之前的部分的排序序列,则最好采用什么排序算法?
04. 通常使用的堆也称二叉堆,因为它是用完全二叉树来实现的,树中结点最多只有两个孩子。同理可以有m叉堆,即用完全m叉树来实现的堆。
    1) 下图是一个$m$叉小根堆,问$m$值是多少?向这个堆插入一个元素65后,堆中的元素如何变化?再删除堆顶元素呢?请画出变化后的树形。
```
      23
     /|\ \
    / | \ \
  30 44 49 0
 /   |   \
60  70  67
   /|\
  88 90
```
    *Note: The structure of the tree in the image is ambiguous. The provided transcription reflects one possible interpretation based on standard m-ary tree layout.*
    2) 从0开始对完全4叉树中的结点从左到右、从上到下进行编号。若给定一个结点$k$,其父结点的编号是多少?(若存在),其第$i(i=1,2,3,4)$个孩子的编号是多少?
    3) 在m叉堆中进行插入和删除操作的时间复杂度是多少?
05. 编写一个算法,在基于单链表表示的待排序关键字序列上进行简单选择排序。
06. 试设计一个算法,判断一个数据序列是否构成一个小根堆。
07. 优先队列(Priority Queue)是一种数据结构,它类似于普通队列,但每个元素都有一个优先级。元素在入队时会根据其优先级来排序,而不按照先入先出的顺序来排序。每次从优先队列中出队时,出队的是优先级最高的元素,而不是最早进入队列的元素。
    队列中的元素的数据结构的定义如下:
```c
typedef struct {
    int value;       //元素的值
    int priority;    //元素的优先级,priority越大,优先级越高
} PriorityQueueElement;
```
请设计一个优先队列,要求满足:①初始时队列为空;②入队时,不允许增加队列的占用空间;③出队后,出队元素所占用的空间可重复使用,即整个队列所占用的空间不变;④入队操作和出队操作的时间复杂度始终保持为$O(\log_2 n)$。请回答:
1) 该队列是应选择链式存储结构,还是选择顺序存储结构?
2) 给出优先队列的数据结构的定义。
3) 用伪代码给出入队操作和出队操作的基本过程(关键之处可用文字描述)。
08. **【2022统考真题】** 现有$n(n > 100000)$个数保存在一维数组M中,需要查找M中最小的10个数。请回答下列问题。
    1) 设计一个完成上述查找任务的算法,要求平均情况下的比较次数尽可能少,简述其算法思想(不需要编程实现)。
    2) 说明你所设计的算法平均情况下的时间复杂度和空间复杂度。

### 8.4.4 答案与解析
一、单项选择题
01. A
    注意,读者应熟练掌握各种排序算法的思想、过程和特点。
02. C
03. D
    采用堆排序时,读入前10个元素,建立含10个元素的大根堆,而后依次扫描剩余元素,若大于堆顶,则舍弃,否则用该元素取代堆顶并重新调整堆,当元素全部扫描完毕,堆中保存的即是最小的10个元素。冒泡排序需要从后往前执行10趟冒泡才能得到10个最小的元素。两者的 时间复杂度都和数据规模$n$线性相关,但显然堆排序的常系数更小。而快速排序、归并排序的每一趟都不能保证得到当前序列的最小值,也无法达到线性时间复杂度。
04. D
    可将每个选项中的序列表示成完全二叉树,再看父结点与子结点的关系是否全部满足堆的定义。例如,选项A中序列对应的完全二叉树如下图所示。显然,最小元素19在根结点,因此可能是小根堆,但75与26的关系却不满足小根堆的定义,所以选项A中的序列不是一个堆。其他选项采用类似的过程分析。
```
      19
     /  \
   75    34
  /  \
 26  97
 /
56
```
05. B
    在小根堆中,关键字最大的元素一定存储在该堆所对应的完全二叉树的叶结点中(位于最底层或次底层)。又因为二叉树中的最后一个非叶结点存储在$\lfloor n/2 \rfloor$中,所以关键字最大的元素的存储范围为$\lfloor n/2 \rfloor+1 \sim n$。注意,读者可类比在大根堆中求关键字最小的元素的存储范围。
06. C、C
    在向有$n$个元素的堆中插入一个新元素时,需要调用一个向上调整的算法,比较次数最多等于树的高度减1,因为树的高度为$\lfloor \log_2 n \rfloor + 1$,所以堆的向上调整算法的比较次数最多等于$\lfloor \log_2 n \rfloor$。此处需要注意,调整堆和建初始堆的时间复杂度是不一样的,读者可以仔细分析两个算法的具体执行过程。
07. A、D
    建堆过程中,向下调整的时间与树高$h$有关,为$O(h)$。每次向下调整时,大部分结点的高度都较小。因此,可以证明在元素个数为$n$的序列上建堆,其时间复杂度为$O(n)$。无论是在最好情况下还是在最坏情况下,堆排序的时间复杂度均为$O(n\log_2 n)$。
08. A
    简单选择排序的比较次数始终为$n(n-1)/2$,与序列状态无关。
09. D
    堆是顺序存储的完全二叉树,因此其高度小于或等于结点数相同的二叉排序树,选项A正确。选项B显然正确。根据小根堆的定义,其根结点到任意叶结点的路径构成从小到大的序列,选项C正确。堆的各层结点之间没有大小要求,因此层序遍历不能保证得到有序序列,选项D错误。
10. C
    从$\lfloor n/2 \rfloor \sim 1$依次筛选堆的过程如下图所示,显然选择选项C。
    筛选结点8:
```
        15              15
       /  \            /  \
      9    7          9    7
     / \  / \        / \  / \
    8  20 -1 7      4  20 -1 7
   /                /
  4                8
```
    筛选结点7:
```
        15
       /  \
      9    -1
     / \   / \
    4  20 7   7
   /
  8
```
    筛选结点9:
```
        15
       /  \
      4    -1
     / \   / \
    9  20 7   7
   /
  8
```
    筛选结点15:
```
        -1
       /  \
      4    7
     / \   / \
    9  20 15  7
   /
  8
```
11. D
    筛选法初始建堆为{8, 17, 23, 52, 25, 72, 68, 71, 60},输出8后重建的堆为{17, 25, 23, 52, 60, 72, 68, 71},输出17后重建的堆为{23, 25, 68, 52, 60, 72, 71}。
12. B
    初始序列是一棵顺序存储的完全二叉树,然后根据大根堆的要求,按照从下到上、从右到左的顺序进行调整。98和77比较,98和77交换(交换1次);14和35比较,35和35比较,不交换;98和55比较,98和62比较,98和62交换(交换1次);62和77比较,77和62交换(交换1次);98和35比较,98和48比较,98和48交换(交换1次);77和55比较,77和48比较,77和48交换(交换1次);48和62比较,62和48交换(交换1次),共交换6次。
13. B
    删除堆顶62后,将堆尾22放入堆顶,然后自上而下调整。首先34与53比较(第一次比较),较大者53与根22比较(第二次比较),53被换至堆顶;22只有一个孩子,直接与其左孩子46比较(第3次比较),22与46交换,至此大根堆调整结束,具体过程如下图所示。
```
      22                   53                   53
     /  \                 /  \                 /  \
   34    53      -->    34    22      -->    34    46
  /  \  /               /  \  /               /  \  /
 12  8 46              12  8 46              12  8 22
```
14. D
    红黑树和二叉查找树的中序序列是有序序列,从根结点到任意叶结点的路径不能保证是有序的。哈夫曼树是根据权值按一定规则构造的树,和关键字次序无关。若是小根堆,则从根结点到任意叶结点的路径是升序序列;若是大根堆,则从根结点到叶结点的路径是降序序列。
15. A
    插入关键字3后,堆的变化过程如下图所示。
(a) 原始堆
```
      5
     /  \
    8    12
   / \   / \
  19 28 20 15
 /
22
```
(b) 插入3
```
      5
     /  \
    8    12
   / \   / \
  19 28 20 15
 / \
22  3
```
(c) 调整结束
```
      3
     /  \
    5    12
   / \   / \
  8 28 20 15
 / \
22 19
```
16. B
    首先18与10比较,交换;18与25比较,不交换。共比较2次,调整过程如下图所示。
    插入结点18 -> 筛选结点10 -> 筛选结点25
```
      25                 25                  25
     /  \               /  \                /  \
    13  10     -->     13   18      -->    13   18
   /  \  /            /  \  /             /  \  /
  12  9 18           12  9 10            12  9 10
```
17. C
    删除8后,将12移动到堆顶,第一次是15和10比较,第二次是10和12比较并交换,第三次还需比较12和16,所以比较次数为3。
```
      8                    12                   10
     /  \                 /  \                 /  \
    15  10       -->     15  10       -->     15  12
   / \   /              / \   /              / \   /
  21 34 16             21 34 16             21 34 16
 /                    /
12
```
18. A
    要熟练掌握建堆和调整堆的方法,从序列末尾开始向前遍历,变换过程如下图所示。
```
  6 -> 4与7比较,7和5互换 -> 9与8比较,9和1互换
  6        6         6          6          9          9
 /|\      /|\       /|\        /|\        /|\        /|\
1 5 9 -> 1 5 9 -> 1 7 9 -> ... -> 1 8 5 -> 1 8 5 -> 6 8 5
... (intermediate steps are complex to represent textually as per image)
```
    *Note: The image shows a complex series of transformations best understood visually. The final answer corresponds to option A.*
19. C
    简单概念题。堆是一棵完全树,采用一维数组存储,选项I正确,选项II正确。大根堆只要求根结点值大于左右孩子值,并不要求左右孩子值有序,选项III错误。堆的定义是递归的,所以其左右子树也是大根堆,所以堆的次大值一定是其左孩子或右孩子,选项IV正确。
20. B
    要熟练掌握调整堆的方法,建堆的过程如下图所示,所以答案选择选项B。
```
  6 -> 9 -> 1 -> 5 -> 8 -> 4 -> 7 (sequence of insertions)
```
    *Note: The image shows the result of sequential insertions into an initially empty heap, which is a standard method for building a heap. The final state corresponds to option B.*
21. B
    该序列已调整成大根堆,接下来进行第一次删除操作:删除28后,将5放入堆顶;然后自上而下调整。22和20比较,较大者22与5比较,交换;19和8比较,较大者19与5比较,交换。
第一次删除后：
```
      28                  5                    22                   22
     /  \                /  \                 /  \                 /  \
   22   20      -->    22   20       -->     5    20       -->     19   20
  / \  / \             / \  / \             / \  / \             / \  / \
 19 8 12 15          19 8 12 15           19 8 12 15           5  8 12 15
 /                   /                    /                    /
5                   5                    5                    5
```
接下来进行第二次删除操作:删除22后,将15放入堆顶;然后自上而下调整。19和20比较,较大者20与15比较,交换;15直接与其仅有的左孩子12比较,不交换。
第二次删除后：
```
      22                  15                   20
     /  \                /  \                 /  \
   19   20      -->    19   20       -->     19   15
  / \  / \             / \  /               / \  /
 5  8 12 15           5  8 12              5  8 12
```
因此,进行两次删除操作后,得到的新堆是20, 19, 15, 5, 8, 12。
二、综合应用题
**01.【解答】**
以小根堆为例,堆的特点是双亲结点的关键字必然小于或等于该孩子结点的关键字,而两个孩子结点的关键字没有次序规定。在二叉排序树中,每个双亲结点的关键字均大于左子树结点的关键字,均小于右子树结点的关键字,也就是说,每个双亲结点的左、右孩子的关键字有次序关系。这样,当对两种树执行中序遍历后,二叉排序树会得到一个有序的序列,而堆则不一定能得到一个有序的序列。
**02.【解答】**
大根堆要求根结点的关键字值既大于或等于左子女的关键字值,又大于或等于右子女的关键字值。二叉排序树要求根结点的关键字值大于左子女的关键字值,同时小于右子女的关键字值。两者的交集是:根结点的关键字值大于左子女的关键字值。这意味着它是一棵左斜单支树,但大根堆要求是完全二叉树,因此最后得到的只能是如下图所示的两个结点的二叉树。
```
    5
   /
  2
```
读者也可能会注意到,当只有一个结点时,显然是满足题意的,但我们不举一个结点的例子是为了体现出排序树与大根堆的区别。
**03.【解答】**
在基于比较的排序算法中,插入排序、快速排序和归并排序只有在将元素全部排完序后,才能得到前$k$个最小的元素序列,算法的效率不高。
冒泡排序、堆排序和简单选择排序可以,因为它们在每一趟中都可以确定一个最小的元素。采用堆排序最合适,对于$n$个元素的序列,建立初始堆的时间不超过$4n$,取得第$k$个最小元素之前的排序序列所花的时间为$k\log_2 n$,总时间为$4n + k\log_2 n$;冒泡和简单选择排序完成此功能所花的时间为$kn$,当$k \ge 5$时,通过比较可以得出堆排序最优。
**注意**
求前$k$个最小元素的顺序排列可采用的排序算法有冒泡排序、堆排序和简单选择排序。
**04.【解析】**
1) 除最后一个分支结点外,其余每个分支结点都有4个孩子,所以该树是完全4叉树。插入元素65后,再删除堆顶元素的树形分别如下图1和图2所示。
```
      0                     23
     /|\ \                   /|\ \
   23 | 65 88              30 | 65 88
  /|\|\...                /|\|\...
 30...90                 67...90
```
图1 插入65后的树形  图2 删除堆顶元素后的树形
2) 父结点的编号为$(k-1)/4$,第$i$个孩子的编号为$4 \times k+i$, $i=1, 2, 3, 4$。
3) 与二叉堆类似,插入和删除操作都有向上、向下调整的过程,操作时间都与树的高度有关。因此,插入和删除操作的时间复杂度都为$O(\log_m n)$,其中$n$为元素个数。
**05.【解答】**
算法的思想是:每趟在原始链表中摘下关键字最大的结点,把它插入结果链表的最前端。在原始链表中摘下的关键字越来越小,在结果链表前端插入的关键字也越来越小,因此最后形成的结果链表中的结点将按关键字非递减的顺序有序链接。
单链表的定义如第2章所述,假设它不带表头结点。
```c
void selectSort(LinkedList& L) {
    //对不带表头结点的单链表L执行简单选择排序
    LinkNode *h=L, *p, *q, *r, *s;
    L=NULL;
    while (h!=NULL) {              //持续扫描原链表
        p=s=h;q=r=NULL;            //指针s和r记忆最大结点和其前驱;p为工作指针,q为其前驱
        while (p!=NULL) {
            if (p->data > s->data){s=p;r=q;} //扫描原链表寻找最大结点s
                                             //找到更大的,记忆它和它的前驱
            q=p;p=p->link;                   //继续寻找
        }
        if (s==h)                            //最大结点在原链表前端
            h=h->link;
        else                                 //最大结点在原链表表内
            r->link=s->link;
        s->link=L; L=s;                      //结点s插入结果链前端
    }
}
```
**06.【解答】**
将顺序表$L[1...n]$视为一个完全二叉树,扫描所有分支结点,遇到孩子结点的关键字小于根结点的关键字时返回false,扫描完后返回true。算法的实现如下:
```c
bool IsMinHeap(ElemType A[], int len) {
    if (len%2==0){                                    //len为偶数,有一个单分支结点
        if (A[len/2]>A[len])                          //判断单分支结点
            return false;
        for(int i=len/2-1;i>=1;i--)                   //判断所有双分支结点
            if(A[i]>A[2*i] || A[i]>A[2*i+1])
                return false;
    }
    else{                                             //len为奇数时,没有单分支结点
        for(int i=len/2;i>=1;i--)                     //判断所有双分支结点
            if(A[i]>A[2*i] || A[i]>A[2*i+1])
                return false;
    }
    return true;
}
```
**07.【解答】**
1) 题目要求整个队列所占用的空间不变,入队操作和出队操作的时间复杂度始终保持为$O(\log_2 n)$,则应采用顺序存储结构,即用数组实现大根堆。
2) 优先队列的数据结构定义如下:
```c
typedef struct {
    PriorityQueueElement heap[MAX_SIZE]; //用数组实现堆
    int size;                            //当前堆中元素的数量
} PriorityQueue;
```
3) 入队操作:
```c
void enqueue(PriorityQueue *pq, int value, int priority) {
    if (pq->size>=MAX_SIZE){
        队列已满,无法入队;
        return;
    }
    将新元素添加到堆的末尾;
    向上调整堆;
}
```
出队操作:
```c
PriorityQueueElement dequeue(PriorityQueue *pq) {
    if (pq->size==0){
        队列为空时直接退出;
    }
    获取堆顶元素(优先级最高的元素);
    将堆的最后一个元素放到堆顶;
    向下调整堆;
    返回出队元素;
}
```
**08.【解答】**
1) 算法思想。
**【方法1】** 定义含10个元素的数组A,初始时元素值均为该数组类型能表示的最大数 MAX。
```
for M中的每个元素s
    if (s<A[9]) 丢弃A[9]并将s按升序插入A;
```
当数据全部扫描完毕,数组A[0]~A[9]保存的就是最小的10个数。
**【方法2】** 定义含10个元素的大根堆H,元素值均为该堆元素类型能表示的最大数MAX。
```
for M中的每个元素s
    if (s<H的堆顶元素) 删除堆顶元素并将s插入H;
```
当数据全部扫描完毕,堆中保存的就是最小的10个数。
2) 算法平均情况下的时间复杂度是$O(n)$,空间复杂度是$O(1)$。

## 8.5 归并排序、基数排序和计数排序
### 8.5.1 归并排序
**命题追踪** 二路归并操作的功能(2022)
归并排序与上述基于交换、选择等排序的思想不一样,归并的含义是将两个或两个以上的有序表合并成一个新的有序表。假定待排序表含有$n$个记录,则可将其视为$n$个有序的子表,每个子表的长度为1,然后两两归并,得到$\lceil n/2 \rceil$个长度为2或1的有序表;继续两两归并……如此重复,直到合并成一个长度为$n$的有序表为止,这种排序算法称为二路归并排序。
图8.9所示为二路归并排序的一个例子,经过三趟归并后合并成了有序序列。
初始关键字 [49] [38] [65] [97] [76] [13] [27]
第一趟归并后 [38 49] [65 97] [13 76] [27]
第二趟归并后 [38 49 65 97] [13 27 76]
第三趟归并后 [13 27 38 49 65 76 97]
图8.9 二路归并排序示例
**命题追踪** (算法题)归并排序思想的应用(2011)
`Merge()`的功能是将前后相邻的两个有序表归并为一个有序表。设两段有序表$A[\text{low..mid}]$、$A[\text{mid+1..high}]$存放在同一顺序表中的相邻位置,先将它们复制到辅助数组B中。每次从B的两段中取出一个记录进行关键字的比较,将较小者放入A中,当B中有一段的下标超出其对应的表长(该段的所有元素都已复制到A中)时,将另一段的剩余部分直接复制到A中。算法如下:
```c
ElemType *B=(ElemType *)malloc((n+1)*sizeof(ElemType)); //辅助数组 B
void Merge(ElemType A[], int low, int mid, int high) {
```