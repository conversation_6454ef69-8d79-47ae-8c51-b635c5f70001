$$
\iint_D f(x,y)d\sigma \le \iint_D g(x,y)d\sigma.
$$
(2) 若在$D$上$m \le f(x,y) \le M$,则
$m\sigma \le \iint_D f(x,y)d\sigma \le M\sigma$, 其中$\sigma$为区域$D$的面积.
(3)
$$
\left|\iint_D f(x,y)d\sigma\right| \le \iint_D |f(x,y)|d\sigma.
$$
**性质2(中值定理)**
设函数$f(x,y)$在闭区域$D$上连续,$\sigma$为区域$D$的面积,则在$D$上至少存在一点$(\xi,\eta)$,使得
$$
\iint_D f(x,y)d\sigma = f(\xi,\eta) \cdot \sigma.
$$

### 二、二重积分的计算

1. 利用直角坐标计算
(1) 先$y$后$x$. 积分区域$D$可以用$a \le x \le b, \varphi_1(x) \le y \le \varphi_2(x)$表示,
$$
\iint_D f(x,y)d\sigma = \int_a^b dx \int_{\varphi_1(x)}^{\varphi_2(x)} f(x,y)dy.
$$
(2) 先$x$后$y$. 积分区域$D$可以用$c \le y \le d, \varphi_1(y) \le x \le \varphi_2(y)$表示,
$$
\iint_D f(x,y)d\sigma = \int_c^d dy \int_{\varphi_1(y)}^{\varphi_2(y)} f(x,y)dx.
$$

2. 利用极坐标计算
先$r$后$\theta$. 积分区域$D$可以用$\alpha \le \theta \le \beta, \varphi_1(\theta) \le r \le \varphi_2(\theta)$表示,
$$
\iint_D f(x,y)d\sigma = \int_\alpha^\beta d\theta \int_{\varphi_1(\theta)}^{\varphi_2(\theta)} f(r\cos\theta, r\sin\theta)rdr.
$$
**【注】** 适合用极坐标计算的二重积分的特征:
(1) 适合用极坐标计算的被积函数. 例如$f(\sqrt{x^2+y^2}),f(\frac{y}{x}),f(\frac{x}{y})$.
(2) 适合用极坐标的积分域. 例如
$x^2+y^2 < R^2; r^2 < x^2+y^2 < R^2; x^2+y^2 < 2ax; x^2+y^2 < 2by$.

3. 利用函数的奇偶性计算
(1) 若积分域$D$关于$y$轴对称,$f(x,y)$关于$x$有奇偶性,则:
$$
\iint_D f(x,y)d\sigma = \begin{cases} 2\iint_{D_{x \ge 0}} f(x,y)d\sigma, & f(x,y)\text{关于}x\text{为偶函数,} \\ 0, & f(x,y)\text{关于}x\text{为奇函数.} \end{cases}
$$
(2) 若积分域$D$关于$x$轴对称,$f(x,y)$关于$y$有奇偶性,则
$$
\iint_D f(x,y)d\sigma = \begin{cases} 2\iint_{D_{y \ge 0}} f(x,y)d\sigma, & f(x,y)\text{关于}y\text{为偶函数,} \\ 0, & f(x,y)\text{关于}y\text{为奇函数.} \end{cases}
$$

4. 利用变量的轮换对称性计算
如果积分域$D$具有轮换对称性,也就是关于直线$y=x$对称,即$D$的表达式中将$x$换作$y$,$y$换作$x$,表达式不变,则
$$
\iint_D f(x,y)d\sigma = \iint_D f(y,x)d\sigma.
$$

> **常考题型与典型例题**
>
> **常考题型**
> 1. 累次积分交换次序或计算
> 2. 二重积分计算

### 一、累次积分交换次序或计算
**【例1】** 交换累次积分$\int_0^1 dx \int_{x^2}^{2-x} f(x,y)dy$的次序.

解 首先画域,$y$应介于$y=x^2$与$y=2-x$之间,$x$介于$0$与$1$之间.
如图1,则
$$
\text{原式} = \int_0^1 dy \int_0^{\sqrt{y}} f(x,y)dx + \int_1^2 dy \int_0^{2-y} f(x,y)dx.
$$

笔记区