## 第3章 内存管理
【考纲内容】
(一)内存管理基础
内存管理概念:逻辑地址与物理地址空间,地址变换,内存共享,内存保护,内存分配与回收
连续分配管理方式;页式管理;段式管理;段页式管理
(二)虚拟内存管理
虚拟内存基本概念;请求页式管理;页框分配与回收;页置换算法
内存映射文件(Memory-Mapped Files);虚拟存储器性能的影响因素及改进方式

【复习提示】
内存管理和进程管理是操作系统的核心内容,需要重点复习。本章围绕分页机制展开:通过分页管理方式在物理内存大小的基础上提高内存的利用率,再进一步引入请求分页管理方式,实现虚拟内存,使内存脱离物理大小的限制,从而提高处理器的利用率。

---
## 3.1 内存管理概念
在学习本节时,请读者思考以下问题:
1) 为什么要进行内存管理?
2) 多级页表解决了什么问题?又会带来什么问题?
在学习经典的管理方法前,同样希望读者先思考,自己给出一些内存管理的想法,并在学习过程中和经典方案进行比较。注意本节给出的内存管理是循序渐进的,后一种方法通常会解决前一种方法的不足。希望读者多多思考,比较每种方法的异同,着重掌握页式管理。

### 3.1.1 内存管理的基本原理和要求
内存管理(Memory Management)是操作系统设计中最重要和最复杂的内容之一。虽然计算机硬件技术一直在飞速发展,内存容量也在不断增大,但仍然不可能将所有用户进程和系统所需要的全部程序与数据放入主存,因此操作系统必须对内存空间进行合理的划分和有效的动态分配。操作系统对内存的划分和动态分配,就是内存管理的概念。
有效的内存管理在多道程序设计中非常重要,它不仅可以方便用户使用存储器、提高内存利用率,还可以通过虚拟技术从逻辑上扩充存储器。内存管理的主要功能有:
*   内存空间的分配与回收。由操作系统负责内存空间的分配和管理,记录内存的空闲空间、内存的分配情况,并回收已结束进程所占用的内存空间。
*   地址转换。程序的逻辑地址与内存中的物理地址不可能一致,因此存储管理必须提供地址变换功能,将逻辑地址转换成相应的物理地址。
*   内存空间的扩充。利用虚拟存储技术从逻辑上扩充内存。
*   内存共享。指允许多个进程访问内存的同一部分。例如,多个合作进程可能需要访问同一块数据,因此必须支持对内存共享区域进行受控访问。
*   存储保护。保证各个进程在各自的存储空间内运行,互不干扰。
在进行具体的内存管理之前,需要了解进程运行的基本原理和要求。

1. 逻辑地址与物理地址

**命题追踪** 进程虚拟地址空间的特点(2023)

编译后,每个目标模块都从0号单元开始编址,这称为该目标模块的相对地址(或逻辑地址)。当链接程序将各个模块链接成一个完整的可执行目标程序时,链接程序顺序依次按各个模块的相对地址构成统一的从0号单元开始编址的逻辑地址空间(或虚拟地址空间),对于32位系统,逻辑地址空间的范围为$0 \sim 2^{32}-1$。进程在运行时,看到和使用的地址都是逻辑地址。用户程序和程序员只需知道逻辑地址,而内存管理的具体机制则是完全透明的。不同进程可以有相同的逻辑地址,因为这些相同的逻辑地址可以映射到主存的不同位置。
物理地址空间是指内存中物理单元的集合,它是地址转换的最终地址,进程在运行时执行指令和访问数据,最后都要通过物理地址从主存中存取。当装入程序将可执行代码装入内存时,必须通过地址转换将逻辑地址转换成物理地址,这个过程称为地址重定位。
操作系统通过内存管理部件(MMU)将进程使用的逻辑地址转换为物理地址。进程使用虚拟内存空间中的地址,操作系统在相关硬件的协助下,将它“转换”成真正的物理地址。逻辑地址通过页表映射到物理内存,页表由操作系统维护并被处理器引用。

2. 程序的链接与装入

创建进程首先要将程序和数据装入内存。将用户源程序变为可在内存中执行的程序,通常需要以下几个步骤:

**命题追踪** 编译、链接和装入阶段的工作内容(2011)

*   **编译**。由编译程序将用户源代码编译成若干目标模块。
*   **链接**。由链接程序将编译后形成的一组目标模块,以及它们所需的库函数链接在一起,形成一个完整的装入模块。
*   **装入**。由装入程序将装入模块装入内存运行。
将用户程序变为可在内存中执行的程序的步骤如图3.1所示。

(编译程序产生的目标模块) -> [链接程序] -> (装入模块) -> [装入程序] -> (内存)

**图3.1 将用户程序变为可在内存中执行的程序的步骤**

将一个装入模块装入内存时,有以下三种装入方式。
(1) **绝对装入**
绝对装入方式只适用于单道程序环境。在编译时,若知道程序将放到内存的哪个位置,则编译程序将产生绝对地址的目标代码。装入程序按照装入模块的地址,将程序和数据装入内存。程序中的逻辑地址与实际内存地址完全相同,因此不需对程序和数据的地址进行修改。
程序中使用的绝对地址,可在编译或汇编时给出,也可由程序员直接赋予。而通常情况下在程序中采用的是符号地址,编译或汇编时再转换为绝对地址。
(2) **可重定位装入**
可重定位装入也称静态重定位。经过编译、链接后的装入模块的始址(起始地址)通常都是从0开始的,程序中使用的指令和数据的地址都是相对于始址而言的逻辑地址。可根据内存的当前情况,将装入模块装入内存的适当位置。在装入时对目标程序中的相对地址的修改过程称为重定位,地址转换通常是在进程装入时一次完成的,如图3.2(a)所示。
当一个作业装入内存时,必须给它分配要求的全部内存空间,若没有足够的内存,则无法装入。作业一旦进入内存,整个运行期间就不能在内存中移动,也不能再申请内存空间。
(3) **动态运行时装入**
动态运行时装入也称动态重定位。程序若要在内存中发生移动,则要采用动态的装入方式。装入程序将装入模块装入内存后,并不会立即将装入模块中的相对地址转换为绝对地址,而是将这种地址转换推迟到程序真正要执行时才进行。因此,装入内存后的所有地址均为相对地址。这种方式需要一个重定位寄存器(存放装入模块的起始位置)的支持,如图3.2(b)所示。
动态重定位的优点:可以将程序分配到不连续的存储区;在程序运行前只需装入它的部分代码即可投入运行,然后在程序运行期间,根据需要动态申请分配内存;便于程序段的共享。

(a) 静态重定位:
逻辑地址空间(地址空间) -> 物理地址空间(存储空间)
`0 LOAD 1,6` -> `100 LOAD 1,106`
`2 Add 1,8` -> `102 Add 1,108`
...

(b) 动态重定位:
逻辑地址(LOAD 1,500) -> + [重定位寄存器: 1000] -> 物理地址(1100 LOAD 1,500)
有效地址500 + 重定位寄存器1000 = 12345

**图3.2 重定位类型**

对目标模块进行链接时,根据链接的时间不同,分为以下三种链接方式。
(1) **静态链接**
在程序运行之前,先将各目标模块及它们所需的库函数链接成一个完整的装入模块,以后不再拆开。将几个目标模块装配成一个装入模块时,需要解决两个问题:①修改相对地址,编译后的所有目标模块都是从0开始的相对地址,当链接成一个装入模块时要修改相对地址。②变换外部调用符号,将每个模块中所用的外部调用符号也都变换为相对地址。
(2) **装入时动态链接**
将用户源程序编译后所得到的一组目标模块,在装入内存时,采用边装入边链接的方式。其优点是便于修改和更新,便于实现对目标模块的共享。
(3) **运行时动态链接**
在程序执行中需要某目标模块时,才对它进行链接。凡在程序执行中未用到的目标模块,都不会被调入内存和链接到装入模块上。其优点是能加快程序的装入过程,还可节省内存空间。

3. 进程的内存映像

不同于存放在硬盘上的可执行程序文件,当一个程序调入内存运行时,就构成了进程的内存映像。一个进程的内存映像一般有几个要素:
*   **代码段**：即程序的二进制代码,代码段是只读的,可以被多个进程共享。
*   **数据段**：即程序运行时加工处理的对象,包括全局变量和静态变量。
*   **进程控制块(PCB)**：存放在系统区。操作系统通过PCB来控制和管理进程。
*   **堆**：用来存放动态分配的变量。通过调用malloc函数动态地向高地址分配空间。
*   **栈**：用来实现函数调用。从用户空间的最大地址往低地址方向增长。
代码段和数据段在程序调入内存时就指定了大小,而堆和栈不一样。当调用像malloc和free这样的C标准库函数时,堆可以在运行时动态地扩展和收缩。用户栈在程序运行期间也可以动态地扩展和收缩,每次调用一个函数,栈就会增长;从一个函数返回时,栈就会收缩。
图3.3是一个进程在内存中的映像。其中,共享库用来存放进程用到的共享函数库代码,如printf()函数等。在只读代码段中,.init是程序初始化时调用的_init函数;text是用户程序的机器代码;.rodata是只读数据。在读/写数据段中,data是已初始化的全局变量和静态变量;.bss是未初始化及所有初始化为0的全局变量和静态变量。

**内存映像图(图3.3)**
*   `0xFFFFFFFF`: 操作系统内核区 (用户代码不可见区)
*   `0xC0000000`: 用户栈 (运行时创建), 栈顶由`%esp` (栈指针) 指向
*   (向下生长)
*   `0x40000000`: 共享库的存储映射区 (共享函数库代码所在区域)
*   (向上生长)
*   动态生成的堆 (运行时由malloc创建)
*   读/写数据段 (.data, .bss) (从可执行文件装入的数据及代码)
*   只读代码段 (.init, .text, .rodata)
*   未使用区
*   `0x08048000`
*   `0`: (地址0)

**图3.3 一个进程在内存中的映像**

4. 内存保护

**命题追踪** 分区分配内存保护的措施 (2009)

确保每个进程都有一个单独的内存空间。内存分配前,需要保护操作系统不受用户进程的影响,同时保护用户进程不受其他用户进程的影响。内存保护可采取两种方法:
1) 在CPU中设置一对上、下限寄存器,存放用户进程在主存中的下限和上限地址,每当CPU要访问一个地址时,分别和两个寄存器的值相比,判断有无越界。
2) 采用重定位寄存器(也称基地址寄存器)和界地址寄存器(也称限长寄存器)进行越界检查。重定位寄存器中存放的是进程的起始物理地址,界地址寄存器中存放的是进程的最大逻辑地址。内存管理部件将逻辑地址与界地址寄存器进行比较,若未发生地址越界,则加上重定位寄存器的值后映射成物理地址,再送交内存单元,如图3.4所示。

**地址变换与保护流程 (图3.4)**
CPU -> (逻辑地址) -> [比较: < 界地址寄存器?]
- (是) -> + [重定位寄存器] -> (物理地址) -> 内存
- (否) -> (地址错误)

**图3.4 重定位寄存器和界地址寄存器的硬件支持**

实现内存保护需要重定位寄存器和界地址寄存器,因此要注意两者的区别。重定位寄存器是用来“加”的,逻辑地址加上重定位寄存器中的值就能得到物理地址;界地址寄存器是用来“比”的,通过比较界地址寄存器中的值与逻辑地址的值来判断是否越界。
加载重定位寄存器和界地址寄存器时必须使用特权指令,只有操作系统内核才可以加载这两个存储器。这种方案允许操作系统内核修改这两个寄存器的值,而不允许用户程序修改。

5. 内存共享

并不是所有的进程内存空间都适合共享,只有那些只读的区域才可以共享。可重入代码也称纯代码,是一种允许多个进程同时访问但不允许被任何进程修改的代码。但在实际执行时,也可以为每个进程配以局部数据区,将在执行中可能改变的部分复制到该数据区,这样,程序在执行时只需对该私有数据区中的内存进行修改,并不去改变共享的代码。
下面用一个例子来说明内存共享的实现方式。考虑一个可以同时容纳40个用户的多用户系统,他们同时执行一个文本编辑程序,若该程序有160KB代码区和40KB数据区,则共需8000KB的内存空间来支持40个用户。若160KB代码是可分享的纯代码,则不论是在分页系统中还是在分段系统中,整个系统只需保留一份副本即可,此时所需的内存空间仅为40KB×40+160KB=1760KB。对于分页系统,假设页面大小为4KB,则代码区占用40个页面、数据区占用10个页面。为实现代码共享,应在每个进程的页表中都建立40个页表项,它们都指向共享代码区的物理页号。此外,每个进程还要为自己的数据区建立10个页表项,指向私有数据区的物理页号。对于分段系统,由于是以段为分配单位的,不管该段有多大,都只需为该段设置一个段表项(指向共享代码段始址,以及段长160KB)。由此可见,段的共享非常简单易行。
此外,在第2章中我们介绍过基于共享内存的进程通信,由操作系统提供同步互斥工具。在本章的后面,还将介绍一种内存共享的实现————内存映射文件。

6. 内存分配与回收

存储管理方式随着操作系统的发展而发展。在操作系统由单道向多道发展时,存储管理方式便由单一连续分配发展为固定分区分配。为了能更好地适应不同大小的程序要求,又从固定分区分配发展到动态分区分配。为了更好地提高内存的利用率,进而从连续分配方式发展到离散分配方式—————页式存储管理。引入分段存储管理的目的,主要是满足用户在编程和使用方面的要求,其中某些要求是其他几种存储管理方式难以满足的。

### 3.1.2 连续分配管理方式
连续分配方式是指为一个用户程序分配一个连续的内存空间,譬如某用户需要100MB的内存空间,连续分配方式就在内存空间中为用户分配一块连续的100MB空间。连续分配方式主要包括单一连续分配、固定分区分配和动态分区分配。

1. 单一连续分配
在单一连续分配方式中,内存被分为系统区和用户区,系统区仅供操作系统使用,通常在低地址部分;用户区内存中仅有一道用户程序,即用户程序独占整个用户区。
这种方式的优点是简单、无外部碎片;不需要进行内存保护,因为内存中永远只有一道程序。缺点是只能用于单用户、单任务的操作系统中;有内部碎片;内存的利用率极低。

2. 固定分区分配
固定分区分配是最简单的一种多道程序存储管理方式,它将用户内存空间划分为若干固定大小的分区,每个分区只装入一道作业。当有空闲分区时,便可再从外存的后备作业队列中选择适当大小的作业装入该分区,如此循环。在划分分区时有两种不同的方法。
*   **分区大小相等**。程序太小会造成浪费,程序太大又无法装入,缺乏灵活性。
*   **分区大小不等**。划分为多个较小的分区、适量的中等分区和少量大分区。
为了便于分配和回收,建立一张分区使用表,通常按分区大小排队,各表项包括对应分区的始址、大小及状态(是否已分配),如图3.5所示。分配内存时,便检索该表,以找到一个能满足要求且尚未分配的分区分配给装入程序,并将对应表项的状态置为“已分配”;若找不到这样的分区,则拒绝分配。回收内存时,只需将对应表项的状态置为“未分配”即可。

**(a) 分区使用表**

| 分区号 | 大小/KB | 起址/KB | 状态 |
| :--- | :--- | :--- | :--- |
| 1 | 12 | 20 | 已分配 |
| 2 | 32 | 32 | 已分配 |
| 3 | 64 | 64 | 已分配 |
| 4 | 128 | 128 | 未分配 |

**(b) 存储空间分配情况**

| 内存区域 | 大小 |
| :--- | :--- |
| 操作系统 | 20KB |
| 作业A | 32KB |
| 作业B | 64KB |
| 作业C | 128KB |
| (空闲) | 256KB |

**图3.5 固定分区说明表和内存分配情况**

这种方式存在两个问题:①程序太大而放不进任何一个分区;②当程序小于固定分区大小时,也要占用一个完整的内存分区,这样分区内部就存在空间浪费,这种现象称为内部碎片。固定分区方式无外部碎片,但不能实现多进程共享一个主存区,所以内存的利用率低。

3. 动态分区分配
(1) 动态分区分配的基本原理
动态分区分配也称可变分区分配,是指在进程装入内存时,根据进程的实际需要,动态地为之分配内存,并使分区的大小正好适合进程的需要。因此,系统中分区的大小和数量是可变的。
如图3.6所示,系统有64MB内存空间,其中低8MB固定分配给操作系统,其余为用户可用内存。开始时装入前三个进程,它们分别分配到所需的空间后,内存仅剩4MB,进程4无法装入。在某个时刻,内存中没有一个就绪进程,CPU出现空闲,操作系统就换出进程2,换入进程4。由于进程4比进程2小,这样在主存中就产生了一个6MB的内存块。之后CPU又出现空闲,需要换入进程2,而主存无法容纳进程2,操作系统就换出进程1,换入进程2。
动态分区在开始时是很好的,但是随着时间的推移,内存中会产生越来越多的小内存块,内存的利用率也随之下降。这些小内存块被称为外部碎片,它存在于所有分区的外部,与固定分区中的内部碎片正好相对。外部碎片可通过紧凑技术来克服,即操作系统不时地对进程进行移动和整理。但是,这需要动态重定位寄存器的支持,且相对费时。紧凑过程实际上类似于Windows系统中的磁盘碎片整理程序,只不过后者是对外存空间的紧凑。

**图3.6 动态分区分配**
(一系列内存布局图展示了进程换入换出导致外部碎片产生和变化的过程)

**命题追踪** 动态分区分配的内存回收方法 (2017)

在动态分区分配中,与固定分区分配类似,设置一张空闲分区链(表),可以按始址排序。分配内存时,检索空闲分区链,找到所需的分区,若其大小大于请求大小,则从该分区中按请求大小分割一块空间分配给装入进程(若剩余部分小到不足以划分,则不需要分割),余下部分仍然留在空闲分区链中。回收内存时,系统根据回收分区的始址,从空闲分区链中找到相应的插入点,此时可能出现四种情况:①回收区与插入点的前一空闲分区相邻,此时将这两个分区合并,并修改前一分区表项的大小为两者之和;②回收区与插入点的后一空闲分区相邻,此时将这两个分区合并,并修改后一分区表项的始址和大小;③回收区同时与插入点的前、后两个分区相邻,此时将这三个分区合并,修改前一分区表项的大小为三者之和,并取消后一分区表项;④回收区没有相邻的空闲分区,此时应该为回收区新建一个表项,填写始址和大小,并插入空闲分区链。
以上三种内存分区管理方法有一个共同特点,即用户程序在主存中都是连续存放的。
(2) 基于顺序搜索的分配算法
将作业装入主存时,需要按照一定的分配算法从空闲分区链(表)中选出一个分区,以分配给该作业。按分区检索方式,可分为顺序分配算法和索引分配算法。顺序分配算法是指依次搜索空闲分区链上的空闲分区,以寻找一个大小满足要求的分区,顺序分配算法有以下四种。

**命题追踪** 各种动态分区分配算法的特点 (2019、2024)

1) **首次适应(First Fit)算法**。空闲分区按地址递增的次序排列。每次分配内存时,顺序查找到第一个能满足大小的空闲分区,分配给作业。首次适应算法保留了内存高地址部分的大空闲分区,有利于后续大作业的装入。但它会使内存低地址部分出现许多小碎片,而每次分配查找时都要经过这些分区,因此增加了开销。
2) **邻近适应(Next Fit)算法**。也称循环首次适应算法,由首次适应算法演变而成。不同之处是,分配内存时从上次查找结束的位置开始继续查找。邻近适应算法试图解决该问题。它让内存低、高地址部分的空闲分区以同等概率被分配,划分为小分区,导致内存高地址部分没有大空闲分区可用。通常比首次适应算法更差。

**命题追踪** 最佳适应算法的分配过程 (2010)

3) **最佳适应(Best Fit)算法**。空闲分区按容量递增的次序排列。每次分配内存时,顺序查找到第一个能满足大小的空闲分区,即最小的空闲分区,分配给作业。最佳适应算法虽然称为最佳,能更多地留下大空闲分区,但性能通常很差,因为每次分配会留下越来越多很小的难以利用的内存块,进而产生最多的外部碎片。
4) **最坏适应(Worst Fit)算法**。空闲分区按容量递减的次序排列。每次分配内存时,顺序查找到第一个能满足要求的空闲分区,即最大的空闲分区,从中分割一部分空间给作业。与最佳适应算法相反,最坏适应算法选择最大的空闲分区,这看起来最不容易产生碎片,但是把最大的空闲分区划分开,会很快导致没有大空闲分区可用,因此性能也很差。
综合来看,首次适应算法的开销小,性能最好,回收分区也不需要对空闲分区重新排序。
(3) 基于索引搜索的分配算法
当系统很大时,空闲分区链可能很长,此时采用顺序分配算法可能很慢。因此,在大、中型系统中往往采用索引分配算法。索引分配算法的思想是,根据其大小对空闲分区分类,对于每类(大小相同)空闲分区,单独设立一个空闲分区链,并设置一张索引表来管理这些空闲分区链。当为进程分配空间时,在索引表中查找所需空间大小对应的表项,并从中得到对应的空闲分区链的头指针,从而获得一个空闲分区。索引分配算法有以下三种。
1) **快速适应算法**。空闲分区的分类根据进程常用的空间大小进行划分。分配过程分为两步:①首先根据进程的长度,在索引表中找到能容纳它的最小空闲分区链表;②然后从链表中取下第一块进行分配。优点是查找效率高、不会产生内存碎片;缺点是回收分区时,需要有效地合并分区,算法比较复杂,系统开销较大。

**命题追踪** 伙伴关系的概念 (2024)

2) **伙伴系统**。规定所有分区的大小均为$2$的$k$次幂($k$为正整数)。当需要为进程分配大小为$n$的分区时($2^{k-1} < n \le 2^k$),在大小为$2^k$的空闲分区链中查找。若找到,则将该空闲分区分配给进程。否则,表示大小为$2^k$的空闲分区已耗尽,需要在大小为$2^{k+1}$的空闲分区链中继续查找。若存在大小为$2^{k+1}$的空闲分区,则将其等分为两个分区,这两个分区称为一对伙伴,其中一个用于分配,而将另一个加入大小为$2^k$的空闲分区链。若不存在,则继续查找,直至找到为止。回收时,需要要将相邻的空闲伙伴分区合并成更大的分区。
3) **哈希算法**。根据空闲分区链表的分布规律,建立哈希函数,构建一张以空闲分区大小为关键字的哈希表,每个表项记录一个对应空闲分区链的头指针。分配时,根据所需分区大小,通过哈希函数计算得到哈希表中的位置,从中得到相应的空闲分区链表。
在连续分配方式中,我们发现,即使内存有超过1GB的空闲空间,但若没有连续的1GB空间,则需要1GB空间的作业仍然是无法运行的;但若采用非连续分配方式,则作业所要求的1GB内存空间可以分散地分配在内存的各个区域,当然,这也需要额外的空间去存储它们(分散区域)的索引,使得非连续分配方式的存储密度低于连续分配方式。非连续分配方式根据分区的大小是否固定,分为分页存储管理和分段存储管理。在分页存储管理中,又根据运行作业时是否要将作业的所有页面都装入内存才能运行,分为基本分页存储管理和请求分页存储管理。

### 3.1.3 基本分页存储管理①
固定分区会产生内部碎片,动态分区会产生外部碎片,这两种技术对内存的利用率都比较低。我们希望内存的使用能尽量避免碎片的产生,这就引入了分页的思想:将内存空间分为若干固定大小(如4KB)的分区,称为页框、页帧或物理块。进程的逻辑地址空间也分为与块大小相等的若干区域,称为页或页面。操作系统以页框为单位为各个进程分配内存空间。
从形式上看,分页的方法像是分区相等的固定分区技术,分页管理不产生外部碎片。但它又有本质的不同点:块的大小相对分区要小很多,而且进程也按照块进行划分,进程运行时按块申请主存可用空间并执行。这样,进程只会在为最后一个不完整的块申请一个主存块空间时,才产生主存碎片,所以尽管会产生内部碎片,但这种碎片相对进程来说也是很小的,每个进程平均只产生半个块大小的内部碎片(也称页内碎片)。

1. 分页存储的几个基本概念
(1) **页面和页面大小**
进程的逻辑地址空间中的每个页面有一个编号,称为页号,从0开始;内存空间中的每个页框也有一个编号,称为页框号(或物理块号),也从0开始。进程在执行时需要申请内存空间,即要为每个页面分配内存中的可用页框,这就产生了页号和页框号的一一对应。
为方便地址转换,页面大小应是2的整数次幂。同时页面大小应该适中,页面太小会使进程的页面数过多,这样页表就会过长,占用大量内存,而且也会增加硬件地址转换的开销,降低页面换入/换出的效率;页面过大又会使页内碎片增多,降低内存的利用率。
(2) **地址结构**

**命题追踪** 分页系统的逻辑地址结构 (2009、2010、2013、2015、2017、2024)

某个分页存储管理的逻辑地址结构如图3.7所示。

| 31 ... 12 | 11 ... 0 |
| :---: | :---: |
| 页号P | 页内偏移量W |

**图3.7 某个分页存储管理的逻辑地址结构**

地址结构包含两部分:前一部分为页号P,后一部分为页内偏移量W。地址长度为32位,其中0~11位为页内地址,即每页大小为$2^{12}$B;12~31位为页号,即最多允许$2^{20}$页。在实际题目中,页号、页内偏移、逻辑地址可能是用十进制数给出的,注意进制之间的转换。
(3) **页表**
为了便于找到进程的每个页面在内存中存放的位置,系统为每个进程建立一张页面映射表,简称页表。进程的每个页面对应一个页表项,每个页表项由页号和块号组成,它记录了页面在内存中对应的物理块号,如图3.8所示。进程执行时,通过查找页表,即可找到每页在内存中的物理块号。可见,页表的作用是实现从页号到物理块号的地址映射。

(a) 逻辑空间 (用户程序)
- 0页
- 1页
- 2页
- ...

(b) 页表
| 页号 | 块号 |
| :---: | :---: |
| 0 | 2 |
| 1 | 3 |
| 2 | 5 |
| 3 | 7 |
| 4 | 8 |
| ... | ... |

(c) 物理空间 (内存)
- 块0
- 块1
- 块2 (0页内容)
- 块3 (1页内容)
- 块4
- 块5 (2页内容)
- ...

**图3.8 页表的作用**

2. 基本地址变换机构
地址变换机构的任务是将逻辑地址转换为内存中的物理地址。地址变换是借助于页表实现的。图3.9给出了分页存储管理系统中的地址变换机构。
在页表中,页表项连续存放,因此页号可以是隐含的,不占用存储空间。

**图3.9 分页存储管理系统中的地址变换机构**
逻辑地址A -> (分解为 页号P, 页内偏移量W)
(页号P) -> [与页表长度M比较, < ?]
- (是) -> (页号P) + [页表始址F (来自页表寄存器)] -> (页表项地址) -> [访问页表] -> (块号b)
- (否) -> 越界中断
(块号b) -> (拼接) + (页内偏移量W) -> 物理地址E

**注**
为了提高地址变换的速度,在系统中设置一个页表寄存器(PTR),存放页表在内存的始址F和页表长度M。寄存器的造价昂贵,因此在单CPU系统中只设置一个页表寄存器。平时,进程未执行时,页表的始址和页表长度存放在本进程的PCB中。当进程被调度执行时,才将页表始址和页表长度装入页表寄存器中。设页面大小为L,逻辑地址A到物理地址E的变换过程如下(假设逻辑地址、页号、每页的长度都是十进制数):

**命题追踪** 页式系统的地址变换过程 (2013、2021、2024)
**命题追踪** 页表项地址的计算与分析 (2024)

① 根据逻辑地址计算出页号$P=A/L$、页内偏移量$W=A\%L$。
② 判断页号是否越界,若页号$P \ge$页表长度$M$,则产生越界中断,否则继续执行。
③ 在页表中查询页号对应的页表项,确定页面存放的物理块号。页号$P$对应的页表项地址 = 页表始址$F$ + 页号$P$ × 页表项长度,取出该页表项内容$b$,即为物理块号。
④ 计算物理地址$E=b \times L+W$,用物理地址$E$去访存。注意,物理地址=页面在内存中的始址+页内偏移量,页面在内存中的始址=块号×块大小(页面大小)。
以上整个地址变换过程均是由硬件自动完成的。例如,若页面大小为1KB,页号2对应的物理块为$b=8$,计算逻辑地址$A=2500$的物理地址$E$的过程如下:$P=2500/1K=2$, $W=2500\%1K = 452$,查找得到页号2对应的物理块的块号为8,$E=8 \times 1024+452=8644$。
计算条件用十进制数和用二进制数给出,过程会稍有不同。页式管理只需给出一个整数就能确定对应的物理地址,因为页面大小$L$是固定的。因此,页式管理中地址空间是一维的。
页表项的大小不是随意规定的,而是有所约束的。如何确定页表项的大小?
页表项的作用是找到该页在内存中的位置。以32位内存地址空间、按字节编址、一页4KB为例,地址空间内一共有$2^{32}B/4KB = 2^{20}$页,因此需要$log_2{2^{20}}=20$位才能保证表示范围能容纳所有页面,又因为内存以字节作为编址单位,即页表项的大小$\ge \lceil 20/8 \rceil = 3B$。所以在这个条件下,为了保证页表项能够指向所有页面,页表项的大小应大于或等于3B。当然,也可以选择更大的页表项,让一个页面能够正好容纳整数个页表项,或便于增加一些其他信息。
下面讨论分页管理方式存在的两个主要问题:①每次访存操作都需要进行逻辑地址到物理地址的转换,地址转换过程必须足够快,否则访存速度会降低;②每个进程引入页表,用于存储映射机制,页表不能太大,否则内存利用率会降低。

3. 具有快表的地址变换机构
由上面介绍的地址变换过程可知,若页表全部放在内存中,则存取一个数据或一条指令至少要访问两次内存:第一次是访问页表,确定所存取的数据或指令的物理地址;第二次是根据该地址存取数据或指令。显然,这种方法比通常执行指令的速度慢了一半。
为此,在地址变换机构中增设一个具有并行查找能力的高速缓冲存储器————快表(TLB),也称相联存储器,用来存放当前访问的若干页表项,以加速地址变换的过程。与此对应,主存中的页表常称为慢表。具有快表的地址变换机构如图3.10所示。

**图3.10 具有快表的地址变换机构**
逻辑地址A -> (分解为 页号P, 页内偏移量W)
(页号P) -> [在快表中查找]
- (命中, 得到块号b) -> (拼接) + (页内偏移量W) -> 物理地址E
- (未命中) -> (页号P) -> [访问慢表(页表)获取块号b, 并将(P,b)存入快表] -> (块号b) -> (拼接) + (页内偏移量W) -> 物理地址E

**命题追踪** 具有快表的地址变换的性能分析 (2009)

在具有快表的分页机制中,地址的变换过程如下:
① CPU给出逻辑地址后,由硬件进行地址转换,将页号与快表中的所有页号进行比较。
② 若找到匹配的页号,说明要访问的页表项在快表中有副本,则直接从中取出该页对应的物理块号,与页内偏移量拼接形成物理地址。这样,存取数据仅一次访存即可实现。
③ 若未找到匹配的页号,则需要访问内存中的页表,读出页表项后,就能得到该页的物理块号,再与页内偏移量拼接形成物理地址,最后用该物理地址去访存。若快表未命中,则存取数据需要两次访存。注意,找到页表项后,应同时将其存入快表,以便后面可能的再次访问。若快表已满,则须按照特定的算法淘汰一个旧页表项。
一般快表的命中率可达90%以上,这样分页带来的速度损失就可降低至10%以下。快表的有效性基于著名的局部性原理,后面讲解虚拟内存时将具体讨论它。

4. 两级页表
引入分页管理后,进程在执行时不需要将所有页调入内存页框,而只需将保存有映射关系的页表调入内存。但仍需考虑页表的大小。以32位逻辑地址空间、页面大小4KB、页表项大小4B为例:页内偏移为$log_2{4K}=12$位,页号部分为20位,则每个进程页表中的页表项数可达$2^{20}$之多,仅页表就要占用$2^{20} \times 4B / 4KB = 1K$个页,还要求是连续的。这显然是不切实际的。

**命题追踪** 多级页表的特点和优点 (2014)

解决上述问题的方法有两种:①对于页表所需的内存空间,采用离散分配方式,用一张索引表来记录各个页表的存放位置,这就解决了页表占用连续内存空间的问题;②只将当前需要的部分页表项调入内存,其余的页表项仍驻留磁盘,需要时再调入(虚拟内存的思想),这就解决了页表占用内存过多的问题。读者也许发现这个方案就和当初引进页表机制的思路一模一样,实际上就是为离散分配的页表再建立一张页表,称为外层页表(或页目录)。仍以上面的条件为例,当采用两级分页时,对页表再进行分页,则外层页表需要1K个页表项,刚好占用4KB,使得外层页表的大小正好等于一页,这样就得到了逻辑地址空间的格式,如图3.11所示。

| 一级页号或页目录号10位 | 二级页号或页号10位 | 页内偏移12位 |
| :---: | :---: | :---: |

**图3.11 逻辑地址空间的格式**

**命题追踪** 两级页表的逻辑地址结构及相关分析 (2010、2013、2015、2017—2019)

两级页表是在普通页表结构上再加一层页表,其结构如图3.12所示。

**图3.12 两级页表结构示意图**
(该图显示一个外层页表,其表项指向不同的页表或数据块。例如，外层页表的0号条目指向一个页表（在物理块3），而这个页表的0号条目指向一个数据块（在物理块1）。)
在页表的每个表项中,存放的是进程的某页对应的物理块号,如0号页存放在1号物理块中,1号页存放在5号物理块中。在外层页表的每个表项中,存放的是某个页表分页的始址,如0号页表存放在3号物理块中。可以利用外层页表和页表来实现进程从逻辑地址到物理地址的变换。

**命题追踪** 二级页表的页表基址寄存器中的内容 (2018、2021)
**命题追踪** 二级页表中的地址变换过程 (2015、2017)

为了方便实现地址变换,需要在系统中增设一个外层页表寄存器(也称页目录基址寄存器),用于存放页目录始址。将逻辑地址中的页目录号作为页目录的索引,从中找到对应页表的始址;再用二级页号作为页表分页的索引,从中找到对应的页表项;将页表项中的物理块号和页内偏移拼接形成物理地址,再用该物理地址访问内存单元。共进行了三次访存。
对于更大的逻辑地址空间,以64位为例,若采用两级分页,且页面大小为4KB,页表项大小为4B;若按物理块大小划分页表,则有42位用于外层页号,此时外层页表有4096G个页表项,需占用16384GB的连续内存空间,显然这是无法接受的,因此必须采用多级页表,再对外层页表分页。建立多级页表的目的在于建立索引,以免浪费内存空间去存储无用的页表项。

### 3.1.4 基本分段存储管理
分页管理方式是从计算机的角度考虑设计的,目的是提高内存的利用率,提升计算机的性能。分页通过硬件机制实现,对用户完全透明。分段管理方式的提出则考虑了用户和程序员,以满足方便编程、信息保护和共享、动态增长及动态链接等多方面的需要。

1. 分段
分段系统将用户进程的逻辑地址空间划分为大小不等的段。例如,用户进程由主程序段、两个子程序段、栈段和数据段组成,于是可以将这个进程划分为5段,每段从0开始编址,并分配一段连续的地址空间(段内要求连续,段间不要求连续,进程的地址空间是二维的)。

**命题追踪** 分段系统的逻辑地址结构分析 (2009)

分段存储管理的逻辑地址结构由段号S与段内偏移量W两部分组成。在图3.13中,段号为16位,段内偏移量为16位,因此一个进程最多有$2^{16}=65536$段,最大段长为64KB。

| 31 ... 16 | 15 ... 0 |
| :---: | :---: |
| 段号S | 段内偏移量W |

**图3.13 分段系统中的逻辑地址结构**

在页式系统中,逻辑地址的页号和页内偏移量对用户是透明的,但在分段系统中,段号和段内偏移量必须由用户显式提供,在高级程序设计语言中,这个工作由编译程序完成。

2. 段表
每个进程都有一张逻辑空间与内存空间映射的段表,进程的每个段对应一个段表项,段表项记录了该段在内存中的始址和段的长度。段表的内容如图3.14所示。

| 段号 | 段长 | 本段在主存的始址 |
| :---: | :---: | :---: |

**图3.14 段表的内容**

配置段表后,执行中的进程可以通过查找段表,找到每段所对应的内存区。可见,段表用于实现从逻辑段到物理内存区的映射,如图3.15所示。

**图3.15 利用段表实现物理内存区映射**
(图中显示了逻辑地址空间中的4个段(MAIN, X, D, S)通过段表映射到物理内存空间中的不同位置。)

段表项是连续存放的,各个段表项的长度相同,因此段号可以是隐含的,不占用存储空间。例如,在某分段系统的32位逻辑地址结构中,段号为16位,段内偏移量为16位(可用16位表示段长),物理内存大小为4GB(可用32位表示整个物理内存空间)。因此,每个段表项最少可占$16+32=48$位。若段表存放的始址为$M$,则$K$号段对应的段表项存放的地址为$M+K \times 6$。

3. 地址变换机构
分段系统的地址变换过程如图3.16所示。为了实现进程从逻辑地址到物理地址的变换功能,在系统中设置了一个段表寄存器,用于存放段表始址F和段表长度M。从逻辑地址A到物理地址E之间的地址变换过程如下:

**图3.16 分段系统的地址变换过程**
逻辑地址A (段号S, 偏移量W) -> [S与段表长度M比较, < ?]
- (是) -> (段号S) + [段表始址F] -> (段表项地址) -> [访问段表] -> (得到段长C, 基址b)
- (否) -> 越界
(偏移量W) -> [与段长C比较, < ?]
- (是) -> (基址b) + (偏移量W) -> 物理地址E
- (否) -> 越界

**命题追踪** 段式系统的地址变换过程 (2016)

① 从逻辑地址A中取出前几位为段号S,后几位为段内偏移量W。
② 判断段号是否越界,若段号S≥段表长度M,则产生越界中断,否则继续执行。
③ 在段表中查询段号S对应的段表项,段号S对应的段表项地址=段表始址F+段号S×段表项长度。取出段表项中该段的段长C,若W≥C,则产生越界中断,否则继续执行。
④ 取出段表项中该段的始址b,计算物理地址E=b+W,用物理地址E去访存。

4. 分页和分段的对比
分页和分段有许多相似之处,两者都是非连续分配方式,都要通过地址映射机构实现地址变换。但是,在概念上两者完全不同,主要表现在以下三个方面:
1) 页是信息的物理单位,分页的主要目的是提高内存利用率,分页完全是系统的行为,对用户是不可见的。段是信息的逻辑单位,分段的主要目的是更好地满足用户需求,用户按照逻辑关系将程序划分为若干段,分段对用户是可见的。
2) 页的大小固定且由系统决定。段的长度不固定,具体取决于用户所编写的程序。
3) 分页管理的地址空间是一维的。段式管理不能通过给出一个整数便确定对应的物理地址,因为每段的长度是不固定的,无法通过除法得出段号,无法通过求余得出段内偏移,所以一定要显式给出段号和段内偏移,因此分段管理的地址空间是二维的。

5. 段的共享与保护
**命题追踪** 页、段共享的原理和特点 (2019、2023)

在分页系统中,虽然也能实现共享,但远不如分段系统来得方便。若被共享的代码占N个页框,则每个进程的页表中都要建立N个页表项,指向被共享的N个页框。而在分段系统中,不管该段有多大,都只需为该段设置一个段表项,因此非常容易实现共享。
为了实现段共享,在系统中配置一张共享段表,所有共享的段都在共享段表中占一个表项。表项中记录了共享段的段号、段长、内存始址、状态(存在)位、外存始址和共享进程计数count等信息。共享进程计数count记录有多少进程正在共享该段,仅当所有共享该段的进程都不再需要时,此时其count=0,才回收该段所占的内存区。对于一个共享段,在不同的进程中可以具有不同的段号,每个进程用自己进程的段号去访问该共享段。①
不能被任何进程修改的代码称为可重入代码或纯代码,它是一种允许多个进程同时访问的代码。为了防止程序在执行时修改共享代码,在每个进程中都必须配以局部数据区,将在执行过程中可能改变的部分复制到数据区,这样,进程就可对该数据区中的内容进行修改。
与分页管理类似,分段管理的保护方法主要有两种:一种是存取控制保护,另一种是地址越界保护。地址越界保护将段表寄存器中的段表长度与逻辑地址中的段号比较,若段号大于段表长度,则产生越界中断;再将段表项中的段长和逻辑地址中的段内偏移进行比较,若段内偏移大于段长,也会产生越界中断。分页管理只需要判断页号是否越界,页内偏移是不可能越界的。

### 3.1.5 段页式存储管理
分页存储管理能有效地提高内存利用率,而分段存储管理能反映程序的逻辑结构并有利于段的共享和保护。将这两种存储管理方法结合起来,便形成了段页式存储管理方式。
在段页式系统中,进程的地址空间首先被分成若干逻辑段,每段都有自己的段号,然后将各段分成若干大小固定的页。对内存空间的管理仍然和分页存储管理一样,将其分成若干和页面大小相同的物理块,对内存的分配以物理块为单位,如图3.17所示。

(a) 程序的段页划分：一个程序划分为多个段（如主程序、子程序、数据），每个段再划分为多个页。
(b) 程序的段页表：系统为程序建立一个段表，段表的每个条目指向一个页表。

**图3.17 段页式管理方式**

在段页式系统中,进程的逻辑地址分为三部分:段号、页号和页内偏移量,如图3.18所示。

| 段号S | 页号P | 页内偏移量W |
| :---: | :---: | :---: |

**图3.18 段页式系统的逻辑地址结构**

为了实现地址变换,系统为每个进程建立一张段表,每个段对应一个段表项,每个段表项至少包括段号(隐含)、页表长度和页表始址;每个段有一张页表,每个页表项至少包括页号(隐含)和块号。此外,系统中还应有一个段表寄存器,指出进程的段表始址和段表长度(段表寄存器和页表寄存器的作用都有两个,一是在段表或页表中寻址,二是判断是否越界)。

①对于段共享的实现,在汤小丹老师的旧版教材中采用的方法是配置共享段表;但是,在新版教材中采用的方法是在每个进程的段表中设置一个段表项,指向被共享的同一个物理段。2019年的统考真题考查的是前述方法。

**注意**
在段页式存储管理中,每个进程的段表只有一个,而页表可能有多个。

在进行地址变换时,首先通过段表查到页表始址,然后通过页表找到物理块号,最后形成物理地址。如图3.19所示,进行一次访问实际需要三次访问主存,这里同样可以使用快表来加快查找速度,其关键字由段号、页号组成,值是对应的物理块号和保护码。

**图3.19 段页式系统的地址变换机构**
逻辑地址A (段号S, 页号P, 偏移量W)
1.  **查段表**：段号S -> 段表 -> 页表始址d, 页表长度C
2.  **查页表**：页号P -> 页表 -> 块号b
3.  **形成物理地址**：物理地址E = b + W

结合上面对段式和页式管理地址空间的分析,得出结论:段页式管理的地址空间是二维的。

### 3.1.6 本节小结
本节开头提出的问题的参考答案如下。
1) 为什么要进行内存管理?
在单道系统阶段,一个系统在一个时间段内只执行一个程序,内存的分配极其简单,即仅分配给当前运行的进程。引入多道程序后,进程之间共享的不仅仅是处理机,还有主存储器。然而,共享主存会形成一些特殊的挑战。若不对内存进行管理,则容易导致内存数据的混乱,以至于影响进程的并发执行。因此,为了更好地支持多道程序并发执行,必须进行内存管理。
2) 多级页表解决了什么问题?又会带来什么问题?
多级页表解决了当逻辑地址空间过大时,页表的长度会大大增加的问题。而采用多级页表时,一次访盘需要多次访问内存甚至磁盘,会大大增加一次访存的时间。
无论是段式管理、页式管理还是段页式管理,读者都只需要掌握下面三个关键问题:①逻辑地址结构,②页(段)表项结构,③寻址过程。搞清楚这三个问题,就相当于搞清楚了上面几种存储管理方式。再次提醒读者区分逻辑地址结构和表项结构。

### 3.1.7 本节习题精选
#### 一、单项选择题
**01. 下面关于存储管理的叙述中,正确的是( )。**
A. 存储保护的目的是限制内存的分配
B. 在内存为$M$、有$N$个用户的分时系统中,每个用户占用$M/N$的内存空间
C. 在虚拟内存系统中,只要磁盘空间无限大,作业就能拥有任意大的编址空间
D. 实现虚拟内存管理必须有相应硬件的支持

**02. 下列关于存储管理目标的说法中,错误的是( )。**
A. 为进程分配内存空间
B. 回收被进程释放的内存空间
C. 提高内存的利用率
D. 提高内存的物理存取速度

**03. 下列关于内存保护的描述中,不正确的是()。**
A. 一个进程不能未被授权就访问另外一个进程的内存空间
B. 内存保护可以仅通过操作系统(软件)来满足,不需要处理器(硬件)的支持
C. 内存保护的方法有界地址保护和上下限地址保护
D. 一个进程不能直接跳转到另一个进程的指令地址中

**04. 内存保护需要由( )完成,以保证进程空间不被非法访问。**
A. 操作系统
B. 硬件机构
C. 操作系统和硬件机构合作
D. 操作系统或者硬件机构独立完成

**05. 下列各种存储管理方式中,需要硬件地址变换机构的是()。**
I. 单一连续分配
II. 固定分区分配
III. 页式存储管理
IV. 动态分区分配
V. 页式虚拟存储管理
A. I、III、V
B. II、III、IV
C. III、IV、V
D. II、III、IV、V

**06. 在固定分区分配中,每个分区的大小是()。**
A. 随作业长度变化
B. 可以不同但预先固定
C. 相同
D. 可以不同但根据作业长度固定

**07. 在动态分区分配方案中,某一进程完成后,系统回收其主存空间并与相邻空闲区合并,为此需修改空闲区表,造成空闲区数减1的情况是( )。**
A. 无上邻空闲区也无下邻空闲区
B. 有上邻空闲区但无下邻空闲区
C. 有下邻空闲区但无上邻空闲区
D. 有上邻空闲区也有下邻空闲区

**08. 设内存的分配情况如下图所示。若要申请一块40KB的内存空间,采用最佳适应算法,则所得到的分区首址为( )。**

| 内存范围 | 状态 |
| :---: | :---: |
| 0 - 100K | 操作系统 |
| 100K - 180K | (空闲) |
| 180K - 190K | 占用 |
| 190K - 280K | (空闲) |
| 280K - 330K | 占用 |
| 330K - 390K | (空闲) |
| 390K - 410K | 占用 |
| 410K - 512K | (空闲) |

A. 100K
B. 190K
C. 330K
D. 410K

**09. 某分段存储管理系统中,段表内容如下表所示,逻辑地址的格式为(段号,段内偏移),则逻辑地址(2,154)对应的物理地址是( )。**

| 段号 | 段首址 | 段长度 |
| :---: | :---: | :---: |
| 0 | 120K | 40K |
| 1 | 760K | 30K |
| 2 | 480K | 20K |
| 3 | 370K | 20K |

A. 120K + 2
B. 480K + 154
C. 30K + 154
D. 480K + 2

**10. 动态重定位是在作业的( )中进行的。**
A. 编译过程
B. 装入过程
C. 链接过程
D. 执行过程

**11. 下列关于程序装入的动态重定位方式的描述中,错误的是()。**
A. 系统将程序装入内存后,程序在内存中的位置可能发生移动
B. 系统为每个进程分配一个重定位寄存器
C. 被访问单元的物理地址 = 逻辑地址 + 重定位寄存器的值
D. 逻辑地址到物理地址的映射过程在进程执行时发生

**12. 动态重定位的过程依赖于( )。**
I. 可重定位装入程序
II. 重定位寄存器
III. 地址变换机构
IV. 目标程序
A. I和II
B. II和III
C. I、II和III
D. I、II、III和IV

**13. 为保证一个程序在主存中被改变存放位置后仍能正确执行,应采用()。**
A. 静态重定位
B. 动态重定位
C. 动态分配
D. 静态分配

**14. 下面的存储管理方案中,( )方式可以采用静态重定位。**
A. 固定分区
B. 可变分区
C. 页式
D. 段式

**15. 对重定位存储管理方式,应()。**
A. 在整个系统中设置一个重定位寄存器
B. 为每道程序设置一个重定位寄存器
C. 为每道程序设置两个重定位寄存器
D. 为每道程序和数据都设置一个重定位寄存器

**16. 在可变分区管理中,采用拼接技术的目的是()。**
A. 合并空闲区
B. 合并分配区
C. 增加主存容量
D. 便于地址转换

**17. 某页式存储管理系统中,按字节编址,页表内容如下表所示。若页的大小为4KB,则地址转换机构将逻辑地址4097转换成的物理地址为( )。(地址均用十进制表示。) **

| 页号 | 页框号 |
| :---: | :---: |
| 0 | 2 |
| 1 | 1 |
| 3 | 0 |
| 4 | 5 |

A. 8193
B. 4097
C. 2049
D. 1025

**18. 不会产生内部碎片的存储管理是()。**
A. 分页式存储管理
B. 分段式存储管理
C. 固定分区式存储管理
D. 段页式存储管理

**19. 多进程在主存中彼此互不干扰的环境下运行,操作系统是通过()来实现的。**
A. 内存分配
B. 内存保护
C. 内存扩充
D. 地址映射

**20. 在动态分区分配存储管理中,随着时间的推移,会产生越来越多的小碎片,可通过紧凑技术解决,即操作系统不时地对进程进行移动和整理,则适合采用()装入技术。**
A. 绝对装入
B. 静态重定位
C. 动态重定位
D. 静态重定位和动态重定位

**21. 在动态分区分配存储管理中,不需要对空闲区链进行排序的分配算法是()。**
A. 首次适应法
B. 最佳适应法
C. 最差适应法
D. 都不需要

**22. 分区管理中采用最佳适应分配算法时,把空闲区按( )次序登记在空闲区表中。**
A. 长度递增
B. 长度递减
C. 地址递增
D. 地址递减

**23. 首次适应算法的空闲分区()。**
A. 按大小递减顺序连在一起
B. 按大小递增顺序连在一起
C. 按地址由小到大排列
D. 按地址由大到小排列

**24. 为了提高搜索空闲分区的速度,在大、中型系统中往往采用基于索引搜索的动态分区分配算法,以下不属于基于索引搜索的动态分区分配算法的是()。**
A. 快速适应算法
B. 伙伴系统
C. 哈希算法
D. 最佳适应算法

**25. 内存存储管理由连续分配方式发展为页式管理方式的主要动力是( )。**
A. 提高内存利用率
B. 提高系统吞吐量
C. 满足用户的需要
D. 更好的满足多道程序的需要

**26. 页式存储管理中的页表是由()建立的。**
A. 编译程序
B. 用户程序
C. 链接程序
D. 操作系统

**27. 在段式存储管理中,共享段是用来实现()的。**
A. 多个进程共享同一段代码或数据
B. 多个进程共享同一段物理内存空间
C. 多个进程共享同一段逻辑地址空间
D. 多个进程共享同一段号

**28. 在段式存储管理中,若一个进程有n个段,则该进程需要()个段表。**
A. n
B. n + 1
C. 1
D. 2

**29. 采用分页或分段管理后,提供给用户的物理地址空间( )。**
A. 分页支持更大的物理地址空间
B. 分段支持更大的物理地址空间
C. 不能确定
D. 一样大

**30. 分页系统中的页面是为( )。**
A. 用户所感知的
B. 操作系统所感知的
C. 编译系统所感知的
D. 装配程序所感知的

**31. 在页式存储管理中,页表的始地址存放在()中。**
A. 物理内存
B. 页表
C. 快表(TLB)
D. 页表寄存器

**32. 在页式存储管理中,当CPU形成一个有效地址时,查找页表的工作是由( )实现的。**
A. 操作系统
B. 页表查询程序
C. 硬件
D. 存储管理进程

**33. 采用段式存储管理时,一个程序如何分段是在( )时决定的。**
A. 分配主存
B. 用户编程
C. 装作业
D. 程序执行

**34. 下面的()方法有利于程序的动态链接。**
A. 分段存储管理
B. 分页存储管理
C. 可变式分区管理
D. 固定式分区管理

**35. 当前编程人员编写好的程序经过编译转换成目标文件后,各条指令的地址编号起始一般定为(①),称为(②)地址。**
① A. 1 B. 0 C. IP D. CS
② A. 绝对 B. 名义 C. 逻辑 D. 实

**36. 可重入程序是通过( )方法来改善系统性能的。**
A. 改变时间片长度
B. 改变用户数
C. 提高对换速度
D. 减少对换数量

**37. 操作系统实现( )存储管理的代价最小。**
A. 分区
B. 分页
C. 分段
D. 段页式

**38. 动态分区也称可变式分区,它是系统运行过程中( )动态建立的。**
A. 在作业装入时
B. 在作业创建时
C. 在作业完成时
D. 在作业未装入时

**39. 在页式存储管理中选择页面的大小,需要考虑下列( )因素。**
I. 页面大的好处是页表项比较少
II. 页面小的好处是可以减少由内碎片引起的内存浪费
III. 影响磁盘访问时间的主要因素通常不是页面大小,所以使用时优先考虑较大的页面
A. I和III
B. II和III
C. I和II
D. I、II和III

**40. 某个操作系统对内存的管理采用页式存储管理方法,所划分的页面大小( )。**
A. 要根据内存大小确定
B. 必须相同
C. 要根据CPU的地址结构确定
D. 要依据外存和内存的大小确定

**41. 引入段式存储管理方式,主要是为了更好地满足用户的一系列要求。下面选项中不属于这一系列要求的是( )。**
A. 方便操作
B. 方便编程
C. 共享和保护
D. 动态链接和增长

**42. 对主存储器的访问,( )。**
A. 以块(页)或段为单位
B. 以字节或字为单位
C. 随存储器的管理方案不同而异
D. 以用户的逻辑记录为单位

**43. 以下存储管理方式中,不适合多道程序设计系统的是( )。**
A. 单用户连续分配
B. 固定式分区分配
C. 可变式分区分配
D. 分页式存储管理方式

**44. 在分页存储管理中,主存的分配( )。**
A. 以页框为单位进行
B. 以作业的大小进行
C. 以物理段进行
D. 以逻辑记录大小进行

**45. 在段式分配中,CPU每次从内存中取一次数据需要( )次访问内存。**
A. 1
B. 3
C. 2
D. 4

**46. 在段页式分配中,CPU每次从内存中取一次数据需要( )次访问内存。**
A. 1
B. 3
C. 2
D. 4

**47. 采用段页式存储管理时,内存地址结构是()。**
A. 线性的
B. 二维的
C. 三维的
D. 四维的

**48. 在段页式存储管理中,地址映射表是()。**
A. 每个进程一张段表,两张页表
B. 每个进程的每个段一张段表,一张页表
C. 每个进程一张段表,每个段一张页表
D. 每个进程一张页表,每个段一张段表

**49. 操作系统采用分页存储管理方式,要求( )。**
A. 每个进程拥有一张页表,且进程的页表驻留在内存中
B. 每个进程拥有一张页表,但只有执行进程的页表驻留在内存中
C. 所有进程共享一张页表,以节约有限的内存空间,但页表必须驻留在内存中
D. 所有进程共享一张页表,只有页表中当前使用的页面必须驻留在内存中,以最大限度地节省有限的内存空间

**50. 在分段存储管理方式中,( )。**
A. 以段为单位,每段是一个连续存储区
B. 段与段之间必定不连续
C. 段与段之间必定连续
D. 每段是等长的

**51. 下列关于段式存储管理的叙述中,错误的是()。**
A. 段是逻辑结构上相对独立的程序块,因此段是可变长的
B. 按程序中实际的段来分配主存,所以分配后的存储块是可变长的
C. 每个段表项必须记录对应段在主存的起始位置和段的长度
D. 分段方式对低级语言程序员和编译器来说是透明的

**52. 段页式存储管理汲取了页式管理和段式管理的长处,其实现原理结合了页式和段式管理的基本思想,即()。**
A. 用分段方法来分配和管理物理存储空间,用分页方法来管理用户地址空间
B. 用分段方法来分配和管理用户地址空间,用分页方法来管理物理存储空间
C. 用分段方法来分配和管理主存空间,用分页方法来管理辅存空间
D. 用分段方法来分配和管理辅存空间,用分页方法来管理主存空间

**53. 以下存储管理方式中,会产生内部碎片的是()。**
I. 分段虚拟存储管理
II. 分页虚拟存储管理
III. 段页式分区管理
IV. 固定式分区管理
A. I、II、III
B. III、IV
C. 仅II
D. II、III、IV

**54. 下列关于页式存储管理的论述中,正确的是()。**
I. 若关闭TLB,则每存取一条指令或一个操作数都至少要访存2次
II. 页式存储管理不会产生内部碎片
III. 页式存储管理中的页面是为用户所能感知的
IV. 页式存储方式可以采用静态重定位
A. I、II、IV
B. I、IV
C. 仅I
D. 全都正确

**55. 在某分页存储管理系统中,地址结构长18位,其中11~17位为页号,0~10位为页内偏移量,则主存的最大容量为( )KB,主存可分为( )个页。若有一作业依次放入2、3、7号物理块,相对地址1500处有一条指令“store r1, 2500”,该指令地址所在页的页号为0,则指令的物理地址为( ),指令数据的存储地址所在页的页框号为( )。**
A. 256、256、5596、3
B. 256、128、5596、3
C. 256、128、5596、7
D. 256、128、3548、7

**56. 在某页式存储管理的系统中,主存容量为1MB,被分成256个页框,页框号为0,1,2,…,255。某作业的地址空间占用4页,其页号为0,1,2,3,被分配到主存的第2,4,1,5号页框中,则作业中的2号页在主存中的始址是()。**
A. 1
B. 1024
C. 2048
D. 4096

**57. 下列关于分页和分段的描述中,正确的是()。**
A. 分段是信息的逻辑单位,段长由系统决定
B. 引入分段的主要目的是实现离散分配并提高内存利用率
C. 分页是信息的物理单位,页长由用户决定
D. 页面在物理内存中只能从页面大小的整数倍地址开始存放

**58. 在采用页式存储管理的系统中,逻辑地址空间大小为256TB,页表项大小为8B,页面大小为4KB,则该系统中的页表应该采用()级页表。**
A. 2
B. 3
C. 4
D. 5

**59. 若对经典的页式存储管理方式的页表做出稍微改造,允许不同页表的页表项指向同一个页帧,则可能的结果有()。**
I. 可以实现对可重入代码的共享
II. 只需修改页表项,就能实现内存“复制”操作
III. 容易发生越界访问
IV. 可以实现进程间通信
A. I、II、IV
B. II、III
C. I、II、III
D. 仅I

**60. 【2009统考真题】分区分配内存管理方式的主要保护措施是( )。**
A. 界地址保护
B. 程序代码保护
C. 数据保护
D. 栈保护

**61. 【2009统考真题】一个分段存储管理系统中,地址长度为32位,其中段号占8位,则最大段长是( )。**
A. $2^8$B
B. $2^{16}$B
C. $2^{24}$B
D. $2^{32}$B

**62. 【2010统考真题】某基于动态分区存储管理的计算机,其主存容量为55MB(初始为空),采用最佳适配(Best Fit)算法,分配和释放的顺序为:分配15MB,分配30MB,释放15MB,分配8MB,分配6MB,此时主存中最大空闲分区的大小是()。**
A. 7MB
B. 9MB
C. 10MB
D. 15MB

**63. 【2010统考真题】某计算机采用二级页表的分页存储管理方式,按字节编址,页大小为$2^{10}$B,页表项大小为2B,逻辑地址结构为**

| 页目录号 | 页号 | 页内偏移量 |
| :---: | :---: | :---: |

**逻辑地址空间大小为$2^{16}$页,则表示整个逻辑地址空间的页目录表中包含表项的个数至少是()。**
A. 64
B. 128
C. 256
D. 512

**64. 【2011统考真题】在虚拟内存管理中,地址变换机构将逻辑地址变换为物理地址,形成该逻辑地址的阶段是( )。**
A. 编辑
B. 编译
C. 链接
D. 装载

**65. 【2014统考真题】下列选项中,属于多级页表优点的是( )。**
A. 加快地址变换速度
B. 减少缺页中断次数
C. 减少页表项所占字节数
D. 减少页表所占的连续内存空间

**66. 【2016统考真题】某进程的段表内容如下所示。**

| 段号 | 段长 | 内存起始地址 | 权限 | 状态 |
| :---: | :---: | :---: | :---: | :---: |
| 0 | 100 | 6000 | 只读 | 在内存 |
| 1 | 200 | | 读写 | 不在内存 |
| 2 | 300 | 4000 | 读写 | 在内存 |

**访问段号为2、段内地址为400的逻辑地址时,进行地址转换的结果是()。**
A. 段缺失异常
B. 得到内存地址4400
C. 越权异常
D. 越界异常

**67. 【2017统考真题】某计算机按字节编址,其动态分区内存管理采用最佳适应算法,每次分配和回收内存后都对空闲分区链重新排序。当前空闲分区信息如下表所示。**

| 分区始址 | 20K | 500K | 1000K | 200K |
| :---: | :---: | :---: | :---: | :---: |
| 分区大小 | 40KB | 80KB | 100KB | 200KB |

**回收始址为60K、大小为140KB的分区后,系统中空闲分区的数量、空闲分区链第一个分区的始址和大小分别是()。**
A. 3, 20K, 380KB
B. 3, 500K, 80KB
C. 4, 20K, 180KB
D. 4, 500K, 80KB

**68. 【2019统考真题】在分段存储管理系统中,用共享段表描述所有被共享的段。若进程 P₁和P₂共享段S,则下列叙述中,错误的是( )。**
A. 在物理内存中仅保存一份段S的内容
B. 段S在P₁和P₂中应该具有相同的段号
C. P₁和P₂共享段S在共享段表中的段表项
D. P₁和P₂都不再使用段S时才回收段S所占的内存空间

**69. 【2019统考真题】某计算机主存按字节编址,采用二级分页存储管理,地址结构如下:**

| 页目录号(10位) | 页号(10位) | 页内偏移(12位) |
| :---: | :---: | :---: |

**虚拟地址 2050 1225H对应的页目录号、页号分别是( )。**
A. 081H, 101H
B. 081H, 401H
C. 201H, 101H
D. 201H, 401H

**70. 【2019统考真题】在下列动态分区分配算法中,最容易产生内存碎片的是( )。**
A. 首次适应算法
B. 最坏适应算法
C. 最佳适应算法
D. 循环首次适应算法

**71. 【2021统考真题】在采用二级页表的分页系统中,CPU页表基址寄存器中的内容是()。**
A. 当前进程的一级页表的起始虚拟地址
B. 当前进程的一级页表的起始物理地址
C. 当前进程的二级页表的起始虚拟地址
D. 当前进程的二级页表的起始物理地址

**72. 【2023 统考真题】进程R和S共享数据 data,若data在R和S中所在页的页号分别为p1和p2,两个页所对应的页框号分别为f1和f2,则下列叙述中,正确的是( )。**
A. p1和p2一定相等,f1和f2一定相等
B. p1和p2一定相等,f1和f2不一定相等
C. p1和p2不一定相等,f1和f2一定相等
D. p1和p2不一定相等,f1和f2不一定相等

**73. 【2024统考真题】下列算法中,每次回收分区时仅合并大小相等的空闲分区的是()。**
A. 伙伴算法
B. 最佳适应算法
C. 最坏适应算法
D. 首次适应算法

#### 二、综合应用题
**01. 某系统的空闲分区见下表,采用动态分区管理策略,现有如下作业序列:96KB, 20KB, 200KB。若用首次适应算法和最佳适应算法来处理这些作业序列,则哪种算法能满足该作业序列请求?为什么?**

| 分区号 | 大小 | 始址 |
| :---: | :---: | :---: |
| 1 | 32KB | 100K |
| 2 | 10KB | 150K |
| 3 | 5KB | 200K |
| 4 | 218KB | 220K |
| 5 | 96KB | 530K |

**02. 某操作系统采用段式管理,用户区主存为512KB,空闲块链入空块表,分配时截取空块的前半部分(小地址部分)。初始时全部空闲。执行申请、释放操作序列 reg(300KB), reg(100KB), release(300KB), reg(150KB), reg(50KB), reg(90KB)后:**
1) 采用最先适配,空块表中有哪些空块?(指出大小及始址)
2) 采用最佳适配,空块表中有哪些空块?(指出大小及始址)
3) 若随后又要申请80KB,针对上述两种情况会产生什么后果?这说明了什么问题?

**03. 下图给出了页式和段式两种地址变换示意(假定段式变换对每段不进行段长越界检查,即段表中无段长信息)。**

**图1 (页式)**
控制寄存器 -> 逻辑地址 (3, 586)
- 3 -> | 10: 1000 | 15: 7000 | 7: 2500 | 12: 4000 | 20: 8500 | -> 物理地址
**图2 (段式)**
控制寄存器 -> 逻辑地址 (3, 586)
- 3 -> | 10: 物理地址 | 15: 物理地址 | 7: 物理地址 | 12: 物理地址 | 20: 物理地址 | -> 物理地址

1) 指出这两种变换各属于何种存储管理。
2) 计算出这两种变换所对应的物理地址。

**04. 在一个段式存储管理系统中,其段表见下表A。试求表B中的逻辑地址所对应的物理地址。**

**表A 段表**

| 段号 | 内存始址 | 段长 |
| :---: | :---: | :---: |
| 0 | 210 | 500 |
| 1 | 2350 | 20 |
| 2 | 100 | 90 |
| 3 | 1350 | 590 |
| 4 | 1938 | 95 |

**表B 逻辑地址**

| 段号 | 段内位移 |
| :---: | :---: |
| 0 | 430 |
| 1 | 10 |
| 2 | 500 |
| 3 | 400 |
| 4 | 112 |
| 5 | 32 |

**05. 页式存储管理允许用户的编程空间为32个页面(每页1KB),主存为16KB。如有一用户程序为10页长,且某个时刻该用户程序页表见下表。**

| 逻辑页号 | 物理块号 |
| :---: | :---: |
| 0 | 8 |
| 1 | 7 |
| 2 | 4 |
| 3 | 10 |

若分别遇到三个逻辑地址0AC5H, 1AC5H, 3AC5H处的操作,计算并说明存储管理系统将如何处理。

**06. 在某页式管理系统中,假定主存为64KB,分成16个页框,页框号为0,1,2,…,15。设某进程有4页,其页号为0,1,2,3,被分别装入主存的第9,0,1,14号页框。**
1) 该进程的总长度是多大?
2) 写出该进程每页在主存中的始址。
3) 若给出逻辑地址(0,0), (1,72), (2,1023), (3,99),请计算出相应的内存地址(括号内的第一个数为十进制页号,第二个数为十进制页内地址)。

**07. 某操作系统存储器采用页式存储管理,页面大小为64B,假定一进程的代码段的长度为702B,页表见表A,该进程在快表中的页表见表B。现进程有如下访问序列:其逻辑地址为八进制的0105, 0217, 0567, 01120, 02500。试问给定的这些地址能否进行转换?**

**表A 进程页表**

| 页号 | 页帧号 | 页号 | 页帧号 |
| :---: | :---: | :---: | :---: |
| 0 | F0 | 6 | F6 |
| 1 | F1 | 7 | F7 |
| 2 | F2 | 8 | F8 |
| 3 | F3 | 9 | F9 |
| 4 | F4 | 10 | F10 |
| 5 | F5 | 6 | F6 |

**表B 快表**

| 页号 | 页帧号 |
| :---: | :---: |
| 0 | F0 |
| 1 | F1 |
| 2 | F2 |
| 3 | F3 |
| 4 | F4 |

**08. 在某页式系统中,假设在查找主存页表的过程中不发生缺页的情况,请回答:**
1) 若对主存的一次存取需1.5µs,问实现一次页面访问时存取时间是多少?
2) 若系统有快表且其平均命中率为85%,而页表项在快表中的查找时间可忽略不计,试问此时的存取时间为多少?

**09. 在页式、段式和段页式存储管理中,假设不发生缺页异常,当访问一条指令或数据时,各需要访问内存几次?其过程如何?假设一个页式存储系统具有快表,多数活动页表项都可以存在其中。若页表存放在内存中,内存访问时间是1µs,检索快表的时间为0.2µs,若快表的命中率是85%,则有效存取时间是多少?若快表的命中率为50%,则有效存取时间是多少?**

**10. 在一个分页存储管理系统中,地址空间分页(每页1KB),物理空间分块,设主存总容量是256KB,描述主存分配情况的位示图如下图所示(0表示未分配,1表示已分配),此时作业调度程序选中一个长为5.2KB的作业投入内存。试问:**
1) 为该作业分配内存后(分配内存时,首先分配低地址的内存空间),请填写该作业的页表内容。
2) 页式存储管理有无内存碎片存在?若有,会存在哪种内存碎片?为该作业分配内存后,会产生内存碎片吗?若产生,则大小为多少?
3) 假设一个64MB内存容量的计算机,采用页式存储管理(页面大小为4KB),内存分配采用位示图方式管理,请问位示图将占用多大的内存?

**位示图**
1111111111111111
1111101111100011
1100000000001111
1111100001000101
0101101101101101
1000000000000000
0111111000000000
1...................

**作业页表**

| 页号 | 块号(从0开始编址) |
| :--: | :---: |
| | |
| | |
| | |
| | |
| | |
| | |

**11. 【2013统考真题】某计算机主存按字节编址,逻辑地址和物理地址都是32位,页表项大小为4B。请回答下列问题:**
1) 若使用一级页表的分页存储管理方式,逻辑地址结构为

| 页号(20位) | 页内偏移量(12位) |
| :---: | :---: |

则页的大小是多少字节?页表最大占用多少字节?
2) 若使用二级页表的分页存储管理方式,逻辑地址结构为

| 页目录号(10位) | 页表索引(10位) | 页内偏移量(12位) |
| :---: | :---: | :---: |

设逻辑地址为LA,请分别给出其对应的页目录号和页表索引的表达式。
3) 采用1)中的分页存储管理方式,一个代码段的起始逻辑地址为0000 8000H,其长度为8KB,被装载到从物理地址0090 0000H开始的连续主存空间中。页表从主存0020 0000H开始的物理地址处连续存放,如下图所示(地址大小自下向上递增)。请计算出该代码段对应的两个页表项的物理地址、这两个页表项中的页框号,以及代码页面2的起始物理地址。

**内存布局图**
...
(物理地址3) -> 代码页面2
(物理地址2) -> 代码页面1 | 0090 0000H
...
(物理地址1) -> 页表 | 0020 0000H
...

---
## 3.2 虚拟内存管理
在学习本节时,请读者思考以下问题:
1) 为什么要引入虚拟内存?
2) 虚拟内存空间的大小由什么因素决定?
3) 虚拟内存是怎么解决问题的?会带来什么问题?
读者要掌握虚拟内存解决问题的思想,了解各种替换算法的优劣,掌握虚实地址的变换方法。

### 3.2.1 虚拟内存的基本概念
1. 传统存储管理方式的特征
3.1节讨论的各种内存管理策略都是为了同时将多个进程保存在内存中,以便允许进行多道程序设计。它们都具有以下两个共同的特征:
1) **一次性**。作业必须一次性全部装入内存后,才能开始运行。这会导致两个问题:①当作业很大而不能全部被装入内存时,将使该作业无法运行;②当大量作业要求运行时,由于内存不足以容纳所有作业,只能使少数作业先运行,导致多道程序并发度的下降。
2) **驻留性**。作业被装入内存后,就一直驻留在内存中,其任何部分都不会被换出,直至作业运行结束。运行中的进程会因等待I/O而被阻塞,可能处于长期等待状态。
由以上分析可知,许多在程序运行中不用或暂时不用的程序(数据)占据了大量的内存空间,