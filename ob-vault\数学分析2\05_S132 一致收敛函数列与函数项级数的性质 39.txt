### §2 一致收敛函数列与函数项级数的性质

$u(x) - u_2(x) + u_3(x) - u_4(x) + \cdots$
在$[a,b]$上不仅收敛,而且一致收敛.

13. 证明:若${f_n(x)}$在区间$I$上一致收敛于0,则存在子列${f_{n_k}}$,使得$\sum f_{n_k}(x)$在$I$上一致收敛.

### §2 一致收敛函数列与函数项级数的性质

本节讨论由函数列与函数项级数所确定的函数的连续性、可积性与可微性.

**定理 13.8** 设函数列${f_n}$在$(a,x_0) \cup (x_0,b)$上一致收敛于$f(x)$,且对每个$n$, $\lim_{x \to x_0} f_n(x) = a_n$,则$\lim_{n \to \infty} a_n$和$\lim_{x \to x_0} f(x)$均存在且相等.

**证** 先证${a_n}$是收敛数列. 对任意$\epsilon>0$,由于${f_n}$一致收敛,故有$N$,当$n>N$和任意正整数$p$,对一切$x \in (a,x_0) \cup (x_0,b)$有
$$
|f_n(x) - f_{n+p}(x)| < \epsilon.
\tag{1}
$$
从而
$$
|a_n - a_{n+p}| = \lim_{x \to x_0} |f_n(x) - f_{n+p}(x)| \le \epsilon.
$$
这样由柯西准则可知${a_n}$是收敛数列.
设$\lim_{n \to \infty} a_n=A$. 再证$\lim_{x \to x_0} f(x) = A$.
由于$f_n(x)$一致收敛于$f(x)$及$a_n$收敛于$A$,因此对任意$\epsilon>0$,存在正数$N$,当$n>N$时,对任意$x \in (a,x_0) \cup (x_0,b)$,
$$
|f_n(x)-f(x)| < \frac{\epsilon}{3} \quad \text{和} \quad |a_n - A| < \frac{\epsilon}{3}
$$
同时成立. 特别取$n=N+1$,有
$$
|f_{N+1}(x) - f(x)| < \frac{\epsilon}{3}, \quad |a_{N+1} - A| < \frac{\epsilon}{3}.
$$
又$\lim_{x \to x_0} f_{N+1}(x)=a_{N+1}$,故存在$\delta>0$,当$0<|x-x_0|<\delta$时,
$$
|f_{N+1}(x) - a_{N+1}| < \frac{\epsilon}{3}
$$
这样,当$x$满足$0<|x-x_0|<\delta$时,
$$
\begin{aligned}
|f(x) - A| &\le |f(x) - f_{N+1}(x)| + |f_{N+1}(x) - a_{N+1}| + |a_{N+1} - A| \\
&< \frac{\epsilon}{3} + \frac{\epsilon}{3} + \frac{\epsilon}{3} = \epsilon,
\end{aligned}
$$
即$\lim_{x \to x_0} f(x) = A$.
□

这个定理指出:在一致收敛的条件下,${f_n(x)}$中两个独立变量$x$与$n$,在分别求极限时其求极限的顺序可以交换,即
$$
\lim_{n \to \infty} \lim_{x \to x_0} f_n(x) = \lim_{x \to x_0} \lim_{n \to \infty} f_n(x).
\tag{2}
$$
类似地,若$f_n(x)$在$(a,b)$上一致收敛且$\lim_{x \to a^+} f_n(x)$存在,可推得$\lim_{n \to \infty} \lim_{x \to a^+} f_n(x) = \lim_{x \to a^+} \lim_{n \to \infty} f_n(x)$;若$f_n(x)$在$(a,b)$上一致收敛和$\lim_{x \to b^-} f_n(x)$存在,则可推得$\lim_{n \to \infty} \lim_{x \to b^-} f_n(x) = \lim_{x \to b^-} \lim_{n \to \infty} f_n(x)$.

由定理13.8可得到以下定理.

**定理 13.9(连续性)** 若函数列${f_n}$在区间$I$上一致收敛,且每一项都连续,则其极限函数$f$在$I$上也连续.

**证** 设$x_0$为$I$上任一点.由于$\lim_{x \to x_0} f_n(x)=f_n(x_0)$,于是由定理13.8知$\lim_{x \to x_0} f(x)$亦存在,且$\lim_{x \to x_0} f(x) = \lim_{n \to \infty} f_n(x_0)=f(x_0)$,因此$f(x)$在$x_0$上连续.
□

由定理13.9可知:若各项为连续函数的函数列在区间$I$上其极限函数不连续,则此函数列在区间$I$上不一致收敛.

例如:函数列${x^n}$的各项在$(-1,1]$上都是连续的,但其极限函数
$$
f(x) = \begin{cases} 0, & -1 < x < 1, \\ 1, & x=1 \end{cases}
$$
在$x=1$时不连续,从而推得${x^n}$在$(-1,1]$上不一致收敛.
注意到函数$f(x)$在$x$上连续仅与它在$x$的近旁的性质有关,因此由定理13.9可得以下推论.

**推论** 若连续函数列${f_n}$在区间$I$上内闭一致收敛于$f$,则$f$在$I$上连续.

上节例1中${f_n(x)} = {x^n}$在$(-1,1)$上不一致收敛,但内闭一致收敛,其极限函数在$(-1,1)$上连续.

**定理 13.10(可积性)** 若函数列${f_n}$在$[a,b]$上一致收敛,且每一项都连续,则
$$
\int_a^b \lim_{n \to \infty} f_n(x)dx = \lim_{n \to \infty} \int_a^b f_n(x)dx.
\tag{3}
$$

**证** 设$f$为函数列${f_n}$在$[a,b]$上的极限函数.由定理13.9,$f$在$[a,b]$上连续,从而$f_n(n=1,2,\cdots)$与$f$在$[a,b]$上都可积.
因为在$[a,b]$上$f_n \rightrightarrows f(n \to \infty)$,故对任给正数$\epsilon$,存在$N$,当$n>N$时,对一切$x \in [a,b]$,都有
$$
|f_n(x)-f(x)| < \epsilon.
$$
再根据定积分的性质,当$n>N$时有
$$
\left| \int_a^b f_n(x)dx - \int_a^b f(x)dx \right| = \left| \int_a^b (f_n(x) - f(x))dx \right| \le \int_a^b |f_n(x) - f(x)|dx
$$
$$
\le \epsilon(b-a).
$$
这就证明了等式(3).
□

这个定理指出:在一致收敛的条件下,极限运算与积分运算的顺序可以交换.

**例 1** 设函数
$$
f_n(x) = \begin{cases} 2n\alpha_n x, & 0 < x < \frac{1}{2n}, \\ 2\alpha_n - 2n\alpha_n x, & \frac{1}{2n} \le x < \frac{1}{n}, \\ 0, & \frac{1}{n} \le x \le 1, \end{cases} \quad n=1,2,\cdots
$$
其图像如图13-4所示.
显然${f_n(x)}$是$[0,1]$上的连续函数列,且对任意$x \in [0,1]$, $\lim_{n \to \infty}f_n(x)=0$.又$\sup_{x \in [0,1]}|f_n(x)-0| = \alpha_n$,因此${f_n(x)}$在$[0,1]$上一致收敛于$0$的充要条件是$\alpha_n \to 0 (n \to \infty)$.

[图 13-4]

由于$\int_0^1 f_n(x)dx = \frac{\alpha_n}{2n}$,因此$\int_0^1 f_n(x)dx \to \int_0^1 f(x)dx=0$的充要条件是$\lim_{n \to \infty} \frac{\alpha_n}{2n} = 0$.这样当$\alpha_n=1$时,虽然${f_n(x)}$不一致收敛于$f(x)$,但定理13.10的结论仍成立.但当$\alpha_n=n$时,${f_n(x)}$不一致收敛于$f(x)$,且$\int_0^1 f_n(x)dx = \frac{1}{2}$也不收敛于$\int_0^1 f(x)dx=0$.
□

例1说明当${f_n(x)}$收敛于$f(x)$时,一致收敛性是极限运算与积分运算交换的充分条件,但不是必要条件.

**定理 13.11(可微性)** 设${f_n}$为定义在$[a,b]$上的函数列,若$x_0 \in [a,b]$为${f_n(x_0)}$的收敛点,${f_n}$的每一项在$[a,b]$上有连续的导数,且${f_n'}$在$[a,b]$上一致收敛,则
$$
\frac{d}{dx} \left( \lim_{n \to \infty} f_n(x) \right) = \lim_{n \to \infty} \frac{d}{dx} f_n(x).
\tag{4}
$$

**证** 设$f_n(x_0) \to A (n \to \infty)$, $f_n' \rightrightarrows g (n \to \infty)$, $x \in [a,b]$.我们要证明函数列${f_n}$在区间$[a,b]$上收敛,且其极限函数的导数存在且等于$g$.
由定理条件,对任一$x \in [a,b]$,总有
$$
f_n(x) = f_n(x_0) + \int_{x_0}^x f_n'(t)dt.
$$
当$n \to \infty$时,右边第一项极限为$A$,第二项极限为$\int_{x_0}^x g(t)dt$ (定理13.10),所以左边极限存在,记为$f$,则有
$$
f(x) = \lim_{n \to \infty} f_n(x) = f(x_0) + \int_{x_0}^x g(t)dt,
$$
其中$f(x_0)=A$.由$g$的连续性及微积分学基本定理(第九章§5)推得
$$
f' = g.
$$
这就证明了等式(4).
□

在定理13.11的条件下,还可推出在$[a,b]$上$f_n \rightrightarrows f (n \to \infty)$,请读者自己证明.
由于函数的可微性是函数的局部性质,故以下推论成立.

**推论** 设函数列${f_n}$定义在区间$I$上,若$x_0 \in I$为${f_n}$的收敛点,且${f_n'}$在$I$上内闭一致收敛,则$f$在$I$上可导,且$f'(x) = \lim_{n \to \infty} f_n'(x)$.

**例 2** 函数列
$$
f_n(x) = \frac{1}{2n}\ln(1+n^2x^2), \quad n=1,2,\cdots
$$
与
$$
f_n'(x) = \frac{nx}{1+n^2x^2}, \quad n=1,2,\cdots
$$
在$[0,1]$上都收敛于0,由于
$$
\lim_{n \to \infty} \max_{x \in [0,1]} |f_n'(x) - f'(x)| = \frac{1}{2},
$$
所以导函数列${f_n'(x)}$在$[0,1]$上不一致收敛,但对任意$\delta>0$,
$$
\sup_{x \in [\delta,1]} |f_n'(x)-f'(x)| \le \frac{n}{1+n^2\delta} \to 0 \quad (n \to \infty),
$$
所以${f_n'}$在$(0,1]$上内闭一致收敛.由推论,在$(0,1]$上,$\lim_{n \to \infty} f_n'(x) = (\lim_{n \to \infty} f_n(x))'$仍成立.
事实上,$\lim_{n \to \infty} f_n'(x) = 0$, $\lim_{n \to \infty} f_n(x) = 0$, $\lim_{n \to \infty} f_n'(x) = (\lim_{n \to \infty} f_n(x))'$在$[0,1]$上都成立.
□

此例说明,一致收敛性是极限运算与求导运算交换的充分条件,而不是必要条件.
现在再来讨论定义在区间$[a,b]$上函数项级数
$$
u_1(x) + u_2(x) + \cdots + u_n(x) + \cdots
\tag{5}
$$
的连续性、逐项求积与逐项求导的性质,这些性质可由函数列的相应性质推出.

**定理 13.12(连续性)** 若函数项级数$\sum u_n(x)$在区间$[a,b]$上一致收敛,且每一项都连续,则其和函数在$[a,b]$上也连续.

这个定理指出:在一致收敛条件下,(无限项)求和运算与求极限运算可以交换顺序,即
$$
\sum \left( \lim_{x \to x_0} u_n(x) \right) = \lim_{x \to x_0} \left( \sum u_n(x) \right).
\tag{6}
$$

**定理 13.13(逐项求积)** 若函数项级数$\sum u_n(x)$在$[a,b]$上一致收敛,且每一项$u_n(x)$都连续,则
$$
\sum \int_a^b u_n(x)dx = \int_a^b \sum u_n(x)dx.
\tag{7}
$$

**定理 13.14(逐项求导)** 若函数项级数$\sum u_n(x)$在$[a,b]$上每一项都有连续的导函数,$x_0 \in [a,b]$为$\sum u_n(x)$的收敛点,且$\sum u_n'(x)$在$[a,b]$上一致收敛,则
$$
\sum \left( \frac{d}{dx} u_n(x) \right) = \frac{d}{dx} \left( \sum u_n(x) \right).
\tag{8}
$$

定理13.13和定理13.14指出,在一致收敛条件下,逐项求积或求导后求和等于求和后再求积或求导.
最后,我们指出,本节中六个定理的意义不只是检验函数列或函数项级数是否满足关系式(2)—(4),(6)—(8),更重要的是根据定理的条件,即使没有求出极限函数或和函数,也能由函数列或函数项级数本身获得极限函数或和函数的解析性质.

**例 3** 设
$$
u_n(x) = \frac{1}{n^3}\ln(1+n^2x^2), \quad n=1,2,\cdots.
$$
证明函数项级数$\sum u_n(x)$在$[0,1]$上一致收敛,并讨论其和函数在$[0,1]$上的连续性、可积性与可微性.

**证** 对每一个$n$,易见$u_n(x)$为$[0,1]$上增函数,故有
$$
u_n(x) \le u_n(1) = \frac{1}{n^3}\ln(1+n^2), \quad n=1,2,\cdots.
$$
又当$t \ge 1$时,有不等式$\ln(1+t^2) < t$,所以
$$
u_n(x) \le \frac{1}{n^3}\ln(1+n^2) < \frac{1}{n^3} \cdot n = \frac{1}{n^2}, \quad n=1,2,\cdots.
$$
以收敛级数$\sum \frac{1}{n^2}$为$\sum u_n(x)$的优级数,推得$\sum u_n(x)$在$[0,1]$上一致收敛.
由于每一个$u_n(x)$在$[0,1]$上连续,根据定理13.12与定理13.13, $\sum u_n(x)$的和函数$S(x)$在$[0,1]$上连续且可积.又由
$$
u_n'(x) = \frac{2x}{n(1+n^2x^2)} \le \frac{2nx}{n^2(1+n^2x^2)} = \frac{1}{n^{3/2}}, \quad n=1,2,\cdots,
$$
即$\sum\frac{1}{n^{3/2}}$也是$\sum u_n'(x)$的优级数,故$\sum u_n'(x)$也在$[0,1]$上一致收敛.由定理13.14,得$S(x)$在$[0,1]$上可微.
□

与函数列的情况相同,定理13.12和定理13.14中的一致收敛的条件可以减弱为内闭一致收敛.

**例 4** 证明:函数$\zeta(x) = \sum_{n=1}^\infty \frac{1}{n^x}$在$(1,+\infty)$上有连续的各阶导函数.

**证** 设$u_n(x)=\frac{1}{n^x}$,则$u_n^{(k)}(x) = (-1)^k \frac{\ln^k n}{n^x}, k=1,2,\cdots$. 设$[a,b] \subset (1,+\infty)$,对任意$x \in [a,b]$,有
$$
|u_n^{(k)}(x)| = \frac{\ln^k n}{n^x} \le \frac{\ln^k n}{n^a}, \quad k=1,2,\cdots.
$$
由于$\lim_{n \to \infty} \frac{\ln^k n}{n^{(a-1)/2}} = 0$,因此充分大的$n$,$\frac{\ln^k n}{n^{(a-1)/2}} < 1$,从而
$$
\frac{\ln^k n}{n^a} = \frac{\ln^k n}{n^{(a-1)/2}} \frac{1}{n^{(a+1)/2}} < \frac{1}{n^{(a+1)/2}}.
$$
因为$\sum_{n=1}^\infty \frac{1}{n^{(a+1)/2}}$收敛,所以$\sum_{n=1}^\infty u_n^{(k)}(x)$在$[a,b]$上一致收敛,于是$\sum_{n=1}^\infty u_n(x)$在$(1,+\infty)$上内闭一致收敛.对$\sum_{n=1}^\infty (-1)^k \frac{\ln^k n}{n^x}$用定理13.12和定理13.14,$\zeta(x)$在$(1,+\infty)$上有连续的各阶导函数,且$\zeta^{(k)}(x) = \sum_{n=1}^\infty (-1)^k \frac{\ln^k n}{n^x}, k=1,2,\cdots$.
□

### 习 题

1. 讨论下列各函数列${f_n}$在所定义的区间上:
   (a) ${f_n}$与${|f_n|}$的一致收敛性;
   (b) ${f_n}$是否有定理13.9,13.10,13.11的条件与结论.
   (1) $f_n(x) = \frac{2x+n}{x+n}, x \in [0,b]$; (2) $f_n(x)=x^n-x^{2n}, x \in [0,1]$;
   (3) $f_n(x) = nxe^{-nx}, x \in [0,1]$.
2. 证明:若函数列${f_n}$在$[a,b]$上满足定理13.11的条件,则${f_n}$在$[a,b]$上一致收敛.
3. 证明定理13.12和定理13.14.
4. 设$S(x)=\sum_{n=1}^\infty \frac{x^{n-1}}{n^3}, x \in [-1,1]$,计算积分$\int_{-1}^x S(t)dt$.
5. 设$S(x)=\sum_{n=1}^\infty \frac{\cos nx}{n\sqrt{n}}, x \in (-\infty, +\infty)$,计算积分$\int_0^\pi S(t)dt$.
6. 设$S(x)=\sum_{n=1}^\infty ne^{-nx}, x > 0$,计算$\int_{\ln 2}^{\ln 3} S(t)dt$.
7. 证明:函数$f(x) = \sum_{n=1}^\infty \frac{\sin nx}{n^3}$在$(-\infty,+\infty)$上连续,且有连续的导函数.
8. 证明:定义在$[0,2\pi]$上的函数项级数$\sum_{n=0}^\infty r^n \cos nx (0<r<1)$满足定理13.13条件,且$\int_0^{2\pi} (\sum_{n=0}^\infty r^n \cos nx)dx = 2\pi$.
9. 讨论下列函数列在所定义区间上的一致收敛性及极限函数的连续性、可微性和可积性:
   (1) $f_n(x) = xe^{-nx^2}, n=1,2,\dots, x \in [-1,1]$;
   (2) $f_n(x) = \frac{nx}{nx+1}, n=1,2,\dots$,
       (i) $x \in [0, +\infty)$, (ii) $x \in [a, +\infty) (a>0)$.
10. 设$f$在$(-\infty, +\infty)$上有任何阶导数,记$F_n = f^{(n)}$,且在任何有限区间内$F_n \rightrightarrows \varphi (n \to \infty)$,试证$\varphi(x)=ce^x$($c$为常数).

### 总练习题

1.  试问$k$为何值时,下列函数列${f_n}$一致收敛:
    (1) $f_n(x) = xn^k e^{-nx}, 0 \le x < +\infty$;
    (2)
    $$
    f_n(x) = \begin{cases} xn^k, & 0 \le x \le \frac{1}{n} \\ \left(\frac{2}{n}-x\right)n^k, & \frac{1}{n} < x \le \frac{2}{n} \\ 0, & \frac{2}{n} < x \le 1. \end{cases}
    $$
2.  证明:(1)若$f_n(x) \rightrightarrows f(x) (n \to \infty), x \in I$,且$|f_n|$在$I$上有界,则${f_n}$至多除有限项外在$I$上是一致有界的;
    (2)若$f_n(x) \to f(x) (n \to \infty), x \in I$,且对每个正整数$n$, $f_n$在$I$上有界,则$f$在$I$上一致有界.
3.  设$f$为$[\frac{1}{2},1]$上的连续函数,证明:
    (1) ${x^n f(x)}$在$[\frac{1}{2}, 1]$上收敛;
    (2) ${x^n f(x)}$在$[\frac{1}{2}, 1]$上一致收敛的充要条件是$f(1)=0$.
4.  若把定理13.10中一致收敛函数列${f_n}$的每一项在$[a,b]$上连续改为在$[a,b]$上可积,试证${f_n}$在$[a,b]$上的极限函数在$[a,b]$上也可积.
5.  设级数$\sum a_n$收敛,证明
    $$
    \lim_{x \to 1^-} \sum_{n=0}^\infty a_n x^n = \sum_{n=0}^\infty a_n.
    $$
6.  设可微函数列${f_n}$在$[a,b]$上收敛,${f_n'}$在$[a,b]$上一致有界,证明:${f_n}$在$[a,b]$上一致收敛.
7.  设连续函数列${f_n(x)}$在$[a,b]$上一致收敛于$f(x)$,而$g(x)$在$(-\infty, +\infty)$上连续. 证明:${g(f_n(x))}$在$[a,b]$上一致收敛于$g(f(x))$.

## 第十四章 幂级数

### §1 幂级数

本章将讨论由幂函数序列${a_n(x-x_0)^n}$所产生的函数项级数
$$
\sum_{n=0}^\infty a_n(x-x_0)^n = a_0 + a_1(x-x_0) + a_2(x-x_0)^2 + \cdots + a_n(x-x_0)^n + \cdots,
\tag{1}
$$
它称为幂级数,是一类最简单的函数项级数,从某种意义上说,它也可以看作是多项式函数的延伸.幂级数在理论和实际上都有很多应用,特别在应用它表示函数方面,使我们对它的作用有许多新的认识.
下面将着重讨论$x_0=0$,即
$$
\sum_{n=0}^\infty a_n x^n = a_0 + a_1 x + a_2 x^2 + \cdots + a_n x^n + \cdots
\tag{2}
$$
的情形,因为只要把(2)中的$x$换成$x-x_0$,就得到(1).

- 幂级数的收敛区间

首先讨论幂级数(2)的收敛性问题.显然任意一个幂级数(2)在$x=0$处总是收敛的,除此之外,它还在哪些点收敛?我们有下面重要的定理.

**定理 14.1(阿贝尔定理)** 若幂级数(2)在$x=\tilde{x} \ne 0$处收敛,则对满足不等式$|x|<|\tilde{x}|$的任何$x$,幂级数(2)收敛而且绝对收敛;若幂级数(2)在$x=\tilde{x}$处发散,则对满足不等式$|x|>|\tilde{x}|$的任何$x$,幂级数(2)发散.

**证** 设级数$\sum_{n=0}^\infty a_n \tilde{x}^n$收敛,从而数列${a_n \tilde{x}^n}$收敛于零且有界,即存在某正数$M$,使得
$$
|a_n \tilde{x}^n| < M \quad (n=0,1,2,\cdots).
$$
另一方面对任意一个满足不等式$|x|<|\tilde{x}|$的$x$,设
$$
r = \left| \frac{x}{\tilde{x}} \right| < 1,
$$
则有