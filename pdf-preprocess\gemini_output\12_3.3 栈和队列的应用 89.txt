**04.【解答】**
1) 顺序存储无法满足要求②的队列占用空间随着入队操作而增加。根据要求来分析:要求①容易满足;链式存储方便开辟新空间,要求②容易满足;对于要求③,出队后的结点并不真正释放,用队首指针指向新的队头结点,新元素入队时,有空余结点则无须开辟新空间,赋值到队尾后的第一个空结点即可,然后用队尾指针指向新的队尾结点,这就需要设计成一个首尾相接的循环单链表,类似于循环队列的思想。设置队头、队尾指针后,链式队列的入队操作和出队操作的时间复杂度均为O(1),要求④可以满足。
因此,采用链式存储结构(两段式单向循环链表),队首指针为front,队尾指针为rear。
2) 该循环链式队列的实现可以参考循环队列,不同之处在于循环链式队列可以方便地增加空间,出队的结点可以循环利用,入队时空间不够也可以动态增加。同样,循环链式队列也要区分队满和队空的情况,这里参考循环队列牺牲一个单元来判断。初始时,创建只有一个空结点的循环单链表,头指针front和尾指针rear均指向空结点,如下图所示。
(图中为一个结点，front和rear指针均指向该结点)

队空的判定条件: `front==rear`.
队满的判定条件: `front==rear->next`.
3) 插入第一个元素后的状态如下图所示。
(图中为两个结点，front指针指向第一个含元素1的结点，rear指针指向第二个空结点，第一个结点的next指针指向第二个结点，第二个结点的next指针指向第一个结点)

4) 操作的基本过程如下:

入队操作:
```
若(front==rear->next) //队满
  则在rear后面插入一个新的空闲结点;
入队元素保存到rear所指结点中; rear=rear->next;返回。
```
出队操作:
```
若(front==rear) //队空
  则出队失败,返回;
取front所指结点中的元素e; front=front->next;返回e。
```

### 3.3 栈和队列的应用
要熟练掌握栈和队列,必须学习栈和队列的应用,把握其中的规律,然后举一反三。接下来将简单介绍栈和队列的一些常见应用。

### 3.3.1 栈在括号匹配中的应用
假设表达式中允许包含两种括号:圆括号和方括号,其嵌套的顺序任意即 `([]())` 或 `[([][])]` 等均为正确的格式,`[(])` 或 `([())` 或 `(()]` 均为不正确的格式。
考虑下列括号序列:

[ ( [ ] [ ] ) ]
1 2 3 4 5 6 7 8

分析如下:
1) 计算机接收第1个括号“`[`”后,期待与之匹配的第8个括号“`]`”出现。
2) 获得了第2个括号“`(`”,此时第1个括号“`[`”暂时放在一边,而急迫期待与之匹配的第7个括号“`)`”出现。
3) 获得了第3个括号“`[`”,此时第2个括号“`(`”暂时放在一边,而急迫期待与之匹配的第4个括号“`]`”出现。第3个括号的期待得到满足,消解之后,第2个括号的期待匹配又成为当前最急迫的任务。
4) 以此类推,可见该处理过程与栈的思想吻合。
算法的思想如下:
1) 初始设置一个空栈,顺序读入括号。
2) 若是左括号,则作为一个新的更急迫的期待压入栈中,自然使原有的栈中所有未消解的期待的急迫性降了一级。
3) 若是右括号,则或使置于栈顶的最急迫期待得以消解,或是不合法的情况(括号序列不匹配,退出程序)。算法结束时,栈为空,否则括号序列不匹配。

### 3.3.2 栈在表达式求值中的应用
表达式求值是程序设计语言编译中一个最基本的问题,它是栈应用的一个典型范例。
1. **算术表达式**
中缀表达式(如`3+4`)是人们常用的算术表达式,操作符以中缀形式处于操作数的中间。与前缀表达式(如`+34`)或后缀表达式(如`34+`)相比,中缀表达式不容易被计算机解析,但仍被许多程序语言使用,因为它更符合人们的思维习惯。
与前缀表达式或后缀表达式不同的是,中缀表达式中的括号是必需的。计算过程中必须用括号将操作符和对应的操作数括起来,用于指示运算的次序。后缀表达式的运算符在操作数后面,后缀表达式中考虑了运算符的优先级,没有括号,只有操作数和运算符。
中缀表达式`A+B*(C-D)-E/F`对应的后缀表达式为`ABCD-*+EF/-`,将后缀表达式与原表达式对应的表达式树(图3.15)的后序遍历序列进行比较,可发现它们有异曲同工之妙。

图3.15 `A+B*(C-D)-E/F`对应的表达式树
(图中为表达式树：根节点为`-`，左子树根为`+`，右子树根为`/`。`+`的左孩子为`A`，右孩子为`*`。`*`的左孩子为`B`，右孩子为`-`。`-`的左孩子为`C`，右孩子为`D`。`/`的左孩子为`E`，右孩子为`F`。)

2. **中缀表达式转后缀表达式**
**命题追踪** 中缀表达式转后缀表达式的方法(2024)
下面先给出一种由中缀表达式转后缀表达式①的手算方法。
① 中缀转前缀的方法类似,且统考真题仅考查过中缀转后缀的过程,因此本节仅介绍中缀转后缀的方法。

1) 按照运算符的运算顺序对所有运算单位加括号。
2) 将运算符移至对应括号的后面,相当于按“左操作数 右操作数 运算符”重新组合。
3) 去除所有括号。
例如,中缀表达式`A+B*(C-D)-E/F`转后缀表达的过程如下(下标表示运算符的运算顺序):
1) 加括号: `((A + (B * (C - D))) - (E / F))`。
2) 运算符后移: `((A (B (C D) -) *) +) (E F) /) -`
3) 去除括号后,得到后缀表达式: `ABCD-*+EF/-`.
在计算机中,中缀表达式转后缀表达式时需要借助一个栈,用于保存暂时还不能确定运算顺序的运算符。从左到右依次扫描中缀表达式中的每一项,具体转化过程如下:
1) 遇到操作数。直接加入后缀表达式。
2) 遇到界限符。若为“`(`”,则直接入栈;若为“`)`”,则不入栈,且依次弹出栈中的运算符并加入后缀表达式,直到遇到“`(`”为止,并直接删除“`(`”。
3) 遇到运算符。①若其优先级高于栈顶运算符或遇到栈顶为“`(`”,则直接入栈;②若其优先级低于或等于栈顶运算符,则依次弹出栈中的运算符并加入后缀表达式,直到遇到一个优先级低于它的运算符或遇到“`(`”或栈空为止,之后将当前运算符入栈。
按上述方法扫描所有字符后,将栈中剩余运算符依次弹出,并加入后缀表达式。
例如,中缀表达式`A+B*(C-D)-E/F`转后缀表达式的过程如表 3.1 所示。

**表3.1 中缀表达式`A+B*(C-D)-E/F`转后缀表达式的过程**
| 步 | 待处理序列 | 栈内 | 后缀表达式 | 扫描项 | 说明 |
| :-- | :--- | :-- | :--- | :--- | :--- |
| 1 | A+B\*(C-D)-E/F | | | A | A加入后缀表达式 |
| 2 | +B\*(C-D)-E/F | | A | + | +入栈 |
| 3 | B\*(C-D)-E/F | + | A | B | B加入后缀表达式 |
| 4 | \*(C-D)-E/F | + | AB | \* | \*优先级高于栈顶,\*入栈 |
| 5 | (C-D)-E/F | +\* | AB | ( | (直接入栈 |
| 6 | C-D)-E/F | +\*( | AB | C | C加入后缀表达式 |
| 7 | -D)-E/F | +\*( | ABC | - | 栈顶为(,-直接入栈 |
| 8 | D)-E/F | +\*(- | ABC | D | D加入后缀表达式 |
| 9 | )-E/F | +\*(- | ABCD | ) | 遇到),弹出-,删除( |
| 10 | -E/F | +\* | ABCD- | - | -优先级低于栈顶,依次弹出\*、+,-入栈 |
| 11 | E/F | | ABCD-\*+ | E | E加入后缀表达式 |
| 12 | /F | - | ABCD-\*+E | / | /优先级高于栈顶,/入栈 |
| 13 | F | -/ | ABCD-\*+E | F | F加入后缀表达式 |
| 14 | | -/ | ABCD-\*+EF | | 字符扫描完毕,弹出剩余运算符 |
| | | | ABCD-\*+EF/- | | 结束 |

**命题追踪** 栈的深度分析(2009、2012)
所谓栈的深度,是指栈中的元素个数,通常是给出入栈和出栈序列,求最大深度(栈的容量应大于或等于最大深度)。有时会间接给出入栈和出栈序列,例如以中缀表达式和后缀表达式的形式给出入栈和出栈序列。掌握栈的先进后出的特点进行手工模拟是解决这类问题的有效方法。
3. **后缀表达式求值**
**命题追踪** 用栈实现表达式求值的分析(2018)
通过后缀表示计算表达式值的过程:从左往右依次扫描表达式的每一项,若该项是操作数,则将其压入栈中;若该项是操作符`<op>`,则从栈中退出两个操作数Y和X,形成运算指令`X<op>Y`,并将计算结果压入栈中。当所有项都扫描并处理完后,栈顶存放的就是最后的计算结果。
例如,后缀表达式`ABCD-*+EF/-`求值的过程需要12步,见表3.2。

**表3.2 后缀表达式`ABCD-*+EF/-`求值的过程**
| 步 | 扫描项 | 项类型 | 动作 | 栈中内容 |
| :-- | :--- | :--- | :--- | :--- |
| 1 | | | 置空栈 | 空 |
| 2 | A | 操作数 | 入栈 | A |
| 3 | B | 操作数 | 入栈 | A B |
| 4 | C | 操作数 | 入栈 | A B C |
| 5 | D | 操作数 | 入栈 | A B C D |
| 6 | * | 操作符 | D、C出栈, 计算C-D, 结果R₁入栈 | A B R₁ |
| 7 | * | 操作符 | R₁、B出栈, 计算B*R₁, 结果R₂入栈 | A R₂ |
| 8 | + | 操作符 | R₂、A出栈, 计算A+R₂, 结果R₃入栈 | R₃ |
| 9 | E | 操作数 | 入栈 | R₃ E |
| 10 | F | 操作数 | 入栈 | R₃ E F |
| 11 | / | 操作符 | F、E出栈, 计算E/F, 结果R₄入栈 | R₃ R₄ |
| 12 | - | 操作符 | R₄、R₃出栈, 计算R₃-R₄, 结果R₅入栈 | R₅ |

### 3.3.3 栈在递归中的应用
递归是一种重要的程序设计方法。简单地说,若在一个函数、过程或数据结构的定义中又应用了它自身,则这个函数、过程或数据结构称为是递归定义的,简称递归。
递归通常把一个大型的复杂问题层层转化为一个与原问题相似的规模较小的问题来求解,递归策略只需少量的代码就可以描述出解题过程所需要的多次重复计算,大大减少了程序的代码量。但在通常情况下,它的效率并不是太高。
以斐波那契数列为例,其定义为
$$
F(n) = \begin{cases} F(n-1)+F(n-2), & n>1 \\ 1, & n=1 \\ 0, & n=0 \end{cases}
$$
这就是递归的一个典型例子,用程序实现时如下:
```c
//斐波那契数列的实现
int F(int n) {
    //边界条件
    if (n==0)
        return 0;
    //边界条件
    else if (n==1)
        return 1;
    //递归表达式
    else
        return F(n-1)+F(n-2);
}
```
必须注意递归模型不能是循环定义的,其必须满足下面的两个条件:
*   递归表达式(递归体)。
*   边界条件(递归出口)。
递归的精髓在于能否将原始问题转换为属性相同但规模较小的问题。

**命题追踪** 栈在函数调用中的作用和工作原理(2015、2017)
在递归调用的过程中,系统为每一层的返回点、局部变量、传入实参等开辟了递归工作栈来进行数据存储,递归次数过多容易造成栈溢出等。而其效率不高的原因是递归调用过程中包含很多重复的计算。下面以$n=5$为例,列出递归调用执行过程,如图3.16所示。

图3.16 F(5)的递归执行过程
(图中为一棵调用树，根为F(5)，其孩子为F(4)和F(3)。F(4)的孩子为F(3)和F(2)。F(3)的孩子为F(2)和F(1)。F(2)的孩子为F(1)和F(0)。)

显然,在递归调用的过程中,`F(3)`被计算2次,`F(2)`被计算3次。`F(1)`被调用5次,`F(0)`被调用3次。所以,递归的效率低下,但优点是代码简单,容易理解。在第5章的树中利用了递归的思想,代码变得十分简单。通常情况下,初学者很难理解递归的调用过程,若读者想具体了解递归是如何实现的,可以参阅编译原理教材中的相关内容。
可以将递归算法转换为非递归算法,通常需要借助栈来实现这种转换。

### 3.3.4 队列在层次遍历中的应用
在信息处理中有一大类问题需要逐层或逐行处理。这类问题的解决方法往往是在处理当前层或当前行时就对下一层或下一行做预处理,把处理顺序安排好,等到当前层或当前行处理完毕,就可以处理下一层或下一行。使用队列是为了保存下一步的处理顺序。下面用二叉树(见图3.17)层次遍历的例子,说明队列的应用。表3.3显示了层次遍历二叉树的过程。

图3.17 二叉树
(图中为一棵二叉树，根为A，其左右孩子分别为B和C。B的左孩子为D，C的左右孩子分别为E和F。D的左孩子为G，E的右孩子为H。)

**表3.3 层次遍历二叉树的过程**
| 序 | 说明 | 队内 | 队 外 |
| :-- | :--- | :-- | :--- |
| 1 | A入 | A | |
| 2 | A出, BC入 | BC | A |
| 3 | B出, D入 | CD | AB |
| 4 | C出, EF入 | DEF | ABC |
| 5 | D出, G入 | EFG | ABCD |
| 6 | E出, HI入 | FGHI | ABCDE |
| 7 | F出 | GHI | ABCDEF |
| 8 | GHI出 | | ABCDEFGHI |

该过程的简单描述如下:
① 根结点入队。
② 若队空(所有结点都已处理完毕),则结束遍历;否则重复③操作。
③ 队列中第一个结点出队,并访问之。若其有左孩子,则将左孩子入队;若其有右孩子,则将右孩子入队,返回②。

### 3.3.5 队列在计算机系统中的应用
队列在计算机系统中的应用非常广泛,以下仅从两个方面来阐述:第一个方面是解决主机与外部设备之间速度不匹配的问题,第二个方面是解决由多用户引起的资源竞争问题。

**命题追踪** 缓冲区的逻辑结构(2009)
对于第一个方面,仅以主机和打印机之间速度不匹配的问题为例做简要说明。主机输出数据给打印机打印,输出数据的速度比打印数据的速度要快得多,因为速度不匹配,若直接把输出的数据送给打印机打印,则显然是不行的。解决的方法是设置一个打印数据缓冲区,主机把要打印输出的数据依次写入这个缓冲区,写满后就暂停输出,转去做其他的事情。打印机就从缓冲区中按照先进先出的原则依次取出数据并打印,打印完后再向主机发出请求。主机接到请求后再向缓冲区写入打印数据。这样做既保证了打印数据的正确,又使主机提高了效率。由此可见,打印数据缓冲区中所存储的数据就是一个队列。

**命题追踪** 多队列出队/入队操作的应用(2016)
对于第二个方面,CPU(中央处理器,它包括运算器和控制器)资源的竞争就是一个典型的例子。在一个带有多终端的计算机系统上,有多个用户需要CPU各自运行自己的程序,它们分别通过各自的终端向操作系统提出占用CPU的请求。操作系统通常按照每个请求在时间上的先后顺序,把它们排成一个队列,每次把CPU分配给队首请求的用户使用。当相应的程序运行结束或用完规定的时间间隔后,令其出队,再把CPU分配给新的队首请求的用户使用。这样既能满足每个用户的请求,又使CPU能够正常运行。

### 3.3.6 本节试题精选
**一、单项选择题**
**01.** 栈的应用不包括( )。
A. 递归 B. 表达式求值 C. 括号匹配 D. 缓冲区

**02.** 表达式`a*(b+c)-d`的后缀表达式是( )。
A. `abcd*+-` B. `abc+*d-` C. `abc*+d-` D. `-+*abcd`

**03.** 下面( )用到了队列。
A. 括号匹配 B. 表达式求值 C. 递归 D. FIFO页面替换算法

**04.** 利用栈求表达式的值时,设立运算数栈OPEN。假设 OPEN 只有两个存储单元,则在下列表达式中,不会发生溢出的是( )。
A. `A-B*(C-D)` B. `(A-B)*C-D` C. `(A-B*C)-D` D. `(A-B)*(C-D)`

**05.** 执行完下列语句段后,`i`的值为( )。
```c
int f(int x) {
    return ((x>0)? x*f(x-1):2);
}
int i;
i=f(f(1));
```
A. 2 B. 4 C. 8 D. 无限递归

**06.** 设有如下递归函数,则计算`F(8)`需要调用该递归函数的次数为( )。
```c
int F(int n) {
    if (n<=3) return 1;
    else return F(n-2)+F(n-4)+1;
}
```
A. 7 B. 8 C. 9 D. 10

**07.** 设有如下递归函数,在`func(func(5))`的执行过程中,第4个被执行的`func`函数是( )。
```c
int func(int x) {
    if (x<=3) return 2;
    else return func(x-2)+func(x-4);
}
```
A. `func(2)` B. `func(3)` C. `func(4)` D. `func(5)`

**08.** 对于一个问题的递归算法求解和其相对应的非递归算法求解,( )。
A. 递归算法通常效率高一些 B. 非递归算法通常效率高一些 C. 两者相同 D. 无法比较

**09.** 执行函数时,其局部变量一般采用( )进行存储。
A. 树形结构 B. 静态链表 C. 栈结构 D. 队列结构

**10.** 执行( )操作时,需要使用队列作为辅助存储空间。
A. 查找散列(哈希)表 B. 广度优先搜索图 C. 前序(根)遍历二叉树 D. 深度优先搜索图

**11.** 下列说法中,正确的是( )。
A. 消除递归不一定需要使用栈
B. 对同一输入序列进行两组不同的合法入栈和出栈组合操作,所得的输出序列也一定相同
C. 通常使用队列来处理函数或过程调用
D. 队列和栈都是运算受限的线性表,只允许在表的两端进行运算

**12.【2009统考真题】** 为解决计算机主机与打印机之间速度不匹配的问题,通常设置一个打印数据缓冲区,主机将要输出的数据依次写入该缓冲区,而打印机则依次从该缓冲区中取出数据。该缓冲区的逻辑结构应该是( )。
A. 栈 B. 队列 C. 树 D. 图

**13.【2012统考真题】** 已知操作符包括“`+`”“`-`”“`*`”“`/`”“`(`”和“`)`”。将中缀表达式`a+b-a*((c+d)/e-f)+g`转换为等价的后缀表达式`ab+acd+e/f-*-g+`时,用栈来存放暂时还不能确定运算次序的操作符。栈初始时为空时,转换过程中同时保存在栈中的操作符的最大个数是( )。
A. 5 B. 7 C. 8 D. 11

**14.【2014统考真题】** 假设栈初始为空,将中缀表达式`a/b+(c*d-e*f)/g`转换为等价的后缀表达式的过程中,当扫描到`f`时,栈中的元素依次是( )。
A. `+(*-` B. `+(-*` C. `/+(*-*` D. `/+-*`

**15.【2015统考真题】** 已知程序如下:
```c
int S(int n)
{ return (n<=0)?0:S(n-1)+n;}
void main()
{ cout<<S(1);}
```
程序运行时使用栈来保存调用过程的信息,自栈底到栈顶保存的信息依次对应的是( )。
A. `main()→S(1)→S(0)`
B. `S(0)→S(1)→main()`
C. `main()→S(0)→S(1)`
D. `S(1)→S(0)→main()`

**16.【2016统考真题】** 设有如下图所示的火车车轨,入口和出口之间有$n$条轨道,列车的行进方向均为从左至右,列车可驶入任意一条轨道。现有编号为1~9的9列列车,驶入的次序依次是8,4,2,5,3,9,1,6,7。若期望驶出的次序依次为1~9,则$n$至少是( )。
(图中所示为火车车轨示意图，左侧为入口，右侧为出口，中间为n条并列的调度轨道。入口处列车顺序为761935248，出口处期望顺序为987654321。)
A. 2 B. 3 C. 4 D. 5

**17.【2017统考真题】** 下列关于栈的叙述中,错误的是( )。
I. 采用非递归方式重写递归程序时必须使用栈
II. 函数调用时,系统要用栈保存必要的信息
III. 只要确定了入栈次序,即可确定出栈次序
IV. 栈是一种受限的线性表,允许在其两端进行操作
A. 仅Ⅰ B. 仅Ⅰ、Ⅱ、Ⅲ C. 仅Ⅰ、Ⅲ、IV D. 仅Ⅱ、Ⅲ、IV

**18.【2018统考真题】** 若栈S1中保存整数,栈S2中保存运算符,函数F()依次执行下述各步操作:
1) 从S1中依次弹出两个操作数a和b。
2) 从S2中弹出一个运算符op。
3) 执行相应的运算`b op a`。
4) 将运算结果压入S1中。
假定 S1 中的操作数依次是5, 8, 3, 2(2在栈顶),S2中的运算符依次是`*`、`-`、`+`(+在栈顶)。调用3次F()后,S1栈顶保存的值是( )。
A. -15 B. 15 C. -20 D. 20

**19.【2024统考真题】** 与表达式`x+y*(z-u)/v`等价的后缀表达式是( )。
A. `xyzu-*v/+` B. `xyzu-v/*+` C. `+x/*y-zuv` D. `+x*y/-zuv`

**二、综合应用题**
**01.** 假设一个算术表达式中包含圆括号、方括号和花括号3种类型的括号,编写一个算法来判别表达式中的括号是否配对,以字符`‘\0’`作为算术表达式的结束符。

### 3.3.7 答案与解析
**一、单项选择题**
**01. D**
缓冲区是用队列实现的,选项A、B、C都是栈的典型应用。

**02. B**
后缀表达式中,每个运算符均直接位于其两个操作数的后面,按照这样的方式逐步根据计算的优先级将每个计算式进行变换,即可得到后缀表达式。
**【另解】** 将两个直接操作数用括号括起来,再将操作符提到括号后,最后去掉括号。例如,对于`((a*(b+c))-d)`,提出操作符并去掉括号后,可得后缀表达式为`abc+*d-`。
学完第5章后,可将表达式画成二叉树的形式,再用后序遍历即可求得后缀表达式。

**03. D**
FIFO 页面替换算法用到了队列。其余的都只用到了栈。

**04. B**
利用栈求表达式的值时,可以分别设立运算符栈和运算数栈,其原理不变。选项B中A入栈,B入栈,计算得R1,C入栈,计算得R2,D入栈,计算得R3,由此得栈深为2。选项A、C、D依次计算得栈深为4、3、3。因此选择选项B。
**技巧**
根据运算符优先级,统计已依次入栈但还未参与计算的运算符的个数。以选项C为例,当“`(`” “`A`” “`-`”入栈时,“`(`”和“`-`”还未参与运算,此时运算符栈大小为2,“`B`”和“`*`”入栈时运算符大小为3,“`C`”入栈时“`B*C`”运算,运算符栈大小为2,以此类推。

**05. B**
栈与递归有着紧密的联系。递归模型包括递归出口和递归体两个方面。递归出口是递归算法的出口,即终止递归的条件。递归体是一个递推的关系式。根据题意有
`f(0)=2`;
`f(1)=1*f(0)=2`;
`f(f(1))=f(2)=2*f(1)=4`。

**06. C**
计算`F(8)`的递归调用树如下图所示:
(图中为F(8)的调用树，根为F(8)，孩子为F(6)和F(4)。F(6)的孩子为F(4)和F(2)。F(4)的孩子为F(2)和F(0)。)
由图可知,递归函数`F()`调用的次数为9。

**07. C**
首先,执行内层参数`func(5)=func(3)+func(1)=4`,共执行3次`func`函数。然后,执行`func(func(5))=func(4)=func(2)+func(0)=4`,因此第4个被执行的`func`函数是`func(4)`。也可以采用画出递归调用树的方式,即某个函数的执行次序等于其在递归调用树的先序遍历中的次序。

**08. B**
通常情况下,递归算法在计算机实际执行的过程中包含很多的重复计算,所以效率会低。

**09. C**
调用函数时,系统会为调用者构造一个由参数表和返回地址组成的活动记录,并将记录压入系统提供的栈中,若被调用函数有局部变量,也要压入栈中。

**10. B**
本题涉及第5章和第6章的内容,图的广度优先搜索类似于树的层序遍历,都要借助于队列。

**11. A**
使用栈可以模拟递归的过程,以此来消除递归,但对于单向递归和尾递归而言,可以用迭代的方式来消除递归,选项A正确。不同的入栈和出栈组合操作,会产生许多不同的输出序列,B错误。通常使用栈来处理函数或过程调用,选项C错误。队列和栈都是操作受限的线性表,但只有队列允许在表的两端进行运算,而栈只允许在栈顶方向进行操作,选项D错误。

**12. B**
在提取数据时必须保持原来数据的顺序,所以缓冲区的特性是先进先出。

**13. A**
在中缀表达式转后缀表达式的过程中,扫描到操作数时直接输出,扫描到操作符时根据其优先级进行相应的出入栈操作。有几点需要注意:①若遇到界限符“`(`”,则直接入栈;②若遇到界限符“`)`”,则不入栈,且依次弹出栈顶运算符,直到遇到“`(`”为止,并删除“`(`”;③若当前运算符的优先级高于栈顶运算符或遇到栈顶为“`(`”,则直接入栈;④若当前运算符的优先级低于或等于栈顶运算符,则依次弹出栈中的运算符并加入后缀表达式,直到遇到一个优先级低于它的运算符或遇到“`(`”或栈空为止,之后将当前运算符入栈。
求栈中操作符的最大个数时,为简单起见,可省略对操作数的处理。

| 步 | 待处理运算符 | 栈 | 扫描运算符 | 动作 |
| :-- | :--- | :-- | :--- | :--- |
| 1 | `+b-a*((c+d)/e-f)+g` | | + | +入栈 |
| 2 | `-a*((c+d)/e-f)+g` | + | - | -优先级等于栈顶+,弹出+,-入栈 |
| 3 | `*((c+d)/e-f)+g` | - | * | *优先级高于栈顶-,*入栈 |
| 4 | `((c+d)/e-f)+g` | -* | ( | (直接入栈 |
| 5 | `(c+d)/e-f)+g` | -*( | ( | (直接入栈 |
| 6 | `+d)/e-f)+g` | -*(( | + | 栈顶为(,+直接入栈 |
| 7 | `)/e-f)+g` | -*((+ | ) | 遇到),弹出+,删除( |
| 8 | `/e-f)+g` | -*( | / | 栈顶为(,/直接入栈 |
| 10 | `-f)+g` | -*(/ | - | -优先级低于栈顶/,弹出/,-入栈 |
| 11 | `)+g` | -*(- | ) | 遇到),弹出-,删除( |
| 12 | `+g` | -* | + | +优先级低于栈顶*,等于-,依次弹出*和-:+入栈 |
| 13 | | + | | |

由上述过程可知,栈中操作符的最大个数为5。

**14. B**
中缀表达式`a/b+(c*d-e*f)/g`转换为后缀表达式的过程如下:

| 步 | 待处理序列 | 栈 | 后缀表达式 | 扫描项 | 动作 |
| :-- | :--- | :-- | :--- | :--- | :--- |
| 1 | `a/b+(c*d-e*f)/g` | | | a | a加入后缀表达式 |
| 2 | `/b+(c*d-e*f)/g` | | a | / | /入栈 |
| 3 | `b+(c*d-e*f)/g` | / | a | b | b加入后缀表达式 |
| 4 | `+(c*d-e*f)/g` | / | ab | + | +优先级低于栈顶的/,弹出/,+入栈 |
| 5 | `(c*d-e*f)/g` | + | ab/ | ( | (入栈 |
| 6 | `c*d-e*f)/g` | +( | ab/ | c | c加入后缀表达式 |
| 7 | `*d-e*f)/g` | +( | ab/c | * | 栈顶为(,*入栈 |
| 8 | `d-e*f)/g` | +(* | ab/c | d | d加入后缀表达式 |
| 9 | `-e*f)/g` | +(* | ab/cd | - | -优先级低于栈顶的*,弹出*,-入栈 |
| 10 | `e*f)/g` | +(- | ab/cd* | e | e加入后缀表达式 |
| 11 | `*f)/g` | +(- | ab/cd*e | * | *优先级高于栈顶的-,*入栈 |
| 12 | `f)/g` | +(-* | ab/cd*e | f | f加入后缀表达式 |
| 13 | `)/g` | +(-* | ab/cd*ef | ) | 遇到),依次弹出*、-加入表达式,删除( |
| 14 | `/g` | + | ab/cd*ef*- | / | /优先级高于栈顶的+,/入栈 |
| 15 | `g` | +/ | ab/cd*ef*- | g | g加入后缀表达式 |
| 16 | | +/ | ab/cd*ef*-g | | 扫描完毕,运算符依次弹出加入表达式 |
| 17 | | | ab/cd*ef*-g/+ | | 完成 |

由此可知,当扫描到`f`时,栈中的元素依次是`+(-*`。
**【另解】** 采用手算方法,得出中缀式`a/b+(c*d-e*f)/g`对应的后缀式为`ab/cd*ef*-g/+`。中缀表达式转后缀表达式时,操作数都直接输出,因此操作数的顺序是固定的。扫描到`f`时,在后缀表达式`f`后面的运算符要么还未入栈,要么还在栈中,需要结合中缀式来判断,`f`后面依次出栈的运算符为`*-/+`, `/`在中缀表达式`f`之后,此时还未入栈,因此栈中的运算符(从栈底到栈顶)为`+-*`;此外,已入栈的界限符(此时还未消解,因此`(也还在栈中,栈中的元素依次是`+(-*`。

**15. A**
递归调用函数时,在系统栈中保存的函数信息需满足先进后出的特点,依次调用了`main()`, `S(1)`, `S(0)`,所以栈底到栈顶的信息依次是`main()`, `S(1)`, `S(0)`。
**注意**
在递归中,系统为每一层的返回点、局部变量、传入实参等开辟了递归工作栈来存储。

**16. C**
根据题意:入队顺序为8,4,2,5,3,9,1,6,7,出队顺序为1~9。入口和出口之间有多个队列($n$条轨道),且每个队列(轨道)可容纳多个元素(多列列车),为便于区分,队列用字母编号。分析如下:显然先入队的元素必须小于后入队的元素(否则,若8和4入同一个队列,8在4前,则出队时也只能8在4前面),这样8入队列A,4入队列B,2入队列C,5入队列B(按照前述原则“大的元素在小的元素后面”也可将5入队列C,但这时剩下的元素3就必须放入一个新的队列中,无法确保“至少”),3入队列C,9入队列A,这时共占了3个队列,后面还有元素1,直接再用一个新的队列D,1从队列D出队后,剩下的元素6和7或入队列B,或入队列C。综上,共占用了4个队列。当然还有其他的入队、出队情况,请读者自己推演,但要确保满足:①队列中后面的元素大于前面的元素;②确保占用最少(满足题意中“至少”)的队列。

**17. C**
Ⅰ的反例:计算斐波那契数列迭代实现只需要一个循环即可实现。Ⅲ的反例:入栈序列为1,2,进行 Push, Push, Pop, Pop操作,出栈次序为2、1;进行 Push, Pop, Push, Pop操作,出栈次序为1,2。IV,栈是一种受限的线性表,只允许在一端进行操作。Ⅱ正确。

**18. B**
第一次调用:①从S1中弹出2和3;②从S2中弹出+;③执行3+2=5;④将5压入S1中,第一次调用结束后S1中剩余5、8、5(5在栈顶),S2中剩余`*`、`-`(-在栈顶)。第二次调用:①从S1中弹出5和8;②从S2中弹出一;③执行8-5=3;④将3压入S1中,第二次调用结束后S1中剩余5、3(3在栈顶),S2中剩余`*`。第三次调用:①从S1中弹出3和5;②从S2中弹出\*;③执行5\*3=15;④将15压入S1中,第三次调用结束后S1中仅剩余15,S2为空。

**19. A**
根据中缀表达式画出对应的二叉树如右图所示,对该二叉树进行后序遍历即可得到后缀表达式为`xyzu-*v/+`。本题也可采用本书前面描述的手算方法。
```
      +
     / \
    x   /
       / \
      *   v
     / \
    y   -
       / \
      z   u
```
**二、综合应用题**
**01.【解答】**
括号匹配是栈的一个典型应用,给出这道题是希望读者好好掌握栈的应用。
算法的基本思想是扫描每个字符,遇到花、方、圆的左括号时入栈,遇到花、方、圆的右括号时检查栈顶元素是否为相应的左括号,若是,出栈,否则配对错误。最后栈若不为空也为错误。

```c
bool BracketsCheck(char *str) {
    InitStack(S); //初始化栈
    int i=0;
    while (str[i]!='\0') {
        switch(str[i]){
            //左括号入栈
            case '(': push(S,'('); break;
            case '[': push(S,'['); break;
            case '{': push(S,'{'); break;
            //遇到右括号,检测栈顶
            case ')': Pop(S,e);
                if (e!='(') return false;
                break;
            case ']': Pop(S,e);
                if (e!='[') return false;
                break;
            case '}': Pop(S,e);
                if (e!='{') return false;
                break;
            default:
                break;
        }//switch
        i++;
    }//while
    if(!IsEmpty(S)){
        printf("括号不匹配\n");
        return false;
    }
    else {
        printf("括号匹配\n");
        return true;
    }
}
```

### 3.4 数组和特殊矩阵
矩阵在计算机图形学、工程计算中占有举足轻重的地位。在数据结构中考虑的是如何用最小的内存空间来存储同样的一组数据。所以,我们不研究矩阵及其运算等,而把精力放在如何将矩阵更有效地存储在内存中,并能方便地提取矩阵中的元素。

### 3.4.1 数组的定义
数组是由$n(n≥1)$个相同类型的数据元素构成的有限序列,每个数据元素称为一个数组元素,每个元素在$n$个线性关系中的序号称为该元素的下标,下标的取值范围称为数组的维界。
数组与线性表的关系:数组是线性表的推广。一维数组可视为一个线性表;二维数组可视为其元素是定长数组的线性表,以此类推。数组一旦被定义,其维数和维界就不再改变。因此,除结构的初始化和销毁外,数组只会有存取元素和修改元素的操作。

### 3.4.2 数组的存储结构
大多数计算机语言都提供了数组数据类型,逻辑意义上的数组可采用计算机语言中的数组数据类型进行存储,一个数组的所有元素在内存中占用一段连续的存储空间。