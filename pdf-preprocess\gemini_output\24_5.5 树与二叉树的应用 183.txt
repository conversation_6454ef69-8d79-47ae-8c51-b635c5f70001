算法代码如下:
```c
typedef struct node
{
    ElemType data;              //数据域
    struct node *fch, *nsib;     //孩子与兄弟域
}*Tree;
int Leaves (Tree t) {           //计算以孩子兄弟表示法存储的森林的叶子数
    if (t==NULL)
        return 0;               //树空返回0
    if (t->fch==NULL)           //若结点无孩子,则该结点必是叶子
        return 1+Leaves(t->nsib); //返回叶结点和其兄弟子树中的叶结点数
    else
        return Leaves(t->fch)+Leaves(t->nsib); //孩子子树和兄弟子树中叶子数之和
}
```
**05.【解答】**
由孩子兄弟链表表示的树,求高度的算法思想:采用递归算法,若树为空,高度为零;否则,高度为第一子女树高度加1和兄弟子树高度的大者。其非递归算法使用队列,逐层遍历树,取得树的高度。算法代码如下:
```c
int Height(CSTree bt) {
    //递归求以孩子兄弟链表表示的树的深度
    int hc,hs;
    if (bt==NULL)
        return 0;
    else{ //否则,高度取子女高度+1和兄弟子树高度的大者
        hc=Height(bt->firstchild); //第一子女树高
        hs=Height(bt->nextsibling); //兄弟树高
        if (hc+1>hs)
            return hc+1;
        else
            return hs;
    }
}
```

### 5.5 树与二叉树的应用

#### 5.5.1 哈夫曼树和哈夫曼编码

**1. 哈夫曼树的定义**
在介绍哈夫曼树之前,先介绍几个相关的概念:
在许多应用中,树中结点常常被赋予一个表示某种意义的数值,称为该结点的权。
从树的根到一个结点的路径长度与该结点上权值的乘积,称为该结点的带权路径长度。
树中所有叶结点的带权路径长度之和称为该树的带权路径长度,记为
$$
WPL = \sum_{i=1}^{n} w_i l_i
$$
式中, $w_i$是第$i$个叶结点所带的权值, $l_i$是该叶结点到根结点的路径长度。
在含有$n$个带权叶结点的二叉树中,其中带权路径长度(WPL)最小的二叉树称为哈夫曼树,也称最优二叉树。例如,图5.24中的3棵二叉树都有4个叶结点 a, b, c, d,分别带权7,5,2,4,它们的带权路径长度分别为

---
(Image content of page 2)

图5.24 具有不同带权长度的二叉树
(a) $WPL = 7×2+5×2+2×2+4×2=36$。
(b) $WPL = 4×2+7×3+5×3+2×1=46$。
(c) $WPL = 7×1+5×2+2×3+4×3=35$。
其中,图5.24(c)树的WPL最小。可以验证,它恰好为哈夫曼树。

**2. 哈夫曼树的构造**
给定$n$个权值分别为$w_1, w_2, \dots, w_n$的结点,构造哈夫曼树的算法描述如下:
1) 将这$n$个结点分别作为$n$棵仅含一个结点的二叉树,构成森林 F。
> **命题追踪**
> 分析哈夫曼树的路径上权值序列的合法性(2010)

2) 构造一个新结点,从F中选取两棵根结点权值最小的树作为新结点的左、右子树,并且将新结点的权值置为左、右子树上根结点的权值之和。
3) 从F中删除刚才选出的两棵树,同时将新得到的树加入F中。
4) 重复步骤2)和3),直至F中只剩下一棵树为止。
> **命题追踪**
> 哈夫曼树的性质(2010、2019)

从上述构造过程中可以看出哈夫曼树具有如下特点:
1) 每个初始结点最终都成为叶结点,且权值越小的结点到根结点的路径长度越大。
2) 构造过程中共新建了$n-1$个结点(双分支结点),因此哈夫曼树的结点总数为$2n-1$。
3) 每次构造都选择2棵树作为新结点的孩子,因此哈夫曼树中不存在度为1的结点。
例如,权值{7,5,2,4}的哈夫曼树的构造过程如图5.25所示。

图5.25 哈夫曼树的构造过程

**3. 哈夫曼编码**
在数据通信中,若对每个字符用相等长度的二进制位表示,称这种编码方式为固定长度编码。若允许对不同字符用不等长的二进制位表示,则这种编码方式称为可变长度编码。可变长度编码比固定长度编码要好得多,其特点是对频率高的字符赋以短编码,而对频率较低的字符则赋以较长一些的编码,从而可以使字符的平均编码长度减短,起到压缩数据的效果。

---
(Image content of page 3)

> **命题追踪**
> 根据哈夫曼编码对编码序列进行译码(2017)

若没有一个编码是另一个编码的前缀,则称这样的编码为前缀编码。举例:设计字符A, B和C对应的编码0,10和110是前缀编码。对前缀编码的解码很简单,因为没有一个编码是其他编码的前缀。所以识别出第一个编码,将它翻译为原字符,再对剩余的码串执行同样的解码操作。例如,码串0010110可被唯一地翻译为A, A, B和C。另举反例:若再将字符D的编码设计为11,此时11是110的前缀,则上述码串的后三位就无法唯一翻译。
> **命题追踪**
> 哈夫曼树的构造及相关的分析(2012、2018、2021、2023)
> **命题追踪**
> 前缀编码的分析及应用(2014、2020)

可以利用二叉树来设计二进制前缀编码。假设为A, B, C, D四个字符设计前缀编码,可以用图5.26所示的二叉树来表示,4个叶结点分别表示4个字符,且约定左分支表示0,右分支表示1,从根到叶结点的路径上用分支标记组成的序列作为该叶结点字符的编码,可以证明如此得到的必为前缀编码。由图5.26得到字符A, B, C, D的前缀编码分别为0,10,110,111。

图5.26 前缀编码示例
> **命题追踪**
> 哈夫曼编码和定长编码的差异(2022)

哈夫曼编码是一种非常有效的数据压缩编码。由哈夫曼树得到哈夫曼编码是很自然的过程。首先,将每个字符当作一个独立的结点,其权值为它出现的频度(或次数),构造出对应的哈夫曼树。然后,将从根到叶结点的路径上分支标记的字符串作为该字符的编码。图5.27所示为一个由哈夫曼树构造哈夫曼编码的示例,矩形方块表示字符及其出现的次数。

各字符编码为
a:0
b:101
c:100
d:111
e:1101
f:1100

图5.27 由哈夫曼树构造哈夫曼编码

这棵哈夫曼树的WPL为
$$
WPL = 1×45+3×(13+12+16)+4×(5+9) = 224
$$
此处的WPL可视为最终编码得到二进制编码的长度,共224位。若采用3位固定长度编码,则得到的二进制编码长度为300位,因此哈夫曼编码共压缩了25%的数据。利用哈夫曼树可以设计出总长度最短的二进制前缀编码。

---
(Image content of page 4)

> **注意**
> 左分支和右分支究竟是表示0还是表示1没有明确规定,因此构造出的哈夫曼树并不唯一,但各哈夫曼树的带权路径长度WPL相同且为最优。此外,如有若干权值相同的结点,则构造出的哈夫曼树更可能不同,但WPL必然相同且为最优。

#### 5.5.2 并查集
**1. 并查集的概念**
并查集是一种简单的集合表示,它支持以下3种操作:
1) Initial(S): 将集合S中的每个元素都初始化为只有一个单元素的子集合。
2) Union(S, Root1, Root2): 把集合S中的子集合Root2并入子集合Root1。要求Root1和Root2互不相交,否则不执行合并。
3) Find(S, x): 查找集合S中单元素x所在的子集合,并返回该子集合的根结点。
**2. 并查集的存储结构**
通常用树的双亲表示作为并查集的存储结构,每个子集合以一棵树表示。所有表示子集合的树,构成表示全集合的森林,存放在双亲表示数组内。通常用数组元素的下标代表元素名,用根结点的下标代表子集合名,根结点的双亲域为负数(可设置为该子集合元素数量的相反数)。
例如,若设有一个全集合为S={0, 1, 2, 3, 4, 5, 6, 7, 8, 9},初始化时每个元素自成一个单元素子集合,每个子集合的数组值为-1,如图5.28所示。

图5.28 并查集的初始化

经过一段时间的计算后,这些子集合合并为3个更大的子集合,即$S_1=\{0,6,7,8\}$, $S_2=\{1,4,9\}$, $S_3=\{2,3,5\}$,此时并查集的树形和存储结构如图5.29所示。

图5.29 用树表示并查集

为了得到两个子集合的并,只需将其中一个子集合根结点的双亲指针指向另一个集合的根结点。因此,$S_1 \cup S_2$可以具有如图5.30所示的表示。

图5.30 $S_1 \cup S_2$可能的表示方法

---
(Image content of page 5)

在采用树的双亲指针数组表示作为并查集的存储表示时,集合元素的编号从0到SIZE-1。其中SIZE是最大元素的个数。

**3. 并查集的基本实现**
并查集的结构定义如下:
```c
#define SIZE 100
int UFSets[SIZE]; //集合元素数组(双亲指针数组)
```
下面是并查集主要运算的实现。
(1) **并查集的初始化操作**
```c
void Initial(int S[]){
    //S即并查集
    for(int i=0;i<SIZE;i++) //每个自成单元素集合
        S[i]=-1;
}
```
(2) **并查集的Find 操作**
在并查集中查找并返回包含元素x的树的根。
```c
int Find(int S[], int x) {
    while(S[x]>=0) //循环寻找x的根
        x=S[x];
    return x; //根的S[]小于0
}
```
判断两个元素是否属于同一集合,只需分别找到它们的根,再比较根是否相同即可。
(3) **并查集的Union 操作**
求两个不相交子集合的并集。若将两个元素所在的集合合并为一个集合,则需要先找到两个元素的根,再令一棵子集树的根指向另一棵子集树的根。
```c
void Union(int S[], int Root1, int Root2) {
    if(Root1==Root2) return; //要求 Root1与Root2是不同的集合
    S[Root2]=Root1; //将根 Root2 连接到另一根 Root1 下面
}
```
Find操作和Union操作的时间复杂度分别为$O(d)$和$O(1)$,其中$d$为树的深度。

**4. 并查集实现的优化**
在极端情况下,$n$个元素构成的集合树的深度为$n$,则Find操作的最坏时间复杂度为$O(n)$。改进的办法是:在做Union操作之前,首先判别子集中的成员数量,然后令成员少的根指向成员多的根,即把小树合并到大树,为此可令根结点的绝对值保存集合树中的成员数量。
(1) **改进的Union操作**
```c
void Union(int S[], int Root1, int Root2) {
    if(Root1==Root2) return;
    if(S[Root2]>S[Root1]) { //Root2 结点数更少
        S[Root1]+=S[Root2]; //累加集合树的结点总数
        S[Root2]=Root1; //小树合并到大树
    }
    else {
        S[Root2]+=S[Root1]; //Root1 结点数更少
        S[Root1]=Root2; //累加结点总数
    } //小树合并到大树
}
```
采用这种方法构造得到的集合树,其深度不超过$log_2n+1$。
随着子集逐对合并,集合树的深度越来越大,为了进一步减少确定元素所在集合的时间,还可进一步对上述Find操作进行优化,当所查元素x不在树的第二层时,在算法中增加一个压缩

---
(Image content of page 6)

路径的功能,即将从根到元素x路径上的所有元素都变成根的孩子。
(2) **改进的Find操作**
```c
int Find(int S[], int x) {
    int root=x;
    while(s[root]>=0) //循环找到根
        root=s[root];
    while(x!=root) { //压缩路径
        int t=S[x]; //t指向x的父结点
        S[x]=root; //x直接挂到根结点下面
        x=t;
    }
    return root; //返回根结点编号
}
```
通过Find操作的压缩路径优化后,可使集合树的深度不超过$O(\alpha(n))$,其中$\alpha(n)$是一个增长极其缓慢的函数,对于常见的正整数$n$,通常$\alpha(n)\le4$。
并查集应用的相关举例见本书配套课程。

#### 5.5.3 本节试题精选
**一、单项选择题**
**01.** 在有$n$个叶结点的哈夫曼树中,非叶结点的总数是( )。
A. $n-1$
B. $n$
C. $2n-1$
D. $2n$

**02.** 给定整数集合{3, 5, 6, 9, 12},与之对应的哈夫曼树是( )。
(Image of four different Huffman trees labeled A, B, C, D)
A.
B.
C.
D.

**03.** 下列编码中,( )不是前缀码。
A. {00, 01, 10, 11}
B. {0, 1, 00, 11}
C. {0, 10, 110, 111}
D. {10, 110, 1110, 1111}

**04.** 设哈夫曼编码的长度不超过4,若已对两个字符编码为1和01,则还最多可对( )个字符编码。
A. 2
B. 3
C. 4
D. 5

**05.** 一棵哈夫曼树共有215个结点,对其进行哈夫曼编码,共能得到( )个不同的码字。
A. 107
B. 108
C. 214
D. 215

**06.** 设某哈夫曼树有5个叶结点,则该哈夫曼树的高度最高可以是( )。
A. 3
B. 4
C. 5
D. 6

**07.** 以下对于哈夫曼树的说法中,错误的是( )
A. 用一组权值构造出的哈夫曼树可能不唯一,但带权路径长度唯一
B. 哈夫曼树具有最小的带权路径长度
C. 哈夫曼树中没有度为1的结点
D. 哈夫曼树中除了度为1的结点,还有度为2的结点和叶结点

**08.** 下列关于哈夫曼树的说法中,错误的是( )。
I. 哈夫曼树的总结点数不能是偶数

---
(Image content of page 7)

II. 哈夫曼树中度为1的结点数等于度为2和0的结点数之差
III. 哈夫曼树的带权路径长度等于其所有分支结点的权值之和
A. 仅III
B. I和II
C. 仅II
D. I、II和III

**09.** 若度为$m$的哈夫曼树中,叶结点个数为$n$,则非叶结点的个数为( )。
A. $n-1$
B. $\lfloor n/m \rfloor - 1$
C. $\lfloor (n-1)/(m-1) \rfloor$
D. $\lfloor n/(m-1) \rfloor - 1$

**10.** 并查集的结构是一种( )。
A. 二叉链表存储的二叉树
B. 双亲表示法存储的树
C. 顺序存储的二叉树
D. 孩子表示法存储的树

**11.** 并查集中最核心的两个操作是:①查找,查找两个元素是否属于同一个集合;②合并,若两个元素不属于同一个集合,且所在的两个集合互不相交,则合并这两个集合。假设初始长度为10(0~9)的并查集,按1-2、3-4、5-6、7-8、8-9、1-8、0-5、1-9的顺序进行查找和合并操作,最终并查集共有( )个集合。
A. 1
B. 2
C. 3
D. 4

**12.** 下列关于并查集的说法中,正确的是( )(注,本题涉及图的考点)。
A. 并查集不能检测图中是否存在环路的问题
B. 通过路径优化后的并查集在最坏情况下的高度仍是$O(n)$
C. Find操作返回集合中元素个数的相反数,它用来作为某个集合的标志
D. Union操作时可根据当前集合的规模,将小集合合并到大集合中

**13.** 下列关于并查集的叙述中,( )是错误的(注,本题涉及图的考点)。
A. 并查集是用双亲表示法存储的树
B. 并查集可用于实现克鲁斯卡尔算法
C. 并查集可用于判断无向图的连通性
D. 在长度为n的并查集中进行查找操作的时间复杂度为$O(log_2n)$

**14.【2010统考真题】**$n(n\ge2)$个权值均不相同的字符构成哈夫曼树,关于该树的叙述中,错误的是( )。
A. 该树一定是一棵完全二叉树
B. 树中一定没有度为1的结点
C. 树中两个权值最小的结点一定是兄弟结点
D. 树中任意一个非叶结点的权值一定不小于下一层任意一个结点的权值

**15.【2014统考真题】**5个字符有如下4种编码方案,不是前缀编码的是( )。
A. 01,0000,0001,001,1
B. 011,000,001,010,1
C. 000,001,010,011,100
D. 0,100,110,1110,1100

**16.【2015统考真题】**下列选项给出的是从根分别到达两个叶结点路径上的权值序列,能属于同一棵哈夫曼树的是( )。
A. 24,10,5和24,10,7
B. 24,10,5和24,12,7
C. 24,10,10和24,14,11
D. 24,10,5和24,14,6

**17.【2017统考真题】**已知字符集{a, b, c, d, e, f, g, h},若各字符的哈夫曼编码依次是0100, 10, 0000, 0101, 001, 011, 11, 0001,则编码序列0100011001001011110101的译码结果是( )。
A. acgabfh
B. adbagbb
C. afbeagd
D. afeefgd

**18.【2018统考真题】**已知字符集{a, b, c, d, e, f},若各字符出现的次数分别为6, 3, 8, 2, 10, 4,

---
(Image content of page 8)

则对应字符集中各字符的哈夫曼编码可能是( )。
A. 00, 1011, 01, 1010, 11, 100
B. 00, 100, 110, 000, 0010, 01
C. 10, 1011, 11, 0011, 00, 010
D. 0011, 10, 11, 0010, 01, 000

**19.【2019统考真题】**对$n$个互不相同的符号进行哈夫曼编码。若生成的哈夫曼树共有115个结点,则$n$的值是( )。
A. 56
B. 57
C. 58
D. 60

**20.【2021统考真题】**若某二叉树有5个叶结点,其权值分别为10, 12, 16, 21, 30,则其最小的带权路径长度(WPL)是( )。
A. 89
B. 200
C. 208
D. 289

**21.【2022统考真题】**对任意给定的含$n(n>2)$个字符的有限集S,用二叉树表示S的哈夫曼编码集和定长编码集,分别得到二叉树$T_1$和$T_2$。下列叙述中,正确的是( )。
A. $T_1$与$T_2$的结点数相同
B. $T_1$的高度大于$T_2$的高度
C. 出现频次不同的字符在$T_1$中处于不同的层
D. 出现频次不同的字符在$T_2$中处于相同的层

**22.【2023统考真题】**在由6个字符组成的字符集S中,各字符出现的频次分别为3, 4, 5, 6, 8, 10,为S构造的哈夫曼编码的加权平均长度为( )。
Α. 2.4
Β. 2.5
C. 2.67
D. 2.75

**二、综合应用题**
**01.** 设给定权集w={5, 7, 2, 3, 6, 8, 9},试构造关于w的一棵哈夫曼树,并求其加权路径长度WPL。
**02.【2012统考真题】**设有6个有序表 A, B, C, D, E, F,分别含有10, 35, 40, 50, 60和200个数据元素,各表中的元素按升序排列。要求通过5次两两合并,将6个表最终合并为1个升序表,并使最坏情况下比较的总次数达到最小。请回答下列问题:
1) 给出完整的合并过程,并求出最坏情况下比较的总次数。
2) 根据你的合并过程,描述$n(n \ge 2)$个不等长升序表的合并策略,并说明理由。
**03.【2020统考真题】**若任意一个字符的编码都不是其他字符编码的前缀,则称这种编码具有前缀特性。现有某字符集(字符个数$\ge 2$)的不等长编码,每个字符的编码均为二进制的0、1序列,最长为L位,且具有前缀特性。请回答下列问题:
1) 哪种数据结构适宜保存上述具有前缀特性的不等长编码?
2) 基于你所设计的数据结构,简述从0/1串到字符串的译码过程。
3) 简述判定某字符集的不等长编码是否具有前缀特性的过程。

#### 5.5.4 答案与解析
**一、单项选择题**
**01. A**
由哈夫曼树的构造过程可知,哈夫曼树中只有度为0和2的结点。在非空二叉树中,有$n_0=n_2+1$,所以$n_2=n_0-1$。
【另解】$n$个结点构造哈夫曼树需要$n-1$次合并过程,每次合并新建一个分支结点,所以选择选项A。
**02. C**

---
(Image content of page 9)

首先,3和5构造为一棵子树,其根权值为8,然后该子树与6构造为一棵新子树,根权值为14,再后9与12构造为一棵子树,最后两棵子树共同构造为一棵哈夫曼树。
**03. B**
若没有一个编码是另一个编码的前缀,则称这样的编码为前缀编码。在选项B中,0是00的前缀,1是11的前缀。
**04. C**
在哈夫曼编码中,一个编码不能是任何其他编码的前缀。3位编码可能是001,对应的4位编码只能是0000和0001。3位编码也可能是000,对应的4位编码只能是0010和0011。若全采用4位编码,则可以为0000,0001,0010和0011。题中问的是最多,所以选择选项C。
**【另解】**若哈夫曼编码的长度只允许小于或等于4,则哈夫曼树的高度最高是5,已知一个字符编码为1,另一个字符编码是01,这说明第二层和第三层各有一个叶结点,为使得该树从第3层起能够对尽可能多的字符编码,余下的二叉树应该是满二叉树,如下图所示,底层可以有4个叶结点,最多可以再对4个字符编码。
(Image of a binary tree)
**05. B**
根据上题的结论,叶结点数为(215+1)/2=108,所以共有108个不同的码字。
**【另解】**在哈夫曼树中只有度为0和2的结点,结点总数$n=n_0+n_2$,且$n_0=n_2+1$,由题知$n=215, n_0=108$。
**06. C**
在哈夫曼树的构造中,每个初始结点最终都成为叶结点,5个初始结点构造的哈夫曼树共新建4个双分支结点,4个双分支结点所构成的高度最高的哈夫曼树如下图所示,其高度是5。
(Image of a skewed binary tree)
**07. D**
在哈夫曼树的构造过程中,每次选根的权值最小的两棵树,一棵作为左子树,一棵作为右子树,生成新的二叉树,新的二叉树根的权值应为其左右两棵子树根结点权值的和。至于谁做左子树,谁做右子树,没有限制,所以构造的哈夫曼树是不唯一的,但其带权路径长度是最小的和唯一的。哈夫曼树只有度为0和2的结点,度为0的结点是外结点,带有权值,没有度为1的结点。

---
(Image content of page 10)

**08. C**
$n$个初始结点构造的哈夫曼树共新建$n-1$个双分支结点,因此哈夫曼树的结点总数是$2n-1$,是个奇数,选项I正确。哈夫曼树中没有度为1的结点,选项II错误。哈夫曼的带权路径长度有两种计算方法:①所有叶结点的带权路径长度之和;②所有分支结点的权值之和,选项III正确。
**09. C**
一棵度为$m$的哈夫曼树应只有度为0和$m$的结点,设度为$m$的结点有$n_m$个,度为0的结点有$n_0$个,又设结点总数为$N, N=n_0+n_m$。因有$N$个结点的哈夫曼树有$N-1$条分支,则$mn_m=N-1=n_m+n_0-1$,整理得$(m-1)n_m=n_0-1, n_m=(n_0-1)/(m-1)$。
**10. B**
并查集的存储结构是用双亲表示法存储的树,主要是为了方便两个重要的操作。
**11. C**
初始时,0~9各自成一个集合。查找1-2时,合并{1}和{2};查找3-4时,合并{3}和{4};查找5-6时,合并{5}和{6};查找7-8时,合并{7}和{8};查找8-9时,合并{7, 8}和{9};查找1-8时,合并{1, 2}和{7, 8, 9};查找0-5时,合并{0}和{5, 6};查找1-9时,它们属于同一个集合。最终的集合为{0, 5, 6}、{1, 2, 7, 8, 9}和{3, 4},因此答案选择选项C。
**12. D**
依次探测图的各条边,用并查集检查该边依附的两个顶点是否已属于同一集合(两个顶点的根结点是否相同)。若是,则说明图中存在环路,选项A错误。经过路径优化后,并查集在最坏情况下的高度远小于$O(n)$,选项B错误。Find操作总返回当前根结点作为集合的标志,选项C错误。
**13. D**
在用并查集实现Kruskal算法求图的最小生成树时:判断是否加入一条边之前,先查找这条边关联的两个顶点是否属于同一个集合(判断加入这条边之后是否形成回路),若形成回路,则继续判断下一条边;若不形成回路,则将该边和边对应的顶点加入最小生成树T,并继续判断下一条边,直到所有顶点都已加入最小生成树T。选项B正确。用并查集判断无向图连通性的方法:遍历无向图的边,每遍历到一条边,就把这条边连接的两个顶点合并到同一个集合中,处理完所有边后,只要是相互连通的顶点都会被合并到同一个子集合中,相互不连通的顶点一定在不同的子集合中。选项C正确。未做路径优化的并查集在最坏情况下的高度为$n$,此时查找操作的时间复杂度为$O(n)$,时间复杂度通常指最坏情况下的时间复杂度。选项D错误。
**14. A**
哈夫曼树为带权路径长度最小的二叉树,不一定是完全二叉树。哈夫曼树中没有度为1的结点,选项B正确。构造哈夫曼树时,最先选取两个权值最小的结点作为左、右子树构造一棵新的二叉树,选项C正确。哈夫曼树中任意一个非叶结点的权值为其左、右子树根结点的权值之和,可知,哈夫曼树中任意一个非叶结点的权值一定不小于下一层任意一个结点的权值。
**15. D**
前缀编码的定义是在一个字符集中,任何一个字符的编码都不是另一个字符编码的前缀。选项D中的编码110是编码1100的前缀,违反了前缀编码的规则,所以选项D不是前缀编码。
**16. D**
在哈夫曼树中,左右孩子权值之和为父结点权值。仅以分析选项A为例:若两个10分别属于两棵不同的子树,则根的权值不等于其孩子的权值和,不符;若两个10属同棵子树,则其权值不等于其两个孩子(叶结点)的权值和,不符。选项B、C选项的排除方法相同。

---
(Image content of page 11)

**17. D**
哈夫曼编码是前缀编码,各个编码的前缀不同,因此直接拿编码序列与哈夫曼编码一一比对即可。序列可分割为0100 011 001 001 011 11 0101,译码结果是afeefgd。选项D正确。
**18. A**
根据各字符出现的次数构造的哈夫曼树如下图所示。由图可知,a、c和e的编码长度应该相同;a和c的第1个编码应该相同,且与e的第1个编码不同;b和d的前3个编码应该相同。
(Image of a Huffman tree for characters a, b, c, d, e, f)
**19. C**
$n$个符号构造成哈夫曼树的过程中,共新建了$n-1$个结点(双分支结点),因此哈夫曼树的结点总数为$2n-1=115$,$n$的值为58。
**20. B**
对于带权值的结点,构造出哈夫曼树的带权路径长度(WPL)最小,哈夫曼树的构造过程如下图所示。求得其WPL=(10+12)×3+(30+16+21)×2=200。
(Image showing the steps of constructing a Huffman tree)
**21. D**
可以画一个简单的特例来证明。图1是满足条件的二叉树$T_1$,图2是满足条件的二叉树$T_2$,结点中有值表示这个结点是编码字符。$T_1$和$T_2$的结点数不同,选项A错误。$T_1$的高度等于$T_2$的高度,选项B错误。出现频次不同的字符在$T_1$中也可能处于相同的层,选项C错误。对于定长编码集,所有字符一定都在$T_2$中处于相同的层,而且都是叶结点。
(Image of two binary trees, 图1 and 图2)
**22. B**
构建哈夫曼树的过程如下图所示。

---
(Image content of page 12)

(Image showing the steps of merging lists, which resembles Huffman tree construction)
对叶结点的哈夫曼编码,共有4个长度为3的叶结点、2个长度为2的叶结点,编码的加权平均长度为$[(3+4+5+6)\times3+(8+10)\times2]/(3+4+5+6+8+10)=2.5$。

**二、综合应用题**
**01.【解答】**
根据哈夫曼树的构造方法,每次从森林中选取两个根结点值最小的树合并成一棵树,将原先的两棵树作为左、右子树,且新根结点的值为左、右孩子关键字之和。构造过程如下图所示。
(Image showing the construction of a Huffman tree for weights {5, 7, 2, 3, 6, 8, 9})
由构造出的哈夫曼树可得$WPL=(2+3)\times4+(5+6+7)\times3+(8+9)\times2=108$。
> **注意**
> 哈夫曼树并不唯一,但带权路径长度一定是相同的。

**02.【解答】**
1) 最先合并的表中的元素在后续的每次合并中都会再次参与比较,因此求最小合并次数类似于求最小带权路径长度,此时可立即想到哈夫曼树。根据哈夫曼树的构造过程,每次选择表集合中长度最小的两个表进行合并。6个表的合并顺序如下图所示。

---
(Image content of page 13)

(Image of a Huffman tree representing the merging of 6 lists)
根据图中的哈夫曼树,6个序列的合并过程如下:
① 在表集合{10, 35, 40, 50, 60, 200}中,选择表A与表B合并,生成含45个元素的表AB。
② 在表集合{40, 45, 50, 60, 200}中,将表AB与表C合并,生成含85个元素的表ABC。
③ 在表集合{50, 60, 85, 200}中,表D与表E合并,生成含110个元素的表DE。
④ 在表集合{85, 110, 200}中,表ABC与表DE合并,生成含195个元素的表ABCDE。
⑤ 当前表集合为{195, 200},表ABCDE与表F合并,生成含395个元素的表ABCDEF。
因为合并两个长度分别为$m$和$n$的有序表,最坏情况下需要比较$m+n-1$次,所以最坏情况下比较的总次数计算如下:
第1次合并:最多比较次数=10+35-1=44。
第2次合并:最多比较次数=40+45-1=84。
第3次合并:最多比较次数=50+60-1=109。
第4次合并:最多比较次数=85+110-1=194。
第5次合并:最多比较次数=195+200-1=394。
比较的总次数最多为44+84+109+194+394=825。
2) 各表的合并策略是:对多个有序表进行两两合并时,若表长不同,则最坏情况下总的比较次数依赖于表的合并次序。可以借助于哈夫曼树的构造思想,依次选择最短的两个表进行合并,此时可以获得最坏情况下的最佳合并效率。
**03.【解答】**
1) 使用一棵二叉树保存字符集中各字符的编码,每个编码对应于从根开始到达某叶结点的一条路径,路径长度等于编码位数,路径到达的叶结点中保存该编码对应的字符。
2) 从左至右依次扫描0/1串中的各位。从根开始,根据串中当前位沿当前结点的左子指针或右子指针下移,直到移动到叶结点时为止。输出叶结点中保存的字符。然后从根开始重复这个过程,直到扫描到0/1串结束,译码完成。
3) 二叉树既可用于保存各字符的编码,又可用于检测编码是否具有前缀特性。判定编码是否具有前缀特性的过程,也是构建二叉树的过程。初始时,二叉树中仅含有根结点,其左子指针和右子指针均为空。
依次读入每个编码C,建立/寻找从根开始对应于该编码的一条路径,过程如下:
对每个编码,从左至右扫描C的各位,根据C的当前位(0或1)沿结点的指针(左子指针或右子指针)向下移动。当遇到空指针时,创建新结点,让空指针指向该新结点并继续移动。沿指针移动的过程中,可能遇到三种情况:
① 若遇到了叶结点(非根),则表明不具有前缀特性,返回。
② 若在处理C的所有位的过程中,均没有创建新结点,则表明不具有前缀特性,返回。

---
(Image content of page 14)

③ 若在处理C的最后一个编码位时创建了新结点,则继续验证下一个编码。
若所有编码均通过验证,则编码具有前缀特性。

### 归纳总结
本章的内容较多,其中二叉树是极其重要的考查点。关于二叉树的有关操作,在2014年的统考中首次出现了线性表以外的算法设计题,需要引起读者的注意。
遍历是二叉树的各种操作的基础,统考时会考查遍历过程中对结点的各种其他操作,而且容易结合递归算法和利用栈或队列的非递归算法。读者需重点掌握各种遍历方法的代码书写,并学会在遍历的基础上,进行一些其他的相关操作。其中递归算法短小精悍,出现的概率较大,请读者不要掉以轻心,要做到对几种遍历方式的程序模板烂熟于心,并结合一定数量的习题,才可以在考试中快速地写出漂亮的代码。
二叉树遍历算法的递归程序:
```c
void Track(BiTree *p) {
    if(p!=NULL) {
        // (1)
        Track(p->lchild);
        // (2)
        Track(p->rchild);
        // (3)
    }
}
```
访问函数visit()位于(1)、(2)、(3)的位置,分别对应于先序、中序、后序遍历。但对于具体题目来说,设计算法时要灵活应用。请读者认真练习下面的例题。
**例题：**设二叉树的存储结构为二叉链表,编写有关二叉树的递归算法。
1) 统计二叉树中度为1的结点个数。
2) 统计二叉树中度为2的结点个数。
3) 统计二叉树中度为0的结点个数。
4) 统计二叉树的高度。
5) 统计二叉树的宽度。
6) 从二叉树中删去所有叶结点。
7) 计算指定结点*p所在的层次。
8) 计算二叉树中各结点中的最大元素的值。
9) 交换二叉树中每个结点的两个子女。
10) 以先序次序输出一棵二叉树中所有结点的数据值及结点所在的层次。

### 思维拓展
输入一个整数data和一棵二元树。从树的根结点开始往下访问一直到叶结点,所经过的所有结点形成一条路径。打印出路径及与data相等的所有路径。例如,输入整数22和下图所示的二元树,则打印出两条路径10, 12和10, 5, 7。