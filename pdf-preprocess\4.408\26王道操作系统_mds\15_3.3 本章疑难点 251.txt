和修改字段(脏位)。
20. **【解答】**
1)
① 页面大小 = $2^{12}B = 4096B = 4KB$。每个数组元素4B,每个页面可以存放$4KB/4B = 1024$个数组元素,正好是数组的一行,数组$a$ 按行优先方式存放。1080 0000H 的虚页号为10800H,因此$a[0]$行存放在虚页号为10800H 的页面中, $a[1]$行存放在页号为10801H的页面中。$a[1][2]$的虚拟地址为$10801 000H + 4×2 = 10801 008H$。
② 转换为二进制$0001000010 0000000001 000000001000$,根据虚拟地址结构可知,对应的页目录号为042H,页号为001H。
③ 进程的页目录表始址为0020 1000H,每个页目录项长4B,因此042H号页目录项的物理地址是$0020 1000H + 4×42H = 0020 1108H$。
④ 页目录项存放的页框号为00301H,二级页表的始址为00301 000H,因此$a[1][2]$所在页的页号为001H,每个页表项4B,因此对应的页表项物理地址是$00301 000H + 001H×4 = 00301 004H$。
2) 根据数组的随机存取特点,数组$a$在虚拟地址空间中所占的区域必须连续,因为数组$a$不止占用一页,相邻逻辑页在物理上不一定相邻,所以数组$a$在物理地址空间中所占的区域可以不连续。
3) 由1)可知每个页面正好可以存放一整行的数组元素,“按行优先方式存放”意味着数组的同一行的所有元素都存放在同一个页面中,同一列的各个元素都存放在不同的页面中,因此数组$a$按行遍历的局部性较好。
21. **【解答】**
1) 页表项的虚拟地址为$B8C0 0000H + 48H << 2 = B8C0 0120H$。页表项的物理地址为$6540 0000H+48H << 2 = 6540 0120H$。相应页表项中的页框号为$BAB4 5678H >> 22 = 2EAH$。
2) 进程$P$的页表所在页的页号为$B8C0 0000H >> 22 = 2E3H$。页表项的虚拟地址为$B8C0 0000H + 2E3H << 2 = B8C0 0B8CH$。页表项中的页框号为$6540 0000H >> 22 = 195H$。
**注意,** 符号“<<”表示左移操作,符号“>>”表示右移操作。将二进制数左移1位,与“乘以2”的效果等价。页表项中的页框号为物理地址的前10位可以通过右移22位得到。

### 3.3 本章疑难点
分页管理方式和分段管理方式在很多地方是相似的,比如在内存中都是不连续的、都有地址变换机构来进行地址映射等。但两者也存在许多区别,表3.6列出了两种方式的对比。

**表3.6 分页管理方式和分段管理方式的比较**
| | 分页 | 分段 |
| :---: | :--- | :--- |
| 目的 | 分页仅是系统管理上的需要,是为实现离散分配方式,以提高内存的利用率。而不是用户的需要 | 段是信息的逻辑单位,它含有一组意义相对完整的信息。分段的目的是能更好地满足用户的需要 |
| 长度 | 页的大小固定且由系统决定,由系统将逻辑地址划分为页号和页内地址两部分,是由机器硬件实现的 | 段的长度不固定,决定于用户所编写的程序,通常由编译程序在编译时根据信息的性质来划分 |
| 地址空间 | 分页的程序地址空间是一维的,即单一的线性地址空间,程序员利用一个记忆符即可表示一个地址 | 分段的程序地址空间是二维的,程序员在标识一个地址时,既需给出段名,又需给出段内地址 |
| 碎片 | 有内部碎片,无外部碎片 | 有外部碎片,无内部碎片 |

## 第4章 文件管理

**【考纲内容】**
(一) 文件
文件的基本概念;文件元数据和索引节点(inode)
文件的操作:建立,删除,打开,关闭,读,写
文件的保护;文件的逻辑结构;文件的物理结构
(二) 目录
目录的基本概念;树形目录;目录的操作;硬链接和软链接
(三) 文件系统
文件系统的全局结构(layout):文件系统在外存中的结构,文件系统在内存中的结构
外存空闲空间管理办法;虚拟文件系统;文件系统挂载(mounting)

**【复习提示】**
本章内容较为具体,要注意对概念的理解。重点掌握文件系统的结构及其实现、文件分配和空闲空间管理等。要掌握文件系统的文件控制块、物理分配方法、索引结构、树形目录结构、文件共享原理、文件系统的布局、虚拟文件系统原理等。这些都是统考真题容易考查的内容。

### 4.1 文件系统基础
在学习本节时,请读者思考以下问题:
1) 什么是文件?
2) 单个文件的逻辑结构和物理结构之间是否存在某些制约关系?
本节内容较为抽象,要注意区分文件的逻辑结构和物理结构。在学习过程中,可尝试以上面的两个问题为线索,构建整个文件系统的概念。在前面的学习中,曾经提醒过读者不要忽略对基本概念的理解。从历年的情况来看,大部分同学对进程管理、内存管理有较好的掌握,但对于文件管理及后面的$I/O$管理,往往理解不太深入。在考试中,即使面对一些基本问题也容易失分,这十分可惜。主要原因还是对概念的理解不够全面和透彻,希望读者能够关注这个问题。

### 4.1.1 文件的基本概念
文件(File)是以硬盘为载体的存储在计算机上的信息集合,文件可以是文本文档、图片、程序等。在系统运行时,计算机以进程为基本单位进行资源的调度和分配;而在用户进行的输入、输出中,则以文件为基本单位。大多数应用程序的输入都是通过文件来实现的,其输出也都保存在文件中,以便信息的长期存储及将来的访问。当用户将文件用于程序的输入、输出时,还希望