⑤ $j=6$时 $k=\text{next}[j-1]=\text{next}[5]=2$, 观察$S[j-1]$ ($S[5]$)与$S[k]$ ($S[2]$)是否相等, $S[5]=a$, $S[2]=a$, $S[5]=S[2]$, 所以$\text{next}[j]=k+1=3$。
```
        ↓j-1=5
a a b a a c
      a a b a a c
        ↑k=2
```
最后的结果为
| 编号 | 1 | 2 | 3 | 4 | 5 | 6 |
| :--- | :-- | :-- | :-- | :-- | :-- | :-- |
| S | a | a | b | a | a | c |
| next | 0 | 1 | 2 | 1 | 2 | 3 |

也可以通过求部分匹配值表的方法来求 next 数组。
2) 利用KMP算法的匹配过程如下。
第一趟: 从主串和模式串的第1个字符开始比较, 失配时$i=6$, $j=6$。
```
主串 a a b a a b a a b a a c
     a a b a a c
```
第二趟: $\text{next}[6]=3$, 主串当前位置和模式串的第3个字符继续比较, 失配时$i=9$, $j=6$。
```
主串 a a b a a b a a b a a c
           a a b a a c
```
第三趟: $\text{next}[6]=3$, 主串当前位置和模式串的第3个字符继续比较, 匹配成功。
```
主串 a a b a a b a a b a a c
                 a a b a a c
```

### 归纳总结

学习 KMP算法时, 应从分析暴力法的弊端入手, 思考如何去优化它。实际上, 已匹配相等的序列就是模式串的某个前缀, 因此每次回溯就相当于模式串与模式串的某个前缀在比较, 这种频繁的重复比较是其效率低的原因。这时, 可从分析模式串本身的结构入手, 以便得知当匹配到某个字符不等时, 应该向右滑动到什么位置, 即已匹配相等的某个前缀若与模式串首尾重合, 则对齐它们, 对齐部分显然无需再比较, 然后直接从主串的当前位置继续开始比较。

### 思维拓展

编程实现: 模式串在主串中有多少个完全匹配的子串? 注意, 统考不考 KMP 算法题。