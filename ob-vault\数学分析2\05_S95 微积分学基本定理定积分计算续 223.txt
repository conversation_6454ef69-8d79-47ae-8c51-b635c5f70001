### §5 微积分学基本定理・定积分计算(续)

4. 设$f$在$[a,b]$上连续,且$f(x)$不恒等于零,证明$\int_a^b (f(x))^2dx > 0$.
5. 设$f$与$g$都在$[a,b]$上可积,证明
   $M(x) = \max\{f(x), g(x)\}, m(x) = \min\{f(x), g(x)\}$
   在$[a,b]$上也都可积.
6. 试求心形线$r=a(1+\cos\theta), 0\le\theta\le 2\pi$上各点极径的平均值.
7. 设$f$在$[a,b]$上可积,且在$[a,b]$上满足$|f(x)|\ge m>0$. 证明$\frac{1}{f}$在$[a,b]$上也可积.
8. 进一步证明积分第一中值定理(包括定理9.7和定理9.8)中的中值点$\xi\in(a,b)$.
9. 证明:若$f$与$g$都在$[a,b]$上可积,且$g(x)$在$[a,b]$上不变号,$M,m$分别为$f(x)$在$[a,b]$上的上、下确界,则必存在某实数$\mu(m\le\mu\le M)$,使得
   $$
   \int_a^b f(x)g(x)dx = \mu \int_a^b g(x)dx.
   $$
10. 证明:若$f$在$[a,b]$上连续,且$\int_a^b f(x)dx = \int_a^b xf(x)dx = 0$,则在$(a,b)$上至少存在两点$x_1, x_2$,使$f(x_1)=f(x_2) = 0$. 又若$\int_a^b x^2f(x)dx=0$,这时$f$在$(a,b)$上是否至少有三个零点?
11. 设$f$在$[a,b]$上二阶可导,且$f''(x)>0$. 证明:
    (1) $f(\frac{a+b}{2}) \le \frac{1}{b-a}\int_a^b f(x)dx$;
    (2) 又若$f(x)\le 0, x\in[a,b]$,则又有
    $$
    f(x) \ge \frac{2}{b-a}\int_a^b f(x)dx, x\in[a,b].
    $$
12. 证明:
    (1) $\ln(1+n) < 1+\frac{1}{2}+\cdots+\frac{1}{n} < 1+\ln n$;
    (2) $\lim_{n\to\infty} \frac{1+\frac{1}{2}+\cdots+\frac{1}{n}}{\ln n} = 1$.

### §5 微积分学基本定理・定积分计算(续)

当函数的可积性问题告一段落,并对定积分的性质有了足够的认识之后,接着要来解决一个以前多次提到过的问题——在定积分形式下证明连续函数必定存在原函数.

#### 一 变限积分与原函数的存在性
设$f$在$[a,b]$上可积,根据定积分的性质4,对任何$x\in[a,b]$,$f$在$[a,x]$上也可积,于是,由
$$
\Phi(x) = \int_a^x f(t)dt, \quad x\in[a,b] \ag{1}
$$
定义了一个以积分上限$x$为自变量的函数,称为变上限的定积分. 类似地,又可定义变下限的定积分:
$$
\Psi(x) = \int_x^b f(t)dt, \quad x\in[a,b]. \ag{2}
$$
$\Phi$与$\Psi$统称为变限积分.注意,在变限积分(1)与(2)中,不可再把积分变量写成$x$(例如$\int_a^x f(x)dx$),以免与积分上、下限的$x$相混淆.
变限积分所定义的函数有着重要的性质.由于
$$
\int_x^b f(t)dt = -\int_b^x f(t)dt,
$$
因此下面只讨论变上限积分的情形.

**定理 9.9** 若$f$在$[a,b]$上可积,则由(1)式所定义的函数$\Phi$在$[a,b]$上连续.

**证** 对$[a,b]$上任一确定的点$x$,只要$x+\Delta x\in[a,b]$,按定义式(1)有
$$
\Delta\Phi = \int_a^{x+\Delta x} f(t)dt - \int_a^x f(t)dt = \int_x^{x+\Delta x} f(t)dt.
$$
因$f$在$[a,b]$上有界,可设$|f(t)|\le M, t\in[a,b]$.于是,当$\Delta x > 0$时有
$$
|\Delta\Phi| = \left|\int_x^{x+\Delta x}f(t)dt\right| \le \int_x^{x+\Delta x}|f(t)|dt \le M\Delta x;
$$
当$\Delta x < 0$时则有$|\Delta\Phi|\le M|\Delta x|$. 由此得到
$$
\lim_{\Delta x\to 0} \Delta\Phi = 0,
$$
即证得$\Phi$在点$x$连续.由$x$的任意性,$\Phi$在$[a,b]$上处处连续.
□

**定理 9.10 (原函数存在定理)** 若$f$在$[a,b]$上连续,则由(1)式所定义的函数$\Phi$在$[a,b]$上处处可导,且
$$
\Phi'(x) = \frac{d}{dx}\int_a^x f(t)dt = f(x), \quad x\in[a,b]. \ag{3}
$$

**证** 对$[a,b]$上任一确定的$x$,当$\Delta x \ne 0$且$x+\Delta x\in[a,b]$时,按定义式(1)和积分第一中值定理,有
$$
\begin{aligned}
\frac{\Delta\Phi}{\Delta x} &= \frac{1}{\Delta x}\int_x^{x+\Delta x} f(t)dt \\
&= f(x+\theta\Delta x), \quad 0 \le \theta \le 1.
\end{aligned}
$$
由于$f$在点$x$连续,故有
$$
\Phi'(x) = \lim_{\Delta x\to 0}\frac{\Delta\Phi}{\Delta x} = \lim_{\Delta x\to 0}f(x+\theta\Delta x) = f(x).
$$
由$x$在$[a,b]$上的任意性,证得$\Phi$是$f$在$[a,b]$上的一个原函数.
□

本定理沟通了导数和定积分这两个从表面看去似不相干的概念之间的内在联系;同时也证明了“连续函数必有原函数”这一基本结论,并以积分形式(1)给出了$f$的一个原函数.正因为定理9.10的重要作用而被誉为微积分学基本定理.

此外,又因$f$的任意两个原函数只能相差一个常数,所以当$f$为连续函数时,它的任一原函数$F$必满足
$$
F(x) = \int_a^x f(t)dt + C.
$$
若在此式中令$x=a$,得到$C=F(a)$,从而有
$$
\int_a^x f(t)dt = F(x) - F(a).
$$
再令$x=b$,即得
$$
\int_a^b f(t)dt = F(b) - F(a). \ag{4}
$$
这是牛顿—莱布尼茨公式的又一证明,比照定理9.1,现在只需假设被积函数$f$为连续函数,其原函数$F$的存在性已为定理9.10所保证,无需另作假设.

**例 1** 求极限 $\lim_{x\to+\infty}(\int_0^x e^{t^2}dt)^{\frac{1}{x^2}}$.

**解** 应用洛必达法则及定理9.10得到
$$
\begin{aligned}
\lim_{x\to+\infty}\ln(\int_0^x e^{t^2}dt)^{\frac{1}{x^2}} &= \lim_{x\to+\infty} \frac{\ln(\int_0^x e^{t^2}dt)}{x^2} \\
&= \lim_{x\to+\infty} \frac{\frac{e^{x^2}}{\int_0^x e^{t^2}dt}}{2x} = \lim_{x\to+\infty} \frac{e^{x^2}}{2x\int_0^x e^{t^2}dt} \\
&= \lim_{x\to+\infty} \frac{2xe^{x^2}}{2\int_0^x e^{t^2}dt + 2x^2e^{x^2}} \\
&= \lim_{x\to+\infty} \frac{e^{x^2}+2x^2e^{x^2}}{2e^{x^2}+2x^2e^{x^2}} = \lim_{x\to+\infty} \frac{1+2x^2}{2+2x^2} = 1.
\end{aligned}
$$
所以
$$
\lim_{x\to+\infty}(\int_0^x e^{t^2}dt)^{\frac{1}{x^2}} = \lim_{x\to+\infty} e^{\frac{\ln(\int_0^x e^{t^2}dt)}{x^2}} = e.
$$
□

利用变限积分又能证明下述积分第二中值定理.

**定理 9.11 (积分第二中值定理)** 设函数$f$在$[a,b]$上可积.
(i) 若函数$g$在$[a,b]$上减,且$g(x)\ge 0$,则存在$\xi\in[a,b]$,使得
$$
\int_a^b f(x)g(x)dx = g(a)\int_a^\xi f(x)dx; \ag{5}
$$
(ii) 若函数$g$在$[a,b]$上增,且$g(x)\ge 0$,则存在$\eta\in[a,b]$,使得
$$
\int_a^b f(x)g(x)dx = g(b)\int_\eta^b f(x)dx. \ag{6}
$$

**证** 下面只证(i),类似地可证(ii). 设
$$
F(x) = \int_a^x f(t)dt, \quad x\in[a,b].
$$
由于$f$在$[a,b]$上可积,因此$F$在$[a,b]$上连续,从而存在最大值$M$和最小值$m$.
若$g(a)=0$,由假设$g(x)=0, x\in[a,b]$,此时对任何$\xi\in[a,b]$,(5)式恒成立.下面设$g(a)>0$,这时(5)式即为
$$
F(\xi) = \int_a^\xi f(t)dt = \frac{1}{g(a)}\int_a^b f(x)g(x)dx. \ag{5'}
$$
所以问题转化为只需证明
$$
m \le \frac{1}{g(a)}\int_a^b f(x)g(x)dx \le M, \ag{7}
$$
因为由此可借助$F$的介值性立刻证得(5').当然(7)式又等同于
$$
mg(a) \le \int_a^b f(x)g(x)dx \le Mg(a), \ag{7'}
$$
下面就来证明这个不等式.
由条件$f$有界,设$|f(x)|\le L, x\in[a,b]$;而$g$必为可积,从而对任给的$\epsilon>0$,必有分割$T:a=x_0<x_1<\cdots<x_n=b$,使
$$
\sum_{i=1}^n \omega_i^g\Delta x_i < \frac{\epsilon}{L}.
$$
现把$I = \int_a^b f(x)g(x)dx$按积分区间可加性写成
$$
\begin{aligned}
I &= \sum_{i=1}^n \int_{x_{i-1}}^{x_i} f(x)g(x)dx \\
&= \sum_{i=1}^n \int_{x_{i-1}}^{x_i} [g(x)-g(x_{i-1})]f(x)dx + \sum_{i=1}^n g(x_{i-1})\int_{x_{i-1}}^{x_i}f(x)dx \\
&= I_1 + I_2.
\end{aligned}
$$
对于$I_1$,必有
$$
|I_1| \le \sum_{i=1}^n \int_{x_{i-1}}^{x_i}|g(x)-g(x_{i-1})|\cdot|f(x)|dx \\
\le L\sum_{i=1}^n \omega_i^g\Delta x_i < L \cdot \frac{\epsilon}{L} = \epsilon.
$$
对于$I_2$,由于$F(x_0)=F(a)=0$,和
$$
\int_{x_{i-1}}^{x_i}f(x)dx = \int_a^{x_i}f(x)dx - \int_a^{x_{i-1}}f(x)dx = F(x_i) - F(x_{i-1}),
$$
可得
$$
\begin{aligned}
I_2 &= \sum_{i=1}^n g(x_{i-1})[F(x_i)-F(x_{i-1})] \\
&= g(x_0)[F(x_1)-F(x_0)] + \cdots + g(x_{n-1})[F(x_n)-F(x_{n-1})]
\end{aligned}
$$
$$
\begin{aligned}
&= F(x_1)[g(x_0)-g(x_1)] + \cdots + F(x_{n-1})[g(x_{n-2})-g(x_{n-1})] \\
&\quad + F(x_n)g(x_{n-1}) \\
&= \sum_{i=1}^{n-1} F(x_i)[g(x_{i-1})-g(x_i)] + F(b)g(x_{n-1}).
\end{aligned}
$$
再由$g(x)\ge 0$且减,使得其中$g(x_{i-1})\ge 0, g(x_{i-1})-g(x_i)\ge 0, i=1,2,\dots,n-1$. 于是利用$F(x_i)\le M, i=1,2,\dots,n$估计得
$$
I_2 \le M\sum_{i=1}^{n-1}[g(x_{i-1})-g(x_i)]+Mg(x_{n-1}) = Mg(a).
$$
同理由$F(x_i)\ge m, i=1,2,\dots,n$又有$I_2 \ge mg(a)$.
综合$I=I_1+I_2, |I_1|<\epsilon, mg(a)\le I_2\le Mg(a)$,得到
$$
-\epsilon + mg(a) \le I \le Mg(a) + \epsilon.
$$
由$\epsilon$为任意小正数,这便证得
$$
mg(a) \le I \le Mg(a),
$$
即不等式(7')成立.随之又有(7), (5')和(5)式成立.
□

**推论** 设函数$f$在$[a,b]$上可积.若$g$为单调函数,则存在$\xi\in[a,b]$,使得
$$
\int_a^b f(x)g(x)dx = g(a)\int_a^\xi f(x)dx + g(b)\int_\xi^b f(x)dx. \ag{8}
$$

**证** 若$g$为单调递减函数,令$h(x) = g(x)-g(b)$,则$h$为非负、递减函数.由定理9.11(i),存在$\xi\in[a,b]$,使得
$$
\int_a^b f(x)h(x)dx = h(a)\int_a^\xi f(x)dx = [g(a)-g(b)]\int_a^\xi f(x)dx.
$$
由于$\int_a^b f(x)h(x)dx = \int_a^b f(x)g(x)dx - g(b)\int_a^b f(x)dx$,因此证得
$$
\begin{aligned}
\int_a^b f(x)g(x)dx &= g(b)\int_a^b f(x)dx + [g(a)-g(b)]\int_a^\xi f(x)dx \\
&= g(a)\int_a^\xi f(x)dx + g(b)\int_\xi^b f(x)dx.
\end{aligned}
$$
若$g$为单调递增函数,只需令$h(x)=g(x)-g(a)$,并由定理9.11(ii)和(6),同样可证得(8)式成立.
□

积分第二中值定理以及它的推论是今后建立反常积分收敛判别法的工具.

#### 二 换元积分法与分部积分法
对原函数的存在性有了正确的认识,就能顺利地把不定积分的换元积分法和分部积分法移植到定积分计算中来.

**定理 9.12 (定积分换元积分法)** 若函数$f$在$[a,b]$上连续,$\phi'$在$[\alpha,\beta]$上可积,且满足
$\phi(\alpha)=a, \phi(\beta)=b, \phi([\alpha,\beta]) \subseteq [a,b]$,
则有定积分换元公式:
$$
\int_a^b f(x)dx = \int_\alpha^\beta f(\phi(t))\phi'(t)dt. \ag{9}
$$

**证** 由于$f$在$[a,b]$上连续,因此它的原函数存在.设$F$是$f$在$[a,b]上的一个原函数,由复合函数微分法
$$
\frac{d}{dt}(F(\phi(t))) = F'(\phi(t))\phi'(t) = f(\phi(t))\phi'(t),
$$
可见$F(\phi(t))$是$f(\phi(t))\phi'(t)$的一个原函数.因为$f(\phi(t))\phi'(t)$在$[\alpha,\beta]$上可积,根据牛顿—莱布尼茨公式(定理9.1)的注2,2)之所述,证得
$$
\int_\alpha^\beta f(\phi(t))\phi'(t)dt = F(\phi(\beta)) - F(\phi(\alpha)) = F(b)-F(a) = \int_a^b f(x)dx.
$$
□

从以上证明看到,在用换元法计算定积分时,一旦得到了用新变量表示的原函数后,不必作变量还原,而只要用新的积分限代入并求其差值就可以了.这就是定积分换元积分法与不定积分换元积分法的区别,这一区别的原因在于不定积分所求的是被积函数的原函数,理应保留与原来相同的自变量;而定积分的计算结果是一个确定的数,如果(9)式一边的定积分计算出来了,那么另一边的定积分自然也求得了.

**注** 如果在定理9.12的条件中只假定$f$为可积函数,但还要求$\phi$是单调的,那么(9)式仍然成立.(本节习题第14题.)

**例 2** 计算$\int_0^1 \sqrt{1-x^2}dx$.

**解** 令$x=\sin t$,当$t$由$0$变到$\frac{\pi}{2}$时,$x$由$0$增到$1$,故取$[\alpha,\beta]=[0,\frac{\pi}{2}]$.应用公式(9),并注意到在第一象限中$\cos t\ge 0$,则有
$$
\int_0^1 \sqrt{1-x^2}dx = \int_0^{\pi/2} \sqrt{1-\sin^2 t}\cos tdt = \int_0^{\pi/2} \cos^2 t dt
$$
$$
= \frac{1}{2}\int_0^{\pi/2}(1+\cos 2t)dt = \frac{1}{2}\left(t+\frac{1}{2}\sin 2t\right)\bigg|_0^{\pi/2} = \frac{\pi}{4}.
$$
□

**例 3** 计算$\int_0^{\pi/2} \sin t \cos^2 t dt$.

**解** 逆向使用公式(9),令$x=\cos t, dx=-\sin t dt$,当$t$由$0$变到$\frac{\pi}{2}$时,$x$由$1$减到$0$,则有
$$
\int_0^{\pi/2} \sin t\cos^2 t dt = -\int_1^0 x^2dx = \int_0^1 x^2dx = \frac{1}{3}.
$$
□

**例 4** 计算$J=\int_0^1 \frac{\ln(1+x)}{1+x^2}dx$.

**解** 令$x=\tan t$,当$t$从$0$变到$\frac{\pi}{4}$时,$x$从$0$增到$1$.于是由公式(9)及$dt=\frac{dx}{1+x^2}$得到
$$
\begin{aligned}
J &= \int_0^{\pi/4} \ln(1+\tan t)dt = \int_0^{\pi/4} \ln\frac{\cos t + \sin t}{\cos t}dt \\
&= \int_0^{\pi/4} \ln\frac{\sqrt{2}\cos(\frac{\pi}{4}-t)}{\cos t}dt \\
&= \int_0^{\pi/4}\ln\sqrt{2}dt + \int_0^{\pi/4}\ln\cos(\frac{\pi}{4}-t)dt - \int_0^{\pi/4}\ln\cos tdt.
\end{aligned}
$$
对最末第二个定积分作变换$u=\frac{\pi}{4}-t$,有
$$
\int_0^{\pi/4}\ln\cos(\frac{\pi}{4}-t)dt = \int_{\pi/4}^0\ln\cos u(-du) = \int_0^{\pi/4}\ln\cos u du,
$$
它与上面第三个定积分相消.故得
$$
J = \int_0^{\pi/4}\ln\sqrt{2}dt = \frac{\pi}{4}\ln\sqrt{2} = \frac{\pi}{8}\ln 2.
$$
□

事实上,例4中的被积函数的原函数虽然存在,但难以用初等函数来表示,因此无法直接使用牛顿—莱布尼茨公式.可是像上面那样,利用定积分的性质和换元公式(9),消去了其中无法求出原函数的部分,最终得出这个定积分的值.
换元积分法还可用来证明一些特殊的积分性质,如本节习题中的第5,6,7等题.

**定理 9.13 (定积分分部积分法)** 若$u(x),v(x)$为$[a,b]$上的可微函数,且$u'(x)$和$v'(x)$都在$[a,b]$上可积,则有定积分分部积分公式:
$$
\int_a^b u(x)v'(x)dx = u(x)v(x)\bigg|_a^b - \int_a^b u'(x)v(x)dx. \ag{10}
$$

**证** 因为$uv$是$uv'+u'v$在$[a,b]$上的一个原函数,所以有
$$
\int_a^b u(x)v'(x)dx + \int_a^b u'(x)v(x)dx = \int_a^b [u(x)v'(x) + u'(x)v(x)]dx
$$
$$
= u(x)v(x)\bigg|_a^b.
$$
移项后即为(10)式.
□

为方便起见,公式(10)允许写成
$$
\int_a^b u(x)dv(x) = u(x)v(x)\bigg|_a^b - \int_a^b v(x)du(x). \ag{10'}
$$

**例 5** 计算$\int_1^e x^2\ln xdx$.

**解** $\int_1^e x^2\ln xdx = \frac{1}{3}\int_1^e \ln x d(x^3) = \frac{1}{3}(x^3\ln x\big|_1^e - \int_1^e x^2dx)$
$= \frac{1}{3}(e^3 - \frac{1}{3}x^3\big|_1^e) = \frac{1}{9}(2e^3+1)$.
□

**例 6** 计算$\int_0^{\pi/2}\sin^n x dx$和$\int_0^{\pi/2}\cos^n xdx, n=1,2,\dots$.

**解** 当$n\ge 2$时,用分部积分求得
$$
\begin{aligned}
J_n &= \int_0^{\pi/2}\sin^n x dx = -\sin^{n-1}x\cos x\bigg|_0^{\pi/2} + (n-1)\int_0^{\pi/2}\sin^{n-2}x\cos^2 x dx \\
&= (n-1)\int_0^{\pi/2}\sin^{n-2}x dx - (n-1)\int_0^{\pi/2}\sin^n x dx \\
&= (n-1)J_{n-2} - (n-1)J_n.
\end{aligned}
$$
移项整理后得到递推公式:
$$
J_n = \frac{n-1}{n}J_{n-2}, \quad n\ge 2. \ag{11}
$$
由于
$$
J_0 = \int_0^{\pi/2}dx = \frac{\pi}{2}, \quad J_1 = \int_0^{\pi/2}\sin x dx = 1,
$$
重复应用递推式(11)便得
$$
\begin{aligned}
J_{2m} &= \frac{2m-1}{2m}\cdot\frac{2m-3}{2m-2}\cdots\frac{1}{2}\cdot\frac{\pi}{2} = \frac{(2m-1)!!}{(2m)!!}\frac{\pi}{2}, \\
J_{2m+1} &= \frac{2m}{2m+1}\cdot\frac{2m-2}{2m-1}\cdots\frac{2}{3}\cdot 1 = \frac{(2m)!!}{(2m+1)!!}.
\end{aligned} \ag{12}
$$
令$x=\frac{\pi}{2}-t$,可得
$$
\int_0^{\pi/2}\cos^n xdx = -\int_{\pi/2}^0\cos^n(\frac{\pi}{2}-t)dt = \int_0^{\pi/2}\sin^n tdt.
$$
因而这两个定积分是等值的.
□

由例6结论(12)可导出著名的沃利斯(Wallis)公式:
$$
\frac{\pi}{2} = \lim_{m\to\infty}\left[\frac{(2m)!!}{(2m-1)!!}\right]^2\frac{1}{2m+1}. \ag{13}
$$
事实上,由
$$
\int_0^{\pi/2}\sin^{2m+1}xdx < \int_0^{\pi/2}\sin^{2m}xdx < \int_0^{\pi/2}\sin^{2m-1}xdx,
$$
把(12)式代入,得到
$$
\frac{(2m)!!}{(2m+1)!!} < \frac{(2m-1)!!}{(2m)!!}\frac{\pi}{2} < \frac{(2m-2)!!}{(2m-1)!!}.
$$
由此又得
$$
A_m = \left[\frac{(2m)!!}{(2m-1)!!}\right]^2\frac{1}{2m+1} < \frac{\pi}{2} < \left[\frac{(2m)!!}{(2m-1)!!}\right]^2\frac{1}{2m} = B_m.
$$
因为
$$
0 < B_m - A_m = \left[\frac{(2m)!!}{(2m-1)!!}\right]^2\frac{1}{2m(2m+1)} < \frac{\pi}{2}\frac{1}{2m} \to 0 (m\to\infty),
$$
所以$\lim_{m\to\infty}(B_m - A_m)=0$. 而$\frac{\pi}{2}-A_m < B_m-A_m$,故得
$$
\lim_{m\to\infty}A_m = \frac{\pi}{2}\text{(即(13)式)}.
$$
沃利斯公式(13)揭示了$\pi$与整数之间的一种很不寻常的关系.

#### 三 泰勒公式的积分型余项
若在$[a,b]$上$u(x),v(x)$有$n+1$阶连续导函数,则有
$$
\int_a^b u(x)v^{(n+1)}(x)dx = \left[u(x)v^{(n)}(x)-u'(x)v^{(n-1)}(x)+\cdots+(-1)^n u^{(n)}(x)v(x)\right]\bigg|_a^b + (-1)^{n+1}\int_a^b u^{(n+1)}(x)v(x)dx \ag{14}
$$
($n=1,2,\dots$).

这是推广的分部积分公式,读者不难用数学归纳法加以证明.下面应用公式(14)导出泰勒公式的积分型余项.
设函数$f$在点$x_0$的某邻域$U(x_0)$上有$n+1$阶连续导函数.令$x\in U(x_0)$, $u(t)=(x-t)^n, v(t)=f(t), t\in[x_0,x]$ (或$[x,x_0]$).利用(14)式得
$$
\begin{aligned}
\int_{x_0}^x (x-t)^n f^{(n+1)}(t)dt &= \left[(x-t)^n f^{(n)}(t) + n(x-t)^{n-1}f^{(n-1)}(t) + \dots + n!f(t)\right]\bigg|_{x_0}^x + \int_{x_0}^x 0\cdot f(t)dt \\
&= n!f(x) - n!\left[f(x_0)+f'(x_0)(x-x_0)+\cdots+\frac{f^{(n)}(x_0)}{n!}(x-x_0)^n\right]
\end{aligned}
$$
$$
= n!R_n(x),
$$
其中$R_n(x)$即为泰勒公式的$n$阶余项.由此求得
$$
R_n(x) = \frac{1}{n!}\int_{x_0}^x f^{(n+1)}(t)(x-t)^n dt, \ag{15}
$$
这就是泰勒公式的积分型余项.
由于$f^{(n+1)}(t)$连续,$(x-t)^n$在$[x_0,x]$(或$[x,x_0]$)上保持同号,因此由推广的积分第一中值定理,可将(15)式写作
$$
R_n(x) = \frac{f^{(n+1)}(\xi)}{n!}\int_{x_0}^x (x-t)^n dt = \frac{f^{(n+1)}(\xi)}{(n+1)!}(x-x_0)^{n+1},
$$
其中$\xi = x_0+\theta(x-x_0), 0\le\theta\le 1$.这就是以前所熟悉的拉格朗日型余项.
如果直接用积分第一中值定理于(15),则得
$$
R_n(x) = \frac{1}{n!}f^{(n+1)}(\xi)(x-\xi)^n(x-x_0),
$$
$$
\xi = x_0+\theta(x-x_0), 0\le\theta\le 1.
$$
由于
$$
(x-\xi)^n(x-x_0) = [x-x_0-\theta(x-x_0)]^n(x-x_0) = (1-\theta)^n(x-x_0)^{n+1},
$$
因此又可进一步把$R_n(x)$改写为
$$
R_n(x) = \frac{f^{(n+1)}(x_0+\theta(x-x_0))}{n!}(1-\theta)^n(x-x_0)^{n+1}, \quad 0\le\theta\le 1. \ag{16}
$$
特别当$x_0=0$时,又有
$$
R_n(x) = \frac{f^{(n+1)}(\theta x)}{n!}(1-\theta)^n x^{n+1}, \quad 0\le\theta\le 1. \ag{17}
$$
公式(16)、(17)称为泰勒公式的柯西型余项.各种形式的泰勒公式余项,将在第十四章里显示它们的功用.

### 习 题

1. 设$f$为连续函数,$u,v$均为可导函数,且可实行复合$f\circ u$与$f\circ v$.证明:
   $$
   \frac{d}{dx}\int_{u(x)}^{v(x)}f(t)dt = f(v(x))v'(x) - f(u(x))u'(x).
   $$
2. 设$f$在$[a,b]$上连续,$F(x)=\int_a^x f(t)(x-t)dt$. 证明$F''(x)=f(x), x\in[a,b]$.
3. 求下列极限:
   (1) $\lim_{x\to 0}\frac{1}{x}\int_0^x\cos t^2dt$;
   (2) $\lim_{x\to\infty}\frac{(\int_0^x e^{t^2}dt)^2}{\int_0^x e^{2t^2}dt}$;
4. 计算下列定积分:
   (1) $\int_0^{\pi/2} \cos^5 x\sin 2xdx$;
   (2) $\int_0^2 \sqrt{4-x^2}dx$;
   (3) $\int_0^a x^2\sqrt{a^2-x^2}dx (a > 0)$;
   (4) $\int_0^1 \frac{dx}{(x^2+1)^{3/2}}$;
   (5) $\int_0^1 \frac{dx}{e^x+e^{-x}}$;
   (6) $\int_0^{\pi/2}\frac{\cos x}{1+\sin^2 x}dx$;
   (7) $\int_0^1\arcsin xdx$;
   (8) $\int_0^{\pi/2}e^x\sin xdx$;
   (9) $\int_1^e |\ln x|dx$;
   (10) $\int_0^1 \sqrt{e^x}dx$;
   (11) $\int_0^a x^2\sqrt{\frac{a-x}{a+x}}dx(a > 0)$;
   (12) $\int_0^{\pi/2}\frac{\cos\theta}{\sin\theta+\cos\theta}d\theta$.
5. 设$f$在$[-a,a]$上可积. 证明:
   (1) 若$f$为奇函数,则$\int_{-a}^a f(x)dx = 0$;
   (2) 若$f$为偶函数,则$\int_{-a}^a f(x)dx = 2\int_0^a f(x)dx$.
6. 设$f$为$(-\infty, +\infty)$上以$p$为周期的连续周期函数. 证明对任何实数$a$,恒有
   $$
   \int_a^{a+p}f(x)dx = \int_0^p f(x)dx.
   $$
7. 设$f$为连续函数. 证明:
   (1) $\int_0^{\pi/2}f(\sin x)dx = \int_0^{\pi/2} f(\cos x)dx$;
   (2) $\int_0^\pi xf(\sin x)dx = \frac{\pi}{2}\int_0^\pi f(\sin x)dx$.
8. 设$J(m,n) = \int_0^{\pi/2}\sin^m x\cos^n xdx(m,n$为正整数).证明:
   $$
   J(m,n) = \frac{n-1}{m+n}J(m,n-2) = \frac{m-1}{m+n}J(m-2,n),
   $$
   并求$J(2m,2n)$.
9. 证明:若在$(0,+\infty)$上$f$为连续函数,且对任何$a>0$有
   $\int_x^{ax} \frac{f(t)}{t}dt$=常数,$x\in(0, +\infty)$,
   则$f(x) = \frac{c}{x}, x\in(0,+\infty)$, c为常数.
10. 设$f$为连续可微函数,试求
    $$
    \frac{d}{dx}\int_a^x (x-t)f'(t)dt,
    $$
    并用此结果求$\int_0^x (x-t)\sin tdt$.
11. 设$y=f(x)$为$[a,b]$上严格增的连续曲线(图9-12). 试证存在$\xi\in(a,b)$,使图中两阴影部分面积相等.
    
12. 设$f$为$[0,2\pi]$上的单调递减函数. 证明:对任何正整数$n$恒有
    $$
    \int_0^{2\pi} f(x)\sin nxdx \ge 0.
    $$
13. 证明:当$x>0$时有不等式
    $$
    \left|\int_x^{x+c} \frac{\sin t}{t^2}dt\right| \le \frac{2}{x} \quad (c > 0).
    $$
14. 证明:若$f$在$[a,b]$上可积,$\phi$在$[\alpha,\beta]$上严格单调且$\phi'$在$[\alpha,\beta]$上可积,$\phi(\alpha)=a,\phi(\beta)=b$,则有
    $$
    \int_a^b f(x)dx = \int_\alpha^\beta f(\phi(t))\phi'(t)dt.
    $$
15. 若$f$在$[a,b]$上连续可微,则存在$[a,b]$上连续可微的增函数$g$和连续可微的减函数$h$,使得
    $$
    f(x) = g(x)+h(x), \quad x\in[a,b].
    $$
16. *证明:若在$[a,b]$上$f$为连续函数,$g$为连续可微的单调函数,则存在$\xi\in[a,b]$,使得
    $$
    \int_a^b f(x)g(x)dx = g(a)\int_a^\xi f(x)dx + g(b)\int_\xi^b f(x)dx.
    $$
    (提示:与定理9.11及其推论相比较,这里的条件要强得多,因此可望有一个比较简单的,不同于定理9.11的证明.)

### *§6 可积性理论补叙

#### 一 上和与下和的性质
在§3第二段里,我们已经引入了上和$S(T)$和下和$s(T)$的概念,即对于分割 $T:a=x_0<x_1<\dots<x_n=b$,以及$\Delta_i=[x_{i-1},x_i], \Delta x_i=x_i-x_{i-1}$,有
$$
S(T) = \sum_{i=1}^n M_i\Delta x_i, \quad s(T) = \sum_{i=1}^n m_i\Delta x_i,
$$
其中$M_i=\sup_{x\in\Delta_i}f(x), m_i=\inf_{x\in\Delta_i}f(x), i=1,2,\dots,n$.由于假设$f$在$[a,b]$上有界,因此上述$M_i,m_i$以及$f$在$[a,b]$上的上、下确界$M$与$m$都存在,而且对于任何$\xi_i\in\Delta_i$