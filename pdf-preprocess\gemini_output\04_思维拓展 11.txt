### 二、综合应用题
01.【解答】
① 基本语句`k=k+10*i`共执行了$n-2$次，所以$T(n) = O(n)$。
② 设循环体共执行$t$次，每循环一次，循环变量$y$加1，最终$t=y$。故$t^2 \le n$，得$T(n) = O(n^{1/2})$。
③ 内循环执行$m$次，外循环执行$n$次，根据乘法原理，共执行了$m \times n$次，故$T(m,n) = O(m \times n)$。

### 归纳总结
本章的重点是分析程序的时间复杂度。一定要掌握分析时间复杂度的方法和步骤，很多读者在做题时一眼就能看出程序的时间复杂度，但就是无法规范地表述其推导过程。为此，编者查阅众多资料，总结出了此类题型的两种形式，供大家参考。

1. 循环主体中的变量参与循环条件的判断
在用于递推实现的算法中，首先找出基本运算的执行次数$x$与问题规模$n$之间的关系式，解得$x=f(n)$，$f(n)$的最高次幂为$k$，则算法的时间复杂度为$O(n^k)$。例如，
1.  ```c
    int i=1;
    while(i<=n)
        i=i*2;
    ```
2.  ```c
    int y=5;
    while((y+1)*(y+1)<n)
        y=y+1;
    ```
在例1中，设基本运算`i=i*2`的执行次数为$t$，则$2^t \le n$，解得$t \le \log_2 n$，故$T(n)=O(\log n)$。
在例2中，设基本运算`y=y+1`的执行次数为$t$，则$t=y-5$，且$(t+5+1)(t+5+1)<n$，解得$t<\sqrt{n}-6$，即$T(n)=O(\sqrt{n})$。

2. 循环主体中的变量与循环条件无关
此类题可采用数学归纳法或直接累计循环次数。多层循环时从内到外分析，忽略单步语句、条件判断语句，只关注主体语句的执行次数。此类问题又可分为递归程序和非递归程序：
*   递归程序一般使用公式进行递推。时间复杂度的分析如下：
    $T(n)=1+T(n-1)=1+1+T(n-2)=\dots=n-1+T(1)$
    即$T(n)=O(n)$。
*   非递归程序的分析比较简单，可以直接累计次数。本节前面给出了相关的习题。

### 思维拓展
求解斐波那契数列
$$
F(n) = 
\begin{cases}
0, & n=0 \\
1, & n=1 \\
F(n-1)+F(n-2), & n>1
\end{cases}
$$
有两种常用的算法：递归算法和非递归算法。试分别分析两种算法的时间复杂度。
**提示**
请结合归纳总结中的两种方法进行解答。

---

## 第2章 线性表

### 【考纲内容】
(一) 线性表的基本概念
(二) 线性表的实现
    顺序存储；链式存储
(三) 线性表的应用

### 【知识框架】
*   线性表
    *   顺序存储 ———— 顺序表
    *   链式存储
        *   单链表 ———— 指针实现
        *   双链表 ———— 指针实现
        *   循环链表 ———— 指针实现
        *   静态链表(借助数组实现)

### 【复习提示】
线性表是算法题命题的重点。这类算法题的实现比较容易且代码量较少，但是要求具有最优的性能(时间/空间复杂度)，才能获得满分。因此，应牢固掌握线性表的各种基本操作(基于两种存储结构)，在平时的学习中多注重培养动手能力。另需提醒的是，算法最重要的是思想！考场上的时间紧迫，在试卷上不一定要求代码具有实际的可执行性，因此应尽力表达出算法的思想和步骤，而不必过于拘泥所有细节。此外，采用时间/空间复杂度较差的方法也能拿到大部分分数，因此在时间紧迫的情况下，建议直接采用暴力法。注意，算法题只能用C/C++语言实现。

### 2.1 线性表的定义和基本操作

#### 2.1.1 线性表的定义
线性表是具有相同数据类型的$n(n \ge 0)$个数据元素的有限序列，其中$n$为表长，当$n=0$时线性表是一个空表。若用$L$命名线性表，则其一般表示为
$$
L = (a_1, a_2, \dots, a_i, a_{i+1}, \dots, a_n)
$$
式中，$a_1$是唯一的“第一个”数据元素，也称表头元素；$a_n$是唯一的“最后一个”数据元素，也称表尾元素。除第一个元素外，每个元素有且仅有一个直接前驱。除最后一个元素外，每个元素有且仅有一个直接后继(“直接前驱”和“前驱”、“直接后继”和“后继”通常被视为同义词)。以上就是线性表的逻辑特性，这种线性有序的逻辑结构正是线性表名字的由来。